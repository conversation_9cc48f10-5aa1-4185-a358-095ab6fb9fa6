//
//  AppDelegate.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "AppDelegate.h"
#import "SCAuthManager.h"
#import "SCAppIntegrationManager.h"
#import "SCALoginViewController.h"

@interface AppDelegate ()

@property (nonatomic, strong) NSTimer *networkCheckTimer;

@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    
    [kSCCodeMar configWithApiHost:@"https://test-app.ather.club" imHost:@"http://test-im.ather.club" termConditionsUrl:@"https://www.baidu.com" privacyPolicyUrl:@"https://www.baidu.com"];
    
    [kSCCodeMar initWith:self.window toLogin:^{
        dispatch_async(dispatch_get_main_queue(), ^{
           // A 面
        });
        
    } toLogout:^{
        //处理A面的清空逻辑
        dispatch_async(dispatch_get_main_queue(), ^{
           
        });
    } launchImageViewCallback:^(UIImageView * _Nonnull imageView) {
        imageView.image = [UIImage imageNamed:@"ic_launch"];
    } applogoCallback:^UIImage * _Nullable{
        return [UIImage imageNamed:@"appLogo.jpeg"];
    }];
    
    [kSCCodeMar application:application didFinishLaunchingWithOptions:launchOptions];

    self.window.backgroundColor = [UIColor whiteColor];
    [self.window makeKeyAndVisible];
    
    [self startNetworkCheck];
    
    return YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    [kSCCodeMar applicationDidBecomeActive:application];
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    [kSCCodeMar application:app openURL:url options:options];
    return YES;
}

- (void)checkNetworkStatus {
    if ([kSCCodeMar isNetworkAvailable]) {
        [self.networkCheckTimer invalidate];
        self.networkCheckTimer = nil;
        SCLog(@"checkNetworkStatus-------");
        
        [kSCCodeMar checkLoginWithAuditModeHandler:^(NSString * _Nullable loginStr) {
            [self routeToALogin];
        }];
    
    }
}

- (void)startNetworkCheck {
    if (self.networkCheckTimer) {
        [self.networkCheckTimer invalidate];
        self.networkCheckTimer = nil;
    }
    
    self.networkCheckTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
                                                            target:self
                                                          selector:@selector(checkNetworkStatus) 
                                                          userInfo:nil 
                                                           repeats:YES];
}

- (void)routeToALogin {
    SCALoginViewController *loginVC = [[SCALoginViewController alloc]init];
    self.window.rootViewController = loginVC;
}

@end
