//
//  SCAppIntegrationManager.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// 回调block定义
typedef void(^SCDeleteAccountCallback)(void);
typedef void (^SCVoidCallback)(void);
typedef void(^SCStrategyCallback)(BOOL isAudit, BOOL isMatchEnabled);
typedef void(^ _Nullable SCAuditModeHandler)(  NSString * _Nullable loginStr);
typedef void (^ SCImageViewCallBack)(UIImageView *imageView);
typedef UIImage* _Nullable (^ SCImageCallBack)(void);

@interface SCAppIntegrationManager : NSObject

@property (nonatomic, copy) NSString *apiHost;
@property (nonatomic, copy) NSString *imHost;
@property (nonatomic, copy) NSString *termConditionsUrl;
@property (nonatomic, copy) NSString *privacyPolicyUrl;

@property (nonatomic, weak) UIWindow *rootWindow;
@property (nonatomic, copy) void(^toLogin)(void);
@property (nonatomic, copy) void(^toLogout)(void);
@property (nonatomic, copy) SCImageCallBack applogoCallback;
@property (nonatomic, assign, readonly) BOOL isCourage;  // 是否是审核模式
/// 预先获取的Token，用在获取策略接口数据或者其他用途。
@property (nonatomic, copy) NSString *accessToken;
// @property (nullable, nonatomic, strong) SCTokenModel *preTokenModel; // 已移除，使用字典替代
@property (nullable, nonatomic, strong) NSDictionary *preTokenDict;

#pragma mark - 单例
+ (instancetype)shared;
// 配置信息
- (void)configWithApiHost:(NSString *)apiHost imHost:(NSString *)imHost termConditionsUrl:(NSString *)termConditionsUrl privacyPolicyUrl:(NSString *)privacyPolicyUrl;
#pragma mark - 初始化配置
/// 初始化SDK配置
/// @param window 主window
/// @param toLogin 登录回调
/// @param toLogout 登出回调
- (void)initWith:(UIWindow *)window 
         toLogin:(void(^)(void))toLogin 
        toLogout:(void(^)(void))toLogout
    launchImageViewCallback:(SCImageViewCallBack)launchImageViewCallback
            applogoCallback:(SCImageCallBack)applogoCallback;

#pragma mark - 回调设置
/// 设置删除账号回调
- (void)setDeleteAccountCallback:(SCDeleteAccountCallback)callback;

#pragma mark - App生命周期
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(nullable NSDictionary *)launchOptions;
- (void)applicationDidBecomeActive:(UIApplication *)application;
- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options;

#pragma mark - 用户相关
/// 删除账号
- (void)deleteAccount;

/// 获取UUID
- (NSString *)UUID;

/// 获取用户Token
- (NSString *)userToken;

/// 是否已经登录
- (BOOL)isLogin;

/// 获取策略配置
/// @param token 用户token
/// @param callback 回调策略参数
/// @param failure 失败回调
- (void)getStrategyWithToken:(NSString *)token
                   callback:(SCStrategyCallback)callback
                   failure:(void(^)(SCXErrorModel *error))failure;

/// 获取策略配置（字典版本）
/// @param token 用户token
/// @param callback 回调策略参数和字典数据
/// @param failure 失败回调
- (void)getStrategyDictWithToken:(NSString *)token
                        callback:(void(^)(BOOL isAudit, BOOL isMatchEnabled, NSDictionary *strategyDict))callback
                         failure:(void(^)(SCXErrorModel *error))failure;

#pragma mark - 页面跳转与登录
/// 预登录处理
/// @param completion 完成回调，返回是否预登录成功
- (void)preLoginCompletion:(void(^)(NSString *token))completion failure:(void(^)(SCXErrorModel *error))failure;

/// 跳转到登录页面
- (void)navigateToLoginPage;

/// 跳转到主页面
- (void)navigateToMainPage;

/// 审核模式回调A 面
- (void)callbackToAPageIsLogin:(NSString *)loginStr;

#pragma mark - 网络状态
- (BOOL)isNetworkAvailable;

#pragma mark - 登录检查
/// 统一的登录检查方法
/// @param auditModeHandler 审核模式下的处理回调，返回A面是否已登录
- (void)checkLoginWithAuditModeHandler:(SCAuditModeHandler)auditModeHandler;

/// 此方法不直接回调，一般是 B 面自己调用
- (void)checkUserLogin;
- (void)checkUserLoginCompletion:(SCVoidCallback)completion;

@end

NS_ASSUME_NONNULL_END

