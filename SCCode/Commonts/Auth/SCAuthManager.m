//
//  AuthManager.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCAuthManager.h"
//工具类
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
//其他管理类
#import "SCAPIServiceManager.h"

//Cantroller
#import "SCHomeViewController.h"
#import "SCBaseNavigationController.h"
#import "SCLoginViewController.h"
//数据模型
#import "SCXErrorModel.h"
//服务
#import "SCBannerService.h"
#import "SCCoinsService.h"
#import "SCPayService.h"
#import "SCIMService.h"
#import "SCTranslationService.h"
#import "SCSocketService.h"
#import "SCGiftService.h"
#import "SCActionService.h"
#import "SCCallService.h"
#import "SCOnlineStatesService.h"
#import "SCAnchorService.h"

#import "SCCallDBManager.h"
#import "SCAPIServiceManager.h"
#import "SCCategorySocket.h"
// 工具
#import "SCTrackingUtils.h"

@interface SCAuthManager()



@end

@implementation SCAuthManager
#pragma mark - 单例
static SCAuthManager *_instance = nil;
+ (instancetype)instance {
    static dispatch_once_t onceToken;

    dispatch_once(&onceToken, ^{
        _instance = [[SCAuthManager alloc] init];
        [_instance _init];
    });
    
    return _instance;
}

#pragma mark - 登录注册
//初始化 配置
-(void) _init{

    NSString * loginStr = [[NSUserDefaults standardUserDefaults] objectForKey:kSCLoginTokenKey];
    if(!kSCIsStrEmpty(loginStr)){
        _loginUserDict = [SCDataConverter safeDictionaryFromJSONString:loginStr];
    }
//    NSString * configStr = [[NSUserDefaults standardUserDefaults] stringForKey:kSCAppConfigKey];
//    if(!kSCIsStrEmpty(configStr)){
//        _appConfig = [SCDataConverter safeDictionaryFromJSONString:configStr];
//    }
    NSInteger availableCoins = [self _getAvailableCoinsFromLoginUser];
    _availableCoinsObx = [[SCObservable<NSNumber *>  alloc] initWithValue:@(availableCoins)];
    _languageObx = [[SCObservable<NSNumber *> alloc] initWithValue:@(0)];
    //初始化新用户的Obs
    _strategyObs = [[SCObservable<NSDictionary *>  alloc] initWithValue:nil];
    _languageMap = @{};
}

// 辅助方法：从登录用户字典中获取可用金币
- (NSInteger)_getAvailableCoinsFromLoginUser {
    if (!_loginUserDict) return 0;
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:_loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
    return [SCDictionaryHelper integerFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserAvailableCoinsKey defaultValue:0];
}

// 辅助方法：从登录用户字典中获取用户ID
- (NSString *)_getUserIDFromLoginUser {
    if (!_loginUserDict) return nil;
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:_loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
    return [SCDictionaryHelper userIDFromDictionary:userInfo];
}

// 辅助方法：从登录用户字典中获取token
- (NSString *)_getTokenFromLoginUser {
    return [SCDictionaryHelper stringFromDictionary:_loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:nil];
}

- (BOOL)isLogin{
    NSString *token = [self _getTokenFromLoginUser];
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:_loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
    return !kSCIsStrEmpty(token) && userInfo != nil;
}

-(void) doLogin:(void (^)(NSDictionary *tokenDict))success
       failure:(void (^)(SCXErrorModel *error))failure {
    kWeakSelf(self)
    void (^performLogin)(void) = ^{
        [SCAPIServiceManager requestLogin:^(NSDictionary *tokenDict) {
            [weakself sc_blank_empty];
            if (success) {
                NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:tokenDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
                NSString *userID = [SCDictionaryHelper userIDFromDictionary:userInfo];
                NSLog(@"userId---------->%@", userID);
                success(tokenDict);
            }
        } failure:^(SCXErrorModel *error) {
            [weakself sc_blank_empty];
            if (failure) {
                failure(error);
            }
        }];
    };

    if (self.appConfig != nil) {
        performLogin();
    } else {
        // 先获取 config
        [self remoteAppConfig:^(NSDictionary * _Nonnull appConfigDict) {
            [weakself sc_blank_empty];
            performLogin();
        } failure:failure];
    }
}

-(void)saveLoginUser:(NSDictionary *)tokenDict{
    _loginUserDict = tokenDict;
    NSInteger availableCoins = [self _getAvailableCoinsFromLoginUser];
    [_availableCoinsObx postValue:@(availableCoins)];
    [self _saveLoginUser];
}
-(void) _saveLoginUser{
    NSString *jsonString = [SCDataConverter jsonStringFromDictionary:self.loginUserDict];
    [[NSUserDefaults standardUserDefaults] setObject:jsonString forKey:kSCLoginTokenKey];
}

///更新内存中余额数据。用于金币临时变化，在修改后应该尽快，刷新接口的可以余额，以便于校对
-(void) updateLocalAvailableCoins:(NSInteger)availableCoins{
    if (_loginUserDict) {
        NSMutableDictionary *mutableLoginUser = [_loginUserDict mutableCopy];
        NSMutableDictionary *userInfo = [[SCDictionaryHelper dictionaryFromDictionary:_loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}] mutableCopy];
        userInfo[SCDictionaryKeys.shared.kSCUserAvailableCoinsKey] = @(availableCoins);
        mutableLoginUser[SCDictionaryKeys.shared.kSCUserInfoKey] = userInfo;
        _loginUserDict = [mutableLoginUser copy];
        [self _saveLoginUser];
        [_availableCoinsObx postValue:@(availableCoins)];
    }
}


- (void) doLogunSuccess:(NSDictionary * _Nonnull)tokenDict complete:(void(^_Nullable)(SCXErrorModel * _Nullable error))complete{
    //赋值并且保存登录成功的数据
    [self saveLoginUser:tokenDict];
    [SCAPIServiceManager requestSwitchWithMode:0 success:nil failure:nil];
    [self loginSuccessRouteHome];
    kSCBlockExeNotNil(complete,nil);
  }
-(void) loginSuccessRouteHome{
    
    ///初始化服务
    [self _initServices];
    //如果有AppConfig数据则直接转跳首页
    ///异步刷新当前用户信息
    [self remoteLoginUserInfo:nil failure:nil];
    ///异步刷新强化引导
    [self remoteStrongGuide:nil failure:nil];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        //转跳首页
        SCHomeViewController * homeVC = [[SCHomeViewController alloc] init];
        SCBaseNavigationController * nv = [[SCBaseNavigationController alloc] initWithRootViewController:homeVC];
        kSCCodeMar.rootWindow.rootViewController = nv;
    });
    
}

- (void)routeToLoginVC {
    SCLoginViewController *viewController = [[SCLoginViewController alloc] init];
    SCBaseNavigationController *nVC = [[SCBaseNavigationController alloc] initWithRootViewController:viewController];
    kSCCodeMar.rootWindow.rootViewController = nVC;
}

/// 切换语言后跳转到首页
- (void)routeHomeVC {
    SCHomeViewController * homeVC = [[SCHomeViewController alloc] init];
    SCBaseNavigationController * nv = [[SCBaseNavigationController alloc] initWithRootViewController:homeVC];
    kSCCodeMar.rootWindow.rootViewController = nv;
    //将呼叫弹窗移到上层
    [kScAuthMar.callService checkBeCallingPopup];
}

///检测登录状态
- (void) detectLogin:(void(^_Nullable)(SCXErrorModel * error))complete{
    if(![self isLogin]){
        [kSCCodeMar navigateToLoginPage];
        return;
    }

    [kScAuthMar doLogunSuccess:kScAuthMar.loginUserDict complete:complete];
}



- (void)doLogout:(void (^)(SCXErrorModel * _Nullable))onComplete{
    kWeakSelf(self);
    //退出登录接口
    [SCAPIServiceManager requestLogout:^{
        
        [weakself doLogoutNotRequest:onComplete];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(onComplete,error);
    }];
}

- (void)sc_blank_empty{}
///移除登录信息并且退出到登录页面【不请求退出登录接口，只处理本地逻辑】
-(void)doLogoutNotRequest:(void (^)(SCXErrorModel * _Nullable))onComplete {
    [self _stopServices];
    //删除登录信息
    _loginUserDict = nil;
    [_strategyObs unsubscribeAll];
    [[NSUserDefaults standardUserDefaults] setObject:nil forKey:kSCLoginTokenKey];
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kSCLoginTokenKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
    //转跳登录页面
    kSCAuthCallService.isMatch = NO;
    kSCAuthCallService.isVideo = NO;
    [kSCCodeMar checkUserLoginCompletion:^{
        kSCBlockExeNotNil(onComplete,nil);
    }];
    kSCCodeMar.toLogout();
}

-(void)deleteAccount:(void (^)(SCXErrorModel * _Nullable))onComplete{
    kWeakSelf(self);
    //退出登录接口
    [SCAPIServiceManager requestDeleteAccount:^{
        //数据库截断
        [SCCallDBManager.sharedManager truncateDB];
        //停止全部服务
        [self _stopServices];
        if(weakself){
            kStrongSelf;
            strongSelf -> _loginUserDict = nil;
            [strongSelf -> _strategyObs unsubscribeAll];
        }
        kSCAuthCallService.isMatch = NO;
        kSCAuthCallService.isVideo = NO;
        [[NSUserDefaults standardUserDefaults] setObject:nil forKey:kSCLoginTokenKey];
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:kSCLoginTokenKey];
        NSString *appDomain = [[NSBundle mainBundle] bundleIdentifier];
        [[NSUserDefaults standardUserDefaults] removePersistentDomainForName:appDomain];
        [[NSUserDefaults standardUserDefaults] synchronize];
        //转跳登录页面
        [kSCCodeMar checkUserLogin];
        kSCCodeMar.toLogout();
//        [self doLogoutNotRequest:nil];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(onComplete,error);
    }];
}


#pragma mark - 配置
-(void) remoteStrategWithToken:(NSString *_Nullable)token
                       success:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
                       failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestStrategyWithToken:token success:^(NSDictionary *strategyDict) {
        __strong SCAuthManager * strongSelf = weakself;
        if(strongSelf != nil){
            strongSelf -> _strategyObs.value = strategyDict;
        }
        if(success)
            success(strategyDict);
    } failure:^(SCXErrorModel *error) {
        if(failure)
            failure(error);
        [weakself _saveWithAppConfig:nil];
    }];
}

-(void) remoteStrateg:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
              failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    [self remoteStrategWithToken:nil success:success failure:failure];
}


#pragma mark - AppConfig
-(void) remoteAppConfig:(void (^_Nullable)(NSDictionary * _Nonnull appConfigDict))success
                failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    kWeakSelf(self)
    NSString *currentVer = [SCDictionaryHelper stringFromDictionary:self.appConfig forKey:SCDictionaryKeys.shared.kSCDictKeyVer defaultValue:nil];
    [SCAPIServiceManager requestAppConfig:currentVer success:^(NSDictionary *appConfigDict) {
        [kScAuthMar _saveWithAppConfig:appConfigDict];
        [weakself sc_blank_empty];
        if(success)
            success(appConfigDict);
    } failure:^(SCXErrorModel *error) {
        //因为 相同的AppConfig 版本号 因为不返回数据模型所以需要加个判断。
//        if(!kSCIsStrEmpty(currentVer) && error.code == SCNetworkResponstCodejsonParsingError){
//            if(success)
//                success(kScAuthMar -> _appConfig);
//        }else{
        [weakself sc_blank_empty];
            if(failure)
                failure(error);
//        }

    }];
}
- (void) _saveWithAppConfig:(NSDictionary *_Nullable)configDict{
    _appConfig = configDict;
    [self checkOldToNew];
//    NSString *jsonString = [SCDataConverter jsonStringFromDictionary:configDict];
//    [[NSUserDefaults standardUserDefaults] setObject:jsonString forKey:kSCAppConfigKey];


}

#pragma mark - 融云迁移
- (void)checkOldToNew {
    NSString *newAppKey = @"";
    // 从字典中获取区域代码和应用key
    RCAreaCode rcAreaCode = [SCDictionaryHelper rcAreaCodeModelFromConfigDict:_appConfig];
    NSString *rcAppKey = [SCDictionaryHelper rcAppKeyFromConfigDict:_appConfig];

    if (rcAreaCode != RCAreaCodeInvalid) { // 假设0表示Invalid
        if (rcAppKey && rcAppKey.length > 0) {
            newAppKey = rcAppKey;
        }
    }
    if (newAppKey.length == 0) return;

    NSString *oldAppKey = [[NSUserDefaults standardUserDefaults] stringForKey:@"lastRongCloudAppKey"];

    if (oldAppKey == nil || oldAppKey.length == 0) {
        oldAppKey = [SCDictionaryHelper rckFromConfigDict:_appConfig];
    }
    
    // 如果 oldAppKey 为空，或者 newAppKey 为空，则不迁移
    if (oldAppKey == nil || oldAppKey.length == 0 || newAppKey.length == 0) {
        return;
    }
    
    if ([newAppKey isEqualToString:oldAppKey]) {
        // 新旧appKey相同，不迁移
        return;
    }
    
    NSString *oldDir = [self getRongCloudStorageDocDirWithNewAppKey:oldAppKey];
    if (oldDir.length == 0) return;
    
    NSURL *oldStorage = [[NSFileManager defaultManager] fileExistsAtPath:oldDir] ? [NSURL fileURLWithPath:oldDir] : nil;
    
    if (oldStorage == nil) {
        // doc目录下不存在，尝试获取library目录
        oldDir = [self getRongCloudStorageLibraryDirWithNewAppKey:oldAppKey];
        oldStorage = [[NSFileManager defaultManager] fileExistsAtPath:oldDir] ? [NSURL fileURLWithPath:oldDir] : nil;
        
        if (oldStorage == nil) {
            return;
        }
    }
    
    // 获取 Documents/newAppKey/userId/storage 目录
    NSString *newDir = [self getRongCloudStorageLibraryDirWithNewAppKey:newAppKey];
    if (newDir.length == 0) return;
    
    NSString *storegeFile = [self getFileRongCloudStorageLibraryDirWithNewAppKey:newAppKey fileName:@"storage"];
    NSString *storegeshm = [self getFileRongCloudStorageLibraryDirWithNewAppKey:newAppKey fileName:@"storage-shm"];
    NSString *storegewal = [self getFileRongCloudStorageLibraryDirWithNewAppKey:newAppKey fileName:@"storage-wal"];
    
    // 如果新目录已经存在则证明新版本已经存在内容，不需要迁移
    if (![[NSFileManager defaultManager] fileExistsAtPath:storegeFile] &&
        ![[NSFileManager defaultManager] fileExistsAtPath:storegeshm] &&
        ![[NSFileManager defaultManager] fileExistsAtPath:storegewal]) {
        // 只迁移文件目录下的 "storage"、"storage-shm"、"storage-wal" 文件
        NSError *error;
        [self moveRongCloudStorageDirWithOldDir:oldDir newDir:newDir error:&error];
        if (error) {
        }
    } else {
    }
}

- (void)moveRongCloudStorageDirWithOldDir:(NSString *)oldDir newDir:(NSString *)newDir error:(NSError **)error {
    // 判断 newDir 是否存在，不存在则创建
    if (![[NSFileManager defaultManager] fileExistsAtPath:newDir]) {
        [[NSFileManager defaultManager] createDirectoryAtPath:newDir withIntermediateDirectories:YES attributes:nil error:error];
        if (*error) return;
    }
    
    NSArray *oldFiles = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:oldDir error:error];
    if (*error) return;
    
    for (NSString *file in oldFiles) {
        NSString *filePath = [oldDir stringByAppendingPathComponent:file];
        NSString *fileName = [filePath lastPathComponent];
        
        if ([fileName isEqualToString:@"storage"] || [fileName isEqualToString:@"storage-shm"] || [fileName isEqualToString:@"storage-wal"]) {
            NSString *newFilePath = [newDir stringByAppendingPathComponent:fileName];
            
            NSError *moveError;
            [[NSFileManager defaultManager] moveItemAtPath:filePath toPath:newFilePath error:&moveError];
            if (moveError) {
            } else {
            }
        }
    }
}

- (NSString *)getRongCloudStorageDocDirWithNewAppKey:(NSString *)newAppKey {
    // 获取当前用户ID
    NSString *userId = [self _getUserIDFromLoginUser] ?: @"";

    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    if (paths.count == 0) {
        return @"";
    }
    return [NSString stringWithFormat:@"%@/%@/%@", paths[0], newAppKey, userId];
}

- (NSString *)getRongCloudStorageLibraryDirWithNewAppKey:(NSString *)newAppKey {
    // 获取当前用户ID
    NSString *userId = [self _getUserIDFromLoginUser] ?: @"";

    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    if (paths.count == 0) {
        return @"";
    }
    return [NSString stringWithFormat:@"%@/RongCloud/%@/%@", paths[0], newAppKey, userId];
}

- (NSString *)getFileRongCloudStorageLibraryDirWithNewAppKey:(NSString *)newAppKey fileName:(NSString *)fileName {
    // 获取当前用户ID
    NSString *userId = [self _getUserIDFromLoginUser] ?: @"";

    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSApplicationSupportDirectory, NSUserDomainMask, YES);
    if (paths.count == 0) {
        return @"";
    }
    return [NSString stringWithFormat:@"%@/RongCloud/%@/%@/%@", paths[0], newAppKey, userId, fileName];
}


#pragma mark - 用户详情数据
///从服务器刷新当前用户信息
-(void) remoteLoginUserInfo:(void (^_Nullable)(NSDictionary * _Nonnull userInfoDict))success
                    failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{

    kWeakSelf(self)
    NSString *userID = [self _getUserIDFromLoginUser];
    [SCAPIServiceManager requestUserInfoWithUserId:userID cachePolicy:SCNetCachePolicyNotCache success:^(NSDictionary * _Nonnull userInfoDict) {
        __strong SCAuthManager * strongSelf = weakself;
        if(strongSelf != nil){
            // 更新登录用户字典中的用户信息
            NSMutableDictionary *mutableLoginUser = [strongSelf -> _loginUserDict mutableCopy];
            mutableLoginUser[SCDictionaryKeys.shared.kSCUserInfoKey] = userInfoDict;
            strongSelf -> _loginUserDict = [mutableLoginUser copy];
            [strongSelf saveLoginUser:strongSelf -> _loginUserDict];
        }
        kSCBlockExeNotNil(success,userInfoDict);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}

///更新本地用户信息（同时更新Model和字典）
-(void) updateLocalUserInfo:(NSDictionary * _Nonnull)updates {
    // 更新字典数据
    NSMutableDictionary *mutableLoginUser = [self -> _loginUserDict mutableCopy];
    NSMutableDictionary *mutableUserInfo = [[SCDictionaryHelper dictionaryFromDictionary:mutableLoginUser forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}] mutableCopy];

    // 应用更新
    [mutableUserInfo addEntriesFromDictionary:updates];
    mutableLoginUser[SCDictionaryKeys.shared.kSCUserInfoKey] = [mutableUserInfo copy];
    self -> _loginUserDict = [mutableLoginUser copy];

    // 同时更新Model对象以保持兼容性
//    if (self.loginUser.userInfo) {
//        for (NSString *key in updates) {
//            id value = updates[key];
//            if ([key isEqualToString:kSCUserNicknameKey]) {
//                self.loginUser.userInfo.nickname = value;
//            } else if ([key isEqualToString:@"birthday"]) {
//                self.loginUser.userInfo.birthday = value;
//            } else if ([key isEqualToString:kSCUserCountryKey]) {
//                self.loginUser.userInfo.country = value;
//            } else if ([key isEqualToString:@"about"]) {
//                self.loginUser.userInfo.about = value;
//            }
//        }
//    }

    // 保存到本地存储
    [self saveLoginUser:self -> _loginUserDict];
}

///更新用户媒体列表（同时更新Model和字典）
-(void) updateUserMediaList:(NSArray * _Nonnull)mediaList {
    // 更新字典数据
    NSMutableDictionary *mutableLoginUser = [self -> _loginUserDict mutableCopy];
    NSMutableDictionary *mutableUserInfo = [[SCDictionaryHelper dictionaryFromDictionary:mutableLoginUser forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}] mutableCopy];

    // 将媒体列表转换为字典数组
//    NSMutableArray *mediaDictArray = [[NSMutableArray alloc] init];
//    for (SCMediaListModel *media in mediaList) {
//        NSMutableDictionary *mediaDict = [[NSMutableDictionary alloc] init];
//        mediaDict[@"mediaId"] = media.mediaId ?: @"";
//        mediaDict[@"url"] = media.url ?: @"";
//        mediaDict[@"type"] = @(media.type);
//        mediaDict[@"mediaPath"] = media.mediaPath ?: @"";
//        [mediaDictArray addObject:mediaDict];
//    }

    mutableUserInfo[@"mediaList"] = [mediaList copy];
    mutableLoginUser[SCDictionaryKeys.shared.kSCUserInfoKey] = [mutableUserInfo copy];
    self -> _loginUserDict = [mutableLoginUser copy];

    // 同时更新Model对象以保持兼容性
//    if (self.loginUser.userInfo) {
//        self.loginUser.userInfo.mediaList = mediaList;
//    }

    // 保存到本地存储
    [self saveLoginUser:self -> _loginUserDict];
}

///更新用户设置开关（同时更新Model和字典）
-(void) updateUserSwitchSettings:(NSDictionary * _Nonnull)switchSettings {
    // 更新字典数据
    NSMutableDictionary *mutableLoginUser = [self -> _loginUserDict mutableCopy];
    NSMutableDictionary *mutableUserInfo = [[SCDictionaryHelper dictionaryFromDictionary:mutableLoginUser forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}] mutableCopy];

    // 应用设置更新
    [mutableUserInfo addEntriesFromDictionary:switchSettings];
    mutableLoginUser[SCDictionaryKeys.shared.kSCUserInfoKey] = [mutableUserInfo copy];
    self -> _loginUserDict = [mutableLoginUser copy];

    // 同时更新Model对象以保持兼容性
//    if (self.loginUser.userInfo) {
//        for (NSString *key in switchSettings) {
//            id value = switchSettings[key];
//            if ([key isEqualToString:@"isSwitchNotDisturbIm"]) {
//                self.loginUser.userInfo.isSwitchNotDisturbIm = [value boolValue];
//            } else if ([key isEqualToString:@"isSwitchNotDisturbCall"]) {
//                self.loginUser.userInfo.isSwitchNotDisturbCall = [value boolValue];
//            }
//        }
//    }

    // 保存到本地存储
    [self saveLoginUser:self -> _loginUserDict];
}

#pragma mark - 强化引导
-(void) remoteStrongGuide:(void (^_Nullable)(NSDictionary * _Nonnull strongGuideDict))success
                  failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestStrongGuideWithSuccess:^(NSDictionary *strongGuideDict) {
        __strong SCAuthManager * strongSelf = weakself;
        if(strongSelf != nil){
            strongSelf -> _strongGuideModel = strongGuideDict;
        }
        kSCBlockExeNotNil(success,strongGuideDict);
    } failure:^(SCXErrorModel *error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}
#pragma mark - 服务
///初始化服务
-(void) _initServices{
    NSString *newAppKey = [SCDictionaryHelper configItemDataFromDict:_appConfig itemName:SCDictionaryKeys.shared.kSCDictKeyRck];
    // 获取区域代码 - 需要根据实际字典结构调整
    RCAreaCode areaCode = [SCDictionaryHelper rcAreaCodeModelFromConfigDict:_appConfig];
    NSString *rcAppKey = [SCDictionaryHelper rcAppKeyFromConfigDict:_appConfig];
    if (areaCode != RCAreaCodeInvalid) {
        if (rcAppKey && rcAppKey.length > 0) {
            newAppKey = rcAppKey;
        }
    }

    [[NSUserDefaults standardUserDefaults] setObject:newAppKey forKey:@"lastRongCloudAppKey"];

    NSString *userID = [self _getUserIDFromLoginUser];
    ///初始化 首页Banner服务 (用于首页显示和其他子页面显示)
    _bannerService = [[SCBannerService alloc] initWithUserId:userID];
    ///异步请求Banner
    [_bannerService remoteBannerWithSuccess:nil failure:nil];

    //金币初始化
    _coinsService = [[SCCoinsService alloc] initWithUserId:userID];
    //拉取新用户促销
    [_coinsService remotePromotionWithSuccess:nil failure:nil];
    //拉取活动促销
    [_coinsService remoteActivityPromotionWithSuccess:nil failure:nil];

    //支付服务
    _payService = [[SCPayService alloc] initWithUserId:userID];

    //初始化 IM
    _imService = [[SCIMService alloc] initWithUserId:userID rck:newAppKey areaCode:areaCode];
    //开始链接融云
    [_imService connect];

    //初始化翻译服务
    NSString *googleTranslationKey = [SCDictionaryHelper googleTranslationKeyFromConfigDict:_appConfig];
    _translationService = [[SCTranslationService alloc] initWithUserId:userID key:googleTranslationKey];

    //socket服务
    _socketService = [[SCSocketService alloc] initWithUserId:userID];
    [_socketService connect];

    _giftService = [[SCGiftService alloc] initWithUserId:userID];
    [_giftService remoteGiftListWithSuccess:nil failure:nil];

    _actionService = [[SCActionService alloc] initWithUserId:userID];

    _callService = [[SCCallService alloc] initWithUserId:userID];
    //在线状态
    _onlineStatesService = [[SCOnlineStatesService alloc] initWithUserId:userID];
    
    _anchorService = [[SCAnchorService alloc]initWithUserId:userID];
    
    
}
///销毁全部服务
- (void) _stopServices{
    [_bannerService destroyService];
    _bannerService = nil;
    
    [_coinsService destroyService];
    _coinsService = nil;
    
    [_payService destroyService];
    _payService = nil;
    
    [_imService destroyService];
    _imService = nil;
    
    [_translationService destroyService];
    _translationService = nil;
    
    [_socketService destroyService];
    _socketService = nil;
    
    [_giftService destroyService];
    _giftService = nil;
    
    [_actionService destroyService];
    _actionService = nil;
    
    [_anchorService destroyService];
    _anchorService = nil;
}

@end

