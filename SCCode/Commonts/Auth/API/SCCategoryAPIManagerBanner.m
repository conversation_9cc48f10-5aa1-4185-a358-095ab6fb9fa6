//
//  SCCategoryAPIManagerBanner.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCCategoryAPIManagerBanner.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerBanner)

+(void) requestBannerInfWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetBannerInfo method:SCNetMethodPOST parameters:nil headers:nil success:^(id responseObject) {
        NSArray * result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@[]); // 返回空数组作为默认值
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

@end
