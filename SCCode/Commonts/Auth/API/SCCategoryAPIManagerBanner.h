//
//  SCCategoryAPIManagerBanner.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCAPIServiceManager.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerBanner)
+(void) requestBannerInfWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
