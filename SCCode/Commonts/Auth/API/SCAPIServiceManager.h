//
//  SCAPIServiceManager.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
#import "SCNetworkManager.h"
// #import "SCStrongGuideModel.h" // 已移除，使用字典替代

typedef enum : NSUInteger {
    UserMediaActionTypeAdd = 1,
    UserMediaActionTypeUpdate = 2,
    UserMediaActionTypeDelete = 3,
} UserMediaActionType;

@class SCXErrorModel,SCProgress;

@interface SCAPIServiceManager : NSObject

///登录
+(void)requestLogin:(void (^_Nullable)(NSDictionary * _Nonnull tokenDict))success
            failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
///退出登录
+(void)requestLogout:(void (^_Nullable)(void))success
             failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
///删除账号
+(void)requestDeleteAccount:(void (^_Nullable)(void))success
                    failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

///获取配置
+(void)requestStrategy:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
               failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
///获取配置,带 token
+(void)requestStrategyWithToken:(NSString *_Nullable)token
                        success:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
                        failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
///获取App配置
+ (void)requestAppConfig:(NSString * _Nullable)ver success:(void (^_Nullable)(NSDictionary *_Nonnull appConfigDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///主播列表
+(void) requestAnchorList:(NSString * _Nonnull)category tag:(NSString *_Nonnull) tag region:(NSString * _Nullable)region page:(int) page pageSize:(int) pageSize success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull anchorDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取主播排行榜
+(void) requestAnchorRankList:(int)count  success:(void (^_Nullable)(NSDictionary *_Nonnull anchorRankDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取用户排行榜
/// @param count 数量
+(void) requestUserRankList:(int)count  success:(void (^_Nullable)(NSDictionary *_Nonnull userRankDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取用户详情
+(void) requestUserInfoWithUserId:(NSString * _Nonnull) userId  cachePolicy:(SCNetCachePolicy )cachePolicy
                          success:(void (^_Nullable)(NSDictionary *_Nonnull userInfoDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取用户在线状态
+(void) requestUserStatusWithUserIds:(NSArray<NSString *> * _Nonnull) userIds success:(void (^_Nullable)(NSDictionary<NSString *,NSString *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;


///获取用户扩展信息
+(void) requesGetUserExtraInfoWithUserID:(NSString * _Nonnull) userID success:(void (^_Nullable)(NSDictionary*_Nonnull userExtraInfoDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;


///获取OSSPolicy
+(void) requesOSSPolicyWithSuccess:(void (^_Nullable)(NSDictionary*_Nonnull ossPolicyDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///图片上传（字典版本）
+ (NSURLSessionDataTask *_Nonnull) uploadImage:(NSData *_Nonnull)imageData fileName:(NSString *_Nonnull)fileName ossPolicyDict:(NSDictionary *_Nonnull) ossPolicyDict uploadProgress:( void (^_Nullable)(SCProgress *_Nonnull uploadProgress)) uploadProgressBlock success:(void (^_Nullable)(NSString *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///更新当前用户头像
+(void) requesUpdateAvatarWithPath:(NSString *_Nonnull)avatarPath success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///更新资源
+(void) requesUpdateImageMediaWithType:(UserMediaActionType)type deleteMediaId:(NSString *_Nullable)deleteMediaId mediaPath:(NSString * _Nullable)mediaPath replaceMediaId:(NSString *_Nullable)replaceMediaId success:(void (^_Nullable)(NSArray<NSDictionary *> * _Nonnull mediaDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///更新用户信息
+(void) requesUpdateUserInfoWithNickName:(NSString *_Nullable)nickName birthday:(NSString *_Nullable)birthday country:(NSString *_Nullable)country about:(NSString *_Nullable)about success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///获取用户简短的信息
+(void) requestUserBaseWithUserId:(NSString *_Nonnull) userId success:(void (^_Nullable)(NSDictionary *_Nonnull userBaseDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///拉黑举报
+(void) requestBlockAndReportWithUserId:(NSString * _Nonnull) broadcasterId complainCategory:(NSString * _Nonnull)complainCategory complainSub:(NSString * _Nullable)complainSub success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///取消拉黑
+(void) requestUnBlockWithUserId:(NSString * _Nonnull) blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///关注
+(void)requestFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///取消关注
+(void)requestUnFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///关注列表（字典版本）
+(void) requestFollowListWithPage:(int) page pageSize:(int) pageSize cachePolicy:(SCNetCachePolicy)cachePolicy success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull followDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///拉黑列表
+(void) requestBlockListWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull blockDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///免打扰开关
+(void) requestSwitchNotDisturbWithCall:(BOOL) isSwitchNotDisturbCall isSwitchNotDisturbIm:(BOOL)isSwitchNotDisturbIm success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取用户等级
+(void) requestUserLevelWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull userLevelDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取强化引导
+(void) requestStrongGuideWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull strongGuideDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取机器人客服消息
+(void) requestRobotCustomerQuestionSetWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull robotQuestionDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///风控上报
+(void) requestUploadRiskWithInfo:(NSString *_Nonnull) info success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///AF上传
+(void) requestUploadAFWithParam:(NSDictionary *_Nonnull)params token:(NSString * _Nullable )token success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;


@end

