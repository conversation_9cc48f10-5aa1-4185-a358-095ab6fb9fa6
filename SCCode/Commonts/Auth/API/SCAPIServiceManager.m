//
//  SCAPIServiceManager.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCAPIServiceManager.h"
#import "SCAppUtils.h"
#import "SCSafetyUtils.h"
#import "SCXErrorModel.h"
#import "SCTrackingUtils.h"
#import "SCDictionaryHelper.h"
#import "SCDataConverter.h"
#import "SCDictionaryKeys.h"
#import "SCModelCompatibility.h"

@implementation SCAPIServiceManager

+(void)requestLogin:(void (^_Nullable)(NSDictionary * _Nonnull tokenDict))success
            failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    NSString *info = [SCSafetyUtils encryptInfo];
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPILogin method:SCNetMethodPOSTFORM  parameters:@{@"oauthType":@4,@"token":[SCAppUtils identifier], @"info": info ?: @""} headers:@{} success:^(id responseObject) {
        if(success){
            // 直接使用字典
            NSDictionary *tokenDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
            if (tokenDict) {
                success(tokenDict);
                
                NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:tokenDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
                if (userInfo) {
                    NSString *token = [SCDictionaryHelper stringFromDictionary:tokenDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:nil];
                    
                    [SCTrackingUtils.shared reportAscribeRecordWithToken:token userId:kSCUserIDFromDict(userInfo)];
                }
              
                // 检查是否首次注册
                BOOL isFirstRegister = [SCDictionaryHelper boolFromDictionary:tokenDict forKey:SCDictionaryKeys.shared.kSCIsFirstRegisterKey defaultValue:NO];
                if (isFirstRegister) {
                    [SCTrackingUtils trackAjEvent:SCAPIManage.shared.kAdjustRegisterToken];
                } else {
                    [SCTrackingUtils trackAjEvent:SCAPIManage.shared.kAdjustLoginToken];
                }
            }
        }

    } failure:^(SCXErrorModel *error) {
        if(failure){
            failure(error);
        }
    }];
}
+(void)requestLogout:(void (^_Nullable)(void))success
            failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPILogout method:SCNetMethodPOSTFORM  parameters:nil headers:@{} success:^(id responseObject) {
     
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void)requestDeleteAccount:(void (^_Nullable)(void))success
            failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIDeleteAccount method:SCNetMethodPOSTFORM  parameters:nil headers:@{} success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Failed to delete account, please try again later".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


+(void)requestStrategyWithToken:(NSString *)token
                        success:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
                        failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIStrategy method:SCNetMethodGET token:token parameters:nil headers:@{} success:^(id responseObject) {
        if(success){
            // 直接使用字典
            NSDictionary *strategyDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
            if (strategyDict) {
                // 添加isEnergy字段
                NSMutableDictionary *mutableDict = [strategyDict mutableCopy];
                NSInteger isReviewPkg = [SCDictionaryHelper integerFromDictionary:responseObject forKey:@"isReviewPkg" defaultValue:0];
                mutableDict[@"isEnergy"] = @(isReviewPkg != 0);
                success(mutableDict);
            }
        }
    } failure:^(SCXErrorModel *error) {
        if(failure){
            failure(error);
        }
    }];
}

+(void)requestStrategy:(void (^_Nullable)(NSDictionary * _Nonnull model))success
                failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    [SCAPIServiceManager requestStrategyWithToken:nil success:success failure:failure];
}

+ (void)requestAppConfig:(NSString * _Nullable)ver success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    //注意这里的传参 ver并不是版本号 首次请求时ver参数传0，然后把返回值保存在本地，后续的请求ver就传返回值中的ver。如果后台没有新配置会返回空数组，否则会返回新的ver和新的配置信息。
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIAppConfig method:SCNetMethodGET  parameters:nil headers:nil success:^(id responseObject) {
        if(success){
            // 直接返回字典
            NSDictionary *configDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
            if (configDict) {
                success(configDict);
            } else {
                success(@{}); // 返回空字典作为默认值
            }
        }
    } failure:^(SCXErrorModel *error) {
        if(failure){
            failure(error);
        }
    }];
}

+(void) requestAnchorList:(NSString * _Nonnull)category tag:(NSString *_Nonnull) tag region:(NSString * _Nullable)region page:(int) page pageSize:(int) pageSize success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    NSMutableDictionary * parameters = @{@"category":category,@"tag":tag,@"page":@(page),@"limit":@(pageSize),@"isPageMode":@(true),@"isRemoteImageUrl":@(true)}.mutableCopy;
    if(!kSCIsStrEmpty(region)){
        parameters[@"region"] = region;
    }

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetAnchorList method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        //判断 responseObject 是否是数组
        NSArray *array = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(array){
            NSMutableArray *result = [NSMutableArray array];
            for (NSDictionary *dic in array) {
                if ([dic isKindOfClass:[NSDictionary class]]) {
                    // 标准化用户字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedUserDictionary:dic];
                    [result addObject:standardDict];
                }
            }
            if(success){
                success(result);
            }
        }else{
            if(failure){
                failure([[SCXErrorModel alloc] initWitMsg:@"Get Anchor List Error".translateString]);
            }
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requestAnchorRankList:(int)count  success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetAnchorRank method:SCNetMethodPOST parameters:@{@"count":@(count)} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSDictionary* result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result && success){
            success(result);
        } else if(success) {
            success(@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requestUserRankList:(int)count  success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUserRank method:SCNetMethodPOST parameters:@{@"count":@(count)} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSDictionary* result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result && success){
            success(result);
        } else if(success) {
            success(@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
+(void) requestUserInfoWithUserId:(NSString *) userId  cachePolicy:(SCNetCachePolicy)cachePolicy
 success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    if (!userId) {
        return;
    }
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUserInfo method:SCNetMethodGET parameters:@{@"userId":userId} headers:nil cachePolicy:cachePolicy success:^(id responseObject) {
        NSDictionary* result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result && success){
            // 标准化用户信息字典
            NSDictionary *standardDict = [SCModelCompatibility standardizedUserDictionary:result];
            success(standardDict);
        } else if(success) {
            success(@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requestUserStatusWithUserIds:(NSArray<NSString *> *) userIds success:(void (^_Nullable)(NSDictionary<NSString *,NSString *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUsersOnlineStatus method:SCNetMethodPOST parameters:@{@"userIds" : userIds} headers:nil success:^(id responseObject) {
        NSDictionary<NSString *,NSString *>* result = responseObject;
        if(success){
            success(result);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取主播额外信息接口

+(void) requesGetUserExtraInfoWithUserID:(NSString * _Nonnull) userID success:(void (^_Nullable)(NSDictionary*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUserExtraInfo method:SCNetMethodGET parameters:@{@"userId":userID} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}



///获取OSSPolicy
+(void) requesOSSPolicyWithSuccess:(void (^_Nullable)(NSDictionary*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetOSSPolicy method:SCNetMethodGET parameters:nil headers:nil success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+ (NSURLSessionDataTask *_Nonnull) uploadImage:(NSData *_Nonnull)imageData fileName:(NSString *_Nonnull)fileName ossPolicyDict:(NSDictionary *_Nonnull) ossPolicyDict uploadProgress:( void (^_Nullable)(SCProgress *_Nonnull uploadProgress)) uploadProgressBlock success:(void (^_Nullable)(NSString *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    ///使用UUID随机一个名称
//    NSString *fileName = [NSString stringWithFormat:@"%@.jpg", [NSUUID UUID].UUIDString];

    // 从字典中安全获取OSS策略信息
    NSString *accessKeyId = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"accessKeyId" defaultValue:@""];
    NSString *policy = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"policy" defaultValue:@""];
    NSString *signature = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"signature" defaultValue:@""];
    NSString *dir = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"dir" defaultValue:@""];
    NSString *callback = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"callback" defaultValue:@""];
    NSString *host = [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:@"host" defaultValue:@""];

    NSDictionary *parametrs = @{@"ossaccessKeyId":accessKeyId,
                                @"policy":policy,
                                @"signature":signature,
                                @"key":[NSString stringWithFormat:@"%@%@",dir,fileName],
                                @"callback":callback
    };
    
    return [SCNetworkManager uploadMultipleWithURL:host imageData:imageData fileName:fileName parameters:parametrs headers:nil uploadProgress:uploadProgressBlock success:^(id  _Nullable responseObject) {
        if([responseObject isKindOfClass:[NSDictionary class]]){
            kSCBlockExeNotNil(success,responseObject[@"filename"]);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"upload image fail".translateString]);
        }
        
    } failure:^(SCXErrorModel * _Nullable error) {
        kSCBlockExeNotNil(failure,error);
    }];
}



+(void) requesUpdateAvatarWithPath:(NSString *_Nonnull)avatarPath success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUpdateAvatar method:SCNetMethodPOST parameters:@{@"avatarPath":avatarPath} headers:nil success:^(id responseObject) {
        kSCBlockExeNotNil(success);
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
+(void) requesUpdateImageMediaWithType:(UserMediaActionType)type deleteMediaId:(NSString *_Nullable)deleteMediaId mediaPath:(NSString * _Nullable)mediaPath replaceMediaId:(NSString *_Nullable)replaceMediaId success:(void (^_Nullable)(NSArray<NSDictionary *> * _Nonnull mediaDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSMutableDictionary *params = @{@"actionType":@(type),@"coins":@(0)}.mutableCopy;
    if(!kSCIsStrEmpty(deleteMediaId)){
        params[@"deleteMediaId"] = deleteMediaId;
    }
    if(mediaPath){
        params[@"mediaPath"] = mediaPath;
    }
    if(!kSCIsStrEmpty(replaceMediaId)){
        params[@"replaceMediaId"] = replaceMediaId;
    }
    params[@"mediaType"] = @"photo";
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUpdateMedia method:SCNetMethodPOST parameters:params headers:nil success:^(id responseObject) {
        NSArray *mediaArray = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(mediaArray) {
            NSMutableArray *result = [NSMutableArray array];
            for (NSDictionary *mediaDict in mediaArray) {
                if ([mediaDict isKindOfClass:[NSDictionary class]]) {
                    // 标准化媒体字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedMediaDictionary:mediaDict];
                    [result addObject:standardDict];
                }
            }
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@[]); // 返回空数组作为默认值
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requesUpdateUserInfoWithNickName:(NSString *_Nullable)nickName birthday:(NSString *_Nullable)birthday country:(NSString *_Nullable)country about:(NSString *_Nullable)about success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
//    let param: [String : Any] = [
//        "about": about,
//        "birthday": birth,
//        "country": country,
//        "nickname": nickname,
//    ]
    NSMutableDictionary *params = @{}.mutableCopy;
    if(!kSCIsStrEmpty(about)){
        params[@"about"] = about;
    }
    if(!kSCIsStrEmpty(birthday)){
        params[@"birthday"] = birthday;
    }
    if(!kSCIsStrEmpty(country)){
        params[@"country"] = country;
    }
    if(!kSCIsStrEmpty(nickName)){
        params[@"nickname"] = nickName;
    }
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUpdateUserInfo method:SCNetMethodPOST parameters:params headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]]) {
            Boolean isSuccess = [(NSNumber *)responseObject boolValue];
            if(isSuccess){
                kSCBlockExeNotNil(success);
                return;
            }
        }
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Update User Info Fail".translateString]);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///获取用户简短的信息
+(void) requestUserBaseWithUserId:(NSString *) userId success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUserBasic method:SCNetMethodGET parameters:@{@"userId":userId} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(id responseObject) {
        NSDictionary* result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result != nil){
            // 标准化用户信息字典
            NSDictionary *standardDict = [SCModelCompatibility standardizedUserDictionary:result];
            kSCBlockExeNotNil(success,standardDict);
        }else {
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get User Base Info Fail".translateString]);
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///拉黑举报
+(void) requestBlockAndReportWithUserId:(NSString * _Nonnull) broadcasterId complainCategory:(NSString * _Nonnull)complainCategory complainSub:(NSString * _Nullable)complainSub success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    NSMutableDictionary *parameters = @{@"broadcasterId":broadcasterId,@"complainCategory":complainCategory}.mutableCopy;
    if(!kSCIsStrEmpty(complainSub)){
        parameters[@"complainSub"] = complainSub;
    }
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIBlockAndReport method:SCNetMethodPOST parameters:parameters headers:nil  success:^(id responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


///取消拉黑
+(void) requestUnBlockWithUserId:(NSString * _Nonnull) blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    //取消拉黑
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUnBlock method:SCNetMethodPOST parameters:@{@"blockUserId":blockUserId} headers:nil  success:^(id responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///关注
+(void)requestFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIFollow method:SCNetMethodPOST parameters:@{@"followUserId":followUserId} headers:nil  success:^(id responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///取消关注
+(void)requestUnFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUnFollow method:SCNetMethodPOST parameters:@{@"followUserId":followUserId} headers:nil  success:^(id responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///关注列表（字典版本）
+(void) requestFollowListWithPage:(int) page pageSize:(int) pageSize cachePolicy:(SCNetCachePolicy)cachePolicy success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIFollowList method:SCNetMethodPOST parameters:@{@"page":@(page),@"limit":@(pageSize)} headers:nil cachePolicy:cachePolicy success:^(id responseObject) {
        //判断 responseObject 是否是数组
        NSArray *array = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(array){
            NSMutableArray *result = [NSMutableArray array];
            for (NSDictionary *userDict in array) {
                if ([userDict isKindOfClass:[NSDictionary class]]) {
                    // 标准化用户字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedUserDictionary:userDict];
                    [result addObject:standardDict];
                }
            }
            kSCBlockExeNotNil(success,result);
        }else{
            if(failure){
                failure([[SCXErrorModel alloc] initWitMsg:@"Get Follow List Fail".translateString]);
            }
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}



///拉黑列表
+(void) requestBlockListWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIBlockList method:SCNetMethodPOST parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {

        NSArray *array = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(array){
            NSMutableArray *result = [NSMutableArray array];
            for (NSDictionary *blockUserDict in array) {
                if ([blockUserDict isKindOfClass:[NSDictionary class]]) {
                    // 可以根据需要标准化拉黑用户字典
                    [result addObject:blockUserDict];
                }
            }
            kSCBlockExeNotNil(success,result);
        }else{
            if(failure){
                failure([[SCXErrorModel alloc] initWitMsg:@"Get Block List Fail".translateString]);
            }
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///免打扰开关
+(void) requestSwitchNotDisturbWithCall:(BOOL) isSwitchNotDisturbCall isSwitchNotDisturbIm:(BOOL)isSwitchNotDisturbIm success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPISwitchNotDisturb method:SCNetMethodPOST parameters:@{@"isSwitchNotDisturbCall":@(isSwitchNotDisturbCall),@"isSwitchNotDisturbIm":@(isSwitchNotDisturbIm)} headers:nil  success:^(id responseObject) {

        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue] == YES){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Not Disturb Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取用户等级
+(void) requestUserLevelWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetUserLevel method:SCNetMethodGET parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSDictionary * levelDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(levelDict != nil){
            kSCBlockExeNotNil(success,levelDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get user level fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取强化引导
+(void) requestStrongGuideWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull guideDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetStrongGuide method:SCNetMethodPOST parameters:nil headers:nil success:^(id responseObject) {
        NSDictionary * guideDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(guideDict != nil){
            kSCBlockExeNotNil(success,guideDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get guide fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取机器人客服消息
+(void) requestRobotCustomerQuestionSetWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull questionSetDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetFAQ method:SCNetMethodPOST parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(id responseObject) {
        NSDictionary * questionSetDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(questionSetDict != nil){
            kSCBlockExeNotNil(success,questionSetDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get Robot Customer Question Set Fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+ (void)requestUploadRiskWithInfo:(NSString *)info success:(void (^)(void))success failure:(void (^)(SCXErrorModel * _Nonnull))failure {
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIUploadRisk method:SCNetMethodPOST parameters:@{@"info" : info} headers:@{} success:^(id  _Nonnull responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel * _Nonnull error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requestUploadAFWithParam:(NSDictionary *_Nonnull)params token:(NSString * _Nullable )token success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure {
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIAFUpload method:SCNetMethodPOST token:token parameters:params headers:@{} success:^(id  _Nonnull responseObject) {
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel * _Nonnull error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
@end
