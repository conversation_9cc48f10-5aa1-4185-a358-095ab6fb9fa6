//
//  AuthManager.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
#import "SCObservable.h"

//翻译
#define kSCAuthTranslaService kScAuthMar.translationService
//金币
#define kSCAuthCoinsService kScAuthMar.coinsService
//礼物
#define kSCAuthGiftService kScAuthMar.giftService
//Socket
#define kSCAuthSocketService kScAuthMar.socketService
//Action
#define kSCAuthActionService kScAuthMar.actionService
//呼叫服务
#define kSCAuthCallService kScAuthMar.callService
//用户在线状态刷新
#define kSCAuthOnlineStatesService kScAuthMar.onlineStatesService

@class SCXErrorModel;
@class SCBannerService,SCCoinsService,SCPayService,SCIMService,SCTranslationService,SCSocketService,SCGiftService,SCActionService,SCCallService,SCOnlineStatesService,SCAnchorService,SCHomeViewController;

@interface SCAuthManager : NSObject

+ (nonnull instancetype)instance;


@property(nullable,strong,nonatomic,readonly) NSDictionary * loginUserDict;
@property(assign,nonatomic) BOOL isLogin;
@property(weak,nonatomic,nullable) SCHomeViewController *homeVC;

@property(nonatomic,strong,nonnull)SCObservable<NSNumber *>* availableCoinsObx;
/// 切换语言
@property(nonatomic,strong,nonnull)SCObservable<NSNumber *>* languageObx;

/// 是否镜像
@property (nonatomic, assign) BOOL isLanguageForce;
@property (nonatomic, strong) NSDictionary * _Nonnull languageMap;

///更新内存中余额数据。用于金币临时变化，在修改后应该尽快，刷新接口的可以余额，以便于校对
-(void) updateLocalAvailableCoins:(NSInteger)availableCoins;

//登录
-(void) doLogin:(void (^_Nullable)( NSDictionary * _Nonnull tokenDict))success
        failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure;
//登录成功后调用
- (void) doLogunSuccess:(NSDictionary * _Nonnull)tokenDict complete:(void(^_Nullable)(SCXErrorModel * _Nullable error))complete;
///检测登录状态
- (void) detectLogin:(void(^_Nullable)(SCXErrorModel * _Nullable error))complete;

//删除账号
-(void)deleteAccount:(void (^_Nullable)(SCXErrorModel * _Nullable))onComplete;
///移除登录信息并且退出到登录页面【不请求退出登录接口，只处理本地逻辑】用于异常退出
-(void)doLogoutNotRequest:(void (^ _Nullable)(SCXErrorModel * _Nullable))onComplete;
//退出登录
-(void) doLogout:(void (^ _Nullable)(SCXErrorModel * _Nullable error))onComplete;
///保存登录信息
-(void)saveLoginUser:(NSDictionary * _Nonnull)tokenDict;
///跳转到 home
-(void)routeHomeVC;
///跳到登录页
-(void)routeToLoginVC;

#pragma mark - 配置 interface
@property(nonatomic,nullable,strong,readonly) SCObservable<NSDictionary *> * strategyObs;
-(void) remoteStrategWithToken:(NSString *_Nullable)token
                       success:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
                       failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
-(void) remoteStrateg:(void (^_Nullable)(NSDictionary * _Nonnull strategyDict))success
              failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

#pragma mark - AppConfig interface
@property(nonatomic,nullable,strong,readonly) NSDictionary * appConfig;
-(void) remoteAppConfig:(void (^_Nullable)(NSDictionary * _Nonnull appConfigDict))success
              failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

#pragma mark - 用户详情数据
///从服务器刷新当前用户信息
-(void) remoteLoginUserInfo:(void (^_Nullable)(NSDictionary * _Nonnull userInfoDict))success
              failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

///更新本地用户信息（同时更新Model和字典）
-(void) updateLocalUserInfo:(NSDictionary * _Nonnull)updates;

///更新用户媒体列表（同时更新Model和字典）
-(void) updateUserMediaList:(NSArray * _Nonnull)mediaList;

///更新用户设置开关（同时更新Model和字典）
-(void) updateUserSwitchSettings:(NSDictionary * _Nonnull)switchSettings;
#pragma mark - 强化引导
@property(nonatomic,nullable,strong,readonly) NSDictionary * strongGuideModel;
-(void) remoteStrongGuide:(void (^_Nullable)(NSDictionary * _Nonnull strongGuideDict))success
              failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

#pragma mark - 服务
#pragma mark - BannerService
@property(nonatomic,nullable,strong,readonly) SCBannerService *bannerService;
@property(nonatomic,nullable,strong,readonly) SCCoinsService *coinsService;
@property(nonatomic,nullable,strong,readonly) SCPayService *payService;
@property(nonatomic,nullable,strong,readonly) SCIMService *imService;
@property(nonatomic,nullable,strong,readonly) SCTranslationService *translationService;
@property(nonatomic,nullable,strong,readonly) SCSocketService *socketService;
@property(nonatomic,nullable,strong,readonly) SCGiftService *giftService;
@property(nonatomic,nullable,strong,readonly) SCActionService *actionService;
@property(nonatomic,nullable,strong,readonly) SCCallService *callService;
@property(nonatomic,nullable,strong,readonly) SCOnlineStatesService *onlineStatesService;
@property(nonatomic,nullable,strong,readonly) SCAnchorService *anchorService;

@end
