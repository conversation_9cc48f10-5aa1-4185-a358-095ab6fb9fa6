//
//  SCOSSManager.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/8.
//

#import "SCOSSManager.h"
#import "SCAPIServiceManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCOSSManager ()
@property (nonatomic, strong) NSMutableDictionary<NSString*,NSURLSessionDataTask *> *uploadTaskDic;
@property (nonatomic, strong) NSDictionary *ossPolicyDict; // OSS策略字典（可写）
@end

@implementation SCOSSManager

+ (instancetype)sharedManager {
    static SCOSSManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[SCOSSManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _uploadTaskDic = [NSMutableDictionary new];
    }
    return self;
}



// 字典版本API实现
- (void)remoteOSSPolicyDict:(void (^)(NSDictionary * _Nonnull))success failure:(void (^)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requesOSSPolicyWithSuccess:^(NSDictionary * _Nonnull ossPolicyDict) {
        __strong SCOSSManager *strongSelf = weakself;
        if(strongSelf != nil){
            strongSelf.ossPolicyDict = ossPolicyDict;
        }
        kSCBlockExeNotNil(success,ossPolicyDict);
    } failure:failure];
}

// 字典版本上传方法
- (void)uploadImage:(NSData *) imageData fileName:(NSString * _Nonnull)fileName uploadProgress:( void (^_Nullable)(SCProgress *_Nonnull uploadProgress)) uploadProgressBlock success:(void (^_Nullable)(NSString *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self)
    if(self.ossPolicyDict != nil){
        [weakself uploadImageWithDictInternal:imageData fileName:fileName ossPolicyDict:_ossPolicyDict uploadProgress:uploadProgressBlock success:success];
    }else{
        [self remoteOSSPolicyDict:^(NSDictionary * _Nonnull ossPolicyDict) {
            [weakself uploadImageWithDictInternal:imageData fileName:fileName ossPolicyDict:ossPolicyDict uploadProgress:uploadProgressBlock success:success];
        } failure:failure];
    }
}

// 字典版本内部上传方法
- (void)uploadImageWithDictInternal:(NSData *) imageData fileName:(NSString * _Nonnull)fileName ossPolicyDict:(NSDictionary *)ossPolicyDict uploadProgress:( void (^_Nullable)(SCProgress *_Nonnull uploadProgress)) uploadProgressBlock success:(void (^_Nullable)(NSString *_Nonnull))success {
    kWeakSelf(self)
    [SCAPIServiceManager uploadImage:imageData fileName:fileName ossPolicyDict:ossPolicyDict uploadProgress:uploadProgressBlock success:^(NSString * _Nonnull fileUrl) {
        [weakself.uploadTaskDic removeObjectForKey:fileName];
        kSCBlockExeNotNil(success,fileUrl);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.uploadTaskDic removeObjectForKey:fileName];
    }];
}
///停止上传
- (void) cancelUploadWithFileName:(NSString *)fileName{
    NSURLSessionDataTask * task = self.uploadTaskDic[fileName];
    if(task != nil){
        [task cancel];
        [self.uploadTaskDic removeObjectForKey:fileName];
    }
}

#pragma mark - OSS策略字典访问方法

//+ (NSString *)accessKeyIdFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicyAccessKeyIdKey defaultValue:@""];
//}
//
//+ (NSString *)policyFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicyPolicyKey defaultValue:@""];
//}
//
//+ (NSString *)signatureFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicySignatureKey defaultValue:@""];
//}
//
//+ (NSString *)dirFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicyDirKey defaultValue:@""];
//}
//
//+ (NSString *)callbackFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicyCallbackKey defaultValue:@""];
//}
//
//+ (NSString *)hostFromDict:(NSDictionary *)ossPolicyDict {
//    return [SCDictionaryHelper stringFromDictionary:ossPolicyDict forKey:kSCOSSPolicyHostKey defaultValue:@""];
//}

@end
