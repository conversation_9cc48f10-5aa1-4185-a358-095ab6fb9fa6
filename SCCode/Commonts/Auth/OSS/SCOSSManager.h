//
//  SCOSSManager.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/8.
//

#import <Foundation/Foundation.h>
@class SCProgress;
NS_ASSUME_NONNULL_BEGIN

@interface SCOSSManager : NSObject

@property(nonatomic,nullable,strong,readonly) NSDictionary * ossPolicyDict; // OSS策略字典
+ (instancetype)sharedManager;

// 字典版本API（推荐使用）
-(void) remoteOSSPolicyDict:(void (^_Nullable)(NSDictionary * _Nonnull ossPolicyDict))success
                    failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

// 字典版本上传方法（推荐使用）
- (void)uploadImage:(NSData *) imageData fileName:(NSString * _Nonnull)fileName uploadProgress:( void (^_Nullable)(SCProgress *_Nonnull uploadProgress)) uploadProgressBlock success:(void (^_Nullable)(NSString *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///停止上传
- (void) cancelUploadWithFileName:(NSString *)fileName;

#pragma mark - OSS策略字典访问方法
///// 从字典获取访问密钥ID
//+ (NSString *)accessKeyIdFromDict:(NSDictionary *)ossPolicyDict;
///// 从字典获取策略
//+ (NSString *)policyFromDict:(NSDictionary *)ossPolicyDict;
///// 从字典获取签名
//+ (NSString *)signatureFromDict:(NSDictionary *)ossPolicyDict;
///// 从字典获取目录
//+ (NSString *)dirFromDict:(NSDictionary *)ossPolicyDict;
///// 从字典获取回调
//+ (NSString *)callbackFromDict:(NSDictionary *)ossPolicyDict;
///// 从字典获取主机地址
//+ (NSString *)hostFromDict:(NSDictionary *)ossPolicyDict;
@end

NS_ASSUME_NONNULL_END
