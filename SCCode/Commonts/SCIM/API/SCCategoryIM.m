//
//  SCCategoryIM.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCCategoryIM.h"

// #import "SCAppConfigModel.h" // 已迁移到字典

@implementation SCAPIServiceManager (SCCategoryIM)

///获取融云token
+(void) requestRongCloudTokenWithSuccess:(void (^_Nullable)(NSString * _Nullable token))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    NSMutableDictionary *params = @{}.mutableCopy;
    
    NSString *rcAppKey = [SCDictionaryHelper rcAppKeyFromConfigDict:kScAuthMar.appConfig];
    if (!rcAppKey.isBlank) {
        params[@"appKey"] = rcAppKey;
    }
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGetRongCloudToken method:SCNetMethodGET parameters:params headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSString class]]){
            NSString * result = responseObject;
            kSCBlockExeNotNil(success,result);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"No RongCloudToken".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

@end
