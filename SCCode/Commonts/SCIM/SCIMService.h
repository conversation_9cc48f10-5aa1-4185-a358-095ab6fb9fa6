//
//  SCIMService.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCBaseAppService.h"
#import <RongIMLib/RongIMLib.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    SCIMRongCloudConnectStatusNoConnect,
    SCIMRongCloudConnectStatusConnecting,
    SCIMRongCloudConnectStatusSuccess,
    SCIMRongCloudConnectStatusFail,
} SCIMRongCloudConnectStatus;

@interface SCIMService : SCBaseAppService

@property(nonatomic,assign,readonly) SCIMRongCloudConnectStatus  connectStatus;
- (instancetype)initWithUserId:(NSString *)userId rck:(NSString *)rck areaCode:(RCAreaCode)areaCode;
-(void) connect;

- (BOOL)isSystemWithId:(NSString *)userId;
///系统消息账号
@property(nonatomic,strong,readonly) SCObservable<NSArray<NSDictionary *> *> *topOfficialUsersObs;
///新消息
@property(nonatomic,strong,readonly) SCObservable<RCMessage *> *receiveMessageObs;
///未读消息变化
@property(nonatomic,strong,readonly) SCObservable<NSNumber *> *unreadCountChangeObs;
///获取置顶的系统消息数据
- (void) remoteTopOfficialUsersWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *>  *users)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;


-(BOOL) isUserServiceAccountWithId:(NSString *)userId;
@property(nonatomic,strong) SCObservable<NSDictionary*> * userServiceAccountObx;
///获取客服数据
- (void) remoteUserServiceAccountWithSuccess:(void(^_Nullable)(NSDictionary *  userServiceAccount)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

@end

@interface SCIMService (SCIMConnection) <RCConnectionStatusChangeDelegate>

@end

@interface SCIMService(SCIMMessage)<RCIMClientReceiveMessageDelegate>

@end

@interface SCIMService(SCIMAction)

///发送礼物消息
- (RCMessage *)sendGiftMessageWithGiftCode:(NSString *)giftCode toUserName:(NSString *)toUserName targetId:(NSString *)targetId success:(void (^)(NSInteger messageId))success error:(void (^)(NSInteger messageId))error;
///视频聊天室发送文本消息 【双通道 IM&融云】
- (RCMessage *)sendVideoTextMessageWithText:(NSString *)text targetId:(NSString *)targetId success:(void (^)(RCMessage *message))success error:(void (^)(NSInteger messageId))error ;
///视频聊天室发送礼物消息【目前没有发送，紧回调本地用于显示】
- (RCMessage *)sendVideoGiftMessageWithGiftCode:(NSString *)giftCode name:(NSString *)name targetId:(NSString *)targetId success:(void (^)(RCMessage *message))success error:(void (^)(NSInteger messageId))error;
@end

NS_ASSUME_NONNULL_END
