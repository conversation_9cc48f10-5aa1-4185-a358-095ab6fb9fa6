//
//  SCIMService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCIMService.h"
#import "SCIMUIConfig.h"
#import <RongIMLib/RongIMLib.h>
#import "SCCategoryIM.h"
#import "SCHyperLinkMessage.h"
#import "SCNoneFlagMessage.h"
#import "SCSingleJsonMessage.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCSocketService.h"
#import "SCCallService.h"
#import "SCNoneFlagMessage.h"
#import "SCCoinsService.h"
#import "SCHomeViewController.h"
#import "SCMessagePopupManager.h"

@interface SCIMService()
@property(nonatomic,nonnull,copy) NSString * rck;
@property(nonatomic,nullable,copy) NSString * token;
@property(nonatomic,assign) NSInteger retryNum;
@property(nonatomic,assign) RCAreaCode areaCode;
@end
@implementation SCIMService

//融云Key
- (instancetype)initWithUserId:(NSString *)userId rck:(NSString *)rck areaCode:(RCAreaCode)areaCode
{
    self = [super initWithUserId:userId];
    if (self) {
        _rck = rck;
        _areaCode = areaCode;
        _topOfficialUsersObs = [[SCObservable<NSArray<NSDictionary *> *> alloc] initWithValue:@[]];
        _userServiceAccountObx = [[SCObservable<NSDictionary*> alloc] initWithValue:nil];
        _receiveMessageObs = [[SCObservable<RCMessage *> alloc] initWithValue:nil];
        _unreadCountChangeObs = [[SCObservable<NSNumber *> alloc] initWithValue:nil];
        [self _init];
    }
    return self;
}

-(void) _init{


    _retryNum = 0;
    RCInitOption *initOption = [[RCInitOption alloc]init];
    if (_areaCode != RCAreaCodeInvalid) {
        initOption.areaCode = _areaCode;
    }
    // 使用RCCoreClient进行初始化
    [[RCCoreClient sharedCoreClient] initWithAppKey:_rck option:initOption];

    // 移除RCKitConfigCenter相关配置，因为使用IMLib不需要这些UI配置
    // RCKitConfigCenter.message.disableMessageAlertSound = NO;
    // RCKitConfigCenter.ui.globalMessageAvatarStyle = RC_USER_AVATAR_CYCLE;
    // RCKitConfigCenter.ui.globalMessagePortraitSize = CGSizeMake(kSCMessageAvatarWH, kSCMessageAvatarWH);
    // RCKitConfigCenter.ui.globalConversationAvatarStyle = RC_USER_AVATAR_CYCLE;
    // RCKitConfigCenter.ui.globalConversationPortraitSize = CGSizeMake(kSCConversationAvatarWH, kSCConversationAvatarWH);

    // 设置消息接收代理
    [[RCCoreClient sharedCoreClient] addReceiveMessageDelegate:self];    
    // 添加连接状态监听
    [[RCCoreClient sharedCoreClient] addConnectionStatusChangeDelegate:self];
    // 注册自定义消息类型
    [[RCCoreClient sharedCoreClient] registerMessageType:[SCHyperLinkMessage class]];
    [[RCCoreClient sharedCoreClient] registerMessageType:[SCNoneFlagMessage class]];
    [[RCCoreClient sharedCoreClient] registerMessageType:[SCSingleJsonMessage class]];
    [[RCCoreClient sharedCoreClient] registerMessageType:[RCCommandMessage class]];


    //初始化后开始请求客服和系统消息的用户信息
    [self remoteTopOfficialUsersWithSuccess:nil failure:nil];
    [self remoteUserServiceAccountWithSuccess:nil failure:nil];

}
-(void) connect{
    //先获用户信息的融云ID然后再进行链接
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    _token = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserRongcloudTokenKey defaultValue:nil];
    [self _connect];
}
-(void) _connect{
    _connectStatus = SCIMRongCloudConnectStatusConnecting;
    
//    if(_retryNum > 3){
//        _connectStatus = SCIMRongCloudConnectStatusFail;
//        [kScAuthMar doLogoutNotRequest:nil];
//        return;
//    }
    kWeakSelf(self)
    if(_token == nil){
        [SCAPIServiceManager requestRongCloudTokenWithSuccess:^(NSString * _Nullable token) {
            //重置试错机会
            weakself.retryNum = 0;
            weakself.token = token;
            [weakself _connect];
            
        } failure:^(SCXErrorModel * _Nonnull error) {
            __strong SCIMService * strongSelf = weakself;
            strongSelf -> _connectStatus = SCIMRongCloudConnectStatusFail;
            //等待0.5秒后重试
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                weakself.retryNum += 1;
                [weakself _connect];
            });
            
            
        }];
        return;
    }
    
    // 使用RCCoreClient进行连接
    [[RCCoreClient sharedCoreClient] connectWithToken:_token dbOpened:^(RCDBErrorCode code) {
        //数据库报错
    } success:^(NSString *userId) {
        //链接成功
        weakself.retryNum = 0;
        __strong SCIMService * strongSelf = weakself;
        strongSelf -> _connectStatus = SCIMRongCloudConnectStatusSuccess;

        
        // 连接成功后，通知订阅者更新未读数。
        kScAuthMar.imService.unreadCountChangeObs.value = @(0);
    } error:^(RCConnectErrorCode errorCode) {
        
        if (errorCode == RC_CONN_TOKEN_INCORRECT) {
            weakself.retryNum += 1;
            ///清除当前token然后重新开始
            weakself.token = nil;
            [weakself _connect];

        } else {
            __strong SCIMService * strongSelf = weakself;
            strongSelf -> _connectStatus = SCIMRongCloudConnectStatusFail;
            //无法连接到 IM 服务器，请根据相应的错误码作出对应处理
        }
    }];
    
}

- (BOOL)isSystemWithId:(NSString *)userId{
    NSMutableArray *topUserIds = [[NSMutableArray alloc] init];
    // 从策略字典中获取官方用户ID列表
    NSArray *topOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCTopOfficialUserIdsKey defaultValue:@[]];
    NSArray *broadcasterFollowOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCBroadcasterFollowOfficialUserIdsKey defaultValue:@[]];
    [topUserIds addObjectsFromArray:topOfficialUserIds];
    [topUserIds addObjectsFromArray:broadcasterFollowOfficialUserIds];
    for (NSString * uId in topUserIds) {
        if([uId isEqualToString:userId]){
            return YES;
        }
    }
    return NO;
}

- (void) remoteTopOfficialUsersWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *>  *users)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    dispatch_group_t requestGroup = dispatch_group_create();
    dispatch_queue_t concurrentQueue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);

    kWeakSelf(self)

    __block NSMutableArray<NSDictionary *> * users = [NSMutableArray new];
    __block SCXErrorModel * requestError;

    //遍历数组拿到全部的用户数据
    NSMutableArray *topUserIds = [[NSMutableArray alloc] init];
    NSArray *topOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCTopOfficialUserIdsKey defaultValue:@[]];
    NSArray *broadcasterFollowOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCBroadcasterFollowOfficialUserIdsKey defaultValue:@[]];
    [topUserIds addObjectsFromArray:topOfficialUserIds];
    [topUserIds addObjectsFromArray:broadcasterFollowOfficialUserIds];
    for (NSString * uId in topUserIds) {
        dispatch_group_enter(requestGroup);
        __block BOOL isSuccess = false;
        [SCAPIServiceManager requestUserBaseWithUserId:uId success:^(NSDictionary * _Nonnull userDict) {
            [users addObject:userDict];
            isSuccess = true;
            dispatch_group_leave(requestGroup);
            [weakself sc_black_fun];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_black_fun];
            if(!isSuccess)
                dispatch_group_leave(requestGroup);
        }];
    }
    
    // 等待所有请求完成
    dispatch_group_notify(requestGroup, concurrentQueue, ^{
        
        if(requestError){
            dispatch_async(dispatch_get_main_queue(), ^{
                kSCBlockExeNotNil(failure,requestError);
            });
            return;
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            weakself.topOfficialUsersObs.value = [users copy];
            kSCBlockExeNotNil(success,users);
        });
    });
}

- (void)sc_black_fun{
    
}

- (void)remoteUserServiceAccountWithSuccess:(void (^)(NSDictionary * _Nonnull))success failure:(void (^)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self);
    NSString *userServiceAccountId = [SCDictionaryHelper stringFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCUserServiceAccountIdKey defaultValue:nil];
    [SCAPIServiceManager requestUserBaseWithUserId:userServiceAccountId success:^(NSDictionary * _Nonnull userDict) {
        weakself.userServiceAccountObx.value = userDict;
        kSCBlockExeNotNil(success,userDict);
    } failure:^(SCXErrorModel * _Nonnull error) {
        kSCBlockExeNotNil(failure,error);
        [weakself sc_black_fun];
    }];
}

- (BOOL)isUserServiceAccountWithId:(NSString *)userId{
    NSString *userServiceAccountId = [SCDictionaryHelper stringFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCUserServiceAccountIdKey defaultValue:nil];
    if([userId isEqualToString:userServiceAccountId]){
        return YES;
    }
    return NO;
}



- (void)destroyService{
    [super destroyService];
    // 移除消息接收代理
    [[RCCoreClient sharedCoreClient] removeReceiveMessageDelegate:self];
    // 移除连接状态代理
    [[RCCoreClient sharedCoreClient] removeConnectionStatusChangeDelegate:self];
    //断开融云连接
    [[RCCoreClient sharedCoreClient] disconnect];
}

@end
@implementation SCIMService(SCIMConnection)


- (void)onConnectionStatusChanged:(RCConnectionStatus)status {
    //链接状态
    
}

@end
@implementation SCIMService (SCIMMessage)

/*!
 接收消息的回调方法
 
 @param message     当前接收到的消息
 @param left        还剩余的未接收的消息数，left>=0
 
 @discussion 如果您设置了IMKit消息监听之后，SDK在接收到消息时候会执行此方法（无论App处于前台或者后台）。
 其中，left为还剩余的、还未接收的消息数量。比如刚上线一口气收到多条消息时，通过此方法，您可以获取到每条消息，left会依次递减直到0。
 您可以根据left数量来优化您的App体验和性能，比如收到大量消息时等待left为0再刷新UI。
 */
- (void)onReceived:(RCMessage *)message left:(int)left object:(id)object{
    
    dispatch_async(dispatch_get_main_queue(), ^{
        // 在这里执行需要在主线程上运行的代码
        
        self.receiveMessageObs.value = message;
        
        if([message.objectName isEqualToString:@"RC:TxtMsg"]){
            //处理文本消息
            if ([message.content isKindOfClass:[RCTextMessage class]]) {
                //文本消息
                RCTextMessage *textContent = (RCTextMessage *)message.content;
                if([textContent.extra isEqualToString:@"compensation"]){
                    
                    if (!message.isOffLine) {
                        //处理通话异常
                        [kSCAuthCallService handleCallExceptionsWithContent:textContent.content];
                    }
                    return;
                }
                
                if (!message.isOffLine) {
                    // 使用showMessagePopup方法，内部会自动获取用户信息并使用字典版本API
                    [[SCMessagePopupManager shared] showMessagePopup:textContent.content userId:message.senderUserId];
                }
            }
        }else if([message.objectName isEqualToString:@"RC:CmdMsg"]){
            //透传消息
            if ([message.content isKindOfClass:[RCCommandMessage class]]) {
                RCCommandMessage *textContent = (RCCommandMessage *)message.content;
                
                NSData *data = [textContent.data dataUsingEncoding:NSUTF8StringEncoding];
                NSError *error;
                NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
                if (error || dict == nil) {
                    return;
                }
                if ([textContent.name isEqualToString:@"responseEvent"]) {
                    if (!message.isOffLine) {
                        [kSCAuthSocketService handleEvent:dict];
                    }
                }
                else if ([textContent.name isEqualToString:@"messageEvent"]) {
                    [kSCAuthSocketService handleEvent:dict];
                }
                else if ([textContent.name isEqualToString:@"onGiftAsk"]) {
                    //索要礼物
                    [kSCAuthSocketService handleGiftAskEvent:dict];
                }
            }
        } else if ([message.objectName isEqualToString:@"LC:NoneFlagMsg"]) {
            
            // 获取消息内容
            RCMessageContent *content = message.content;
            
            // 判断是否为活动促销消息
            if ([content isKindOfClass:[SCNoneFlagMessage class]] &&
                [((SCNoneFlagMessage *)content).contentType isEqualToString:@"special_offer"]) {
                // 更新活动促销
                if (!message.isOffLine) {
                    kScAuthMar.homeVC.isCanAutoShowActivityPopup = true;
                    [kScAuthMar.coinsService remoteActivityPromotionWithSuccess:nil failure:nil];
                }
            }
        } else {
            if (!message.isOffLine) {
                [self popupMessage:message];
            }
        }
    });
    
    
}

/// 消息提示框
- (void)popupMessage:(RCMessage *)message {
    NSString *objectName = message.objectName;
    NSString *content = @"";
    if ([objectName isEqualToString:@"RC:ImgMsg"]) {
        content = @"image";
    } else if ([objectName isEqualToString:@"RC:HQVCMsg"]) {
        content = @"voice";
    } else if([objectName isEqualToString:@"RC:FileMsg"]) {
        content = @"file";
    } else if([objectName isEqualToString:@"LC:HyperLinkMsg"]){
        content = @"Link";
    } else if ([objectName isEqualToString:@"LC:SingleJsonMsg"]) {
        if ([message.content isKindOfClass:[SCSingleJsonMessage class]]) {
            SCSingleJsonMessage *messageContent = (SCSingleJsonMessage *)message.content;
            if ([messageContent.contentType isEqualToString:@"tpp"]) {
                content = @"Recharge Card";
            }
        }
    }
    if (!kSCIsStrEmpty(content)) {
        // 使用showMessagePopup方法，内部会自动获取用户信息并使用字典版本API
        [[SCMessagePopupManager shared] showMessagePopup:[NSString stringWithFormat:@"[%@]", content.translateString] userId:message.senderUserId];
    }
}

/*!
 当 Kit 收到消息回调的方法
 
 @param message 接收到的消息
 @return       YES 拦截, 不显示  NO: 不拦截, 显示此消息。
 此处只处理实时收到消息时，在界面上是否显示此消息。
 在重新加载会话页面时，不受此处逻辑控制。
 若要永久不显示此消息，需要从数据库删除该消息，在回调处理中调用 deleteMessages,
 否则在重新加载会话时会将此消息重新加载出来
 
 @discussion 收到消息，会执行此方法。
 
 */
//消息拦截
- (BOOL)interceptMessage:(RCMessage *)message{
    return NO;
}

@end
#pragma mark - Action

@implementation SCIMService (SCIMAction)

///发送礼物消息
- (RCMessage *)sendGiftMessageWithGiftCode:(NSString *)giftCode toUserName:(NSString *)toUserName targetId:(NSString *)targetId success:(void (^)(NSInteger messageId))success error:(void (^)(NSInteger messageId))error {
    SCSingleJsonMessage *giftMessage = [SCSingleJsonMessage messageCreateWithContent:@"You have sent" contentType:@"gift"];
    
    NSDictionary *param = @{
        @"giftCode": giftCode,
        @"fromUserName": kSCCurrentUserNickname ?: @"",
        @"toUserName": toUserName
    };
    //转为JSON字符串
    NSString *giftStr = [SCDataConverter jsonStringFromDictionary:param];
    if (giftStr) {
        giftMessage.extra = @"";
        giftMessage.content = giftStr;
    }
    kWeakSelf(self)
    __block RCMessage *message = nil;
    [[RCCoreClient sharedCoreClient] sendMessage:ConversationType_PRIVATE
                                         targetId:targetId
                                          content:giftMessage
                                      pushContent:nil
                                         pushData:nil
                                         attached:^(RCMessage * _Nullable attachedMessage) {
        // 在attached回调中获取message对象
        message = attachedMessage;
    } success:^(long messageId) {
        [weakself sc_blank_empty];
        if (success) {
            success(messageId);
        }
    } error:^(RCErrorCode nErrorCode, long messageId) {
        [weakself sc_blank_empty];
        if (error) {
            error(messageId);
        }
    }];
    
    return message;
}
- (void)sc_blank_empty{}

///视频聊天室发送文本消息 【双通道 IM&融云】
- (RCMessage *)sendVideoTextMessageWithText:(NSString *)text targetId:(NSString *)targetId success:(void (^)(RCMessage *message))success error:(void (^)(NSInteger messageId))error {
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970];
    
    NSDictionary *param = @{
        @"fromUserId": self.userId,
        @"content": text,
        @"timestamp": @((int64_t)timestamp)
    };
    
    NSDictionary *dataParam = @{
        @"code": @"200",
        @"commandId": [NSString stringWithFormat:@"rc%lld", (int64_t)timestamp],
        @"command": @"onChat",
        @"data": param
    };
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dataParam options:0 error:nil];
    NSString *str = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    RCCommandMessage *textMessage = [RCCommandMessage messageWithName:@"messageEvent" data:str];
    RCMessage *message = [[RCMessage alloc] initWithType:ConversationType_PRIVATE targetId:targetId direction:MessageDirection_SEND content:textMessage];
    kWeakSelf(self)
    [[RCCoreClient sharedCoreClient] sendMessage:message
                                      pushContent:nil
                                         pushData:nil
                                         attached:^(RCMessage * _Nullable attachedMessage) {
        // 在attached回调中可以获取最终的message对象
        // 这里message已经在上面创建，所以不需要重新赋值
    } successBlock:^(RCMessage * _Nonnull successMessage) {
        kSCBlockExeNotNil(success,successMessage);
        [weakself sc_black_fun];
    } errorBlock:^(RCErrorCode nErrorCode, RCMessage * _Nonnull errorMessage) {
        kSCBlockExeNotNil(error,errorMessage.messageId);
        [weakself sc_black_fun];
    }];
    
    
    NSDictionary *socketParam = @{
        @"toUserId": targetId,
        @"content": text,
        @"timestamp": @((int64_t)timestamp)
    };
    [kSCAuthSocketService sendMessageWithCommand:@"messageEvent" data:socketParam success:^{
        kSCBlockExeNotNil(success,message);
        [weakself sc_black_fun];
    } failure:^(SCXErrorModel * _Nonnull errorE) {
        kSCBlockExeNotNil(error,0);
        [weakself sc_black_fun];
    }];
    
    
    return message;
}

///视频聊天室发送礼物消息【目前没有发送，紧回调本地用于显示】
- (RCMessage *)sendVideoGiftMessageWithGiftCode:(NSString *)giftCode name:(NSString *)name targetId:(NSString *)targetId success:(void (^)(RCMessage *message))success error:(void (^)(NSInteger messageId))error {
    NSTimeInterval timestamp = [[NSDate date] timeIntervalSince1970];
    NSInteger firstIndex = arc4random_uniform(21); // 0到20的随机数
    NSInteger secondIndex = arc4random_uniform(21); // 0到20的随机数
    // 创建时间戳字符串
    NSString *timestampStr = [NSString stringWithFormat:@"%lld%ld%ld", (long long)timestamp, (long)firstIndex, (long)secondIndex];
    NSInteger newTimestamp = [timestampStr integerValue];
    NSDictionary *param = @{
        @"fromUserId": self.userId ?: @"",
        @"content": [NSString stringWithFormat:@"%@ to you a %@", name, giftCode],
        @"timestamp": @((int64_t)newTimestamp),
        @"giftCode": giftCode
    };
    
    NSDictionary *dataParam = @{
        @"code": @"200",
        @"commandId": [NSString stringWithFormat:@"rc%lld", (int64_t)newTimestamp],
        @"command": @"onGift",
        @"data": param
    };
    
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dataParam options:0 error:nil];
    NSString *str = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    RCCommandMessage *textMessage = [RCCommandMessage messageWithName:@"messageEvent" data:str];
    RCMessage *message = [[RCMessage alloc] initWithType:ConversationType_PRIVATE targetId:targetId direction:MessageDirection_SEND content:textMessage];
    
    // 调用success回调函数
    if (success) {
        success(message);
    }
    kSCBlockExeNotNil(success,message);
    
    NSDictionary *socketParam = @{
        @"toUserId": targetId,
        @"fromUserId": self.userId ?: @"",
        @"content": [NSString stringWithFormat:@"%@ to you a %@", name, giftCode],
        @"timestamp": @((int64_t)newTimestamp)
    };
    //发送Socket
    ///xxxx
    return message;
}

@end
