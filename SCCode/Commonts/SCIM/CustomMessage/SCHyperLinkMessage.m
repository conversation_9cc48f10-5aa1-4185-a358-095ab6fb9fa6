//
//  SCHyperLinkMessage.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCHyperLinkMessage.h"

@implementation SCHyperLinkMessage

+ (instancetype)messageCreateWithContent:(NSString *)content contentType:(NSString *)contentType {
    SCHyperLinkMessage *message = [[SCHyperLinkMessage alloc] init];
    message.content = content;
    message.contentType = contentType;
    return message;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        
    }
    return self;
}

- (NSData *)encode {
    NSMutableDictionary *dataDict = [NSMutableDictionary dictionary];
    dataDict[@"content"] = self.content;
    dataDict[@"contentType"] = self.contentType;
    if (self.extra != nil) {
        dataDict[@"extra"] = self.extra;
    }
    
    if (self.senderUserInfo != nil) {
        dataDict[@"user"] = [self encodeUserInfo:self.senderUserInfo];
    }
    
    NSData *jsonData = [NSJ<PERSON>NSerialization dataWithJSONObject:dataDict options:NSJSONWritingPrettyPrinted error:nil];
    
    return jsonData;
}

- (void)decodeWithData:(NSData *)data {
    NSDictionary *dataDict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:nil];
    
    self.content = [NSString stringWithFormat:@"%@", dataDict[@"content"]];
    self.contentType = [NSString stringWithFormat:@"%@", dataDict[@"contentType"]];
    if (dataDict[@"extra"] != nil) {
        self.extra = [NSString stringWithFormat:@"%@", dataDict[@"extra"]];
    }
    if (dataDict[@"user"] != nil) {
        [self decodeUserInfo:dataDict[@"user"]];
    }
}

+ (RCMessagePersistent)persistentFlag {
    return MessagePersistent_ISCOUNTED;
}

- (NSString *)conversationDigest {
    return self.content;
}

- (void)encodeWithCoder:(NSCoder *)coder {
    [coder encodeObject:self.content forKey:@"content"];
    [coder encodeObject:self.contentType forKey:@"contentType"];
    [coder encodeObject:self.extra forKey:@"extra"];
}

- (instancetype)initWithCoder:(NSCoder *)decoder {
    self = [super init];
    if (self) {
        self.content = [decoder decodeObjectForKey:@"content"] ?: @"";
        self.contentType = [decoder decodeObjectForKey:@"contentType"] ?: @"";
        self.extra = [decoder decodeObjectForKey:@"extra"] ?: @"";
    }
    return self;
}

+ (NSString *)getObjectName {
    return @"LC:HyperLinkMsg";
}

@end
