//
//  SCTimer.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCTimer.h"

@implementation SCTimer{
    dispatch_source_t _timer;
}

- (instancetype)initWithInterval:(NSTimeInterval)interval {
    self = [super init];
    if (self) {
        self.interval = interval;
    }
    return self;
}

- (void)startTimer {
    [self stopTimer];
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    _timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, queue);
    dispatch_source_set_timer(_timer, DISPATCH_TIME_NOW, self.interval * NSEC_PER_SEC, 0);
    dispatch_source_set_event_handler(_timer, ^{
        if (self.timerBlock) {
            self.timerBlock();
        }
    });
    dispatch_resume(_timer);
}

- (void)stopTimer {
    if (_timer) {
        dispatch_source_cancel(_timer);
        _timer = nil;
    }
}

@end
