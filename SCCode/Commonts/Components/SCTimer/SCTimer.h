//
//  SCTimer.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/15.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCTimer : NSObject

@property (nonatomic, assign) NSTimeInterval interval; // 定时器间隔时间，单位为秒
@property (nonatomic, copy) void (^timerBlock)(void); // 定时器回调的block

- (instancetype)initWithInterval:(NSTimeInterval)interval;
- (void)startTimer;
- (void)stopTimer;

@end


NS_ASSUME_NONNULL_END
