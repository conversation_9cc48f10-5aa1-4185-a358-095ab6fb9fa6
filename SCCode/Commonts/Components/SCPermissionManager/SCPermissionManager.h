//
//  SCPermissionManager.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/3/21.
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <Photos/Photos.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, SCPermissionType) {
    SCPermissionTypeCamera,
    SCPermissionTypePhotoLibrary,
    SCPermissionTypeMicrophone
};

typedef void(^SCPermissionCompletionBlock)(BOOL granted, BOOL shouldShowAlert);
typedef void(^SCPermissionAlertActionBlock)(void);

@interface SCPermissionManager : NSObject

+ (instancetype)shared;

// 检查权限状态
- (void)checkPermission:(SCPermissionType)type 
             completion:(SCPermissionCompletionBlock)completion;

// 显示权限提示弹窗（带回调）
- (void)showPermissionAlert:(SCPermissionType)type 
          fromViewController:(UIViewController *)viewController
                cancelBlock:(nullable SCPermissionAlertActionBlock)cancelBlock;

// 显示自定义消息的权限提示弹窗（带回调）
- (void)showPermissionAlertWithTitle:(NSString *)title 
                            message:(NSString *)message 
                            fromVC:(UIViewController *)viewController
                       cancelBlock:(nullable SCPermissionAlertActionBlock)cancelBlock;

// 显示简单的提示弹窗
- (void)showAlertWithMessage:(NSString *)message 
                     fromVC:(UIViewController *)viewController;

// 兼容旧版本的方法（不带回调）
- (void)showPermissionAlert:(SCPermissionType)type 
          fromViewController:(UIViewController *)viewController;

// 检查多个权限状态
- (void)checkPermissions:(NSArray<NSNumber *> *)types 
             completion:(SCPermissionCompletionBlock)completion;

@end

NS_ASSUME_NONNULL_END 
