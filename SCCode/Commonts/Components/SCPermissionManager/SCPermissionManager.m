//
//  SCPermissionManager.m
//  Supercall
//
//  Created by sumengliu on 2024/3/21.
//

#import "SCPermissionManager.h"

@implementation SCPermissionManager

+ (instancetype)shared {
    static SCPermissionManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (void)checkPermission:(SCPermissionType)type completion:(SCPermissionCompletionBlock)completion {
    switch (type) {
        case SCPermissionTypeCamera:
            [self checkCameraPermission:completion];
            break;
        case SCPermissionTypePhotoLibrary:
            [self checkPhotoLibraryPermission:completion];
            break;
        case SCPermissionTypeMicrophone:
            [self checkMicrophonePermission:completion];
            break;
    }
}

- (void)checkCameraPermission:(SCPermissionCompletionBlock)completion {
    AVAuthorizationStatus status = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
    kWeakSelf(self)
    switch (status) {
        case AVAuthorizationStatusAuthorized:
            completion(YES, NO);
            break;
        case AVAuthorizationStatusNotDetermined:
        {
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
                [weakself sc_blank_empty];
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(granted, !granted);
                });
            }];
        }            
            break;
        default:
            completion(NO, YES);
            break;
    }
}
- (void)sc_blank_empty{}
- (void)checkPhotoLibraryPermission:(SCPermissionCompletionBlock)completion {
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    kWeakSelf(self)
    switch (status) {
        case PHAuthorizationStatusAuthorized:
            completion(YES, NO);
            break;
        case PHAuthorizationStatusNotDetermined:
        {
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                [weakself sc_blank_empty];
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(status == PHAuthorizationStatusAuthorized, status != PHAuthorizationStatusAuthorized);
                });
            }];
        }
            break;
        default:
            completion(NO, YES);
            break;
    }
}

- (void)checkMicrophonePermission:(SCPermissionCompletionBlock)completion {
    AVAuthorizationStatus status = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeAudio];
    kWeakSelf(self)
    switch (status) {
        case AVAuthorizationStatusAuthorized:
            completion(YES, NO);
            break;
        case AVAuthorizationStatusNotDetermined:
        {
            [AVCaptureDevice requestAccessForMediaType:AVMediaTypeAudio completionHandler:^(BOOL granted) {
                [weakself sc_blank_empty];
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(granted, !granted);
                });
            }];
        }
            break;
        default:
            completion(NO, YES);
            break;
    }
}

- (void)showPermissionAlert:(SCPermissionType)type 
          fromViewController:(UIViewController *)viewController
                cancelBlock:(SCPermissionAlertActionBlock)cancelBlock {
    NSString *appName = [[NSBundle mainBundle].infoDictionary objectForKey:@"CFBundleDisplayName"] ?: @"App";
    NSString *title;
    NSString *message;
    
    switch (type) {
        case SCPermissionTypeCamera: {
            title = @"Camera Access Required".translateString;
            message = [@"### needs access to your camera. Please enable camera access in Settings.".translateString 
                      stringByReplacingOccurrencesOfString:@"###" withString:appName];
            break;
        }
        case SCPermissionTypePhotoLibrary: {
            title = @"Photos Access Required".translateString;
            message = [@"### needs access to your photos. Please enable photos access in Settings.".translateString 
                      stringByReplacingOccurrencesOfString:@"###" withString:appName];
            break;
        }
        case SCPermissionTypeMicrophone: {
            title = @"Microphone Access Required".translateString;
            message = [@"### needs access to your microphone. Please enable microphone access in Settings.".translateString 
                      stringByReplacingOccurrencesOfString:@"###" withString:appName];
            break;
        }
    }
    
    [self showPermissionAlertWithTitle:title message:message fromVC:viewController cancelBlock:cancelBlock];
}

- (void)showPermissionAlertWithTitle:(NSString *)title 
                            message:(NSString *)message 
                            fromVC:(UIViewController *)viewController
                       cancelBlock:(SCPermissionAlertActionBlock)cancelBlock {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:title
                                                                 message:message
                                                          preferredStyle:UIAlertControllerStyleAlert];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"Cancel".translateString
                                            style:UIAlertActionStyleCancel
                                          handler:^(UIAlertAction * _Nonnull action) {
        if (cancelBlock) {
            cancelBlock();
        }
    }]];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"Settings".translateString
                                            style:UIAlertActionStyleDefault
                                          handler:^(UIAlertAction * _Nonnull action) {
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]
                                         options:@{}
                               completionHandler:nil];
    }]];
    
    [viewController presentViewController:alert animated:YES completion:nil];
}

- (void)showAlertWithMessage:(NSString *)message fromVC:(UIViewController *)viewController {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:nil
                                                                 message:message
                                                          preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK".translateString
                                                      style:UIAlertActionStyleDefault
                                                    handler:nil];
    [alert addAction:okAction];
    [viewController presentViewController:alert animated:YES completion:nil];
}

// 添加不带回调的兼容方法
- (void)showPermissionAlert:(SCPermissionType)type fromViewController:(UIViewController *)viewController {
    [self showPermissionAlert:type fromViewController:viewController cancelBlock:nil];
}

- (void)checkPermissions:(NSArray<NSNumber *> *)types completion:(SCPermissionCompletionBlock)completion {
    if (types.count == 0) {
        completion(YES, NO);
        return;
    }
    
    // 创建递归检查权限的函数
    __block NSInteger currentIndex = 0;
    __weak typeof(self) weakSelf = self;
    
    // 声明 __block 变量以在 block 内部修改
    __block void(^checkNextPermission)(void);
    
    checkNextPermission = ^{
        if (currentIndex >= types.count) {
            // 所有权限都检查完毕且通过
            completion(YES, NO);
            // 清除引用，避免循环引用
            checkNextPermission = nil;
            return;
        }
        
        SCPermissionType type = [types[currentIndex] integerValue];
        [weakSelf checkPermission:type completion:^(BOOL granted, BOOL shouldShowAlert) {
            [weakSelf sc_blank_empty];
            if (!granted) {
                // 如果有任何一个权限未通过，立即返回失败
                completion(NO, shouldShowAlert);
                // 清除引用，避免循环引用
                checkNextPermission = nil;
                return;
            }
            
            // 检查下一个权限
            currentIndex++;
            if (checkNextPermission) {
                checkNextPermission();
            }
        }];
    };
    
    // 开始检查第一个权限
    checkNextPermission();
}

@end
