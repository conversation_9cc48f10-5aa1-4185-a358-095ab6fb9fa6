//
//  SCPopupManager.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/10/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 定义弹窗层级枚举
typedef NS_ENUM(NSInteger, SCPopupLevel) {
    SCPopupLevelLow = 0,      // 低层级
    SCPopupLevelNormal = 100, // 普通层级（默认）
    SCPopupLevelHigh = 200,   // 高层级
    SCPopupLevelAlert = 300   // 最高层级（警告/重要提示）
};

// 定义弹窗动画样式
typedef NS_ENUM(NSInteger, SCPopupAnimationStyle) {
    SCPopupAnimationStyleFade = 0,     // 渐变显示（默认）
    SCPopupAnimationStyleCenter,        // 从中间弹出
    SCPopupAnimationStyleBottom         // 从底部弹出
};

@protocol SCPopupManagerDelegate <NSObject>
@optional
/// 处理背景点击事件，通过回调决定是否关闭弹窗
/// @param completion 完成回调，传入 YES 表示关闭弹窗，NO 表示不关闭
- (void)handleBackgroundTapWithCompletion:(void(^)(BOOL shouldDismiss))completion;
@end

@interface SCPopupManager : NSObject

@property (nonatomic, strong) NSMutableArray<UIViewController *> *activePopups;

+ (instancetype)shared;

// 添加新的显示方法，支持层级和动画样式
- (void)showPopup:(UIViewController *)popup 
 inViewController:(nullable UIViewController *)viewController 
           level:(SCPopupLevel)level 
   animationStyle:(SCPopupAnimationStyle)style;

// 原有方法默认使用普通层级和默认动画样式
- (void)showPopup:(UIViewController *)popup 
 inViewController:(nullable UIViewController *)viewController;

- (void)showPopup:(UIViewController *)popup
 inViewController:(nullable UIViewController *)viewController
   animationStyle:(SCPopupAnimationStyle)style;

- (void)showPopup:(UIViewController *)popup
 inViewController:(nullable UIViewController *)viewController
            level:(SCPopupLevel)level;

- (void)dismissPopup:(UIViewController *)popup;
- (void)dismissAllPopups;

/**
 检查指定类型的弹窗是否已经显示
 @param popupClass 弹窗类型
 @return 是否已显示
 */
- (BOOL)isShowingPopupOfClass:(Class)popupClass;

@end

NS_ASSUME_NONNULL_END
