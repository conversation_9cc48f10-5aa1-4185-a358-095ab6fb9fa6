//
//  SCPopupManager.m
//  Supercall
//
//  Created by sumengliu on 2024/10/29.
//

#import "SCPopupManager.h"
#import <objc/runtime.h>

@interface SCPopupManager ()
// 使用字典存储弹窗和对应的层级
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, UIViewController *> *popupLevels;
@property (nonatomic, copy) NSString *backgroundViewKey;
@property (nonatomic, copy) NSString *tapViewKey;
@property (nonatomic, copy) NSString *popupLevelKey;
@property (nonatomic, copy) NSString *animationStyleKey;
@end

@implementation SCPopupManager

+ (instancetype)shared {
    static SCPopupManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SCPopupManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activePopups = [NSMutableArray array];
        _popupLevels = [NSMutableDictionary dictionary];
        _backgroundViewKey = @"backgroundView";
        _tapViewKey = @"tapView";
        _popupLevelKey = @"popupLevel";
        _animationStyleKey = @"animationStyle";
    }
    return self;
}

- (void)showPopup:(UIViewController *)popup 
 inViewController:(nullable UIViewController *)viewController 
           level:(SCPopupLevel)level 
   animationStyle:(SCPopupAnimationStyle)style {
    
    UIView *containerView;
    if (viewController) {
        containerView = viewController.view;
    } else {
        NSSet<UIScene *> *scenes = UIApplication.sharedApplication.connectedScenes;
        UIWindowScene *windowScene = (UIWindowScene *)[scenes anyObject];
        containerView = windowScene.windows.firstObject;
    }
    
    // 1. 创建并添加背景蒙层（只负责视觉效果）
    UIView *backgroundView = [[UIView alloc] initWithFrame:containerView.bounds];
    backgroundView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.2];
    backgroundView.alpha = 0;
    [containerView addSubview:backgroundView];
    
    // 2. 设置popup视图
    popup.view.frame = containerView.bounds;
    popup.view.backgroundColor = [UIColor clearColor];
    [containerView addSubview:popup.view];
    
    // 3. 创建并添加点击响应视图（插入到popup.view最底层）
    UIView *tapView = [[UIView alloc] initWithFrame:popup.view.bounds];
    tapView.backgroundColor = [UIColor clearColor];
    
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleBackgroundTap:)];
    [tapView addGestureRecognizer:tapGesture];
    
    [popup.view insertSubview:tapView atIndex:0];
    
    // 存储弹窗层级和关联对象
    self.popupLevels[@(level)] = popup;
    objc_setAssociatedObject(popup, (__bridge const void * _Nonnull)(self.backgroundViewKey), backgroundView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(popup, (__bridge const void * _Nonnull)(self.tapViewKey), tapView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(popup, (__bridge const void * _Nonnull)(self.popupLevelKey), @(level), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    objc_setAssociatedObject(popup, (__bridge const void * _Nonnull)(self.animationStyleKey), @(style), OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    
    // 添加到容器
    [containerView addSubview:popup.view];
    [self updatePopupLevels:containerView];
    [self.activePopups addObject:popup];
    
    // 根据动画样式设置初始状态
    switch (style) {
        case SCPopupAnimationStyleCenter: {
            popup.view.transform = CGAffineTransformMakeScale(0.1, 0.1);
            popup.view.center = containerView.center;
            popup.view.alpha = 1;
            break;
        }
        case SCPopupAnimationStyleBottom: {
            CGRect frame = popup.view.frame;
            frame.origin.y = containerView.bounds.size.height;
            popup.view.frame = frame;
            popup.view.alpha = 1;
            break;
        }
        default: {
            popup.view.alpha = 0;
            break;
        }
    }
    // 执行显示动画
    [UIView animateWithDuration:0.3 
                          delay:0 
         usingSpringWithDamping:0.8 
          initialSpringVelocity:0.5 
                        options:UIViewAnimationOptionCurveEaseOut 
                     animations:^{
        backgroundView.alpha = 1;
        switch (style) {
            case SCPopupAnimationStyleCenter: {
                popup.view.transform = CGAffineTransformIdentity;
                break;
            }
            case SCPopupAnimationStyleBottom: {
                CGRect frame = popup.view.frame;
                frame.origin.y = 0;
                popup.view.frame = frame;
                break;
            }
            default: {
                popup.view.alpha = 1;
                break;
            }
        }
    } completion:nil];
}

- (void)showPopup:(UIViewController *)popup
 inViewController:(nullable UIViewController *)viewController
            level:(SCPopupLevel)level {
    [self showPopup:popup inViewController:viewController level:level animationStyle:SCPopupAnimationStyleFade];
}

- (void)showPopup:(UIViewController *)popup
 inViewController:(nullable UIViewController *)viewController
   animationStyle:(SCPopupAnimationStyle)style {
    [self showPopup:popup inViewController:viewController level:SCPopupLevelNormal animationStyle:style];
}

// 原有方法使用默认层级
- (void)showPopup:(UIViewController *)popup inViewController:(nullable UIViewController *)viewController {
    [self showPopup:popup inViewController:viewController level:SCPopupLevelNormal animationStyle:SCPopupAnimationStyleFade];
}

// 更新弹窗层级顺序
- (void)updatePopupLevels:(UIView *)containerView {
    // 获取所有层级并排序
    NSArray *levels = [self.popupLevels.allKeys sortedArrayUsingSelector:@selector(compare:)];
    
    // 按层级顺序重新排列弹窗
    for (NSNumber *level in levels) {
        UIViewController *popup = self.popupLevels[level];
        if (popup && popup.view.superview == containerView) {
            [containerView bringSubviewToFront:popup.view];
        }
    }
}

- (void)dismissPopup:(UIViewController *)popup {
    NSNumber *styleNumber = objc_getAssociatedObject(popup, (__bridge const void * _Nonnull)(self.animationStyleKey));
    SCPopupAnimationStyle style = styleNumber ? [styleNumber integerValue] : SCPopupAnimationStyleFade;
    
    UIView *backgroundView = objc_getAssociatedObject(popup, (__bridge const void * _Nonnull)(self.backgroundViewKey));
    NSNumber *popupLevel = objc_getAssociatedObject(popup, (__bridge const void * _Nonnull)(self.popupLevelKey));
    
    if (popupLevel) {
        [self.popupLevels removeObjectForKey:popupLevel];
    }
    
    [UIView animateWithDuration:0.25 animations:^{
        backgroundView.alpha = 0;
        
        switch (style) {
            case SCPopupAnimationStyleCenter: {
                popup.view.transform = CGAffineTransformMakeScale(0.1, 0.1);
                popup.view.alpha = 0;
                break;
            }
            case SCPopupAnimationStyleBottom: {
                CGRect frame = popup.view.frame;
                frame.origin.y = popup.view.superview.bounds.size.height;
                popup.view.frame = frame;
                break;
            }
            default: {
                popup.view.alpha = 0;
                break;
            }
        }
    } completion:^(BOOL finished) {
        if (popup.parentViewController) {
            [popup willMoveToParentViewController:nil];
            [popup removeFromParentViewController];
        }
        [popup.view removeFromSuperview];
        [self.activePopups removeObject:popup];
    }];
}

- (void)dismissAllPopups {
    [self.popupLevels removeAllObjects];
    
    [UIView animateWithDuration:0.25 animations:^{
        for (UIViewController *popup in self.activePopups) {
            popup.view.alpha = 0;
        }
    } completion:^(BOOL finished) {
        for (UIViewController *popup in self.activePopups) {
            if (popup.parentViewController) {
                [popup willMoveToParentViewController:nil];
                [popup removeFromParentViewController];
            }
            [popup.view removeFromSuperview];
        }
        [self.activePopups removeAllObjects];
    }];
}

// 添加处理背景点击的方法
- (void)handleBackgroundTap:(UITapGestureRecognizer *)gesture {
    UIView *tapView = gesture.view;
    
    // 查找与背景视图关联的弹窗
    for (UIViewController *popup in self.activePopups) {
        UIView *popupTapView = objc_getAssociatedObject(popup, (__bridge const void * _Nonnull)(self.tapViewKey));
        if (popupTapView == tapView) {
            // 检查弹窗是否实现了代理方法
            if ([popup conformsToProtocol:@protocol(SCPopupManagerDelegate)]) {
                id<SCPopupManagerDelegate> delegate = (id<SCPopupManagerDelegate>)popup;
                if ([delegate respondsToSelector:@selector(handleBackgroundTapWithCompletion:)]) {
                    kWeakSelf(self);
                    [delegate handleBackgroundTapWithCompletion:^(BOOL shouldDismiss) {
                        if (shouldDismiss) {
                            [weakself dismissPopup:popup];
                        }
                    }];
                    break;
                }
            }
            // 如果没有实现代理方法，默认关闭弹窗
            [self dismissPopup:popup];
            break;
        }
    }
}

- (BOOL)isShowingPopupOfClass:(Class)popupClass {
    if (!popupClass) {
        return NO;
    }
    
    for (UIViewController *popup in self.activePopups) {
        if ([popup isMemberOfClass:popupClass]) {
            return YES;
        }
    }
    return NO;
}
@end
