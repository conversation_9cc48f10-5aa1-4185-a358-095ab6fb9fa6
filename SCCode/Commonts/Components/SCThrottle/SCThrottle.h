//
//  SCThrottle.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/10/26.
//


#import <Foundation/Foundation.h>

typedef void (^SCThrottleCallback)(void);

@interface SCThrottle : NSObject
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag onExecute:(SCThrottleCallback _Nonnull)onExecute;
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag onAfter:(SCThrottleCallback _Nullable)onAfter;
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onExecute:(SCThrottleCallback _Nonnull)onExecute;
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onAfter:(SCThrottleCallback _Nullable)onAfter;
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onExecute:(SCThrottleCallback _Nullable)onExecute onAfter:(SCThrottleCallback _Nullable)onAfter;
+ (void)cancelWithTag:(NSString *_Nonnull)tag;
+ (void)cancelAll;
+ (NSInteger)count;
@end
