//
//  SCThrottle.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/10/26.
//

#import "SCThrottle.h"


@interface _SCThrottleOperation : NSObject
@property (nonatomic, copy) NSString *_Nonnull tag;
@property (nonatomic, copy) SCThrottleCallback _Nullable callback;
@property (nonatomic, copy) SCThrottleCallback _Nullable onAfter;
@property (nonatomic, strong) NSTimer *_Nullable timer;
@end

@implementation _SCThrottleOperation
@end

@implementation SCThrottle {
    NSMutableDictionary *_Nonnull _operations;
}

+ (instancetype _Nonnull)sharedInstance {
    static SCThrottle *_Nonnull _sharedInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _sharedInstance = [[SCThrottle alloc] init];
    });
    return _sharedInstance;
}

- (instancetype _Nonnull)init {
    self = [super init];
    if (self) {
        _operations = [[NSMutableDictionary alloc] init];
    }
    return self;
}

+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag onExecute:(SCThrottleCallback _Nonnull)onExecute{
    return [self throttleWithTag:tag duration:1 onExecute:onExecute onAfter:nil];
}
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag onAfter:(SCThrottleCallback _Nullable)onAfter{
    return [self throttleWithTag:tag duration:1 onExecute:nil onAfter:onAfter];
}

+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onExecute:(SCThrottleCallback _Nonnull)onExecute{
    return [self throttleWithTag:tag duration:duration onExecute:onExecute onAfter:nil];
}
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onAfter:(SCThrottleCallback _Nullable)onAfter{
    return [self throttleWithTag:tag duration:duration onExecute:nil onAfter:onAfter];
}
+ (BOOL)throttleWithTag:(NSString *_Nonnull)tag duration:(NSTimeInterval)duration onExecute:(SCThrottleCallback _Nullable)onExecute onAfter:(SCThrottleCallback _Nullable)onAfter {
    SCThrottle *_Nonnull instance = [SCThrottle sharedInstance];
    BOOL throttled = [instance->_operations objectForKey:tag] != nil;
    if (throttled) {
        return YES;
    }
    
    _SCThrottleOperation *_Nonnull operation = [[_SCThrottleOperation alloc] init];
    operation.tag = tag;
    operation.callback = onExecute;
    operation.onAfter = onAfter;
    operation.timer = [NSTimer scheduledTimerWithTimeInterval:duration target:instance selector:@selector(timerFired:) userInfo:operation repeats:NO];
    [instance->_operations setObject:operation forKey:tag];
    
    kSCBlockExeNotNil(onExecute);
    
    return NO;
}

+ (void)cancelWithTag:(NSString *_Nonnull)tag {
    SCThrottle *_Nonnull instance = [SCThrottle sharedInstance];
    _SCThrottleOperation *_Nullable operation = [instance->_operations objectForKey:tag];
    if (operation) {
        [operation.timer invalidate];
        [instance->_operations removeObjectForKey:tag];
    }
}

+ (void)cancelAll {
    SCThrottle *_Nonnull instance = [SCThrottle sharedInstance];
    for (_SCThrottleOperation *_Nonnull operation in instance->_operations.allValues) {
        [operation.timer invalidate];
    }
    [instance->_operations removeAllObjects];
}

+ (NSInteger)count {
    SCThrottle *_Nonnull instance = [SCThrottle sharedInstance];
    return instance->_operations.count;
}

- (void)timerFired:(NSTimer *_Nonnull)timer {
    _SCThrottleOperation *_Nonnull operation = timer.userInfo;
    [operation.timer invalidate];
    [self->_operations removeObjectForKey:operation.tag];
    if (operation.onAfter) {
        operation.onAfter();
    }
}

@end
