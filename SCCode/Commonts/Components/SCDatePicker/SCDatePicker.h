//
//  SCDatePicker.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCDatePicker : UIView


- (instancetype)initWithSureBlock:(void (^)(NSDate *))sureBlock title:(NSString *_Nullable)title sureTitle:(NSString *_Nullable)sureTitle currentDate:(NSDate * _Nullable)currentDate maxDate:(NSDate *_Nullable)maxDate minDate:(NSDate * _Nullable)minDate;
- (void)show;
- (void)hide;

@end

NS_ASSUME_NONNULL_END
