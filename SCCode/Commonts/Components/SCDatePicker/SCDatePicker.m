//
//  SCDatePicker.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCDatePicker.h"
#import "SCLanguageManager.h"
#import <Masonry/Masonry.h>

@interface SCDatePicker()

@property (nonatomic, strong) UIView *myMaskView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIDatePicker *datePicker;



@property (nonatomic, copy) void (^sureBlock)(NSDate *);
@property (nonatomic, strong) NSString *title;
@property (nonatomic, strong) NSString *sureTitle;
@property (nonatomic, strong) NSDate *currentDate;
@property (nonatomic, strong) NSDate *maxDate;
@property (nonatomic, strong) NSDate *minDate;
@end
@implementation SCDatePicker


- (instancetype)initWithSureBlock:(void (^)(NSDate *))sureBlock title:(NSString *_Nullable)title sureTitle:(NSString *_Nullable)sureTitle currentDate:(NSDate * _Nullable)currentDate maxDate:(NSDate *_Nullable)maxDate minDate:(NSDate * _Nullable)minDate{
    self = [super initWithFrame:[UIScreen mainScreen].bounds];
    if (self) {
        _sureBlock = sureBlock;
        _title = title;
        _sureTitle = sureTitle;
        _currentDate = currentDate;
        _maxDate = maxDate;
        _minDate = minDate;
        
        self.frame = [UIScreen mainScreen].bounds;
        self.myMaskView = [[UIView alloc] initWithFrame:self.bounds];
        self.myMaskView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.2];
        self.myMaskView.alpha = 0.0;
        [self addSubview:self.myMaskView];
        
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(overlayTapped)];
        [self.myMaskView addGestureRecognizer:tapGesture];
        
        self.contentView = [[UIView alloc] initWithFrame:CGRectMake(0, CGRectGetHeight(self.bounds), CGRectGetWidth(self.bounds), 316)];
        self.contentView.backgroundColor = [UIColor clearColor];
        self.contentView.layer.cornerRadius = 18;
        [self addSubview:self.contentView];
        
        [self createSubview];
    }
    return self;
}

- (void)show {
    self.frame = [UIScreen mainScreen].bounds;
    [[UIApplication sharedApplication].keyWindow addSubview:self];
    
    [UIView animateWithDuration:0.3 animations:^{
        self.myMaskView.alpha = 1.0;
        self.contentView.frame = CGRectMake(0, CGRectGetHeight(self.bounds) - CGRectGetHeight(self.contentView.frame), CGRectGetWidth(self.bounds), CGRectGetHeight(self.contentView.frame));
    }];
}

- (void)hide {
    [UIView animateWithDuration:0.1 animations:^{
        self.myMaskView.alpha = 0.0;
        self.contentView.frame = CGRectMake(0, CGRectGetHeight(self.bounds), CGRectGetWidth(self.bounds), CGRectGetHeight(self.contentView.frame));
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

- (void)createSubview {
    self.contentView.backgroundColor = [UIColor colorWithHexString:@"#EEEEEE"];
    self.contentView.layer.cornerRadius = kSCBigCornerRadius;
    self.contentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    
    UILabel *titleLb = [UILabel labelWithText:self.title textColor:[UIColor scBlack] font:kScUIFontSemibold(20) alignment:NSTextAlignmentCenter];
    
    [self.contentView addSubview:titleLb];
    [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView.mas_leading).offset(13);
        make.top.equalTo(self.contentView.mas_top).offset(13);
        make.height.equalTo(@30);
    }];
    
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setTitle:@"Cancel".translateString forState:UIControlStateNormal];
    closeBtn.backgroundColor = [UIColor scGrayBtn];
    [closeBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    closeBtn.layer.cornerRadius = 15;
    closeBtn.layer.masksToBounds = YES;
    [closeBtn addTarget:self action:@selector(closeAction) forControlEvents:UIControlEventTouchUpInside];
    
    [self.contentView addSubview:closeBtn];
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView.mas_trailing).offset(-13);
        make.centerY.equalTo(titleLb.mas_centerY);
        make.width.equalTo(@74);
        make.height.equalTo(@30);
    }];
    
    UIView *dateContentView = [[UIView alloc] init];
    dateContentView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.07];
    dateContentView.layer.cornerRadius = 18;
    
    [self.contentView addSubview:dateContentView];
    [dateContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView.mas_leading).offset(13);
        make.trailing.equalTo(self.contentView.mas_trailing).offset(-13);
        make.top.equalTo(titleLb.mas_bottom).offset(20);
        make.height.equalTo(@154);
    }];
    
    UIButton *saveBtn = [UIButton buttonWithTitle:self.sureTitle titleColor:[UIColor scWhite] font:kScUIFontMedium(20) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(sureAction)];
    [saveBtn sc_setThemeGradientBackground];
        
    [self.contentView addSubview:saveBtn];
    [saveBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView.mas_leading).offset(27);
        make.trailing.equalTo(self.contentView.mas_trailing).offset(-27);
        make.top.equalTo(dateContentView.mas_bottom).offset(20);
        make.height.equalTo(@54);
    }];
    
    UIDatePicker *datePicker = [[UIDatePicker alloc] init];
    if (@available(iOS 13.4, *)) {
        datePicker.preferredDatePickerStyle = UIDatePickerStyleWheels;
    }
    datePicker.datePickerMode = UIDatePickerModeDate;
    if(_maxDate != nil){
        datePicker.maximumDate = _maxDate;
    }else{
        datePicker.maximumDate = [NSDate date];
    }
    if(_minDate != nil){
        datePicker.minimumDate = _minDate;
    }else{
        
    }
    
    datePicker.date = self.currentDate;
    datePicker.tintColor = [UIColor blackColor];
    
    //使用应用内选择的语言
    NSString *selectedLanguageCode = [SCLanguageManager getDeviceLanguage];
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:selectedLanguageCode];
    datePicker.locale = locale;
    
    [dateContentView addSubview:datePicker];
    self.datePicker = datePicker;
    [datePicker mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.right.bottom.equalTo(dateContentView);
    }];
    
    UIView *borderView = [[UIView alloc] init];
    borderView.layer.cornerRadius = 8;
    borderView.layer.borderWidth = 1;
    borderView.layer.borderColor = [UIColor blackColor].CGColor;
    borderView.userInteractionEnabled = NO;
    
    [dateContentView addSubview:borderView];
    [borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(dateContentView.mas_leading).offset(10);
        make.trailing.equalTo(dateContentView.mas_trailing).offset(-10);
        make.centerY.equalTo(dateContentView.mas_centerY);
        make.height.equalTo(@32);
    }];
}

- (void)closeAction {
    [self hide];
}

- (void)sureAction {
    if (self.sureBlock) {
        self.sureBlock(self.datePicker.date);
    }
    [self hide];
}

- (void)overlayTapped {
    [self hide];
}

@end
