//
//  SCLanguageManager.m
//  Supercall
//
//  Created by sumengliu on 2024/10/22.
//

#import "SCLanguageManager.h"
#import <objc/runtime.h>

@implementation SCLanguageManager

+ (void)setLanguage:(NSString *)languageCode {
    [[NSUserDefaults standardUserDefaults] setObject:@[languageCode] forKey:kAppleLanguagesKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (void)systemLanguage {
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:kAppleLanguagesKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

+ (NSString *)getDeviceLanguage {
    NSString *language = [[NSUserDefaults standardUserDefaults] stringForKey:kSCLocalLanguageKey];
    if (language != nil && ![language isEqualToString:@""]) {
        return language;
    }
    
    NSArray *preferredLanguages = [NSLocale preferredLanguages];
    if (preferredLanguages.count == 0) {
        return nil;
    }
    
    NSString *preferredLanguage = preferredLanguages.firstObject;
    NSDictionary *languageComponents = [NSLocale componentsFromLocaleIdentifier:preferredLanguage];
    NSString *languageCode = languageComponents[NSLocaleLanguageCode];
    
    if (languageCode == nil) {
        return nil;
    }
    
    language = languageCode;
    if ([language isEqualToString:@"zh"]) {
        language = @"zh-tw";
    }
    
    return language;
}

@end
