#import "SCCircularCountdownView.h"

@interface SCCircularCountdownView()

@property (nonatomic, strong) CAShapeLayer *trackLayer;
@property (nonatomic, strong) CAShapeLayer *progressLayer;
@property (nonatomic, assign) NSTimeInterval totalDuration;

@end

@implementation SCCircularCountdownView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupView];
    }
    return self;
}

- (void)setupView {
    // 默认配置
    self.progressColor = [UIColor colorWithHexString:@"#FFDF09"];
    self.trackColor = [[UIColor blackColor] colorWithAlphaComponent:0.5];
    self.lineWidth = 4.0;
    
    // 创建轨道层
    self.trackLayer = [CAShapeLayer layer];
    self.trackLayer.strokeColor = self.trackColor.CGColor;
    self.trackLayer.fillColor = [UIColor clearColor].CGColor;
    self.trackLayer.lineWidth = 2.0;
    [self.layer addSublayer:self.trackLayer];
    
    // 创建进度层
    self.progressLayer = [CAShapeLayer layer];
    self.progressLayer.strokeColor = self.progressColor.CGColor;
    self.progressLayer.fillColor = [UIColor clearColor].CGColor;
    self.progressLayer.lineWidth = self.lineWidth;
    self.progressLayer.lineCap = kCALineCapRound;
    [self.layer addSublayer:self.progressLayer];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    CGPoint center = CGPointMake(CGRectGetWidth(self.bounds)/2, CGRectGetHeight(self.bounds)/2);
    CGFloat radius = MIN(CGRectGetWidth(self.bounds), CGRectGetHeight(self.bounds))/2 - self.lineWidth;
    
    // 创建圆形路径
    UIBezierPath *circlePath = [UIBezierPath bezierPathWithArcCenter:center
                                                             radius:radius
                                                         startAngle:-M_PI_2
                                                           endAngle:3*M_PI_2
                                                          clockwise:YES];
    
    self.trackLayer.path = circlePath.CGPath;
    self.progressLayer.path = circlePath.CGPath;
}

- (void)setTotalDuration:(NSTimeInterval)totalDuration {
    _totalDuration = totalDuration;
}

- (void)updateWithRemainingTime:(NSTimeInterval)remainingTime {
    if (self.totalDuration <= 0) return;
    
    if (remainingTime > self.totalDuration) {
        self.totalDuration  = remainingTime;
    }
    
    CGFloat progress = remainingTime / self.totalDuration;
    // 确保进度在0-1之间
    progress = MAX(0, MIN(1, progress));
    self.progressLayer.strokeEnd = progress;
}

@end 
