//
//  SCObservable.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCObservable.h"
#import "SCDisposeBag.h"


@interface SCObservable()
@end
@implementation SCObservable {
    NSMutableArray *_subscribers;
    NSMutableArray *_errors;
}

- (instancetype)initWithValue:(id)value {
    self = [super init];
    if (self) {
        _value = value;
        _subscribers = [NSMutableArray new];
        _errors = [NSMutableArray new];
    }
    return self;
}

//如果之前存在数据也会回调
- (void)subscribe:(void (^)(id value))block error:(void (^)(SCXErrorModel *error))errorBlock disposeBag:(SCDisposeBag *)disposeBag{
    if (!disposeBag) return;
    kWeakSelf(self)
    if(block != nil){
        [_subscribers addObject:block];
        if(self.value){
            // 通知订阅者当前的值
           dispatch_async(dispatch_get_main_queue(), ^{
               block(weakself.value);
           });
        }
        if(disposeBag){
            [disposeBag insert:^{
                [weakself unsubscribe:block];
            }];
        }
    }
    if(errorBlock != nil){
        [_errors addObject:errorBlock];
       
        if (_error && errorBlock) {
            SCXErrorModel * blockError = _error;
            dispatch_async(dispatch_get_main_queue(), ^{
                errorBlock(blockError);
            });// 如果有错误，立即通知订阅者
        }
        if(disposeBag){
            [disposeBag insert:^{
                [weakself unsubscribeError:errorBlock];
            }];
        }
        
    }
}
///仅那当前订阅之后的数据回调
- (void)afterSubscribe:(void (^)(id value))block error:(void (^)(SCXErrorModel *error))errorBlock disposeBag:(SCDisposeBag *)disposeBag{
    NSParameterAssert(disposeBag);
    kWeakSelf(self)
    if(block != nil){
        [_subscribers addObject:block];
        if(disposeBag){
            [disposeBag insert:^{
                [weakself unsubscribe:block];
            }];
        }
    }
    if(errorBlock != nil){
        [_errors addObject:errorBlock];
        if(disposeBag){
            [disposeBag insert:^{
                [weakself unsubscribeError:errorBlock];
            }];
        }
        
    }
}

- (void)unsubscribe:(void (^)(id value))block {
    [_subscribers removeObject:block];
}

- (void)unsubscribeAll {
    [_subscribers removeAllObjects];
}

- (void)unsubscribeError:(void (^)(SCXErrorModel *error))errorBlock {
    [_errors removeObject:errorBlock];
}

- (void)setValue:(id)value {
    _value = value;
    _error = nil;
    for (void (^subscriber)(id value) in _subscribers) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (subscriber) {
                subscriber(value); // 在主线程中通知订阅者新的值
            }
        });
    }
}

- (void)postValue:(id)value {
    // 如果新值和旧值相等，直接返回
    if (![_value isEqual:value]) {
         self.value = value;
    }
}

- (void)setError:(SCXErrorModel *)error {
    _error = error;
    _value = nil;
    for (void (^subscriber)(SCXErrorModel *error) in _errors) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (subscriber) {
                subscriber(error); // 在主线程中通知订阅者新的值
            }
        });
    }
}

@end
