//
//  SCObservable.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import <Foundation/Foundation.h>
#import "SCDisposeBag.h"
#import "SCXErrorModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCObservable<__covariant T> : NSObject
@property (nonatomic, strong,nullable) T value;
@property (nonatomic, strong,nullable) SCXErrorModel * error;
- (instancetype)initWithValue:(T _Nullable)value;
//如果之前存在数据也会回调
- (void)subscribe:(void (^)(T _Nullable value))block error:(void (^_Nullable)(SCXErrorModel *error))errorBlock disposeBag:(SCDisposeBag *)disposeBag NS_SWIFT_UNAVAILABLE("") NS_REFINED_FOR_SWIFT;
///仅那当前订阅之后的数据回调
- (void)afterSubscribe:(void (^)(T _Nullable value))block error:(void (^_Nullable)(SCXErrorModel *error))errorBlock disposeBag:(SCDisposeBag *)disposeBag NS_SWIFT_UNAVAILABLE("") NS_REFINED_FOR_SWIFT;
- (void)unsubscribe:(void (^)(T _Nullable value))block;
- (void)unsubscribeAll;
- (void)setError:(SCXErrorModel *)error;
- (void)postValue:(T)value;

@end

NS_ASSUME_NONNULL_END
