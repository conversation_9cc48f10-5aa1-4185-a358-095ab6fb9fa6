//
//  SCDisposeBag.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/12.
//

#import "SCDisposeBag.h"

@implementation SCDisposeBag{
    NSMutableArray *_subscriptions;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _subscriptions = [NSMutableArray array];
    }
    return self;
}

- (void)insert:(id)subscription {
    [_subscriptions addObject:subscription];
}

- (void)dispose {
    for (void (^subscriber)(void) in _subscriptions) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (subscriber) {
                subscriber(); // 关闭，会移除监听
            }
        });
    }
    [_subscriptions removeAllObjects];
}

- (void)dealloc{
    [self dispose];
}

@end
