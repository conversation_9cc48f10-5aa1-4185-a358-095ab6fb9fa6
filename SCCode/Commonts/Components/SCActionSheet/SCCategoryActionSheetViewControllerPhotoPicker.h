//
//  SCCategoryActionSheetViewControllerPhotoPicker.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/7.
//

#import "SCActionSheetViewController.h"
#import <PhotosUI/PhotosUI.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^ImagePickerCompletion)(UIImage * _Nullable selectedImage);

@interface SCImagePickerManager : NSObject <PHPickerViewControllerDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate>

+ (instancetype)shared;

@property (nonatomic, copy) ImagePickerCompletion completion;

- (void)presentPhotoLibraryWithFromVC:(UIViewController *)viewController completion:(ImagePickerCompletion)completion;
- (void)presentCameraWithFromVC:(UIViewController *)viewController completion:(ImagePickerCompletion)completion;
@end


@interface SCActionSheetViewController (SCCategoryActionSheetViewControllerPhotoPicker)
+(SCActionSheetViewController *) showPhotpPickerWithFromVC:(UIViewController *)fromVC albumBlock:(void(^_Nullable)(UIImage * _Nullable selectedImage)) albumBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock;
@end

NS_ASSUME_NONNULL_END
