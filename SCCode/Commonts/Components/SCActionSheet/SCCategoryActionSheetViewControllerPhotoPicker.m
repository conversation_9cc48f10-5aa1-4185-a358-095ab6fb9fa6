//
//  SCCategoryActionSheetViewControllerPhotoPicker.m
//  Supercall
//
//  Created by 关伟洪 on 2024/1/7.
//

#import "SCCategoryActionSheetViewControllerPhotoPicker.h"
#import "SCPermissionManager.h"


@implementation SCImagePickerManager

+ (instancetype)shared {
    static SCImagePickerManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SCImagePickerManager alloc] init];
    });
    return instance;
}

- (void)presentPhotoLibraryWithFromVC:(UIViewController *)viewController completion:(ImagePickerCompletion)completion {
    
    self.completion = completion;
    
    [[SCPermissionManager shared] checkPermission:SCPermissionTypePhotoLibrary completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            [self showPhotoPickerWithVC:viewController];
        } else if (shouldShowAlert) {
            [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypePhotoLibrary fromViewController:viewController];
        }
    }];
}

- (void)presentCameraWithFromVC:(UIViewController *)viewController completion:(ImagePickerCompletion)completion {
    self.completion = completion;
    
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeCamera completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                [self showCameraPickerWithVC:viewController];
            } else {
                [[SCPermissionManager shared] showAlertWithMessage:@"Camera not available".translateString fromVC:viewController];
            }
        } else if (shouldShowAlert) {
            [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeCamera fromViewController:viewController];
        }
    }];
}

// 封装照片选择器展示逻辑
- (void)showPhotoPickerWithVC:(UIViewController *)viewController {
    dispatch_async(dispatch_get_main_queue(), ^{
        PHPickerConfiguration *config = [[PHPickerConfiguration alloc] init];
        config.selectionLimit = 1;
        config.filter = [PHPickerFilter imagesFilter];
        config.preferredAssetRepresentationMode = PHPickerConfigurationAssetRepresentationModeCurrent;
        
        PHPickerViewController *pickerVC = [[PHPickerViewController alloc] initWithConfiguration:config];
        pickerVC.delegate = self;
        pickerVC.modalPresentationStyle = UIModalPresentationFullScreen;
        [viewController presentViewController:pickerVC animated:YES completion:nil];
    });
}

#pragma mark - PHPickerViewControllerDelegate

- (void)picker:(PHPickerViewController *)picker didFinishPicking:(NSArray<PHPickerResult *> *)results {
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    if (results.count == 0) {
        if (self.completion) {
            self.completion(nil);
        }
        return;
    }
    
    PHPickerResult *result = results.firstObject;
    [result.itemProvider loadObjectOfClass:[UIImage class] completionHandler:^(__kindof id<NSItemProviderReading>  _Nullable object, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([object isKindOfClass:[UIImage class]]) {
                if (self.completion) {
                    self.completion((UIImage *)object);
                }
            } else {
                if (self.completion) {
                    self.completion(nil);
                }
            }
        });
    }];
}

#pragma mark - UIImagePickerControllerDelegate

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info {
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    UIImage *image = info[UIImagePickerControllerOriginalImage];
    if (self.completion) {
        self.completion(image);
    }
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:nil];
    if (self.completion) {
        self.completion(nil);
    }
}

#pragma mark - Helper Methods

- (void)showCameraPickerWithVC:(UIViewController *)viewController {
    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
    picker.sourceType = UIImagePickerControllerSourceTypeCamera;
    picker.delegate = self;
    picker.allowsEditing = NO;
    picker.modalPresentationStyle = UIModalPresentationFullScreen;
    [viewController presentViewController:picker animated:YES completion:nil];
}

@end


@implementation SCActionSheetViewController (SCCategoryActionSheetViewControllerPhotoPicker)

+(SCActionSheetViewController *) showPhotpPickerWithFromVC:(UIViewController *)fromVC albumBlock:(void(^_Nullable)(UIImage * _Nullable selectedImage)) albumBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock{
    SCActionSheetViewController * vc;
    vc = [SCActionSheetViewController showWithFromVC:fromVC actionStrs:@[@"Album".translateString, @"Camera".translateString] cancelStr:@"Cancel".translateString isAuthClose:true actionBlock:^(NSInteger index) {
        [SCActionSheetViewController showPhotpPickerWithFromVC:fromVC actionBlock:index albumBlock:albumBlock];
    } cancelBlock:cancelBlock];
    return vc;
}

+ (void)showPhotpPickerWithFromVC:(UIViewController *)fromVC actionBlock:(NSInteger)index albumBlock:(void(^_Nullable)(UIImage * _Nullable selectedImage)) albumBlock {
    if(index == 0){
        [[SCImagePickerManager shared] presentPhotoLibraryWithFromVC:fromVC completion:^(UIImage * _Nullable selectedImage) {
            kSCBlockExeNotNil(albumBlock,selectedImage);
        }];
        
    }else if(index == 1){
        
        [[SCImagePickerManager shared] presentCameraWithFromVC:fromVC completion:^(UIImage * _Nullable selectedImage) {
            kSCBlockExeNotNil(albumBlock,selectedImage);
        }];
    }
}

@end
