//
//  SCActionSheetViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN


typedef enum : NSUInteger {
    SCActionSheetButtonStyleDefault,
    SCActionSheetButtonStyleCancel,
} SCActionSheetButtonStyle;


@interface UserMorePopoupAction : NSObject

@property (nonatomic, copy) NSString *title;
@property (nonatomic, assign) SCActionSheetButtonStyle style;

- (instancetype)initWithTitle:(NSString *)title style:(SCActionSheetButtonStyle)style;

@end

@interface SCActionSheetViewController : UIViewController
//回调
///按钮回调
@property (nonatomic,copy) void(^bottomBlock)(NSInteger);
///取消回调
@property (nonatomic,copy) void(^cancelBlock)(void);
@property (nonatomic, strong) UIView *cancelBtn;
@property (nonatomic, strong) UIView *bottomLayout;
@property(nonatomic,nullable,strong) UIView *headerView;
- (instancetype)initWithActions:(NSArray<UserMorePopoupAction *> *)actions cancel:(UserMorePopoupAction *)cancel isAuthClose:(BOOL)isAuthClose;

///不设置高度，自动根据约束撑开高度，子类可以继承重新和约束
-(UIView * _Nullable)buildHeaderView;




+(instancetype)showWithFromVC:(UIViewController *)fromVC actions:(NSArray<UserMorePopoupAction *> * _Nullable)actions cancel:(UserMorePopoupAction * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock;

+(instancetype)showWithFromVC:(UIViewController *)fromVC actionStrs:(NSArray<NSString *> * _Nullable)actions cancelStr:(NSString * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock;
@end


NS_ASSUME_NONNULL_END
