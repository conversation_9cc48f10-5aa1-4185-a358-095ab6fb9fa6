//
//  SCActionSheetViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/6.
//

#import "SCActionSheetViewController.h"
#import <Masonry/Masonry.h>

@implementation UserMorePopoupAction

- (instancetype)initWithTitle:(NSString *)title style:(SCActionSheetButtonStyle)style {
    self = [super init];
    if (self) {
        _title = title;
        _style = style;
    }
    return self;
}

@end

@interface SCActionSheetViewController ()


@property (nonatomic, strong) NSMutableArray<UIView *> *buttons;
@property (nonatomic, strong) NSArray<UserMorePopoupAction *> *actions;
@property (nonatomic, strong) UserMorePopoupAction *cancel;
@property (nonatomic, assign) BOOL isAuthClose;
//背景蒙层
@property (nonatomic, weak) UIView * backgroundView;

@end

@implementation SCActionSheetViewController


- (instancetype)initWithActions:(NSArray<UserMorePopoupAction *> *)actions cancel:(UserMorePopoupAction *)cancel isAuthClose:(BOOL)isAuthClose {
    self = [super init];
    if (self) {
        _isAuthClose = isAuthClose;
        _actions = actions;
        _cancel = cancel;
    }
    return self;
}


- (void)viewDidLoad {
    [super viewDidLoad];
    [self initView];
}

- (UIView *)buildHeaderView{
    return nil;
}

- (void)initView {
    _backgroundView = UIView.new.setBackgroundColor(UIColor.scTranBlackBGColor).addSuperView(self.view);
    [_backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    kSCAddTapGesture(_backgroundView, self, onClickBG:)

    
    self.bottomLayout = [[UIView alloc] init];
    self.bottomLayout.backgroundColor = [UIColor scWhite];
    //顶部圆角
    self.bottomLayout.clipsToBounds = YES;
    self.bottomLayout.layer.cornerRadius = 37.0f;
    self.bottomLayout.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        
    [self.view addSubview:self.bottomLayout];
    [self.bottomLayout mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    ///获取头部视图
    self.headerView = [self buildHeaderView];
    if(self.headerView){
        [self.bottomLayout addSubview:self.headerView];
    }
    
    if (self.cancel) {
        UIView *cancelButton = [self createButtonWithAction:self.cancel tag:-1 tagAction:self tagSelect:@selector(cancelAction:) isShowLine:YES];
        cancelButton.tag = -1;
        [self.bottomLayout addSubview:cancelButton];
        self.cancelBtn = cancelButton;
    }
    
    self.buttons = [NSMutableArray array];
    for (NSInteger i = 0; i < self.actions.count; i++) {
        UserMorePopoupAction *action = self.actions[i];
        UIView *button = [self createButtonWithAction:action tag:i tagAction:self tagSelect:@selector(buttonAction:) isShowLine:self.headerView == nil ?  (i != [self.actions count]):YES];
        [self.bottomLayout addSubview:button];
        [self.buttons addObject:button];
    }
    
    
    if (self.cancelBtn) {
        
            [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.bottomLayout.mas_leading).offset(0);
                make.trailing.equalTo(self.bottomLayout.mas_trailing).offset(0);
                make.bottom.equalTo(self.bottomLayout).offset(- (MAX(kSCSafeAreaBottomHeight, 11.0f)));
                make.height.equalTo(@57);
            }];
    }
    
    UIView *lastButton = self.cancelBtn;
    
    
    for (NSInteger i = (self.buttons.count - 1); i < self.buttons.count; i--) {
        UIView *button = self.buttons[i];
        if (i == (self.buttons.count - 1)) {
            [button mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.bottomLayout.mas_leading).offset(0);
                make.trailing.equalTo(self.bottomLayout.mas_trailing).offset(0);
                make.height.equalTo(@53);
                if(self.cancelBtn != nil){
                    make.bottom.equalTo(self.cancelBtn.mas_top).offset(0);
                }else{
                    make.bottom.equalTo(self.bottomLayout.mas_top).offset(39);
                }
            }];
        } else {
            [button mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.bottomLayout.mas_leading).offset(0);
                make.trailing.equalTo(self.bottomLayout.mas_trailing).offset(0);
                make.bottom.equalTo(self.buttons[i + 1].mas_top).offset(0);
                make.height.equalTo(@53);
            }];
        }
        lastButton = button;
    }
    if(self.headerView){
        [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.top.trailing.equalTo(self.bottomLayout);
        }];
    }
    
    if (lastButton) {
        [lastButton mas_makeConstraints:^(MASConstraintMaker *make) {
            if(self.headerView){
                make.top.equalTo(self.headerView.mas_bottom).offset(0);
            }else{
                make.top.equalTo(self.bottomLayout.mas_top).offset(0);
            }
        }];
    }
}

- (UIView *)createButtonWithAction:(UserMorePopoupAction *)action tag:(NSInteger)tag tagAction:(id)tagAction tagSelect:(SEL)tagSelect isShowLine:(BOOL)isShowLine {
    UIView *itemView = [UIView new];
    //顶部分割线
    UIView *lineV = [UIView new];
    [lineV setBackgroundColor:[UIColor scLineColor]];
    [itemView addSubview:lineV];
    [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.equalTo(itemView);
        make.height.mas_equalTo(kSCLineHeight);
    }];
    [lineV setHidden:!isShowLine];
    
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    button.tag = tag;
    [button setTitle:action.title forState:UIControlStateNormal];
    [itemView addSubview:button];
    if(action.style == SCActionSheetButtonStyleCancel){//取消按钮
        button.titleLabel.font = kScUIFontRegular(16);
        [button setTitleColor:[UIColor scWhite] forState:UIControlStateNormal];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(itemView);
            make.leading.equalTo(itemView).offset(12.0f);
            make.trailing.equalTo(itemView).offset(-12.0f);
            make.top.equalTo(lineV.mas_bottom).offset(11.0f);
        }];
        [button sc_setThemeGradientBackground];
    }else{//普通按钮
        button.titleLabel.font = kScUIFontMedium(16);
        [button setTitleColor:[UIColor scBlack] forState:UIControlStateNormal];
        [button setBackgroundImage:[UIImage imageWithColor:[UIColor scWhite]] forState:UIControlStateNormal];
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.equalTo(itemView);
            make.top.equalTo(lineV.mas_bottom);
        }];
    }
    
    [button addTarget:tagAction action:tagSelect forControlEvents:UIControlEventTouchUpInside];
    
    return itemView;
}

-(void) buttonAction:(UIButton *)sender{
    NSInteger tag = sender.tag;
    if(self.isAuthClose)
        [self dismissViewControllerAnimated:YES completion:nil];
    kSCBlockExeNotNil(self.bottomBlock,tag);
    
}

-(void)cancelAction:(UIButton *)sender{
    if(self.isAuthClose)
        [self dismissViewControllerAnimated:YES completion:nil];
    kSCBlockExeNotNil(self.cancelBlock);
    
}
//点击背景
-(void)onClickBG:(UIView *)sender{
    [self dismissViewControllerAnimated:YES completion:nil];
    kSCBlockExeNotNil(self.cancelBlock);
}


+(instancetype)showWithFromVC:(UIViewController *)fromVC actions:(NSArray<UserMorePopoupAction *> * _Nullable)actions cancel:(UserMorePopoupAction * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock{
    SCActionSheetViewController *vc = [[self alloc] initWithActions:actions cancel:cancel isAuthClose:isAuthClose];
    vc.cancelBlock = cancelBlock;
    vc.bottomBlock = actionBlock;
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:vc animated:YES completion:nil];
    return vc;
}

+(instancetype)showWithFromVC:(UIViewController *)fromVC actionStrs:(NSArray<NSString *> * _Nullable)actions cancelStr:(NSString * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock{
    
    NSMutableArray<UserMorePopoupAction *> * actionList = [NSMutableArray new];
    if(actions != nil){
        for (NSString *actionItemStr in actions) {
            UserMorePopoupAction * item = [[UserMorePopoupAction alloc] initWithTitle:actionItemStr style:SCActionSheetButtonStyleDefault];
            [actionList addObject:item];
        }
    }
    UserMorePopoupAction *cancelAction;
    if(!kSCIsStrEmpty(cancel)){
        cancelAction = [[UserMorePopoupAction alloc] initWithTitle:cancel style:SCActionSheetButtonStyleCancel];
    }
    
    SCActionSheetViewController *vc = [[self alloc] initWithActions:actionList cancel:cancelAction isAuthClose:isAuthClose];
    vc.cancelBlock = cancelBlock;
    vc.bottomBlock = actionBlock;
    vc.modalPresentationStyle = UIModalPresentationOverFullScreen;
    vc.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:vc animated:YES completion:nil];
    return vc;
}
@end

