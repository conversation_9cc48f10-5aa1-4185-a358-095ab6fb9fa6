//
//  SCTranslationService.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/19.
//

#import "SCBaseAppService.h"

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    SCTranslationStatusNormal = 0,
    SCTranslationStatusTranslating,
    SCTranslationStatusFail,
    SCTranslationStatusSuccess,
} SCTranslationStatus;

@interface SCTranslationService : SCBaseAppService

- (instancetype)initWithUserId:(NSString *)userId key:(NSString *)key;
@property(nonatomic,assign) BOOL isAutoTranslation;
//获取翻译状态
-(SCTranslationStatus) getTranslationStatusWithText:(NSString *)text;
//获取缓存
-(NSString *) getCacheWithText:(NSString *)text;
// 翻译文本
- (void)translateText:(NSString *)text completion:(void (^)(NSString *, SCXErrorModel *))completion;
///翻译不使用缓存
- (void)translateNoCachedText:(NSString *)text completion:(void (^)(NSString *, SCXErrorModel *))completion;
@end

NS_ASSUME_NONNULL_END
