//
//  SCTranslationService.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/19.
//

#import "SCTranslationService.h"
#import <AFNetworking/AFNetworking.h>
#import "SCLanguageManager.h"
@interface SCTranslationService (){
    
    
    
}
@property(nonatomic,strong) NSCache *memoryCache; // 一级缓存，内存缓存
@property(nonatomic,strong) NSFileManager *fileManager; // 文件管理器
@property(nonatomic,strong) NSString *cacheFolderPath; // 缓存文件夹路径
@property(nonatomic,strong) NSMutableDictionary<NSString*,NSNumber *> * translationStatusDic; //记录翻译的状态
@property(nonatomic, copy) NSString *key;
@end

@implementation SCTranslationService


- (instancetype)initWithUserId:(NSString *)userId key:(NSString *)key {
    self = [super initWithUserId:userId];
    if (self) {
        _translationStatusDic = [NSMutableDictionary new];
        _isAutoTranslation = [[NSUserDefaults standardUserDefaults] boolForKey:[NSString stringWithFormat:@"%@-sc_auto_translation_tuoff_key",self.userId]];
        _key = key;
        _memoryCache = [[NSCache alloc] init];
        _fileManager = [NSFileManager defaultManager];
        
        // 获取缓存文件夹路径
        NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
        NSString *cacheDirectory = [paths objectAtIndex:0];
        _cacheFolderPath = [cacheDirectory stringByAppendingPathComponent:@"TranslationCache"];
        
        // 创建缓存文件夹
        [self createCacheFolderIfNeeded];
    }
    return self;
}

-(NSString *)languageCode{
    NSString *currentLocale = [SCLanguageManager getDeviceLanguage];
    if(kSCIsStrEmpty(currentLocale)){
        currentLocale = @"en";
    }
    return currentLocale;
}
-(NSString *)cachedKeyWith:(NSString *)text{
    return  [[NSString stringWithFormat:@"%@:%@",self.languageCode,text] md5String];
}

-(SCTranslationStatus) getTranslationStatusWithText:(NSString *)text{
    
    NSString *textKey = [self cachedKeyWith:text];
    
    NSNumber *status = [_translationStatusDic objectForKey:textKey];
    if(status){
        return status.integerValue;
    }
    // 先从一级缓存中查找翻译结果
    NSString *cachedTranslation = [self getCacheWithText:text];
    if (cachedTranslation) {
        [_translationStatusDic setObject:@(SCTranslationStatusSuccess) forKey:textKey];
        return SCTranslationStatusSuccess;
    }
    
    return SCTranslationStatusNormal;
}



//获取缓存
-(NSString *) getCacheWithText:(NSString *)text{
    NSString *translationText = nil;
    NSString *textKey = [self cachedKeyWith:text];
    // 先从一级缓存中查找翻译结果
    NSString *cachedTranslation = [_memoryCache objectForKey:textKey];
    if (cachedTranslation) {
        translationText = cachedTranslation;
    }
    
    // 从二级缓存（文件缓存）中查找翻译结果
    NSString *filePath = [_cacheFolderPath stringByAppendingPathComponent:textKey];
    if ([_fileManager fileExistsAtPath:filePath]) {
        NSString *fileContents = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:nil];
        if (fileContents) {
            // 更新一级缓存
            [_memoryCache setObject:fileContents forKey:textKey];
            translationText = fileContents;
        }
    }
    return  translationText;
}



- (void)setIsAutoTranslation:(BOOL)isAutoTranslation{
    _isAutoTranslation = isAutoTranslation;
    
    [[NSUserDefaults standardUserDefaults] setBool:isAutoTranslation forKey:[NSString stringWithFormat:@"%@-sc_auto_translation_tuoff_key",self.userId]];
    [[NSUserDefaults standardUserDefaults] synchronize];
}



- (void)translateText:(NSString *)text completion:(void (^)(NSString *, SCXErrorModel *))completion {
    
    
    NSString *textKey = [self cachedKeyWith:text];
    
    NSString *t = [self getCacheWithText:text];
    if(!kSCIsStrEmpty(t)){
        [self.translationStatusDic setObject:@(SCTranslationStatusSuccess) forKey:textKey];
        completion(t,nil);
        return;
    }
    //记录状态 正在加载中
    [self.translationStatusDic setObject:@(SCTranslationStatusTranslating) forKey:textKey];
    kWeakSelf(self)
    // 如果二级缓存中没有，调用Google翻译API进行翻译
    [self callGoogleTranslateAPI:text languageCode:self.languageCode completion:^(NSString *translatedText, SCXErrorModel *error) {
        if (translatedText && !error) {
            // 更新一级缓存
            [weakself.memoryCache setObject:translatedText forKey:textKey];
            NSString *filePath = [weakself.cacheFolderPath stringByAppendingPathComponent:textKey];
            // 更新二级缓存
            [translatedText writeToFile:filePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
            //保存翻译状态
            [weakself.translationStatusDic setObject:@(SCTranslationStatusSuccess) forKey:textKey];
        }else{
            //保存翻译状态
            [weakself.translationStatusDic setObject:@(SCTranslationStatusFail) forKey:textKey];
        }
        completion(translatedText, error);
    }];
}

///翻译不使用缓存
- (void)translateNoCachedText:(NSString *)text completion:(void (^)(NSString *, SCXErrorModel *))completion{
    // 如果二级缓存中没有，调用Google翻译API进行翻译
    kWeakSelf(self)
    [self callGoogleTranslateAPI:text languageCode:self.languageCode completion:^(NSString *translatedText, SCXErrorModel *error) {
        [weakself sc_blank_empty];
        completion(translatedText, error);
    }];
}
- (void)sc_blank_empty{}

- (void)callGoogleTranslateAPI:(NSString *)text languageCode:(NSString *)languageCode completion:(void (^)(NSString *, SCXErrorModel *))completion {
    
    NSString *url = @"https://translation.googleapis.com/language/translate/v2";
    NSDictionary *parameters = @{
        @"q": text,
        @"target": languageCode,
        @"key": self.key
    };
    kWeakSelf(self)
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    [manager POST:url parameters:parameters headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [weakself sc_blank_empty];
        NSDictionary *responseDict = (NSDictionary *)responseObject;
        
        NSDictionary *data = responseDict[@"data"];
        NSArray *translations = data[@"translations"];
        
        if (translations.count > 0) {
            NSDictionary *firstTranslation = translations[0];
            NSString *translatedText = firstTranslation[@"translatedText"];
            
            kSCBlockExeNotNil(completion,translatedText,nil);
            
        } else {
            kSCBlockExeNotNil(completion,nil,[[SCXErrorModel alloc] initWitMsg:@"No translation result.".translateString]);
        }
        
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [weakself sc_blank_empty];
        SCXErrorModel *errorModel = [[SCXErrorModel alloc] initWithCode:error.code msg:error.domain userInfo:error.userInfo];
        kSCBlockExeNotNil(completion,nil,errorModel);
    }];
    
    
}



- (void)createCacheFolderIfNeeded {
    BOOL isDirectory;
    if (![_fileManager fileExistsAtPath:_cacheFolderPath isDirectory:&isDirectory]) {
        NSError *error;
        [_fileManager createDirectoryAtPath:_cacheFolderPath withIntermediateDirectories:YES attributes:nil error:&error];
        if (error) {
            
        }
    } else if (!isDirectory) {
        
    }
}

@end
