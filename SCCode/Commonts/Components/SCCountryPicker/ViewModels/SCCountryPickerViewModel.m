//
//  SCCountryPickerViewModel.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCCountryPickerViewModel.h"
#import "SCCountryModel.h"
#import "SCCountryService.h"
// 移除YYModel依赖
@interface  SCCountryPickerViewModel ()

@end

@implementation SCCountryPickerViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initData];
    }
    return self;
}

-(void) initData{
    _countrys = [SCCountryService countrys];

}

@end
