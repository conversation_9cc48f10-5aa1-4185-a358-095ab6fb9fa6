//
//  SCCountryService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCCountryService.h"
#import "SCDataConverter.h"
#import "SCModelCompatibility.h"

@interface SCCountryService ()
@property (nonatomic, strong, class) NSMutableDictionary<NSString *, NSString *> *emojiCache;
@end

@implementation SCCountryService

+ (NSMutableDictionary<NSString *, NSString *> *)emojiCache {
    static NSMutableDictionary *cache = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [NSMutableDictionary dictionary];
    });
    return cache;
}

+ (void)setEmojiCache:(NSMutableDictionary<NSString *,NSString *> *)emojiCache {
    // 由于我们使用静态变量，这个setter方法实际上不会被调用
    // 但是我们需要实现它来满足编译器的要求
}

+(NSArray<SCCountryModel *> *)countrys{
    //读取JSON文件
    NSString *path = [SCResourceManager jsonPathWithName:@"sc_country.json"];
    NSString *jsonString = [[NSString alloc] initWithContentsOfFile:path encoding:NSUTF8StringEncoding error:nil];
    //将JSON数据转化为数组 - 使用字典处理而不是YYModel
    NSArray *jsonArray = [SCDataConverter arrayFromJSONString:jsonString];
    NSMutableArray *countryArray = [[NSMutableArray alloc] init];
    for(NSDictionary *dic in jsonArray) {
        SCCountryModel *model = [[SCCountryModel alloc] init];
        model.name = dic[@"name"] ?: @"";
        model.code = dic[@"code"] ?: @"";
        model.emoji = dic[@"emoji"] ?: @"";
        [countryArray addObject:model];
    }
    return [countryArray copy];
}

// 优化后的emoji获取方法
+ (NSString *)emojiForCountry:(NSString *)countryName {
    if (kSCIsStrEmpty(countryName)) return @"";
    
    // 先查找缓存
    NSString *cachedEmoji = self.emojiCache[countryName];
    if (cachedEmoji) {
        return cachedEmoji;
    }
    
    // 缓存未命中，执行查找
    NSArray *countrys = [self countrys];
    NSLocale *englishLocale = [[NSLocale alloc] initWithLocaleIdentifier:@"en"];
    
    for (SCCountryModel *country in countrys) {
        NSString *localizedName = [englishLocale localizedStringForCountryCode:country.code];
        if ([countryName hasPrefix:country.code] ||
            [countryName hasPrefix:localizedName ?: @""] ||
            [countryName isEqualToString:localizedName ?: @""]) {
            NSString *emoji = country.emoji ?: @"";
            // 存入缓存
            self.emojiCache[countryName] = emoji;
            return emoji;
        }
    }
    
    // 没找到结果也缓存空字符串，避免下次重复查找
    self.emojiCache[countryName] = @"";
    return @"";
}

@end
