//
//  SCCountryPickeTableViewCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCCountryPickeTableViewCell.h"
#import <Masonry/Masonry.h>
@interface SCCountryPickeTableViewCell ()
@property(nonatomic, weak) UILabel *titleL;
@end
@implementation SCCountryPickeTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)initUI{
    [super initUI];
    self.backgroundColor = [UIColor clearColor];
    self.contentView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    self.contentView.layer.cornerRadius = kSCNormalCornerRadius;
    
    UILabel *titleL = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontMedium(16.0f) alignment:NSTextAlignmentLeft];
    [self.contentView addSubview:titleL];
    self.titleL = titleL;
    
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self);
        make.height.mas_equalTo(45);
    }];
    
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(18);
        make.centerY.equalTo(self.contentView);
    }];
}

- (void)setModel:(SCCountryModel *)model{
    _model = model;
    self.titleL.text = [NSString stringWithFormat:@"%@ %@",model.emoji,model.name];
}

@end
