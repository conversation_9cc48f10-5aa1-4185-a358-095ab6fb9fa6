//
//  SCCountrySelectCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCCountrySelectCell.h"
#import "SCCountryModel.h"
#import "SCFontManager.h"

@implementation SCCountrySelectCell

- (void)initUI{
    [super initUI];
    self.backgroundColor = UIColor.whiteColor;
    self.setCornerRadius(kSCCountrySelectCellHeight/2.0f);
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[
        kSCColorWithHexStr(@"#FF5450"),
        kSCColorWithHexStr(@"#460645")
    ] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *bgImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(28.0f, 28.0f)];
    _bgIV = [UIImageView new].setImage(kScAuthMar.isLanguageForce ? [bgImage imageWithHorizontallyFlippedOrientation] : bgImage).addSuperView(self.contentView);
    
    [_bgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.spacing = kSCCountrySelectCellPadding;
    [self.contentView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(kSCCountrySelectCellPadding);
        make.trailing.equalTo(self.contentView).offset(-kSCCountrySelectCellPadding);
        make.top.bottom.equalTo(self.contentView);
    }];
    
    UILabel *countryLabel = [[UILabel alloc]init];
    countryLabel.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
    countryLabel.textColor = UIColor.scBlack;
    _countryEmL = countryLabel;
    [stackView addArrangedSubview:countryLabel];
    
    UILabel *nameLbl = [[UILabel alloc]init];
    nameLbl.font = [SCFontManager italicFontWithSize:12];
    nameLbl.textColor = UIColor.scBlack;
    nameLbl.textAlignment = NSTextAlignmentCenter;
    _nameLabel = nameLbl;
    [stackView addArrangedSubview:nameLbl];
}

- (void)bindModel:(SCCountryModel *)model isSelect:(BOOL) isSelect{
    self.nameLabel.text = model.name;
    self.countryEmL.text = model.emoji;
    if(!kSCIsStrEmpty(self.countryEmL.text)){
        self.countryEmL.hidden = NO;
    }else{
        self.countryEmL.hidden = YES;
        [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(74);
        }];
    }
    self.nameLabel.textColor = isSelect ? UIColor.scWhite : UIColor.scBlack;
    self.bgIV.hidden = !isSelect;
}

+(CGSize) cellSizeWithModel:(SCCountryModel *)model{
    if(kSCIsStrEmpty(model.emoji)){
        CGSize textSize = [model.name sizeWithAttributes:@{NSFontAttributeName: [SCFontManager italicFontWithSize:12]}];
        
        return CGSizeMake(MAX(74.0f, kSCCountrySelectCellPadding + textSize.width + kSCCountrySelectCellPadding) , kSCCountrySelectCellHeight);;
    }else{
        CGSize textSize = [model.name sizeWithAttributes:@{NSFontAttributeName: [SCFontManager italicFontWithSize:12]}];
        CGSize emojiSize = [model.emoji sizeWithAttributes:@{NSFontAttributeName: kScUIFontMedium(16)}];
        return CGSizeMake(kSCCountrySelectCellPadding + emojiSize.width + kSCCountrySelectCellPadding + textSize.width + kSCCountrySelectCellPadding, kSCCountrySelectCellHeight);
    }
    
}

@end
