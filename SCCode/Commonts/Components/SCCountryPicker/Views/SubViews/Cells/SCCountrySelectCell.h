//
//  SCCountrySelectCell.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCBaseCollectionCell.h"
#import "SCCountryModel.h"

NS_ASSUME_NONNULL_BEGIN

#define kSCCountrySelectCellHeight 28.0f
#define kSCCountrySelectCellPadding 4.5f

@interface SCCountrySelectCell : SCBaseCollectionCell
@property(nonatomic, weak) UILabel *nameLabel;
@property(nonatomic, weak) UILabel *countryEmL;
@property(nonatomic, weak) UIImageView *bgIV;
- (void)bindModel:(SCCountryModel *)model isSelect:(BOOL) isSelect;
+(CGSize) cellSizeWithModel:(SCCountryModel *)model;
@end

NS_ASSUME_NONNULL_END
