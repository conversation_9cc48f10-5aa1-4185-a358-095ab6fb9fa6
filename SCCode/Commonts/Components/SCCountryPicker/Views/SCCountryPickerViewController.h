//
//  SCSCCountryPickerViewController.h
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/6.
//

#import "SCBaseViewController.h"
//因为国家数据模型为主要业务一定会被使用所以需要导入而不是class
#import "SCCountryModel.h"
NS_ASSUME_NONNULL_BEGIN
#pragma mark - UI初始化
@interface SCCountryPickerViewController<UITableViewDelegate, UITableViewDataSource> : SCBaseViewController

///显示
+ (void)showWithFromVC:(UIViewController *)fromVC defaultCountry:(NSString * _Nullable)defaultCountry selectBlock:(void (^)(SCCountryModel * _Nonnull))selectBlock;
@end

NS_ASSUME_NONNULL_END
