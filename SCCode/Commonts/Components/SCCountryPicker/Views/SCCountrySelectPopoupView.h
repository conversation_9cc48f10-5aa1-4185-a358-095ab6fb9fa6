//
//  SCCountrySelectPopoupView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCBaseView.h"
#import "SCCountryModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCCountrySelectPopoupView<UICollectionViewDelegate,UICollectionViewDataSource> : SCBaseView

+(void) showFromPoint:(CGPoint) point superView:(UIView *)superView defaultCountry:(NSString * _Nullable)countryCode selectBlock:(void(^)(SCCountryModel *country)) selectBlock;



@end

NS_ASSUME_NONNULL_END
