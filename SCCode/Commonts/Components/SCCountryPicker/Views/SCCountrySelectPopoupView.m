//
//  SCCountrySelectPopoupView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCCountrySelectPopoupView.h"
#import "SCCountryModel.h"
#import "SCCountryService.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCCountrySelectCell.h"
#import "SCCollectionViewLeftAlignedFlowLayout.h"
#import "SCAlignedCollectionViewFlowLayout.h"

@interface SCCountrySelectPopoupView (){
    CGFloat _contentWidth;
    CGFloat _contentPaddingH;
    CGFloat _collectionTopPadding;
    CGFloat _collectionBottomPadding;
    CGFloat _collectionItemHPadding;
    CGFloat _collectionItemVPadding;
    CGFloat _collectionItemHeight;
    CGFloat _collectionViewWidth;
}

//布局内容
@property(nonatomic,weak) UIView * contentView;
@property(nonatomic,weak) UIView * bgV;
//泡泡背景
@property (nonatomic, weak) UIImageView *contentBgIV;
//列表
@property (nonatomic, weak) UICollectionView *collectionView;
//选中
@property (nonatomic, copy) NSString *selectCountryCode;
@property (nonatomic, strong) NSArray<SCCountryModel *> *countrys;
@property (nonatomic, strong) SCAlignedCollectionViewFlowLayout *layout;
@property (nonatomic, copy) void(^selectBlock)(SCCountryModel *country);
@property (nonatomic, weak) UIVisualEffectView *blurEffectView;




@end

@implementation SCCountrySelectPopoupView


- (void)initData{
    [super initData];
    NSMutableArray<SCCountryModel *> *countrys = [NSMutableArray new];
    
    SCCountryModel * allCountryModel = [[SCCountryModel alloc] init];
    allCountryModel.name = @"All";
    [countrys addObject:allCountryModel];
    
    NSArray<SCCountryModel *> *allCountrys = [SCCountryService countrys];
    NSMutableDictionary<NSString *, SCCountryModel *> *countryDic = [NSMutableDictionary new];
    for (SCCountryModel *item in allCountrys) {
        countryDic[item.code] = item;
    }
    // 从策略字典中获取主播墙地区列表
    NSArray<NSString *> * regins = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:@"broadcasterWallRegions" defaultValue:@[]];
    for (NSString * item in regins) {
        if (countryDic[item]) {
            [countrys addObject:countryDic[item]];
        }else{
            SCCountryModel * allCountryModel = [[SCCountryModel alloc] init];
            allCountryModel.name = item;
            allCountryModel.name = item;
            [countrys addObject:allCountryModel];
        }
    }
    
    self.countrys = [countrys copy];
}

- (void)initUI{
    [super initUI];
    _contentPaddingH = 15.0f;
    _contentWidth = kSCScreenWidth - (_contentPaddingH * 2);
    _collectionTopPadding = 27.0f;
    _collectionBottomPadding = 21.0f;
    _collectionItemHPadding = 8.0f;
    _collectionItemVPadding = 10.0f;
    _collectionItemHeight = 28.0f;
    _collectionViewWidth = _contentWidth - _contentPaddingH * 2.0f;
    CGFloat currentWidth = 0;
    CGFloat row = 1;
    for (int i = 0; i < [self.countrys count]; i++) {
        SCCountryModel *itemModel = self.countrys[i];
        CGSize size = [SCCountrySelectCell cellSizeWithModel:itemModel];
        if(currentWidth == 0){
            if(currentWidth + size.width > _collectionViewWidth){
                row += 1;
            }else{
                currentWidth += size.width;
            }
        }else if(currentWidth + size.width + _collectionItemHPadding > _collectionViewWidth){
            row += 1;
            currentWidth = size.width;
        }else{
            currentWidth += size.width + _collectionItemHPadding;
        }
    }
    CGFloat collectionViewHeight = row * (kSCCountrySelectCellHeight + _collectionItemVPadding) - _collectionItemVPadding;
    
    
    
    _bgV = [UIView new].setBackgroundColor(UIColor.clearColor).addSuperView(self);
    _bgV.userInteractionEnabled = YES;
    kSCAddTapGesture(_bgV, self, onTapBg);
   
    
    _contentView = [UIView new].setBackgroundColor(UIColor.clearColor).addSuperView(self);
    
    
    // 修改：先创建毛玻璃效果视图
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    [_contentView addSubview:blurEffectView];
    _blurEffectView = blurEffectView;
    
    // 修改：调整背景图片的 alpha
    _contentBgIV = [UIImageView new]
        .setImage([[SCResourceManager loadImageWithName:@"ic_country_select_popoup_bg" isAutoForce:YES] 
            resizableImageWithCapInsets:UIEdgeInsetsMake(25, 15, 15, 70)
            resizingMode:UIImageResizingModeStretch])
        .addSuperView(_contentView);
    
    _layout = [[SCAlignedCollectionViewFlowLayout alloc] init];
    _layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    _layout.minimumLineSpacing = _collectionItemHPadding;
    _layout.minimumInteritemSpacing = _collectionItemVPadding;
    //左对齐
    _layout.sectionInset = UIEdgeInsetsMake(0, 0, 0, 0);
    _layout.estimatedItemSize = CGSizeMake(1, kSCCountrySelectCellHeight);
    
    
    _collectionView = [UICollectionView collectionViewWithFrame:CGRectZero layout:_layout delegate:self dataSource:self cellClass:[SCCountrySelectCell class] forCellReuseIdentifier:[SCCountrySelectCell cellIdentifier]].setBackgroundColor(UIColor.clearColor).addSuperView(self.contentView);
    _collectionView.showsVerticalScrollIndicator = NO;
    _collectionView.showsHorizontalScrollIndicator = NO;
    _collectionView.alwaysBounceVertical = YES;
    _collectionView.scrollEnabled = YES;
    //左对齐
    _collectionView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    _collectionView.scrollIndicatorInsets = UIEdgeInsetsMake(0, 0, 0, 0);
    self.collectionView.frame = CGRectMake(_contentPaddingH, _collectionTopPadding, _contentWidth - _contentPaddingH * 2, collectionViewHeight);

    self.contentView.scSize = CGSizeMake(_contentWidth, collectionViewHeight + _collectionTopPadding + _collectionBottomPadding);
    self.contentBgIV.scSize = CGSizeMake(_contentWidth, collectionViewHeight + _collectionTopPadding + _collectionBottomPadding);
    
    // 添加：设置毛玻璃视图的frame
    self.blurEffectView.frame = CGRectMake(0, 0, _contentWidth, collectionViewHeight + _collectionTopPadding + _collectionBottomPadding);
    self.blurEffectView.layer.cornerRadius = 15; // 设置圆角
    self.blurEffectView.layer.masksToBounds = YES;
    
    // 确保视图层级正确
    [_contentView bringSubviewToFront:_collectionView];
    
    [self.collectionView reloadData];
    [self.collectionView setNeedsLayout];
    [self.collectionView layoutIfNeeded];
    if (self.collectionView.contentSize.height > 0) {
        self.collectionView.frame = CGRectMake(_contentPaddingH, _collectionTopPadding, self.collectionView.contentSize.width, self.collectionView.contentSize.height);

        self.contentView.scSize = CGSizeMake(_contentWidth, self.collectionView.contentSize.height + _collectionTopPadding + _collectionBottomPadding);
        self.contentBgIV.scSize = CGSizeMake(_contentWidth, self.collectionView.contentSize.height + _collectionTopPadding + _collectionBottomPadding);
    }
}

- (void)layoutSubviews{
    [super layoutSubviews];
    self.bgV.frame = self.bounds;
    // 更新毛玻璃视图的 frame
    self.blurEffectView.frame = CGRectMake(self.contentBgIV.scLeft, self.contentBgIV.scTop + 8.5, self.contentBgIV.scWidth, self.contentBgIV.scHeight - 8.5);
}

#pragma mark - Action
-(void)onTapBg{
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}




-(void)showFromPoint:(CGPoint) point superView:(UIView *)superView{
    if(self.superview){
        [self removeFromSuperview];
    }
    [superView addSubview:self];
    self.alpha = 0;
    self.contentView.scTop = point.y;
    self.contentView.scLeft = point.x;
    self.frame = superView.bounds;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
    }];
}

+(void) showFromPoint:(CGPoint) point superView:(UIView *)superView defaultCountry:(NSString *)countryCode selectBlock:(void(^)(SCCountryModel *country)) selectBlock {
    SCCountrySelectPopoupView *popView = [[SCCountrySelectPopoupView alloc] init];
    popView.selectCountryCode = countryCode;
    popView.selectBlock = selectBlock;
    [popView showFromPoint:point superView:superView];
}






- (nonnull __kindof UICollectionViewCell *)collectionView:(nonnull UICollectionView *)collectionView cellForItemAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCCountrySelectCell *cell = [SCCountrySelectCell initWithFormCollectionView:collectionView forIndexPath:indexPath];
    SCCountryModel *itemModel = self.countrys[indexPath.item];
    [cell bindModel:itemModel isSelect:kSCIsStrEmpty(itemModel.code) ? kSCIsStrEmpty(self.selectCountryCode):[itemModel.code isEqualToString:self.selectCountryCode]];
    return cell;
}

- (NSInteger)collectionView:(nonnull UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self.countrys count];
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    SCCountryModel *itemModel = self.countrys[indexPath.item];
    if(self.selectBlock){
        self.selectBlock(itemModel);
    }
    [self onTapBg];
}

@end
