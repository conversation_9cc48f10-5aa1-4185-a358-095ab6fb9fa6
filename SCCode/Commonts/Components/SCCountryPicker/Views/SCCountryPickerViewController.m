//
//  SCSCCountryPickerViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCCountryPickerViewController.h"
//第三方库
#import <Masonry/Masonry.h>
//View
#import "SCCountryPickeTableViewCell.h"
#import "SCNavigationBar.h"
//Model
#import "SCCountryPickerViewModel.h"
#import "SCCountryModel.h"
//VC
#import "SCBaseNavigationController.h"


#pragma mark - 属性
@interface SCCountryPickerViewController ()
#pragma mark UI属性
@property (nonatomic, strong) UITableView *listView;
@property (nonatomic, strong) UITextField *searchTF;
@property (nonatomic, copy) void (^selectBlock)(SCCountryModel *);
#pragma mark 数据属性
@property (nonatomic, strong) NSMutableArray<SCCountryModel *> *dispPlayDataArray;
@property (nonatomic, copy) NSString *currentCountry;
@property (nonatomic, assign) BOOL iqEnble;
@property (nonatomic, strong) SCCountryPickerViewModel *viewModel;
//默认选中的国家
@property (nonatomic, copy) NSString *defaultCountry;
@end

#pragma mark - UI初始化
@implementation SCCountryPickerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self createUI];
    [self initData];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
}

- (void)initData {
    self.viewModel = [[SCCountryPickerViewModel alloc] init];
    self.dispPlayDataArray = [self.viewModel.countrys mutableCopy];
    [self.listView reloadData];
}

- (void)searchTextChange:(UITextField *)textField {
    if (self.searchTF.text == nil || [self.searchTF.text isEqualToString:@""]) {
        self.dispPlayDataArray =  [self.viewModel.countrys mutableCopy];
    } else {
        if (self.viewModel.countrys.count > 0) {
            self.dispPlayDataArray = [NSMutableArray array];
            for (SCCountryModel *country in self.viewModel.countrys) {
                if ([country.name containsString:self.searchTF.text]) {
                    [self.dispPlayDataArray addObject:country];
                }
            }
        }
    }
    [self.listView reloadData];
}
- (void)createUI {
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.title = @"Country".translateString;
    
    UIView *bgView = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#631414"]);
    bgView.layer.cornerRadius = 40;
    bgView.layer.masksToBounds = YES;
    bgView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.scContentView addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scContentView.mas_top).offset(0);
        make.leading.trailing.equalTo(self.scContentView);
        make.bottom.equalTo(self.scContentView);
    }];
    
    UIView *searchContentView = [[UIView alloc] init];
    searchContentView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    searchContentView.layer.cornerRadius = 26;
    searchContentView.layer.masksToBounds = YES;
    
    [bgView addSubview:searchContentView];
    [searchContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bgView.mas_top).offset(10);
        make.leading.equalTo(bgView.mas_leading).offset(16);
        make.trailing.equalTo(bgView.mas_trailing).offset(-16);
        make.height.equalTo(@52);
    }];
    
    UIImageView *searchIconImgView = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_zoom"]];
    [searchContentView addSubview:searchIconImgView];
    [searchIconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(searchContentView).offset(12);
        make.width.height.equalTo(@20);
        make.centerY.equalTo(searchContentView);
    }];
    
    self.searchTF = [[UITextField alloc] init];
    self.searchTF.textColor = [UIColor scWhite];
    self.searchTF.font = [UIFont systemFontOfSize:15];
    self.searchTF.placeholder = @"Search for your country".translateString;
    [self.searchTF addTarget:self action:@selector(searchTextChange:) forControlEvents:UIControlEventEditingChanged];
    [searchContentView addSubview:self.searchTF];
    [self.searchTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(searchIconImgView.mas_trailing).offset(10);
        make.trailing.equalTo(searchContentView.mas_trailing).offset(-10);
        make.centerY.equalTo(searchContentView);
        make.height.equalTo(searchContentView);
    }];
    
    UIView *listContentView = [[UIView alloc] init];
    [bgView addSubview:listContentView];
    [listContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(searchContentView.mas_bottom).offset(10);
        make.leading.equalTo(bgView.mas_leading).offset(16);
        make.trailing.equalTo(bgView.mas_trailing).offset(-16);
        make.bottom.equalTo(bgView.mas_bottom).offset(-20);
    }];
    
    self.listView = [UITableView tableViewWithFrame:self.scContentView.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    self.listView.backgroundColor = [UIColor clearColor];
    [listContentView addSubview:self.listView];
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(listContentView.mas_top).offset(10);
        make.leading.equalTo(listContentView.mas_leading).offset(12);
        make.trailing.equalTo(listContentView.mas_trailing).offset(-12);
        make.bottom.equalTo(listContentView.mas_bottom).offset(-12);
    }];
}

- (void)onBlack{
    [self dismissViewControllerAnimated:YES completion:nil];
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 54;
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCCountryPickeTableViewCell *cell = [SCCountryPickeTableViewCell initWithFormTableView:tableView];
    if(indexPath.row < [self.dispPlayDataArray count]){
        SCCountryModel *country = self.dispPlayDataArray[indexPath.row];
        [cell setModel:country];
    }
    
    return  cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.dispPlayDataArray count];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if(indexPath.row < [self.dispPlayDataArray count]){
        SCCountryModel *country = self.dispPlayDataArray[indexPath.row];
        kSCBlockExeNotNil(self.selectBlock,country);
        [self onBlack];
    }
}

#pragma mark - 路由
+ (void)showWithFromVC:(UIViewController *)fromVC defaultCountry:(NSString * _Nullable)defaultCountry selectBlock:(void (^)(SCCountryModel * _Nonnull))selectBlock{
    
    SCCountryPickerViewController *vc = [[SCCountryPickerViewController alloc] init];
    vc.selectBlock = selectBlock;
    vc.defaultCountry = defaultCountry;
    SCBaseNavigationController *nv = [[SCBaseNavigationController alloc] initWithRootViewController:vc];
    nv.modalPresentationStyle = UIModalPresentationFullScreen;
    nv.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:nv animated:YES completion:nil];
}

@end
