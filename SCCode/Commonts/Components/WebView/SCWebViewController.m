//
//  SCWebViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/17.
//

#import "SCWebViewController.h"
//Const


//Model
#import "SCOrderResultModel.h"
// 移除SCSocketOrderEventModel，改为使用字典

//Service
#import "SCPayService.h"
#import "SCSocketService.h"
#import "SCCallService.h"

//Controller
#import "SCBaseNavigationController.h"
#import "SCCoinsPopupViewController.h"
#import "SCNavigationBar.h"

//Tools
#import "SCAVAudioSessionUtils.h"


@interface SCWebViewController ()

// 添加一个方法来执行 recharge JS
- (void)executeRecharge;

@property (nonatomic, strong) NSArray<NSString *> *filterSchemes;
@property (nonatomic, strong) NSURL *currentURL;

@end

@implementation SCWebViewController

- (instancetype)init {
    self = [super init];
    if (self) {
        // 初始化过滤的 schemes
        _filterSchemes = @[@"http", @"https", @"about", @"file"];
        _filterSound = false;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    
    kWeakSelf(self)
    [kSCAuthCallService.callingShowObx subscribe:^(NSNumber * _Nullable value) {
        if(!weakself.filterSound && !value.boolValue){
            [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayback withMode:AVAudioSessionModeMoviePlayback error:NULL];
        }
    } error:nil disposeBag:self.disposeBag];
    [self setupOrderObservers];
}

- (void)setupOrderObservers {
    kWeakSelf(self)
    // 监听订单状态变化
    [kScAuthMar.payService.orderChangeObx afterSubscribe:^(SCOrderResultModel * _Nonnull value) {
        [weakself executeRecharge];
    } error:nil disposeBag:self.disposeBag];
    
    // 监听 socket 订单事件 - 使用字典处理
    [kSCAuthSocketService.orderEventObs afterSubscribe:^(NSDictionary * _Nonnull orderDict) {
        [weakself executeRecharge];
    } error:nil disposeBag:self.disposeBag];
}

- (void)executeRecharge {
    if (!self.webView) {
        
        return;
    }
    
    // 构建 JS 代码
    NSString *jsCode = @"HttpTool.NativeToJs('recharge')";
    kWeakSelf(self)
    // 在主线程执行 JS
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.webView evaluateJavaScript:jsCode completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            [weakself sc_blank_empty];
            if (error) {
                
            } else {
                
            }
        }];
    });
}

- (void)sc_blank_empty{
    
}

- (void)initUI{
    [super initUI];
    
    // 创建WKWebViewConfiguration
    
    
    WKWebViewConfiguration * configuration = [self defaultConfiguration];
    // 创建WKWebView
    self.webView = [[WKWebView alloc] initWithFrame:self.view.bounds configuration:configuration];
    [self.scContentView addSubview:self.webView];
    
    // 保存当前URL并加载网页
    self.currentURL = [NSURL URLWithString:self.webAddress];
    [self loadCurrentURL];
    
    // 创建进度条
    self.progressView = [[UIProgressView alloc] initWithProgressViewStyle:UIProgressViewStyleDefault];
    [self.progressView setTintColor:UIColor.scTheme];
    self.progressView.frame = CGRectMake(0, 0, CGRectGetWidth(self.view.frame), 2);
    [self.scContentView addSubview:self.progressView];
    // 设置进度条的初始值
    [self.progressView setProgress:0.0 animated:NO];
    self.webView.navigationDelegate = self;
    [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:nil];

    
    [self.webView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(2.0);
        make.leading.trailing.top.equalTo(self.scContentView);
    }];
    
}

-(WKWebViewConfiguration *) defaultConfiguration{
    WKWebViewConfiguration *configuration = [[WKWebViewConfiguration alloc] init];
    WKUserContentController *userContentController = [[WKUserContentController alloc] init];
    
    // Add message handlers for each event
    [userContentController addScriptMessageHandler:self name:@"newTppClose"];
    [userContentController addScriptMessageHandler:self name:@"newTppLogEvent"];
    [userContentController addScriptMessageHandler:self name:@"recharge"];
    [userContentController addScriptMessageHandler:self name:@"rechargeSource"];
    
    configuration.userContentController = userContentController;
    return configuration;
}


- (void)onBlack{
    if(self.navigationController == nil || self.navigationController.viewControllers.count == 1){
        [self dismissViewControllerAnimated:YES completion:nil];
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
}

#pragma mark - WKNavigationDelegate
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    // 网页加载完成，进度条设为满
    [self.progressView setProgress:1.0 animated:YES];
    
    // 延迟一段时间后隐藏进度条
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.progressView.hidden = YES;
    });
    
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    // 网页加载失败，隐藏进度条
    self.progressView.hidden = YES;
    
}

- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    // 网页开始加载，显示进度条
    self.progressView.hidden = NO;
    
}

- (void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    // 网页加载失败，隐藏进度条
    self.progressView.hidden = YES;
    
}

- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation {
    // 网页内容开始到达时，将进度条设为一半
//    [self.progressView setProgress:0.5 animated:YES];
    
    
}
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSString *,id> *)change context:(void *)context {
    
    if ([keyPath isEqualToString:@"estimatedProgress"]) {
        
        self.progressView.progress = self.webView.estimatedProgress;
        if (self.progressBlock) {
            self.progressBlock(self.webView.estimatedProgress);
        }
        
        if (self.progressView.progress >= 0.9) {
            __weak typeof (self)weakSelf = self;
            [UIView animateWithDuration:0.25f delay:0.3f options:UIViewAnimationOptionCurveEaseOut animations:^{
                weakSelf.progressView.transform = CGAffineTransformMakeScale(1.0f, 1.4f);
            } completion:^(BOOL finished) {
                weakSelf.progressView.hidden = YES;

            }];
        }
    }else{
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    decisionHandler(WKNavigationResponsePolicyAllow);
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    
    // 处理返回操作
    if (navigationAction.navigationType == WKNavigationTypeBackForward && webView.backForwardList.backList.count == 0) {
        [self onBlack];
        decisionHandler(WKNavigationActionPolicyAllow);
        return;
    }
    
    // 检查 URL
    NSURL *url = navigationAction.request.URL;
    if (!url) {
        decisionHandler(WKNavigationActionPolicyAllow);
        return;
    }
    
    // 检查是否需要特殊处理 URL
    if ([self shouldOverrideUrlLoadingWithWebView:webView url:url]) {
        decisionHandler(WKNavigationActionPolicyCancel);
        return;
    }
    
    decisionHandler(WKNavigationActionPolicyAllow);
}

- (BOOL)shouldOverrideUrlLoadingWithWebView:(WKWebView *)webView url:(NSURL *)url {
    NSString *scheme = url.scheme.lowercaseString;
    
    // 如果不是标准 scheme，则使用系统浏览器打开
    if (![self.filterSchemes containsObject:scheme]) {
        [[UIApplication sharedApplication] openURL:url
                                        options:@{}
                              completionHandler:nil];
        return YES;
    }
    
    return NO;
}

#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    if ([message.name isEqualToString:@"newTppClose"]) {
        [self handleNewTppClose:message.body];
    } else if ([message.name isEqualToString:@"newTppLogEvent"]) {
        [self handleNewTppLogEvent:message.body];
    } else if ([message.name isEqualToString:@"recharge"]) {
        [self handleRecharge:message.body];
    } else if ([message.name isEqualToString:@"rechargeSource"]) {
        [self handleRechargeSource:message.body];
    }
}

#pragma mark - Message Handlers
- (void)handleNewTppClose:(id)message {
    // Handle close event
    [self onBlack];
}

- (void)handleNewTppLogEvent:(id)message {
    // Handle log event
    if ([message isKindOfClass:[NSDictionary class]]) {
        NSDictionary *eventData = (NSDictionary *)message;
        
        // Add your log event handling logic here
    }
}

- (void)handleRecharge:(id)message {
    // Handle recharge event
    [SCCoinsPopupViewController showWithFromVC:self entry:SCPayEntry.shared.kPayEntrySourceSlotMachine];
}

- (void)handleRechargeSource:(id)message {
    // Handle recharge source event
    if ([message isKindOfClass:[NSDictionary class]]) {
        NSDictionary *sourceData = (NSDictionary *)message;
        
        // Add your recharge source handling logic here
    }
}

// Don't forget to remove observers in dealloc
- (void)dealloc {
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    // Remove all script message handlers
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"newTppClose"];
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"newTppLogEvent"];
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"recharge"];
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"rechargeSource"];
}

//打开隐私条款网页
+(void)showPrivacyPolicyWithFromVC:(UIViewController *)fromVC{
    [SCWebViewController showWithFromVC:fromVC title:@"Privacy Policy".translateString url:kSCCodeMar.privacyPolicyUrl];
}
//打开用户条款
+(void)showTermConditionsWithFromVC:(UIViewController *)fromVC{
    
    [SCWebViewController showWithFromVC:fromVC title:@"Terms & Conditions".translateString url:kSCCodeMar.termConditionsUrl];
}

+(void) showWithFromVC:(UIViewController *)fromVC title:(NSString *)title url:(NSString *)url{
    SCWebViewController *vc = [[SCWebViewController alloc] init];
    vc.webAddress = url;
    
    if(fromVC.navigationController != nil){
        [fromVC.navigationController pushViewController:vc animated:YES];
    }else{
        SCBaseNavigationController *nv = [[SCBaseNavigationController alloc] initWithRootViewController:vc];
        nv.modalPresentationStyle = UIModalPresentationFullScreen;
        nv.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
        [fromVC presentViewController:nv animated:YES completion:nil];
    }
    
    
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if(!_filterSound && !kSCAuthCallService.isCalling){
        [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayback withMode:AVAudioSessionModeMoviePlayback error:NULL];
    }
    
}

- (void)loadCurrentURL {
    if (self.currentURL) {
        NSURLRequest *request = [NSURLRequest requestWithURL:self.currentURL];
        
        [self.webView loadRequest:request];
    } else {
        
    }
}

@end
