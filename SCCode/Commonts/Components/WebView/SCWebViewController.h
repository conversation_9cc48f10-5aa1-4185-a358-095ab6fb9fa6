//
//  SCWebViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/17.
//

#import "SCBaseViewController.h"
#import "SCBaseViewController.h"
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCWebViewController : SCBaseViewController<WKNavigationDelegate,WKScriptMessageHandler>
@property (nonatomic, strong) WKWebView *webView;
@property (nonatomic, strong) NSString *webAddress;
@property (nonatomic, strong) UIProgressView *progressView;
//是否过滤声音设置
@property (nonatomic, assign) BOOL filterSound;
-(WKWebViewConfiguration *) defaultConfiguration;
@property(nonatomic,copy) void(^progressBlock)(CGFloat);

//打开隐私条款网页
+(void)showPrivacyPolicyWithFromVC:(UIViewController *)fromVC;
//打开用户条款
+(void)showTermConditionsWithFromVC:(UIViewController *)fromVC;
//打开网页
+(void) showWithFromVC:(UIViewController *)fromVC title:(NSString *)title url:(NSString *)url;
@end

NS_ASSUME_NONNULL_END
