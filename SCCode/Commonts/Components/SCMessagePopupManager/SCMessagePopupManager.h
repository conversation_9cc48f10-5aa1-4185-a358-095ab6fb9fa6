#import <Foundation/Foundation.h>

@interface SCMessagePopupManager : NSObject

@property (nonatomic, strong, readonly) UIView *currentPopupView;

// 配置项
@property (nonatomic, assign) NSTimeInterval autoHideInterval; // 自动隐藏时间
@property (nonatomic, assign) NSTimeInterval debounceInterval; // 防抖时间
@property (nonatomic, assign) CGFloat popupHeight;  // 弹窗高度
@property (nonatomic, assign) CGFloat popupWidth;   // 弹窗宽度

// 是否启用消息弹窗
@property (nonatomic, assign) BOOL enableMessagePopup;

+ (instancetype)shared;

// 显示消息弹窗
- (void)showMessagePopup:(NSString *)message
                 userId:(NSString *)userId;

// 显示消息弹窗（字典版本）
- (void)showMessagePopupDict:(NSString *)message
                      userId:(NSString *)userId
                    userDict:(NSDictionary *)userDict;

// 隐藏当前消息弹窗
- (void)hideCurrentPopup;

// 清除所有状态（用于退出登录时调用）
- (void)clear;

@end 