#import "SCMessagePopupManager.h"
#import "SCVideoCallViewController.h"
#import "SCFlashChatMatchViewController.h"
#import "SCConversationInfoViewController.h"
#import <Masonry/Masonry.h>
#import "SCAPIServiceManager.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

// 字典版本的消息对象
@interface SCMessagePopupMessageDict : NSObject
@property (nonatomic, copy) NSString *message;
@property (nonatomic, copy) NSString *userId;
@property (nonatomic, strong) NSDictionary *userDict;
@end

@implementation SCMessagePopupMessageDict
@end

@interface SCMessagePopupManager()

@property (nonatomic, strong) UIView *currentPopupView;
@property (nonatomic, strong) NSDate *lastPopupTime;
@property (nonatomic, strong) NSTimer *autoHideTimer;
@property (nonatomic, copy) void (^messageButtonTapHandler)(NSString *userId);
@property (nonatomic, strong) NSDictionary *currentUserDict;

@end

@implementation SCMessagePopupManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _autoHideInterval = 3.0;
        _debounceInterval = 0.3;
        _popupHeight = 90;

        _popupWidth = [UIScreen mainScreen].bounds.size.width - 30;
        _enableMessagePopup = YES;
    }
    return self;
}

+ (instancetype)shared {
    static SCMessagePopupManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SCMessagePopupManager alloc] init];
    });
    return instance;
}

- (void)showMessagePopup:(NSString *)message userId:(NSString *)userId {
    if (!self.enableMessagePopup) {
        return;
    }

    // 检查是否在禁止显示的页面
    if ([self isInForbiddenViewController]) {
        return;
    }

    // 检查是否被呼叫
    if ([self isBeingCalled]) {
        return;
    }

    // 防抖处理：如果在防抖时间内，直接返回
    NSDate *now = [NSDate date];
    if (self.lastPopupTime && [now timeIntervalSinceDate:self.lastPopupTime] < self.debounceInterval) {
        return;
    }

    kWeakSelf(self);
    // 使用字典版本的API调用
    [SCAPIServiceManager requestUserInfoWithUserId:userId cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(NSDictionary * _Nonnull userDict) {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 直接调用字典版本方法
            [weakself showMessagePopupDict:message userId:userId userDict:userDict];
        });
    } failure:nil];
}

// 字典版本的显示消息弹窗方法
- (void)showMessagePopupDict:(NSString *)message userId:(NSString *)userId userDict:(NSDictionary *)userDict {
    if (!self.enableMessagePopup) {
        return;
    }

    // 防抖处理
    NSDate *now = [NSDate date];
    if (self.lastPopupTime && [now timeIntervalSinceDate:self.lastPopupTime] < self.debounceInterval) {
        return;
    }
    self.lastPopupTime = now;

    // 如果当前有显示的消息，先隐藏
    if (self.currentPopupView) {
        [self hideCurrentPopupWithCompletion:^{
            // 创建并显示新消息 - 使用字典版本
            [self createAndShowPopupWithMessageDict:message userId:userId userDict:userDict];
        }];
    } else {
        // 直接创建并显示新消息 - 使用字典版本
        [self createAndShowPopupWithMessageDict:message userId:userId userDict:userDict];
    }
}



// 字典版本的弹窗创建方法
- (void)createAndShowPopupWithMessageDict:(NSString *)message userId:(NSString *)userId userDict:(NSDictionary *)userDict {
    if (!self.enableMessagePopup) {
        return;
    }

    // 检查是否在禁止显示的页面
    if ([self isInForbiddenViewController]) {
        return;
    }

    // 检查是否被呼叫
    if ([self isBeingCalled]) {
        return;
    }

    // 直接使用字典数据
    self.currentUserDict = userDict;

    // 确保在主线程执行
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self createAndShowPopupWithMessageDict:message userId:userId userDict:userDict];
        });
        return;
    }

    // 更新最后显示时间
    self.lastPopupTime = [NSDate date];

    // 创建弹窗容器
    UIView *popupView = [[UIView alloc] init];
    self.currentPopupView = popupView;

    // 设置基本样式
    popupView.backgroundColor = [UIColor clearColor];
    popupView.layer.cornerRadius = 19.0f;
    popupView.layer.shadowColor = [UIColor blackColor].CGColor;
    popupView.layer.shadowOffset = CGSizeMake(0, 2);
    popupView.layer.shadowOpacity = 0.1;
    popupView.layer.shadowRadius = 4;

    // 设置初始frame（仅用于动画）
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat x = (screenWidth - self.popupWidth) / 2;
    popupView.frame = CGRectMake(x, -self.popupHeight, self.popupWidth, self.popupHeight);

    SCGradientColors *colors = [SCGradientColors themeColorWithOrientation:SCGradientColorsOrientationHorizontal];
    UIImage *gradientImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(self.popupWidth, self.popupHeight)];
    if (kScAuthMar.isLanguageForce) {
        gradientImage = [gradientImage imageWithHorizontallyFlippedOrientation];
    }
    UIImageView *bgImageView = [[UIImageView alloc]initWithImage:gradientImage];
    bgImageView.layer.cornerRadius = 19.0f;
    bgImageView.layer.masksToBounds = YES;
    [popupView addSubview:bgImageView];

    // 添加头像
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = 25.0f;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    [popupView addSubview:avatarImageView];

    // 从字典获取头像URL
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    [avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    // 添加消息按钮
    UIButton *messageButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [messageButton setImage:[SCResourceManager loadImageWithName:@"ic_notifi_message"] forState:UIControlStateNormal];
    [messageButton addTarget:self action:@selector(handleMessageTap) forControlEvents:UIControlEventTouchUpInside];
    [popupView addSubview:messageButton];

    // 添加用户名标签
    UILabel *nameLabel = [[UILabel alloc] init];
    nameLabel.font = [SCFontManager semiBoldFontWithSize:16.0f];
    nameLabel.textColor = [UIColor whiteColor];
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    nameLabel.text = nickname;
    [popupView addSubview:nameLabel];

    // 添加消息内容标签
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = message;
    messageLabel.numberOfLines = 1;
    messageLabel.font = [UIFont systemFontOfSize:12];
    messageLabel.textColor = [UIColor whiteColor];
    [popupView addSubview:messageLabel];

    // 使用 Masonry 设置约束
    [avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(50, 50));
        make.leading.mas_equalTo(14);
        make.centerY.equalTo(popupView);
    }];

    [messageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.trailing.mas_equalTo(-20);
        make.centerY.equalTo(popupView);
    }];

    [nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(avatarImageView.mas_trailing).offset(8);
        make.trailing.equalTo(messageButton.mas_leading).offset(-8);
        make.top.equalTo(avatarImageView).offset(4);
        make.height.mas_equalTo(20);
    }];

    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(nameLabel);
        make.trailing.equalTo(nameLabel);
        make.top.equalTo(nameLabel.mas_bottom).offset(4);
        make.height.mas_equalTo(17);
    }];

    // 添加点击手势
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapPopup:)];
    [popupView addGestureRecognizer:tap];

    // 添加滑动手势
    UIPanGestureRecognizer *pan = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    [popupView addGestureRecognizer:pan];

    // 添加到主窗口并显示动画
    UIWindow *keyWindow = [self getKeyWindow];
    [keyWindow addSubview:popupView];

    [UIView animateWithDuration:0.3 animations:^{
        popupView.frame = CGRectMake(x, kSCSafeAreaTopHeight, self.popupWidth, self.popupHeight);
    } completion:^(BOOL finished) {
        [self startAutoHideTimer];
    }];
}

- (UIWindow *)getKeyWindow {
    UIWindow *keyWindow = nil;
    
    if (@available(iOS 13.0, *)) {
        NSSet<UIScene *> *scenes = [[UIApplication sharedApplication] connectedScenes];
        for (UIScene *scene in scenes) {
            if (scene.activationState == UISceneActivationStateForegroundActive && [scene isKindOfClass:[UIWindowScene class]]) {
                UIWindowScene *windowScene = (UIWindowScene *)scene;
                for (UIWindow *window in windowScene.windows) {
                    if (window.isKeyWindow) {
                        keyWindow = window;
                        break;
                    }
                }
            }
        }
    } else {
        keyWindow = [UIApplication sharedApplication].keyWindow;
    }
    
    return keyWindow;
}

- (void)hideCurrentPopup {
    [self hideCurrentPopupWithVelocity:0];
}

- (void)hideCurrentPopupWithVelocity:(CGFloat)velocity {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self hideCurrentPopupWithVelocity:velocity];
        });
        return;
    }
    
    if (!self.currentPopupView) {
        return;
    }
    
    [self.autoHideTimer invalidate];
    self.autoHideTimer = nil;
    
    // 根据速度计算动画时间
    CGFloat duration = velocity == 0 ? 0.3 : MIN(0.3, MAX(0.1, ABS(self.popupHeight / velocity)));
    
    // 隐藏动画
    [UIView animateWithDuration:duration
                          delay:0
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.currentPopupView.frame = CGRectMake(self.currentPopupView.frame.origin.x,
                                                -self.popupHeight,
                                                self.currentPopupView.frame.size.width,
                                                self.currentPopupView.frame.size.height);
    } completion:^(BOOL finished) {
        [self.currentPopupView removeFromSuperview];
        self.currentPopupView = nil;
        self.currentUserDict = nil;
    }];
}

- (void)handleTapPopup:(UITapGestureRecognizer *)tap {
    [self handleMessageTap];
}

- (void)startAutoHideTimer {
    [self.autoHideTimer invalidate];
    self.autoHideTimer = [NSTimer scheduledTimerWithTimeInterval:self.autoHideInterval
                                                         target:self
                                                       selector:@selector(hideCurrentPopup)
                                                       userInfo:nil
                                                        repeats:NO];
}

- (BOOL)isInForbiddenViewController {
    UIViewController *topVC = [UIViewController currentViewController];
    // 判断是否在视频页面、匹配页面等
    return [topVC isKindOfClass:[SCVideoCallViewController class]] ||
           [topVC isKindOfClass:[SCFlashChatMatchViewController class]] ||
            [topVC isKindOfClass:[SCConversationInfoViewController class]];
}

- (BOOL)isBeingCalled {
    // 呼叫中，匹配，视频，不显示
    return !kSCAuthCallService.isCanCall;
}

- (void)clear {
    [self hideCurrentPopup];
    self.lastPopupTime = nil;
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)gesture {
    CGPoint translation = [gesture translationInView:gesture.view];
    CGPoint velocity = [gesture velocityInView:gesture.view];
    
    switch (gesture.state) {
        case UIGestureRecognizerStateChanged: {
            // 只允许向上滑动
            CGFloat newY = gesture.view.frame.origin.y + translation.y;
            if (newY <= kSCSafeAreaTopHeight) {  // 初始Y位置
                gesture.view.frame = CGRectMake(gesture.view.frame.origin.x,
                                              newY,
                                              gesture.view.frame.size.width,
                                              gesture.view.frame.size.height);
            }
            [gesture setTranslation:CGPointZero inView:gesture.view];
            break;
        }
            
        case UIGestureRecognizerStateEnded: {
            CGFloat currentY = gesture.view.frame.origin.y;
            CGFloat dismissThreshold = kSCSafeAreaTopHeight - (self.popupHeight * 0.3); // 移动超过30%就关闭
            
            // 根据滑动速度或位置判断是否需要关闭
            if (velocity.y < -500 || currentY < dismissThreshold) {
                // 滑动速度快或位置超过阈值，关闭弹窗
                [self hideCurrentPopupWithVelocity:velocity.y];
            } else {
                // 恢复到原始位置
                [self restorePopupPosition];
            }
            break;
        }
            
        case UIGestureRecognizerStateCancelled: {
            [self restorePopupPosition];
            break;
        }
            
        default:
            break;
    }
}

- (void)restorePopupPosition {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self restorePopupPosition];
        });
        return;
    }
    
    [UIView animateWithDuration:0.2 animations:^{
        self.currentPopupView.frame = CGRectMake(self.currentPopupView.frame.origin.x,
                                                 kSCSafeAreaTopHeight,
                                                self.currentPopupView.frame.size.width,
                                                self.currentPopupView.frame.size.height);
    }];
}

- (void)handleMessageTap {
    // 获取当前顶部控制器
    UIViewController *topVC = [UIViewController currentViewController];
    if (!topVC) return;

    // 隐藏当前弹窗
    [self hideCurrentPopup];

    // 使用字典数据打开会话
    if (self.currentUserDict) {
        NSString *userID = [SCDictionaryHelper stringFromDictionary:self.currentUserDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        [SCConversationInfoViewController showWithFromVC:topVC tager:userID userDict:self.currentUserDict];
    }
}

- (void)hideCurrentPopupWithCompletion:(void (^)(void))completion {
    if (![NSThread isMainThread]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self hideCurrentPopupWithCompletion:completion];
        });
        return;
    }
    
    if (!self.currentPopupView) {
        if (completion) {
            completion();
        }
        return;
    }
    
    [self.autoHideTimer invalidate];
    self.autoHideTimer = nil;
    
    [UIView animateWithDuration:0.3
                          delay:0
                        options:UIViewAnimationOptionCurveEaseOut
                     animations:^{
        self.currentPopupView.frame = CGRectMake(self.currentPopupView.frame.origin.x,
                                                -self.popupHeight,
                                                self.currentPopupView.frame.size.width,
                                                self.currentPopupView.frame.size.height);
    } completion:^(BOOL finished) {
        [self.currentPopupView removeFromSuperview];
        self.currentPopupView = nil;
        self.currentUserDict = nil;
        if (completion) {
            completion();
        }
    }];
}

@end 
