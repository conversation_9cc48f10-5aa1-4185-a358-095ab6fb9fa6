//
//  SCAlertViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCAlertViewController.h"
#import <Masonry/Masonry.h>

@interface SCAlertViewController ()

//半透明背景
@property (nonatomic,nullable, weak) UIView *backgroundView;
//弹窗布局
@property (nonatomic,nullable, weak) UIView *alertView;
//毛玻璃背景
@property (nonatomic,nullable, weak) UIVisualEffectView *blurView;
//标题
@property (nonatomic,nullable, weak) UILabel *titleLabel;
//内容
@property (nonatomic,nullable, weak) UILabel *contentLabel;
//红色警告文案
@property (nonatomic,nullable, weak) UILabel *redLabel;
//取消按钮
@property (nonatomic,nullable, weak) UIButton *leftButton;
//确定按钮
@property (nonatomic,nullable, weak) UIButton *rightButton;
//顶部icon
@property (nonatomic, nullable, weak) UIImageView *icon;

//标题
@property (nonatomic,nullable, copy) NSString *titleString;
//内容
@property (nonatomic,nullable, copy) NSString *contentString;
//红色警告文案
@property (nonatomic,nullable, copy) NSString *redString;
//取消按钮
@property (nonatomic,nullable, copy) NSString *leftBtnString;
//确定按钮
@property (nonatomic,nullable, copy) NSString *rightBtnString;
//icon
@property (nonatomic, nullable, copy) UIImage *iconImage;
@property (nonatomic,assign) BOOL isBoundsHidden;
//点击左边按钮回调
@property (nonatomic,nullable, copy) void (^leftBlock)(SCAlertViewController * _Nonnull);
//点击右边按钮回调
@property (nonatomic,nullable, copy) void (^rightBlock)(SCAlertViewController * _Nonnull);


@end

@implementation SCAlertViewController

-(instancetype) initWithTitle:(NSString * _Nullable) title content:(NSString * _Nullable) content red:(NSString * _Nullable) red leftBtn:(NSString * _Nullable) leftBtn rightBtn:(NSString * _Nullable) rightBtn icon:(UIImage *_Nullable) icon{
    if (self = [super init]) {
        self.titleString = title;
        self.contentString = content;
        self.redString = red;
        self.leftBtnString = leftBtn;
        self.rightBtnString = rightBtn;
        self.iconImage = icon;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initUI];
    [self _initLayout];
    [self _initEvent];
}

-(void) _initUI{
    self.view.backgroundColor = [UIColor clearColor];
    UIView *backgroundView = [UIView new];
    backgroundView.backgroundColor = [UIColor scTranBlackBGColor];
    [self.view addSubview:backgroundView];
    self.backgroundView = backgroundView;
    UIView *alertView = [UIView new];
    alertView.backgroundColor = [UIColor clearColor];
    alertView.layer.cornerRadius = 16;
    alertView.clipsToBounds = YES;
    [self.view addSubview:alertView];
    self.alertView = alertView;
    
    // 添加高斯模糊效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    [self.alertView addSubview:blurView];
    self.blurView = blurView;
    
    if (_iconImage != nil) {
        UIImageView *iconImageView = [[UIImageView alloc]initWithImage:self.iconImage];
        [self.alertView addSubview:iconImageView];
        self.icon = iconImageView;
    }
    if(!kSCIsStrEmpty(_titleString)){
        UILabel * titleLabel = [UILabel labelWithText:self.titleString textColor:[UIColor scWhite] font:kScUIFontRegular(14) alignment:NSTextAlignmentCenter];
        titleLabel.numberOfLines = 0;
        [self.alertView addSubview:titleLabel];
        self.titleLabel = titleLabel;
    }
    if(!kSCIsStrEmpty(_contentString)){
        UILabel * contentLabel = [UILabel labelWithText:self.contentString textColor:[UIColor scBlack] font:kScUIFontMedium(12) alignment:NSTextAlignmentCenter];
        contentLabel.numberOfLines = 0;
        [self.alertView addSubview:contentLabel];
        self.contentLabel = contentLabel;
    }
    if(!kSCIsStrEmpty(_redString)){
        UILabel * redLabel = [UILabel labelWithText:self.redString textColor:[UIColor scRed] font:kScUIFontRegular(12) alignment:NSTextAlignmentCenter];
        redLabel.numberOfLines = 0;
        [self.alertView addSubview:redLabel];
        self.redLabel = redLabel;
    }
    if(!kSCIsStrEmpty(_leftBtnString)){
        UIButton * leftButton = [UIButton buttonWithTitle:_leftBtnString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16) image:nil backgroundColor:[UIColor colorWithHexString:@"#7B7B7B"] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onClickLeft)];
        [self.alertView addSubview:leftButton];
        self.leftButton = leftButton;
    }
    
    if(!kSCIsStrEmpty(_rightBtnString)){
        UIButton * rightButton = [UIButton buttonWithTitle:_rightBtnString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(onClickRight)];
        [rightButton sc_setThemeGradientBackground];
        [self.alertView addSubview:rightButton];
        self.rightButton = rightButton;
    }
}

static CGFloat kSCNormalButtonHeight = 40;

- (void) _initLayout{
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.equalTo(@(kSCScaleWidth(292)));
    }];
    [self.blurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.alertView);
    }];
    UIView *lasView;
    CGFloat paddingH = 33.0f;
    if (self.icon != nil) {
        [self.icon mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.alertView).offset(-30);
            make.centerX.equalTo(self.alertView);
            make.size.mas_equalTo(self.iconImage.size);
        }];
        lasView = self.icon;
    }
    if(self.titleLabel != nil){
        [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            if(lasView != nil){
                make.top.equalTo(lasView.mas_bottom).offset(16);
            }else{
                make.top.equalTo(self.alertView).offset(16);
            }
            make.leading.equalTo(self.alertView).offset(paddingH);
            make.trailing.equalTo(self.alertView).offset(-paddingH);
        }];
        lasView = self.titleLabel;
    }
    if(self.contentLabel != nil){
        [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            if(lasView != nil){
                make.top.equalTo(lasView.mas_bottom).offset(16);
            }else{
                make.top.equalTo(self.alertView).offset(16);
            }
            make.leading.equalTo(self.alertView).offset(paddingH);
            make.trailing.equalTo(self.alertView).offset(-paddingH);
        }];
        lasView = self.contentLabel;
    }
    if(self.redLabel != nil){
        [self.redLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            if(lasView != nil){
                make.top.equalTo(lasView.mas_bottom).offset(16);
            }else{
                make.top.equalTo(self.alertView).offset(16);
            }
            make.leading.equalTo(self.alertView).offset(paddingH);
            make.trailing.equalTo(self.alertView).offset(-paddingH);
        }];
        lasView = self.redLabel;
    }
    if(self.leftButton != nil){
        [self.leftButton mas_makeConstraints:^(MASConstraintMaker *make) {
            if(lasView != nil){
                make.top.equalTo(lasView.mas_bottom).offset(16);
            }else{
                make.top.equalTo(self.alertView).offset(16);
            }
            make.leading.equalTo(self.alertView).offset(paddingH);
            if(self.rightButton != nil){
                make.trailing.equalTo(self.rightButton.mas_leading).offset(-42);
                make.width.equalTo(self.rightButton.mas_width);
            }else{
                make.trailing.equalTo(self.alertView).offset(-paddingH);
            }
            make.height.equalTo(@(kSCNormalButtonHeight));
            make.bottom.equalTo(self.alertView).offset(-16);
        }];
    }
    if(self.rightButton != nil){
        [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
            if(lasView != nil){
                make.top.equalTo(lasView.mas_bottom).offset(16);
            }else{
                make.top.equalTo(self.alertView).offset(16);
            }
            make.trailing.equalTo(self.alertView).offset(-paddingH);
            
            if(self.leftButton == nil){
                make.leading.equalTo(self.alertView).offset(paddingH);
            }
            make.height.equalTo(@(kSCNormalButtonHeight));
            make.bottom.equalTo(self.alertView).offset(-16);
        }];
    }
    if(self.leftButton == nil && self.rightButton == nil){
        [lasView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.alertView).offset(-16);
        }];
    }
}

- (void) _initEvent{
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onClickBackgroundView)];
    [self.backgroundView addGestureRecognizer:tap];
}


#pragma mark - onClickLeft
- (void) onClickLeft{
    kSCBlockExeNotNil(self.leftBlock,self);
}
- (void) onClickRight{
    kSCBlockExeNotNil(self.rightBlock,self);
}
- (void) onClickBackgroundView{
    if (self.isBoundsHidden) {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
    
}

#pragma mark - Show 入口
+(void)showWithTitle:(NSString * _Nullable) title content:(NSString * _Nullable) content red:(NSString * _Nullable) red leftBtn:(NSString * _Nullable) leftBtn rightBtn:(NSString * _Nullable) rightBtn icon:(UIImage * _Nullable)icon boundsHidden:(BOOL)isBoundsHidden fromVC:(UIViewController * _Nonnull) fromVC leftBlock:(void (^ __nullable)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^ __nullable)(SCAlertViewController * _Nonnull))rightBlock{
    SCAlertViewController *alertVC = [[SCAlertViewController alloc] initWithTitle:title content:content red:red leftBtn:leftBtn rightBtn:rightBtn icon:icon];
    alertVC.leftBlock = leftBlock;
    alertVC.rightBlock = rightBlock;
    alertVC.isBoundsHidden = isBoundsHidden;
    alertVC.modalPresentationStyle = UIModalPresentationOverFullScreen;
    alertVC.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:alertVC animated:YES completion:nil];
    
}
+(void) showNotDisturbCallWithFromVC:(UIViewController *)fromVC leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock{
    [self showWithTitle:@"You will not receive calls from the others.Are you sure to turn it on？".translateString content:nil red:nil leftBtn:@"Cancel".translateString rightBtn:@"Confirm".translateString icon:nil boundsHidden:NO fromVC:fromVC leftBlock:leftBlock rightBlock:rightBlock];
}

+(void) showNotDisturbMessageWithFromVC:(UIViewController *)fromVC leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock{
    [self showWithTitle:@"You will not receive messages from strangers. Are you sure to turn it on？".translateString content:nil red:nil leftBtn:@"Cancel".translateString rightBtn:@"Confirm".translateString icon:nil boundsHidden:NO fromVC:fromVC leftBlock:leftBlock rightBlock:rightBlock];
}

+(void) showDeleteAccountWithFromVC:(UIViewController *)fromVC red:(NSString * _Nullable)redString leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock {
    [self showWithTitle:@"Confirm to delete account".translateString content:nil red:redString leftBtn:@"Delete".translateString rightBtn:@"Wait".translateString icon:nil boundsHidden:YES fromVC:fromVC leftBlock:leftBlock rightBlock:rightBlock];
}

//是否打开后置摄像头
+(void) showTrunOnRearCameraWithFromVC:(UIViewController *)fromVC price:(NSInteger) price days:(NSInteger) days leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock{
    [self showWithTitle:@"Are you sure to turn on the real camera?".translateString content:nil red:[NSString stringWithFormat:@"%ld coins / %ld days",price,days] leftBtn:@"No".translateString rightBtn:@"Yes".translateString icon:nil boundsHidden:YES fromVC:fromVC leftBlock:leftBlock rightBlock:rightBlock];
}

+ (void)showChangeLanguageWithFromVC:(UIViewController *)fromVC title:(NSString *)title leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock {
    [self showWithTitle:title content:nil red:nil leftBtn:@"Cancel".translateString rightBtn:@"Confirm".translateString icon:nil boundsHidden:YES fromVC:fromVC leftBlock:leftBlock rightBlock:rightBlock];
}


@end
