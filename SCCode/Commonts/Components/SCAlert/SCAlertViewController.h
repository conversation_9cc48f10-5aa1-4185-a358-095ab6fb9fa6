//
//  SCAlertViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCAlertViewController : UIViewController

///显示对话框
+(void)showWithTitle:(NSString * _Nullable) title content:(NSString * _Nullable) content red:(NSString * _Nullable) red leftBtn:(NSString * _Nullable) leftBtn rightBtn:(NSString * _Nullable) rightBtn icon:(UIImage * _Nullable)icon boundsHidden:(BOOL)isBoundsHidden fromVC:(UIViewController * _Nonnull) fromVC leftBlock:(void (^ __nullable)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^ __nullable)(SCAlertViewController * _Nonnull))rightBlock;

///显示呼叫免打扰提示框
+(void) showNotDisturbCallWithFromVC:(UIViewController *)fromVC leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock;

///显示消息免打扰提示框
+(void) showNotDisturbMessageWithFromVC:(UIViewController *)fromVC leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock;

///删除账号
+(void) showDeleteAccountWithFromVC:(UIViewController *)fromVC red:(NSString * _Nullable)redString leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock;

//是否打开后置摄像头
+(void) showTrunOnRearCameraWithFromVC:(UIViewController *)fromVC price:(NSInteger) price days:(NSInteger) days leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock;

/// 切换语言
+(void) showChangeLanguageWithFromVC:(UIViewController *)fromVC title:(NSString *)title leftBlock:(void (^)(SCAlertViewController * _Nonnull))leftBlock rightBlock:(void (^)(SCAlertViewController * _Nonnull))rightBlock;
@end

NS_ASSUME_NONNULL_END
