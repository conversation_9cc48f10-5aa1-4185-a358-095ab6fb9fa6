//
//  SCStoreScoreAlertViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/2/1.
//

#import "SCStoreScoreAlertViewController.h"
#import <StoreKit/StoreKit.h>
#import "SCPopupManager.h"
#import "SCFontManager.h"

@interface SCStoreScoreAlertViewController ()

//内容布局
@property (nonatomic, strong) UIImageView *contentV;
//顶部居中的App图标
@property (nonatomic, strong) UIImageView *appIconIV;
//标题
@property (nonatomic, strong) UILabel *titleLabel;
//描述
@property (nonatomic, strong) UILabel *descriptionL;
//按钮垂直居中 间距为 20
//Praise 按钮
@property (nonatomic, strong) UIButton *praiseBtn;
//Negative 按钮
@property (nonatomic, strong) UIButton *negativeBtn;
//Next Time 按钮
@property (nonatomic, strong) UIButton *nextTimeBtn;


@end

@implementation SCStoreScoreAlertViewController

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = UIColor.scTranBlackBGColor;
    
    //背景宽高比
    CGFloat whScale = 335.0f/394.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(20.0*2);
    CGFloat hScale = (contentWidth/whScale) / 394.0f;
    CGFloat wScale = contentWidth / 335.0f;
    
    //内容布局
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"bg_rate_app"]];
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentV = contentBgIV;
    
    //顶部居中的图标
    _appIconIV = [UIImageView new].setImage([SCResourceManager loadImageWithName:@"ic_rate_app"]).addSuperView(_contentV);
    
    //标题
    _titleLabel = [UILabel labelWithText:@"Please rate this app".translateString textColor:UIColor.scWhite font:[SCFontManager boldItalicFontWithSize:22.0f]].addSuperView(_contentV);
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    
    //描述
    _descriptionL = [UILabel labelWithText:@"This apllication was make for you with love and care. We appreciate your positive reviews as well as high ratings!".translateString textColor:UIColor.scWhite font:kScUIFontRegular(14)].addSuperView(_contentV);
    _descriptionL.textAlignment = NSTextAlignmentCenter;

    //Praise 按钮
    _praiseBtn = [UIButton buttonWithTitle:@"Praise".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius].addSuperView(_contentV);
    [_praiseBtn sc_setThemeGradientBackground];
    [_praiseBtn addTarget:self action:@selector(praiseBtnClick) forControlEvents:UIControlEventTouchUpInside];
    //Negative 按钮 灰色背景 黑色字体
    _negativeBtn = [UIButton buttonWithTitle:@"Negative".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:kSCColorWithHexStr(@"#4B4B4B") cornerRadius:kSCNormalCornerRadius].addSuperView(_contentV);
    [_negativeBtn addTarget:self action:@selector(negativeBtnClick) forControlEvents:UIControlEventTouchUpInside];

    //Next Time 按钮 灰色背景 黑色字体
    _nextTimeBtn = [UIButton buttonWithTitle:@"Next Time".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:kSCColorWithHexStr(@"#4B4B4B") cornerRadius:kSCNormalCornerRadius].addSuperView(_contentV);
    [_nextTimeBtn addTarget:self action:@selector(negativeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //布局
    [_contentV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    [_appIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentV).offset(10.0f*hScale);
        make.centerX.equalTo(self.contentV);
        make.size.mas_equalTo(CGSizeMake(78.0f*wScale, 76.0f*hScale));
    }];
    
    [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.appIconIV.mas_bottom).offset(1.0f*hScale);
        make.centerX.equalTo(self.contentV);
        make.leading.equalTo(self.contentV).offset(6.0f*wScale);
        make.trailing.equalTo(self.contentV).offset(-6.0f*wScale);
        make.height.mas_equalTo(28.0f);
    }];
    
    [_descriptionL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(16.0f*hScale);
        make.leading.equalTo(self.contentV).offset(24.0f*wScale);
        make.trailing.equalTo(self.contentV).offset(-24.0f*wScale);
    }];
    
    [_praiseBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descriptionL.mas_bottom).offset(12*hScale);
        make.leading.equalTo(self.contentV).offset(48.0f*wScale);
        make.trailing.equalTo(self.contentV).offset(-48.0f*wScale);
        make.height.mas_equalTo(46.0f*hScale);
    }];
    
    [_negativeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.praiseBtn.mas_bottom).offset(16.0f*hScale);
        make.leading.equalTo(self.contentV).offset(48.0f*wScale);
        make.trailing.equalTo(self.contentV).offset(-48.0f*wScale);
        make.height.mas_equalTo(46.0f*hScale);
    }];
    
    [_nextTimeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.negativeBtn.mas_bottom).offset(16.0f*hScale);
        make.leading.equalTo(self.contentV).offset(48.0f*wScale);
        make.trailing.equalTo(self.contentV).offset(-48.0f*wScale);
        make.height.mas_equalTo(46.0f*hScale);
        make.bottom.equalTo(self.contentV).offset(-20.0f*hScale);
    }];
}


#pragma mark - Action

- (void)praiseBtnClick{
    if ([SKStoreReviewController respondsToSelector:@selector(requestReviewInScene:)]) {
        [SKStoreReviewController requestReviewInScene:self.view.window.windowScene];
    } else {
        // Fallback for older versions of iOS
        [SKStoreReviewController requestReview];
    }
    
    kSCBlockExeNotNil(self.completionBlock);
    [[SCPopupManager shared] dismissPopup:self];
}

- (void)negativeBtnClick{

    kSCBlockExeNotNil(self.completionBlock);
    [[SCPopupManager shared] dismissPopup:self];
    
}

- (void)nextTimeBtnClick{
    kSCBlockExeNotNil(self.completionBlock);
    [[SCPopupManager shared] dismissPopup:self];
}



#pragma mark - Route
//显示对话框
+(BOOL) showWithCompletion:(void(^ _Nullable)(void))completion{
    SCStoreScoreAlertViewController *vc = [[SCStoreScoreAlertViewController alloc] init];
    vc.completionBlock = completion;
    [[SCPopupManager shared] showPopup:vc inViewController:nil animationStyle:SCPopupAnimationStyleCenter];
    return YES;
}
+(BOOL) checkIsCanOpenStoreScoreAlertWithCompletion:(void(^ _Nullable)(void))completion{
    
    BOOL hasShow = [[NSUserDefaults standardUserDefaults] boolForKey:@"sc_app_has_show_store_score_alert_key"];
    if(!hasShow){
        if([SCStoreScoreAlertViewController showWithCompletion:completion]){
            //保存
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"sc_app_has_show_store_score_alert_key"];
            [[NSUserDefaults standardUserDefaults] synchronize];
            return YES;
        }
    }
    return NO;
}


@end
