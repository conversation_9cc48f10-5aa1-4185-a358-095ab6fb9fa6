//
//  SCCallExceptionalAlert.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/1.
//

#import "SCCallExceptionalAlert.h"
#import "SCPopupManager.h"
#import "SCFontManager.h"

@interface SCCallExceptionalAlert ()

//内容布局
@property(nonatomic,strong) UIView *contentView;
//标题
@property (nonatomic,strong) UIImageView *titleView;
@property(nonatomic,strong) UILabel *titleL;
//图片
@property(nonatomic,strong) UIImageView *iconIV;
//描述
@property(nonatomic,strong) UILabel *descL;
//关闭按钮

@property(nonatomic,strong) UIButton *closeBtn;

//描述文本
@property(nonatomic,copy) NSString *contentStr;

@end

@implementation SCCallExceptionalAlert

- (void)initUI{
    [super initUI];
    self.view.backgroundColor = [UIColor clearColor];
    [self setIsHiddenSCNavigationBar:YES];
    
    //背景宽高比
    CGFloat whScale = 334.0f/347.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(20.0*2);
    CGFloat hScale = (contentWidth/whScale) / 347.0f;
    CGFloat wScale = contentWidth / 334.0f;
    
    _contentView = [[UIView alloc] init].setBackgroundColor([UIColor colorWithHexString:@"#282828"]).setCornerRadius(kSCBigCornerRadius).addSuperView(self.view);
    
    UIImage *bgImage = [UIImage imageGradientWithSCGradientColors:[SCGradientColors themeColorWithOrientation:SCGradientColorsOrientationHorizontal] size:CGSizeMake(contentWidth, 72.0f*hScale)];
    _titleView = [[UIImageView alloc]init].addSuperView(_contentView);
    _titleView.image = bgImage;
    
    _titleL = [[UILabel alloc] init].setText(@"Compensation".translateString).setTextColor(UIColor.scWhite).setTextAlignment(NSTextAlignmentCenter).addSuperView(_titleView);
    _titleL.font = [SCFontManager boldItalicFontWithSize:30.0f];
    
    _iconIV = [[UIImageView alloc] init].setImage([SCResourceManager loadImageWithName:@"ic_call_exceptional"]).addSuperView(_contentView);
    
    _descL = [UILabel new].setFontRegularSize(14).setTextColor(UIColor.scWhite).setTextAlignment(NSTextAlignmentCenter).addSuperView(_contentView);
    _descL.numberOfLines = 0;
    [self updateDescl];
    
    _closeBtn = [UIButton buttonWithTitle:@"Got it".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:UIColor.scTheme cornerRadius:kSCNormalCornerRadius].addSuperView(_contentView);
    [_closeBtn sc_setThemeGradientBackground];
    [_closeBtn addTarget:self action:@selector(closeBtnClick) forControlEvents:UIControlEventTouchUpInside];
    
    //内容布局
    [_contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.mas_offset(contentWidth);
//        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    //标题
    [_titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.leading.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(72.0f * hScale);
    }];
    
    [_titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.titleView);
    }];
    
    //图片
    [_iconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleView.mas_bottom).offset(4.0f*hScale);
        make.centerX.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(190*wScale, 134*hScale));
    }];
    
    //描述
    [_descL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconIV.mas_bottom).offset(2.0f *hScale);
        make.leading.equalTo(self.contentView).offset(10*wScale);
        make.trailing.equalTo(self.contentView).offset(-10*wScale);
    }];
    
    //关闭按钮
    [_closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descL.mas_bottom).offset(10*hScale);
        make.height.mas_equalTo(46*hScale);
        make.leading.equalTo(self.contentView).offset(48*wScale);
        make.trailing.equalTo(self.contentView).offset(-48*wScale);
        make.bottom.equalTo(self.contentView).offset(-20);
    }];
}

- (void)setContentStr:(NSString *)contentStr{
    _contentStr = contentStr;
    [self updateDescl];
}

- (void)updateDescl {
    if (!kSCIsStrEmpty(self.contentStr)) {
        // 使用正则表达式提取数字
        NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\\d+" options:0 error:nil];
        NSTextCheckingResult *match = [regex firstMatchInString:self.contentStr options:0 range:NSMakeRange(0, self.contentStr.length)];
        
        NSString *coins = @"";
        if (match) {
            coins = [self.contentStr substringWithRange:match.range];
        }
        
        // 创建带有金币图标的富文本
        NSMutableAttributedString *coinsAttr = [[NSMutableAttributedString alloc] initWithString:coins];
        NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
        attachment.image = [SCResourceManager loadImageWithName:@"ic_coins_middle"];
        attachment.bounds = CGRectMake(0, -3, 16, 13); // 调整图标大小和位置
        NSAttributedString *imageAttr = [NSAttributedString attributedStringWithAttachment:attachment];
        [coinsAttr appendAttributedString:imageAttr];
        
        // 获取翻译后的模板文本
        NSString *translateStr = @"As your call interrupted due to the service issue. We make up ### coins to your account for your loss. Thanks for your support.".translateString;
        
        // 替换模板中的 ### 为带图标的数字
        NSMutableAttributedString *finalAttr = [[NSMutableAttributedString alloc] initWithString:translateStr];
        NSRange replaceRange = [translateStr rangeOfString:@"###"];
        if (replaceRange.location != NSNotFound) {
            [finalAttr replaceCharactersInRange:replaceRange withAttributedString:coinsAttr];
        }
        
        if (_descL) {
            _descL.attributedText = finalAttr;
        }
    }
}

#pragma mark - Action
- (void)closeBtnClick{
    [[SCPopupManager shared] dismissPopup:self];
}

#pragma mark - Route
+(void)showWithContent:(NSString *)content{
    SCCallExceptionalAlert *vc = [[SCCallExceptionalAlert alloc] init];
    vc.contentStr = content;
    [[SCPopupManager shared] showPopup:vc inViewController:nil animationStyle:SCPopupAnimationStyleCenter];
}

@end
