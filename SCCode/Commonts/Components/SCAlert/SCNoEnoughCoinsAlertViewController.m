//
//  SCNoEnoughCoinsAlertViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCNoEnoughCoinsAlertViewController.h"

@interface SCNoEnoughCoinsAlertViewController ()
//半透明背景
@property (nonatomic,nullable, weak) UIView *backgroundView;
//弹窗布局
@property (nonatomic,nullable, weak) UIView *alertView;
//icon
@property (nonatomic,nullable, weak) UIImageView *iconIV;
//标题
@property (nonatomic,nullable, weak) UILabel *titleLabel;
//内容
@property (nonatomic,nullable, weak) UILabel *contentLabel;

//确定按钮
@property (nonatomic,nullable, weak) UIButton *rightButton;
///关闭按钮
@property (nonatomic,nullable, weak) UIButton *closeButton;

//点击转跳没有金币
@property (nonatomic,nullable, copy) void (^getCoinsBlock)(SCNoEnoughCoinsAlertViewController * _Nonnull);
///点击关闭
@property (nonatomic,nullable, copy) void (^closeBlock)(SCNoEnoughCoinsAlertViewController * _Nonnull);

@end

@implementation SCNoEnoughCoinsAlertViewController

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    UIView *backgroundView = [UIView new];
    backgroundView.backgroundColor = [UIColor scTranBlackBGColor];
    [self.view addSubview:backgroundView];
    self.backgroundView = backgroundView;
    UIView *alertView = [UIView new];
    alertView.backgroundColor = [UIColor whiteColor];
    alertView.layer.cornerRadius = 16;
    alertView.layer.masksToBounds = YES;
    [self.view addSubview:alertView];
    self.alertView = alertView;
    
    _iconIV = [UIImageView new].setImageName(@"ic_no_enough_coins").addSuperView(self.alertView);
    
    _titleLabel = [UILabel labelWithText:@"We are sorry".translateString textColor:[UIColor scBlack] font:kScUIFontMedium(23) alignment:NSTextAlignmentCenter].setNumberLines(0).addSuperView(self.alertView);
    
    _contentLabel = [UILabel labelWithText:@"You missed girl, because you don't have enough coins".translateString textColor:[UIColor scBlack] font:kScUIFontRegular(13) alignment:NSTextAlignmentCenter].setNumberLines(0).addSuperView(self.alertView);
    _rightButton = [UIButton buttonWithTitle:@"Get Coins".translateString titleColor:[UIColor scWhite] font:kScUIFontRegular(17) image:nil backgroundColor:[UIColor scTheme] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onClickRight)].addSuperView(self.alertView);
    [_rightButton sc_setThemeGradientBackground];
    _closeButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close"] target:self action:@selector(onClose)].addSuperView(self.alertView);
    
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    [self.alertView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.equalTo(@(kSCAlertWidth));
    }];
    
    [self.iconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(CGSizeMake(88, 86)));
        make.centerX.equalTo(self.alertView);
        make.top.mas_equalTo(24);
    }];
    CGFloat paddingH = 33.0f;
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconIV.mas_bottom).offset(10);
        make.leading.equalTo(self.alertView).offset(paddingH);
        make.trailing.equalTo(self.alertView).offset(-paddingH);
    }];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(16);
        make.leading.equalTo(self.alertView).offset(paddingH);
        make.trailing.equalTo(self.alertView).offset(-paddingH);
    }];
    
    [self.rightButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentLabel.mas_bottom).offset(12);
        make.centerX.equalTo(self.alertView);
        make.height.equalTo(@(kSCNormalButtonHeight));
        make.width.mas_equalTo(206);
        make.bottom.equalTo(self.alertView).offset(-20);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(CGSizeMake(44, 44)));
        make.top.equalTo(self.alertView).offset(0);
        make.trailing.equalTo(self.alertView).offset(0);
    }];
    
}

static CGFloat kSCAlertWidth = 308;
static CGFloat kSCNormalButtonHeight = 46;

#pragma mark - onClickLeft

-(void) onClose{
    kSCBlockExeNotNil(self.closeBlock,self);
}

- (void) onClickRight{
    kSCBlockExeNotNil(self.getCoinsBlock,self);
}

#pragma mark - Show 入口
+(void)showWithFromVC:(UIViewController *)fromVC getCoinsBlock:(void (^ __nullable)(SCNoEnoughCoinsAlertViewController * _Nonnull))getCoinsBlock closeBlock:(void (^ __nullable)(SCNoEnoughCoinsAlertViewController * _Nonnull))closeBlock{
    SCNoEnoughCoinsAlertViewController *alertVC = [[SCNoEnoughCoinsAlertViewController alloc] init];
    alertVC.closeBlock = closeBlock;
    alertVC.getCoinsBlock = getCoinsBlock;
    alertVC.modalPresentationStyle = UIModalPresentationOverFullScreen;
    alertVC.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:alertVC animated:YES completion:nil];
    
}

@end
