//
//  SCOnlineStatusSubscribe.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import <Foundation/Foundation.h>
#import "SCOrderedDictionary.h"
NS_ASSUME_NONNULL_BEGIN
@class SCOnlineStatusSubscribeModel;
@interface SCOnlineStatusSubscribe : NSObject

///记录每个用户ID对应的状态和更新时间
@property(nonatomic,strong) NSMutableArray<NSString *> * userIDs;
@property(nonatomic,assign) BOOL isPause;
///最大数量 [默认最大限制100]
@property(nonatomic,assign) NSUInteger maxNum;
///状态更新通知
@property(nonatomic,strong) SCObservable<NSArray<NSString *> *> *changeObs;

///添加用户ID
-(void)addWithUserID:(NSString *)userId;
///删除用户ID
-(void)deleteWithUserID:(NSString *)userId;
///暂停
-(void)pause;
///开始
-(void)start;
@end



///在线状态数据模型
@interface SCOnlineStatusSubscribeModel : NSObject

//用户ID
@property(nonatomic,copy)NSString * userId;
//在线状态
@property(nonatomic,assign)SCAnchorStatus status;
//更新时间
@property(nonatomic,assign)NSTimeInterval updateTime;
- (instancetype)initWithUserId:(NSString *)userId;

@end

NS_ASSUME_NONNULL_END
