//
//  SCOnlineStatusChangeViewController.h
//  Supercall  需要刷新用户在线状态的ViewController
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/5.
//

#import "SCBaseViewController.h"
#import "SCOnlineStatusSubscribe.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCOnlineStatusChangeViewController : SCBaseViewController

//
@property(nonatomic,strong,readonly)SCOnlineStatusSubscribe * onlineStatusSub;
///如果被更新会回调该方法
-(void) onChangeOnlineStatusWithIds:(NSArray<NSString *> *) ids;
///获取状态
-(SCAnchorStatus) statusWithUserID:(NSString *)userId;
///添加用户ID
-(void)addListenOnlineStatusWithUserID:(NSString *)userId;
@end

NS_ASSUME_NONNULL_END
