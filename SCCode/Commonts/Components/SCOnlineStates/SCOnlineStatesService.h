//
//  SCOnlineStatesService.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/5.
//

#import <Foundation/Foundation.h>
#import "SCOnlineStatusSubscribe.h"
#import "SCBaseAppService.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCOnlineStatesService : SCBaseAppService

///记录每个用户ID对应的状态和更新时间
@property(nonatomic,strong) SCOrderedDictionary<NSString *,NSDictionary *> *userStatusDic;

///添加订阅
- (void)add:(SCOnlineStatusSubscribe *)subscribe dispose:(SCDisposeBag *) dispose;
//立刻刷新
-(void)refresh;

@end

NS_ASSUME_NONNULL_END
