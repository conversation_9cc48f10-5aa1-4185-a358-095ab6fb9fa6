//
//  SCOnlineStatusChangeViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import "SCOnlineStatusChangeViewController.h"
#import "SCOnlineStatesService.h"
#import "SCDictionaryHelper.h"

@interface SCOnlineStatusChangeViewController (){
    SCOnlineStatusSubscribe * _onlineStatusSub;
}

@end

@implementation SCOnlineStatusChangeViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if(_onlineStatusSub){
        [_onlineStatusSub start];
        //进入页面立刻刷新一次
        [kSCAuthOnlineStatesService refresh];
    }
}
- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    if(_onlineStatusSub){
        [_onlineStatusSub pause];
    }
}

- (void)dealloc{
    _onlineStatusSub = nil;
}


- (SCOnlineStatusSubscribe *)onlineStatusSub{
    if(_onlineStatusSub == nil){
        _onlineStatusSub = [[SCOnlineStatusSubscribe alloc] init];
        kWeakSelf(self);
        [_onlineStatusSub.changeObs subscribe:^(NSArray<NSString *> * _Nullable value) {
            [weakself onChangeOnlineStatusWithIds:value];
        } error:nil disposeBag:self.disposeBag];
        [kSCAuthOnlineStatesService add:self.onlineStatusSub dispose:self.disposeBag];
    }
    return _onlineStatusSub;
}

-(void) onChangeOnlineStatusWithIds:(NSArray<NSString *> *) ids{
    
}
- (SCAnchorStatus)statusWithUserID:(NSString *)userId{
    NSDictionary *statusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:userId];
    return (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:statusDict forKey:@"status" defaultValue:AnchorStatusOffline];
}
- (void)addListenOnlineStatusWithUserID:(NSString *)userId{
    [self.onlineStatusSub addWithUserID:userId];
}
@end
