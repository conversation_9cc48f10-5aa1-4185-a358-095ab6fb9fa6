//
//  SCOrderedDictionary.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import "SCOrderedDictionary.h"

@implementation SCOrderedDictionary
{
    NSMutableArray *_keys;
    NSMutableDictionary *_dictionary;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _keys = [NSMutableArray new];
        _dictionary = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (void)setObject:(id)object forKey:(id)key atIndex:(NSUInteger)atIndex {
    NSMutableArray *tempKeys = [_keys mutableCopy];
    
    if ([tempKeys containsObject:key]) {
        NSUInteger fromIndex = [tempKeys indexOfObject:key];
        id objectToMove = [tempKeys objectAtIndex:fromIndex];
        [tempKeys removeObjectAtIndex:fromIndex];
        [tempKeys insertObject:objectToMove atIndex:atIndex];
    } else {
        [tempKeys insertObject:key atIndex:atIndex];
    }
    
    @synchronized (self) {
        _keys = tempKeys;
        [_dictionary setObject:object forKey:key];
    }
}

- (void)setObject:(id)object forKey:(id<NSCopying>)key {
    if (![_keys containsObject:key]) {
        [_keys addObject:key];
    }
    [_dictionary setObject:object forKey:key];
}

- (id)objectForKey:(id<NSCopying>)key {
    return [_dictionary objectForKey:key];
}

- (NSUInteger)count {
    return [_keys count];
}

- (NSArray *)allKeys {
    return [_keys copy];
}

- (NSArray *)allValues {
    NSMutableArray *values = [NSMutableArray array];
    for (id key in _keys) {
        id value = [_dictionary objectForKey:key];
        [values addObject:value];
    }
    return [values copy];
}

- (void)removeObjectForKey:(id<NSCopying>)key {
    [_keys removeObject:key];
    [_dictionary removeObjectForKey:key];
}

- (void)moveObjectAtIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex {
    if (fromIndex < [_keys count] && toIndex < [_keys count]) {
        id key = [_keys objectAtIndex:fromIndex];
        [_keys removeObjectAtIndex:fromIndex];
        [_keys insertObject:key atIndex:toIndex];
    }
}


- (void)exchangeObjectAtIndex:(NSUInteger)index1 withObjectAtIndex:(NSUInteger)index2 {
    if (index1 < [_keys count] && index2 < [_keys count]) {
        [_keys exchangeObjectAtIndex:index1 withObjectAtIndex:index2];
    }
}
@end
