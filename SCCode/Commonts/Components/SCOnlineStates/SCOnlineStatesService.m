//
//  SCOnlineStatesService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import "SCOnlineStatesService.h"
#import "SCAPIServiceManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCOnlineStatesService()

///订阅列表
@property(nonatomic,strong) NSMutableArray<SCOnlineStatusSubscribe *> * subscribes;
///倒计时
@property(nonatomic,strong) NSTimer * timer;
@end

@implementation SCOnlineStatesService

- (instancetype)initWithUserId:(NSString *)userId{
    self = [super initWithUserId:userId];
    if(self){
        _subscribes = [NSMutableArray array];
        _userStatusDic = [[SCOrderedDictionary<NSString *,NSDictionary *> alloc] init];
        [self _startTimer];
        
    }
    return self;
}

- (void)add:(SCOnlineStatusSubscribe *)subscribe dispose:(SCDisposeBag *) dispose{
    [_subscribes addObject:subscribe];
    kWeakSelf(self);
    [dispose insert:^{
        [weakself.subscribes removeObject:subscribe];
    }];

}

- (void)refresh{
    [self _doRequestWithForce:true];
    [self _startTimer];
}

//开始倒计时
- (void)_startTimer{
    [self _stopTimer];
    _timer = [NSTimer scheduledTimerWithTimeInterval:2 target:self selector:@selector(_doRequest) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_timer forMode:NSRunLoopCommonModes];
}
-(void) _doRequest{
    [self _doRequestWithForce:false];
}
-(void) _doRequestWithForce:(BOOL) isForce{
    NSMutableArray *userIds = [NSMutableArray new];
    NSTimeInterval nowTime = [NSDate new].timeIntervalSince1970;
    SCOnlineStatusSubscribe * currentSubscribe = nil;
    for (SCOnlineStatusSubscribe * subscribe in _subscribes) {
        if(!subscribe.isPause){
            currentSubscribe = subscribe;
            //只会拿最新的并且 没有暂停的
            
            for (NSString * key in subscribe.userIDs) {
                if(isForce){
                    [userIds addObject:key];
                }else{
                    NSDictionary * subDict = [_userStatusDic objectForKey:key];
                    if(subDict == nil){
                        [userIds addObject:key];
                    }else{
                        NSTimeInterval updateTime = [SCDictionaryHelper doubleFromDictionary:subDict forKey:@"updateTime" defaultValue:0];
                        if(nowTime - updateTime >= 7){
                            NSString *userId = [SCDictionaryHelper stringFromDictionary:subDict forKey:@"userId" defaultValue:key];
                            [userIds addObject:userId];
                        }
                    }
                }
            }
        }
    }
    if([userIds count] == 0){
        return;
    }
    kWeakSelf(self)
    [SCAPIServiceManager requestUserStatusWithUserIds:userIds success:^(NSDictionary<NSString *,NSString *> * _Nonnull status) {
        [weakself sc_black_empty];
        if(currentSubscribe != nil){
            for (NSString * key in [status allKeys]) {
                SCAnchorStatus scStatus = [SCDictionaryHelper anchorStatusFromString:[status objectForKey:key]];
                NSDictionary * subDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:key];

                // 创建新的状态字典
                NSMutableDictionary *newStatusDict = [NSMutableDictionary dictionary];
                newStatusDict[@"userId"] = key;
                newStatusDict[@"status"] = @(scStatus);
                newStatusDict[@"updateTime"] = @([NSDate new].timeIntervalSince1970);

                [kSCAuthOnlineStatesService.userStatusDic setObject:[newStatusDict copy] forKey:key atIndex:0];
                //判断如果长度超过50则移除最后一个
                if(kSCAuthOnlineStatesService.userStatusDic.count > 50){
                    [kSCAuthOnlineStatesService.userStatusDic removeObjectForKey:[[kSCAuthOnlineStatesService.userStatusDic allKeys] lastObject]];
                }

            }
            //通知发生变化
            currentSubscribe.changeObs.value = [status allKeys];
        }
    } failure:nil];
    
    
}

- (void)sc_black_empty{
    
}

-(void)_stopTimer{
    if(_timer){
        [_timer invalidate];
        _timer = nil;
    }
}

- (void)destroyService{
    [self _stopTimer];
    [_subscribes removeAllObjects];
    _subscribes = nil;
}

- (void)dealloc{
    [self destroyService];
}

@end
