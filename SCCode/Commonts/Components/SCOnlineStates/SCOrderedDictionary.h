//
//  SCOrderedDictionary.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCOrderedDictionary<__covariant KeyType, __covariant ObjectType> : NSObject

- (void)setObject:(ObjectType)object forKey:(KeyType)key atIndex:(NSUInteger) atIndex;
- (void)setObject:(ObjectType)object forKey:(KeyType)key;
- (nullable ObjectType)objectForKey:(KeyType)key;
- (NSUInteger)count;
- (NSArray *)allKeys;
- (NSArray *)allValues;
- (void)removeObjectForKey:(KeyType)key;
- (void)moveObjectAtIndex:(NSUInteger)fromIndex toIndex:(NSUInteger)toIndex;
- (void)exchangeObjectAtIndex:(NSUInteger)index1 withObjectAtIndex:(NSUInteger)index2;

@end

NS_ASSUME_NONNULL_END
