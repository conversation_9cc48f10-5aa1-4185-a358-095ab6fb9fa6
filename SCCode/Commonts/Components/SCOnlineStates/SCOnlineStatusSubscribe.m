//
//  SCOnlineStatusSubscribe.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/5.
//

#import "SCOnlineStatusSubscribe.h"
@interface SCOnlineStatusSubscribe()
@end
@implementation SCOnlineStatusSubscribe

- (instancetype)init
{
    self = [super init];
    if (self) {
        _changeObs = [[SCObservable<NSArray<NSString *>*> alloc] initWithValue:@[]];
        _userIDs = [NSMutableArray new];
    }
    return self;
}

- (NSUInteger)maxNum{
    if(_maxNum == 0){
        _maxNum = 20;
    }
    return _maxNum;
}


- (void)addWithUserID:(nonnull NSString *)userId {
    if(userId == nil){
        return;
    }
    [_userIDs insertObject:userId atIndex:0];
    //如果超出则移除多余的
    if(_userIDs.count > self.maxNum){
        [_userIDs removeLastObject];
    }
//    SCOnlineStatusSubscribeModel * mode = [self.userStatusDic objectForKey:userId];
//    if(mode == nil){
//        mode = [[SCOnlineStatusSubscribeModel alloc] initWithUserId:userId];
//    }
//    //优先插入到最前面
//    [self.userStatusDic setObject:mode forKey:userId atIndex:0];
}

- (void)deleteWithUserID:(nonnull NSString *)userId {
    if(userId == nil){
        return;
    }
    [_userIDs removeObject:userId];
}
//
//- (void)updateWithUserID:(NSString *)userId status:(SCAnchorStatus)status{
//    if(userId == nil){
//        return;
//    }
////    SCOnlineStatusSubscribeModel * mode = [self.userStatusDic objectForKey:userId];
////    if(mode != nil){
////        mode.status = status;
////        mode.updateTime = [NSDate new].timeIntervalSince1970;
////        [self.userStatusDic setObject:mode forKey:userId];
////    }
//}

- (void)pause {
    _isPause = YES;
}
-(void) start{
    _isPause = NO;
}

@end



@implementation SCOnlineStatusSubscribeModel

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super init];
    if (self) {
        _userId = userId;
    }
    return self;
}

@end
