//
//  SCLocalVideoPlayer.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/11/19.
//

#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCLocalVideoPlayer : UIView

/// 初始化播放器
/// @param frame 播放器frame
/// @param videoName 视频文件名（不包含扩展名）
/// @param videoType 视频文件扩展名（例如：@"mp4"）
- (instancetype)initWithFrame:(CGRect)frame videoName:(NSString *)videoName type:(NSString *)videoType;

/// 开始播放
- (void)play;

/// 暂停播放
- (void)pause;

/// 设置视频填充模式
/// @param videoGravity 填充模式
- (void)setVideoGravity:(AVLayerVideoGravity)videoGravity;

/// 设置是否静音
@property (nonatomic, assign) BOOL muted;

/// 设置播放速率
@property (nonatomic, assign) float rate;

@end

NS_ASSUME_NONNULL_END
