//
//  SCLocalVideoPlayer.m
//  Supercall
//
//  Created by sumengliu on 2024/11/19.
//

#import "SCLocalVideoPlayer.h"

@interface SCLocalVideoPlayer ()

@property (nonatomic, strong) AVQueuePlayer *player;
@property (nonatomic, strong) AVPlayerLayer *playerLayer;
@property (nonatomic, strong) AVPlayerItem *playerItem;
@property (nonatomic, strong) AVPlayerLooper *looper;
@property (nonatomic, strong) id timeObserver;
@property (nonatomic, strong) NSTimer *playbackCheckTimer;
@property (nonatomic, assign) NSInteger retryCount;
@property (nonatomic, assign) BOOL shouldBePlaying;
@property (nonatomic, assign) void *playerItemContext;

@end

@implementation SCLocalVideoPlayer

- (instancetype)initWithFrame:(CGRect)frame videoName:(NSString *)videoName type:(NSString *)videoType {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupPlayerWithVideoName:videoName type:videoType];
        self.player.muted = YES;
    }
    return self;
}

- (void)setupPlayerWithVideoName:(NSString *)videoName type:(NSString *)videoType {
    [self cleanupPlayer];
    
    NSString *videoPath = [SCResourceManager.assterPath stringByAppendingString: [NSString stringWithFormat:@"/video/%@.%@", videoName, videoType]];
    NSURL *videoURL = [NSURL URLWithString:[NSString stringWithFormat:@"file://%@", videoPath]];
    
    // 创建 AVPlayerItem
    self.playerItem = [AVPlayerItem playerItemWithURL:videoURL];
    
    // 监听播放状态
    [self.playerItem addObserver:self
                     forKeyPath:@"status"
                        options:NSKeyValueObservingOptionNew
                        context:&_playerItemContext];
    
    // 创建 AVQueuePlayer
    self.player = [AVQueuePlayer queuePlayerWithItems:@[self.playerItem]];
    
    // 创建 AVPlayerLayer
    self.playerLayer = [AVPlayerLayer playerLayerWithPlayer:self.player];
    self.playerLayer.frame = self.bounds;
    self.playerLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    [self.layer addSublayer:self.playerLayer];
    
    // 创建并保存 looper
    self.looper = [AVPlayerLooper playerLooperWithPlayer:self.player 
                                          templateItem:self.playerItem];
    
    // 添加通知观察者
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(playerItemDidPlayToEndTime:)
                                               name:AVPlayerItemDidPlayToEndTimeNotification
                                             object:self.playerItem];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationWillEnterForeground)
                                               name:UIApplicationWillEnterForegroundNotification
                                             object:nil];
    
    // 添加播放进度监听
    __weak typeof(self) weakSelf = self;
    self.timeObserver = [self.player addPeriodicTimeObserverForInterval:CMTimeMake(1, 2)
                                                                 queue:dispatch_get_main_queue()
                                                            usingBlock:^(CMTime time) {
        [weakSelf checkPlaybackStatus];
    }];
}

- (void)cleanupPlayer {
    [self stopPlaybackCheckTimer];
    
    if (self.timeObserver) {
        [self.player removeTimeObserver:self.timeObserver];
        self.timeObserver = nil;
    }
    
    if (self.playerItem) {
        [self.playerItem removeObserver:self forKeyPath:@"status" context:&_playerItemContext];
        [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:self.playerItem];
    }
    
    [self.player pause];
    [self.playerLayer removeFromSuperlayer];
    
    self.player = nil;
    self.playerItem = nil;
    self.playerLayer = nil;
    self.looper = nil;
}

- (void)checkPlaybackStatus {
    if (self.shouldBePlaying && self.player.rate == 0.0) {
        [self attemptToResumePlaying];
    }
}

- (void)attemptToResumePlaying {
    if (self.retryCount < 3) {
        self.retryCount++;
        [self.player play];
        
        // 5秒后重置重试次数
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            self.retryCount = 0;
        });
    }
}

- (void)play {
    self.shouldBePlaying = YES;
    [self.player play];
    [self startPlaybackCheckTimer];
}

- (void)pause {
    self.shouldBePlaying = NO;
    [self.player pause];
    [self stopPlaybackCheckTimer];
}

- (void)startPlaybackCheckTimer {
    [self stopPlaybackCheckTimer];
    self.playbackCheckTimer = [NSTimer scheduledTimerWithTimeInterval:1.0
                                                             target:self
                                                           selector:@selector(checkPlaybackStatus)
                                                           userInfo:nil
                                                            repeats:YES];
}

- (void)stopPlaybackCheckTimer {
    [self.playbackCheckTimer invalidate];
    self.playbackCheckTimer = nil;
}

- (void)applicationWillEnterForeground {
    if (self.shouldBePlaying) {
        [self.player play];
    }
}

- (void)playerItemDidPlayToEndTime:(NSNotification *)notification {
    // 如果需要循环播放，可以在这里处理
    if (self.shouldBePlaying && !self.looper) {
        [self.player seekToTime:kCMTimeZero];
        [self.player play];
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.playerLayer.frame = self.bounds;
}

- (void)setVideoGravity:(AVLayerVideoGravity)videoGravity {
    self.playerLayer.videoGravity = videoGravity;
}

- (void)setMuted:(BOOL)muted {
    self.player.muted = muted;
}

- (BOOL)muted {
    return self.player.muted;
}

- (void)setRate:(float)rate {
    self.player.rate = rate;
}

- (float)rate {
    return self.player.rate;
}

- (void)dealloc {
    [self cleanupPlayer];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)removeFromSuperview {
    [self cleanupPlayer];
    [super removeFromSuperview];
}

- (void)observeValueForKeyPath:(NSString *)keyPath
                     ofObject:(id)object
                       change:(NSDictionary *)change
                      context:(void *)context {
    if (context == &_playerItemContext) {
        if ([keyPath isEqualToString:@"status"]) {
            AVPlayerItemStatus status = AVPlayerItemStatusUnknown;
            NSNumber *statusNumber = change[NSKeyValueChangeNewKey];
            if ([statusNumber isKindOfClass:[NSNumber class]]) {
                status = statusNumber.integerValue;
            }
            
            dispatch_async(dispatch_get_main_queue(), ^{
                switch (status) {
                    case AVPlayerItemStatusReadyToPlay:
                        if (self.shouldBePlaying) {
                            [self.player play];
                        }
                        break;
                        
                    case AVPlayerItemStatusFailed:
                        
                        break;
                        
                    case AVPlayerItemStatusUnknown:
                        break;
                }
            });
        }
    } else {
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

@end
