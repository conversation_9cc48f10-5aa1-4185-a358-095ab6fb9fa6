//
//  SCResourceManager.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/10/18.
//


#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/// 资源管理器
@interface SCResourceManager : NSObject

/// 根路径
+ (NSString *)rootPath;

/// 资源路径
+ (NSString *)assterPath;

/// 图片缓存
+ (NSCache<NSString *, UIImage *> *)imageCache;

/// 初始化资源
/// @param completion 完成回调
+ (void)initAssterWithCompletion:(void (^)(BOOL success))completion;

/// 注册字体
+ (void)registFont;

/// 加载图片（不自动强制镜像）
/// @param imageName 图片名称
+ (nullable UIImage *)loadImageWithName:(NSString *)imageName;

/// 加载图片
/// @param imageName 图片名称
/// @param isAutoForce 是否自动强制镜像
+ (nullable UIImage *)loadImageWithName:(NSString *)imageName isAutoForce:(BOOL)isAutoForce;

/// 加载语言JSON
+ (void)loadLanguageJson;

//获取svga的文件路径
+ (nullable NSString * )svgaPathWithName:(NSString * _Nullable )name;

//获取其他 json 文件路径
+ (nullable NSString *)jsonPathWithName: (NSString *_Nullable) name;

//获取其他 plist 文件路径
+ (nullable NSString *)plistPathWithName: (NSString *_Nullable) name;

@end

@interface NSString (SCStringTranslate)

- (NSString *)translateString;

@end

NS_ASSUME_NONNULL_END
