//
//  SCResourceManager.h
//  Supercall
//
//  Created by sumengliu on 2024/10/18.
//


#import "SCResourceManager.h"
#import <SSZipArchive/SSZipArchive.h>
#import <WebKit/WebKit.h>
#import "SCCryptoUtils.h"
#import "SCAppUtils.h"
#import "SCAuthManager.h"
#import "SCLanguageManager.h"
#import <CoreText/CTFontManager.h>

@implementation SCResourceManager

/// 根路径
+ (NSString *)rootPath {
    return NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;
}

/// 资源路径
+ (NSString *)assterPath {
    return [self.rootPath stringByAppendingPathComponent:kSCAssetNameKey];
}

/// 图片缓存
+ (NSCache<NSString *, UIImage *> *)imageCache {
    static NSCache<NSString *, UIImage *> *cache;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        cache = [[NSCache alloc] init];
        cache.totalCostLimit = 50; // 最大50M
    });
    return cache;
}

/// 初始化资源
+ (void)initAssterWithCompletion:(void (^)(BOOL success))completion {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    
    NSString *fileMD5 = @"";
    NSURL *zipPath = [[NSBundle mainBundle] URLForResource:kSCAssetNameKey withExtension:@"zip"];
    if (zipPath) {
        fileMD5 = [SCCryptoUtils fileMD5:zipPath] ?: @"";
    }
    
    NSString *lastVersion = [userDefaults stringForKey:kSCUnZipResourceVersionKey];
    
    // 判断版本号不对应或者文件路径不存在都会重新解压
    if (![lastVersion isEqualToString:fileMD5] || ![fileManager fileExistsAtPath:self.assterPath]) {
        if (![fileManager fileExistsAtPath:self.assterPath]) {
            [fileManager createDirectoryAtPath:self.assterPath withIntermediateDirectories:YES attributes:nil error:nil];
        }
        
        NSError *error;
        [fileManager removeItemAtPath:self.assterPath error:&error];
        if (error) {
            
            completion(NO);
            return;
        }
        
        [self readZipWithName:kSCAssetNameKey type:@"zip" overwrite:YES password:kSCAssterPwd toPath:self.rootPath completion:^(BOOL success) {
            if (success) {
                
                [userDefaults setValue:fileMD5 forKey:kSCUnZipResourceVersionKey];
                [self loadLanguageJson];
                [self registFont];
            } else {
                
            }
            completion(success);
        } progress:^(double progress) {
            
        }];
    } else {
        [self loadLanguageJson];
        [self registFont];
        completion(YES);
    }
}

/// 注册字体
+ (void)registFont {
    NSString *fontFolderPath = [self.assterPath stringByAppendingPathComponent:@"font_family"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    
    NSError *error;
    NSArray<NSURL *> *fontFileURLs = [fileManager contentsOfDirectoryAtURL:[NSURL fileURLWithPath:fontFolderPath]
                                            includingPropertiesForKeys:@[NSURLNameKey]
                                                               options:NSDirectoryEnumerationSkipsHiddenFiles
                                                                 error:&error];
    
    if (error) {
        
        return;
    }
    
    for (NSURL *fontFileURL in fontFileURLs) {
        NSString *fontFileName = [fontFileURL lastPathComponent];
        
        if ([fontFileName hasSuffix:@".ttf"]) {
            CFErrorRef fontError = NULL;
            CTFontManagerRegisterFontsForURL((__bridge CFURLRef)fontFileURL, kCTFontManagerScopeProcess, &fontError);
            if (fontError) {
                
                CFRelease(fontError);
            }
        }
    }
}
//获取svga的文件路径
+ (NSString * )svgaPathWithName:(NSString *)name {
    return [self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"svga/%@", name]];
}

+ (nullable NSString *)jsonPathWithName: (NSString *_Nullable) name {
    return [self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"json/%@", name]];
}

+ (nullable NSString *)plistPathWithName: (NSString *_Nullable) name {
    return [self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"plist/%@", name]];
}

///
+ (UIImage *)loadImageWithName:(NSString *)imageName {
    return [SCResourceManager loadImageWithName:imageName isAutoForce:NO];
}

/// 加载图片
+ (nullable UIImage *)loadImageWithName:(NSString *)imageName isAutoForce:(BOOL)isAutoForce {
    NSString *newImageName = imageName;
    NSString *suffix = @"png";
    
    // 判断是否已经有其他后缀名
    if ([newImageName containsString:@"."]) {
        suffix = [newImageName componentsSeparatedByString:@"."].lastObject;
    }
    
    BOOL ismirrored = isAutoForce && kScAuthMar.isLanguageForce;
    
    // 判断当前设备是否使用@3x
    if ([UIScreen mainScreen].scale >= 3.0) {
        newImageName = [NSString stringWithFormat:@"%@@3x.%@", imageName, suffix];
        if (![self fileExistsAtPath:newImageName]) {
            newImageName = [NSString stringWithFormat:@"%@@2x.%@", imageName, suffix];
        }
        if (![self fileExistsAtPath:newImageName]) {
            newImageName = [NSString stringWithFormat:@"%@.%@", imageName, suffix];
        }
    } else { // 默认使用2倍图
        newImageName = [NSString stringWithFormat:@"%@@2x.%@", imageName, suffix];
        if (![self fileExistsAtPath:newImageName]) {
            newImageName = [NSString stringWithFormat:@"%@@3x.%@", imageName, suffix];
        }
        if (![self fileExistsAtPath:newImageName]) {
            newImageName = [NSString stringWithFormat:@"%@.%@", imageName, suffix];
        }
    }
    
    // 判断缓存
    UIImage *image = [self.imageCache objectForKey:newImageName];
    if (image) {
        return ismirrored ? [UIImage imageWithCGImage:image.CGImage scale:image.scale orientation:UIImageOrientationUpMirrored] : image;
    }
    
    NSString *imagePath = [self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"images/%@", newImageName]];
    image = [UIImage imageWithContentsOfFile:imagePath];
    
    if (image) {
        // 设置缓存和成本
        NSUInteger cost = (NSUInteger)(image.size.width * image.size.height * image.scale * image.scale) / (1024 * 1024);
        if (cost <= 2) {
            [self.imageCache setObject:image forKey:newImageName cost:cost];
        }
        return ismirrored ? [UIImage imageWithCGImage:image.CGImage scale:image.scale orientation:UIImageOrientationUpMirrored] : image;
    }
    
    return nil;
}

/// 加载语言JSON
+ (void)loadLanguageJson {
    NSString *language = [SCLanguageManager getDeviceLanguage];
    if (!language.length) {
        language = @"en";
    }
    
    NSArray<NSString *> *supportLanguages = @[@"en", @"ar", @"es", @"tr", @"ko", @"de", @"ja", @"it", @"hi", @"zh-tw", @"th", @"fr", @"pt"];
    if (![supportLanguages containsObject:language]) {
        language = @"en";
    }
    
    NSString *oldLanguage = [[NSUserDefaults standardUserDefaults] stringForKey:kSCOldTranslateLanguageKey];
    if (![oldLanguage isEqualToString:language]) {
        [[NSUserDefaults standardUserDefaults] setObject:language forKey:kSCOldTranslateLanguageKey];
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:kSCTranslateMessageKey];
    }
    //映射具体翻译json名称，方便脚本修改
    NSArray<NSString *> *supportMapLanguages = @[@"supercall_en", @"supercall_ar", @"supercall_es", @"supercall_tr", @"supercall_ko", @"supercall_de", @"supercall_ja", @"supercall_it", @"supercall_hi", @"supercall_zh-tw", @"supercall_th", @"supercall_fr", @"supercall_pt"];
    NSString *fileLanguage = supportMapLanguages[[supportLanguages indexOfObject:language]];
    NSString *jsonPath = [self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"translate/%@.json", fileLanguage]];
    NSData *jsonData = [NSData dataWithContentsOfFile:jsonPath];
    
    if (jsonData) {
        NSError *error;
        NSDictionary<NSString *, NSString *> *jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
        if (!error && [jsonObject isKindOfClass:[NSDictionary class]]) {
            kScAuthMar.languageMap = jsonObject;
        }
    }
    
    // 设置语言方向
    if ([@[@"ar", @"he", @"fa", @"ur"] containsObject:language]) {
        [SCLanguageManager setLanguage:@"ar_SA"];
        kScAuthMar.isLanguageForce = YES;
        [self setRTLAppearance];
    } else {
        [SCLanguageManager systemLanguage];
        kScAuthMar.isLanguageForce = NO;
        [self setLTRAppearance];
    }
}

/// 设置从右到左的外观
+ (void)setRTLAppearance {
    [self setSemanticContentAttribute:UISemanticContentAttributeForceRightToLeft];
    [UITextField appearance].textAlignment = NSTextAlignmentRight;
    [UILabel appearance].textAlignment = NSTextAlignmentRight;
    [UITextView appearance].textAlignment = NSTextAlignmentRight;
}

/// 设置从左到右的外观
+ (void)setLTRAppearance {
    [self setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
    [UITextField appearance].textAlignment = NSTextAlignmentLeft;
    [UILabel appearance].textAlignment = NSTextAlignmentLeft;
    [UITextView appearance].textAlignment = NSTextAlignmentLeft;
}

/// 设置语义内容属性
+ (void)setSemanticContentAttribute:(UISemanticContentAttribute)attribute {
    kSCKeyWindow.semanticContentAttribute = attribute;
//    if (SCAppIntegrationManager.appWindowCallBack) {
//        SCAppIntegrationManager.appWindowCallBack().semanticContentAttribute = attribute;
//    }
    [UIView appearance].semanticContentAttribute = attribute;
    [UISearchBar appearance].semanticContentAttribute = attribute;
    [UIButton appearance].semanticContentAttribute = attribute;
    [WKWebView appearance].semanticContentAttribute = attribute;
    [UICollectionView appearance].semanticContentAttribute = attribute;
    [UIImageView appearance].semanticContentAttribute = attribute;
    [UIPageControl appearance].semanticContentAttribute = attribute;
    [UISwitch appearance].semanticContentAttribute = attribute;
}

/// 检查文件是否存在
+ (BOOL)fileExistsAtPath:(NSString *)path {
    return [[NSFileManager defaultManager] fileExistsAtPath:[self.assterPath stringByAppendingPathComponent:[NSString stringWithFormat:@"images/%@", path]]];
}

/// 读取并解压ZIP文件
+ (void)readZipWithName:(NSString *)name
                   type:(NSString *)type
              overwrite:(BOOL)overwrite
               password:(NSString *)password
                 toPath:(NSString *)toPath
             completion:(void (^)(BOOL success))completion
               progress:(void (^)(double progress))progress {
    
    NSString *cleanPassword = [password stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    NSString *zipPath = [[NSBundle mainBundle] pathForResource:name ofType:type];
    
    if (!zipPath) {
        completion(NO);
        return;
    }
    
    [SSZipArchive unzipFileAtPath:zipPath
                    toDestination:toPath
                        overwrite:overwrite
                         password:cleanPassword
                  progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {
        if (progress) {
            progress((double)entryNumber / (double)total);
        }
    } completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
        completion(succeeded);
    }];
}

@end

@implementation NSString (SCStringTranslate)

/// 翻译字符串
- (NSString *)translateString {
    NSString *translated = kScAuthMar.languageMap[self];
    if (translated) {
        return [self htmlToString:translated];
    }
    return self;
}

- (NSString *)htmlToString:(NSString *)htmlString {
    // 如果不包含HTML标签，直接返回
    if (![htmlString containsString:@"<"] && ![htmlString containsString:@"&"]) {
        return htmlString;
    }

    NSData *data = [htmlString dataUsingEncoding:NSUTF8StringEncoding allowLossyConversion:YES];
    if (data) {
        NSError *error = nil;
        NSAttributedString *attributedString = [[NSAttributedString alloc] initWithData:data
                                                                                options:@{NSDocumentTypeDocumentAttribute: NSHTMLTextDocumentType,
                                                                                          NSCharacterEncodingDocumentAttribute: @(NSUTF8StringEncoding)}
                                                                     documentAttributes:nil
                                                                                  error:&error];
        if (attributedString) {
            return attributedString.string;
        } else if (error) {
            
        }
    }
    return htmlString;
}

@end
