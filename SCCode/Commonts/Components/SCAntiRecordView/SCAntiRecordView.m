//
//  SCAntiRecordView.m
//  Supercall
//
//  Created by AI Assistant on 2025/07/08.
//  Copyright © 2025 Supercall. All rights reserved.
//

#import "SCAntiRecordView.h"

API_AVAILABLE(ios(13.0))
@interface SCAntiRecordView ()

/// 安全容器视图，用于防止屏幕录制
@property (nonatomic, strong, nullable) UIView *protectedContainer;

@end

@implementation SCAntiRecordView

#pragma mark - 工厂方法

+ (instancetype)createProtectedViewWithFrame:(CGRect)frame {
    return [[self alloc] initWithFramePrivate:frame];
}

+ (instancetype)createProtectedView {
    return [self createProtectedViewWithFrame:CGRectZero];
}

#pragma mark - 初始化方法

- (instancetype)initWithFramePrivate:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupProtectedContainer];
    }
    return self;
}

#pragma mark - 私有方法

- (void)setupProtectedContainer {
    self.protectedContainer = [self createSecureContainer];
    
    if (self.protectedContainer) {
        [self addSubview:self.protectedContainer];
        [self setupConstraintsForProtectedContainer];
    }
}

- (void)setupConstraintsForProtectedContainer {
    UIView *container = self.protectedContainer;
    if (!container) return;
    
    // 设置优先级
    UILayoutPriority lowPriorityLevel = UILayoutPriorityDefaultLow - 1;
    UILayoutPriority highPriorityLevel = UILayoutPriorityDefaultHigh - 1;
    
    container.translatesAutoresizingMaskIntoConstraints = NO;
    [container setContentHuggingPriority:lowPriorityLevel forAxis:UILayoutConstraintAxisVertical];
    [container setContentHuggingPriority:lowPriorityLevel forAxis:UILayoutConstraintAxisHorizontal];
    [container setContentCompressionResistancePriority:highPriorityLevel forAxis:UILayoutConstraintAxisVertical];
    [container setContentCompressionResistancePriority:highPriorityLevel forAxis:UILayoutConstraintAxisHorizontal];
    
    // 创建约束
    NSLayoutConstraint *topConstraint = [NSLayoutConstraint constraintWithItem:container
                                                                     attribute:NSLayoutAttributeTop
                                                                     relatedBy:NSLayoutRelationEqual
                                                                        toItem:self
                                                                     attribute:NSLayoutAttributeTop
                                                                    multiplier:1.0
                                                                      constant:0];
    
    NSLayoutConstraint *bottomConstraint = [NSLayoutConstraint constraintWithItem:container
                                                                        attribute:NSLayoutAttributeBottom
                                                                        relatedBy:NSLayoutRelationEqual
                                                                           toItem:self
                                                                        attribute:NSLayoutAttributeBottom
                                                                       multiplier:1.0
                                                                         constant:0];
    
    NSLayoutConstraint *leadingConstraint = [NSLayoutConstraint constraintWithItem:container
                                                                         attribute:NSLayoutAttributeLeading
                                                                         relatedBy:NSLayoutRelationEqual
                                                                            toItem:self
                                                                         attribute:NSLayoutAttributeLeading
                                                                        multiplier:1.0
                                                                          constant:0];
    
    NSLayoutConstraint *trailingConstraint = [NSLayoutConstraint constraintWithItem:container
                                                                          attribute:NSLayoutAttributeTrailing
                                                                          relatedBy:NSLayoutRelationEqual
                                                                             toItem:self
                                                                          attribute:NSLayoutAttributeTrailing
                                                                         multiplier:1.0
                                                                           constant:0];
    
    [self addConstraints:@[topConstraint, bottomConstraint, leadingConstraint, trailingConstraint]];
}

- (nullable UIView *)createSecureContainer {
    if (![self checkSystemCompatibility]) {
        return nil;
    }
    
    UITextField *secureField = [[UITextField alloc] init];
    secureField.secureTextEntry = YES;
    
    UIView *containerView = secureField.subviews.firstObject;
    
    // 清理容器内的所有子视图
    for (UIView *subview in containerView.subviews) {
        [subview removeFromSuperview];
    }
    
    containerView.userInteractionEnabled = YES;
    
    NSString *errorMessage = @"[SCAntiRecordView log] Create protected container failed!";
    
#if DEBUG
    NSAssert(containerView != nil, @"%@", errorMessage);
#else
    if (!containerView) {
        
    }
#endif
    
    return containerView;
}

#pragma mark - 系统兼容性检查

- (NSArray<NSString *> *)incompatibleSystemVersions {
    return @[@"15.1"];
}

- (NSString *)currentSystemVersion {
    return [UIDevice currentDevice].systemVersion;
}

- (BOOL)checkSystemCompatibility {
    NSString *currentVersion = [self currentSystemVersion];
    NSArray<NSString *> *incompatibleVersions = [self incompatibleSystemVersions];

    for (NSString *version in incompatibleVersions) {
        if ([currentVersion containsString:version]) {
            return NO;
        }
    }
    return YES;
}

#pragma mark - 子视图管理重写

- (void)addSubview:(UIView *)view {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer addSubview:view];
    } else {
        [super addSubview:view];
    }
}

- (void)insertSubview:(UIView *)view belowSubview:(UIView *)siblingSubview {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer insertSubview:view belowSubview:siblingSubview];
    } else {
        [super insertSubview:view belowSubview:siblingSubview];
    }
}

- (void)insertSubview:(UIView *)view aboveSubview:(UIView *)siblingSubview {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer insertSubview:view aboveSubview:siblingSubview];
    } else {
        [super insertSubview:view aboveSubview:siblingSubview];
    }
}

- (void)insertSubview:(UIView *)view atIndex:(NSInteger)index {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer insertSubview:view atIndex:index];
    } else {
        [super insertSubview:view atIndex:index];
    }
}

- (void)exchangeSubviewAtIndex:(NSInteger)index1 withSubviewAtIndex:(NSInteger)index2 {
    if (self.protectedContainer) {
        [self.protectedContainer exchangeSubviewAtIndex:index1 withSubviewAtIndex:index2];
    } else {
        [super exchangeSubviewAtIndex:index1 withSubviewAtIndex:index2];
    }
}

- (void)bringSubviewToFront:(UIView *)view {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer bringSubviewToFront:view];
    } else {
        [super bringSubviewToFront:view];
    }
}

- (void)sendSubviewToBack:(UIView *)view {
    if (self.protectedContainer && view != self.protectedContainer) {
        [self.protectedContainer sendSubviewToBack:view];
    } else {
        [super sendSubviewToBack:view];
    }
}

@end
