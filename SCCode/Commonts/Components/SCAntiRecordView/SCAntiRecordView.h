//
//  SCAntiRecordView.h
//  Supercall
//
//  Created by AI Assistant on 2025/07/08.
//  Copyright © 2025 Supercall. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * SCAntiRecordView - Objective-C版本的屏幕录制防护视图
 * 
 * 这个类提供了防止屏幕录制和截图的功能，通过利用UITextField的安全文本输入特性
 * 来创建一个安全区域，在该区域内的内容不会被系统录制或截图。
 *
 * 使用方法：
 * 1. 创建 SCAntiRecordView 实例
 * 2. 将需要保护的视图添加到 SCAntiRecordView 中
 * 3. 将 SCAntiRecordView 添加到父视图中
 *
 * 注意：
 * - 仅在 iOS 13.0 及以上版本可用
 * - 某些 iOS 版本可能存在兼容性问题，会自动降级处理
 */
API_AVAILABLE(ios(13.0))
@interface SCAntiRecordView : UIView

/**
 * 创建 SCAntiRecordView 实例的工厂方法
 * @param frame 视图的初始frame，默认为CGRectZero
 * @return SCAntiRecordView 实例
 */
+ (instancetype)createProtectedViewWithFrame:(CGRect)frame;

/**
 * 创建 SCAntiRecordView 实例的便利方法
 * @return 使用 CGRectZero 作为初始frame的 SCAntiRecordView 实例
 */
+ (instancetype)createProtectedView;

/**
 * 禁用默认的初始化方法，强制使用工厂方法
 */
- (instancetype)init NS_UNAVAILABLE;

/**
 * 禁用frame初始化方法，强制使用工厂方法
 */
- (instancetype)initWithFrame:(CGRect)frame NS_UNAVAILABLE;

/**
 * 禁用coder初始化方法
 */
- (instancetype)initWithCoder:(NSCoder *)coder NS_UNAVAILABLE;

@end

NS_ASSUME_NONNULL_END
