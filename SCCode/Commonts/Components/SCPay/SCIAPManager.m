//
//  SCIAPManager.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCIAPManager.h"
#import "SCXErrorModel.h"
#import "SCCategoryAPIManagerPay.h"
#import "SCCoinsService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCIAPManager()
//全部商品信息
@property(nonatomic,strong,readonly) NSMutableDictionary<NSString *,SKProduct *> * appStoreProductsDic;

//查询商品回调
@property(nonatomic,copy) void(^searchProductsBlock)(NSDictionary<NSString *,SKProduct *> * productDic);
//支付回调
@property(nonatomic,copy) void(^ purchaseBlock)(NSDictionary * orderDict,SCXErrorModel *error);
//等待处理的订单
@property (nonatomic, strong) NSDictionary *waitHandleOrderDict;
@end

@implementation SCIAPManager


- (instancetype)init {
    self = [super init];
    if (self) {
        _appStoreProductsDic = [NSMutableDictionary new];
        [[SKPaymentQueue defaultQueue] addTransactionObserver:self];
    }
    return self;
}

- (void)dealloc {
    [self removeIAPObserver];
}



- (void)removeIAPObserver {
    [[SKPaymentQueue defaultQueue] removeTransactionObserver:self];
}

-(void)refreshProducts:(NSSet<NSString *> *)productIdentifiers {
    ///清空缓存重新去请求
    [self.appStoreProductsDic removeAllObjects];
    [self requestProducts:productIdentifiers callBack:nil];
}


- (void)requestProductId:(NSString *)productIdentifier callBack:(void(^)(SKProduct * _Nullable product)) searchProductsBlock{
    SKProduct * product = [self.appStoreProductsDic objectForKey:productIdentifier];
    if(product != nil){
        kSCBlockExeNotNil(searchProductsBlock,product);
        return;
    }
    kWeakSelf(self)
    NSSet<NSString *> *ids = [[NSSet alloc] initWithObjects:productIdentifier, nil];
    [self requestProducts:ids callBack:^(NSDictionary<NSString *,SKProduct *> * _Nonnull productDic) {
        SKProduct * product = [productDic objectForKey:productIdentifier];
        kSCBlockExeNotNil(searchProductsBlock,product);
        [weakself sc_blank_empty];
    }];

    
}
- (void)requestProducts:(NSSet<NSString *> * _Nullable)productIdentifiers callBack:(void(^_Nullable)(NSDictionary<NSString *,SKProduct *> * _Nonnull productDic)) searchProductsBlock{
    self.searchProductsBlock = searchProductsBlock;
    SKProductsRequest *productRequest = [[SKProductsRequest alloc] initWithProductIdentifiers:productIdentifiers];
    productRequest.delegate = self;
    [productRequest start];
}

- (void)purchaseProduct:(SKProduct *)product source:(NSString *) source entry:(NSString *)entry purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock{
    if ([SKPaymentQueue canMakePayments]) {
        kWeakSelf(self);
        [SCAPIServiceManager requestCreatOrderWithCode:product.productIdentifier source:source entry:entry payChannel:@"IAP" success:^(NSDictionary * _Nonnull orderDict) {
            weakself.purchaseBlock = purchaseBlock;
            weakself.waitHandleOrderDict = orderDict;
            SKPayment *payment = [SKPayment paymentWithProduct:product];
            [[SKPaymentQueue defaultQueue] addPayment:payment];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            kSCBlockExeNotNil(purchaseBlock,nil, [[SCXErrorModel alloc] initWitMsg:@"Pay Fail".translateString]);
        }];
        
    } else {    
        
        kSCBlockExeNotNil(purchaseBlock,nil, [[SCXErrorModel alloc] initWitMsg:@"Pay Fail".translateString]);
    }
}

- (void)sc_blank_empty{
    
}

-(void) handleSuccessDict:(NSDictionary *)orderDict{

#warning 补充充值成功的逻辑
    //记录购买的code.用于本地拦截已充值隐藏 对应的促销
    //埋点
    //刷新用户数据 （会刷新金币和充值状态）
    [kScAuthMar remoteLoginUserInfo:nil failure:nil];
    [kScAuthMar.coinsService remotePromotionWithSuccess:nil failure:nil];
    [kScAuthMar.coinsService remoteCoinsListWithSuccess:nil failure:nil];
    [kScAuthMar.coinsService remoteActivityPromotionWithSuccess:nil failure:nil];

    kSCBlockExeNotNil(self.purchaseBlock,orderDict,nil);
    self.purchaseBlock = nil;
    self.waitHandleOrderDict = nil;
}



-(void) handleFail:(SCXErrorModel *)error{
    kSCBlockExeNotNil(self.purchaseBlock,nil,error);
}

#pragma mark - SKPaymentTransactionObserver

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray<SKPaymentTransaction *> *)transactions {
    kWeakSelf(self)
    // 监听购买结果
    for (SKPaymentTransaction *transaction in transactions) {
        switch (transaction.transactionState) {
            case SKPaymentTransactionStatePurchased:
            {
                // 用户已成功购买商品，处理购买逻辑，例如解锁功能或提供订阅服务
                
                void (^paymentCallBack)(NSDictionary *) = ^(NSDictionary *orderDict) {
                    // 和等待中的订单吻合则通知服务器处理
                    NSURL *appStoreReceiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
                    NSData *receiptData = [NSData dataWithContentsOfURL:appStoreReceiptURL];
                    if (receiptData) {
                        NSString *payload = [receiptData base64EncodedStringWithOptions:0];
                        NSString *orderNo = kSCOrderNoFromDict(orderDict);
                        [SCAPIServiceManager requestPaymentIpaWithOrderNo:orderNo payload:payload transactionId:transaction.transactionIdentifier success:^{

                            [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
                            [weakself handleSuccessDict:orderDict];

                        } failure:^(SCXErrorModel * _Nonnull error) {
                            [weakself handleFail:error];
                        }];
                    }
                };
                
                if(self.waitHandleOrderDict != nil && [kSCOrderGoodsCodeFromDict(self.waitHandleOrderDict) isEqualToString:transaction.payment.productIdentifier]){
                    paymentCallBack(self.waitHandleOrderDict);
                }else{
                    //丢失订单，重新创建订单

                    [SCAPIServiceManager requestCreatOrderWithCode:transaction.payment.productIdentifier source:nil entry:nil payChannel:@"IAP" success:^(NSDictionary * _Nonnull orderDict) {
                        [weakself sc_blank_empty];
                        paymentCallBack(orderDict);
                    } failure:^(SCXErrorModel * _Nonnull error) {

                        [weakself handleFail:error];
                    }];
                }
                
                break;
            }
                
            case SKPaymentTransactionStateFailed:
                // 购买失败，处理失败逻辑
                [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
                self.waitHandleOrderDict = nil;
                //购买失败
                [weakself handleFail:[[SCXErrorModel alloc] initWitMsg:@"Pay Fail".translateString]];
                self.purchaseBlock = nil;
                break;
                
            case SKPaymentTransactionStateRestored:
                // 用户恢复购买，处理恢复逻辑
                [[SKPaymentQueue defaultQueue] finishTransaction:transaction];
                break;
                
            default:
                break;
        }
    }
}
- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response {
    NSMutableDictionary<NSString *,SKProduct *> *dic = [NSMutableDictionary new];
    if (response.products.count > 0) {
        // 在这里更新你的UI，显示可购买的商品信息
        for (SKProduct *product in response.products) {
            ///更新缓存
            self.appStoreProductsDic[product.productIdentifier] = product;
            dic[product.productIdentifier] = product;
        }
    }
    kSCBlockExeNotNil(self.searchProductsBlock,dic);
    self.searchProductsBlock = nil;

}



@end
