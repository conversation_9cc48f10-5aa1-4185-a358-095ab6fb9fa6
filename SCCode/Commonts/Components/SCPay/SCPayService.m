//
//  SCPayService.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCPayService.h"
#import "SCCategoryAPIManagerPay.h"
#import "SCIAPManager.h"
#import "SCOrderResultModel.h"
// #import "SCCoinsModel.h" // 已移除，使用字典替代

#import "SCThirdPartyPayWebViewController.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCStrongGuidePopoUp.h"
#import "SCTrackingUtils.h"
#import "SCThirdPartyPayPopup.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCPayService()
///苹果支付
@property(nonatomic,strong) SCIAPManager *iapManager;

@end

@implementation SCPayService
@synthesize thirdPartCannelDictObx = _thirdPartCannelDictObx;

- (instancetype)initWithUserId:(NSString *)userId{
    self = [super initWithUserId:userId];
    if (self) {
        _thirdPartCannelDictObx = [[SCObservable<NSDictionary *> alloc] initWithValue:nil];
        _orderChangeObx = [[SCObservable<SCOrderResultModel *>  alloc] initWithValue:nil];
        _iapManager = [[SCIAPManager alloc] init];
    }
    return self;
}

- (void)remoteThirdPayCannelWithDictSuccess:(void (^)(NSDictionary * _Nonnull))success failure:(void (^)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestThirdPayCannelWithSuccess:^(id _Nonnull responseData) {
        // 直接使用API返回的字典数据
        NSDictionary *channelDict = nil;
        if ([responseData isKindOfClass:[NSDictionary class]]) {
            channelDict = (NSDictionary *)responseData;
        } else {
            // 如果API仍返回Model，需要转换（过渡期处理）
            channelDict = @{}; // 临时处理，实际应该更新API
        }
        weakself.thirdPartCannelDictObx.value = channelDict;
        kSCBlockExeNotNil(success,channelDict);
    } failure:^(SCXErrorModel * _Nonnull error) {
        weakself.thirdPartCannelDictObx.error = error;
        kSCBlockExeNotNil(failure,error);
    }];
}

///刷新appStore商品信息
-(void) refreshProducts:(NSSet<NSString *> *)codes{
    [self.iapManager refreshProducts:codes];
}
/// 调用苹果支付
- (void)iAPWithProductCode:(NSString *)code source:(NSString *)source entry:(NSString *)entry purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock{
    
    
    kWeakSelf(self)
    
    //导量
    // 从策略字典中获取强引导开关状态
    BOOL isSwitchStrongGuide = [SCDictionaryHelper boolFromDictionary:kScAuthMar.strategyObs.value forKey:@"isSwitchStrongGuide" defaultValue:NO];
    if(kScAuthMar.strongGuideModel && isSwitchStrongGuide){
        NSInteger nativeRechargeRedirect = [SCDictionaryHelper integerFromDictionary:kScAuthMar.strongGuideModel forKey:SCDictionaryKeys.shared.kSCStrongGuideNativeRechargeRedirectKey defaultValue:1];
        if(nativeRechargeRedirect == 0){
            [SCStrongGuidePopoUp createStrongGuideViewWithDict:kScAuthMar.strongGuideModel useOtherPaymentBlock:nil];
            kSCBlockExeNotNil(purchaseBlock,nil,[[SCXErrorModel alloc] initWitMsg:@"Pay Fail".translateString]);
            return;
        }
    }
    
    [weakself.iapManager requestProductId:code callBack:^(SKProduct * _Nullable product) {
        if(product == nil){
            SCOrderResultModel *resultModel = [[SCOrderResultModel alloc] initWithIsSuccess:NO msg:@"The product not found".translateString code:code cannel:@"IAP" orderDict:nil];
            weakself.orderChangeObx.value = resultModel;
            kSCBlockExeNotNil(purchaseBlock,nil,[[SCXErrorModel alloc] initWitMsg:@"The product not found".translateString]);
            return;
        }
        
        [weakself purchaseCodeDict:code product:product source:source entry:entry purchaseBlock:purchaseBlock];
        
    }];
    
}

- (void)purchaseCodeDict:(NSString *)code product:(SKProduct *)product source:(NSString *) source entry:(NSString *)entry purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock {
    kWeakSelf(self)
    [self.iapManager purchaseProduct:product source:source entry:entry purchaseBlock:^(NSDictionary * _Nullable orderDict, SCXErrorModel * _Nullable error) {
        if(error != nil){
            weakself.orderChangeObx.error = error;
        }else{
            NSString *orderNo = kSCOrderNoFromDict(orderDict);
            double payAmount = kSCOrderPayAmountFromDict(orderDict);
            [SCTrackingUtils trackPurchaseSuccessWithOrderNo:orderNo ?: @"" price:payAmount ?: 0];
            SCOrderResultModel *resultModel = [[SCOrderResultModel alloc] initWithIsSuccess:YES msg:nil code:code cannel:@"IAP" orderDict:orderDict];
            weakself.orderChangeObx.value = resultModel;
        }
        kSCBlockExeNotNil(purchaseBlock,orderDict,error);

    }];
}



/// 根据Channel字典支付
-(void) thirdPayWithCannelDict:(NSDictionary *)channelDict coinsDict:(NSDictionary *)coinsDict entry:(NSString*)entry fromVC:(UIViewController *)fromVC  purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock waitBlock:(void(^)(NSDictionary * orderDict)) waitBlock{

    // 数据验证
    if (![channelDict isKindOfClass:[NSDictionary class]]) {
        kSCBlockExeNotNil(purchaseBlock,nil, [[SCXErrorModel alloc] initWitMsg:@"Invalid channel data".translateString]);
        return;
    }

    kWeakSelf(self)
    NSString *payChannel = [SCDictionaryHelper stringFromDictionary:channelDict forKey:@"payChannel" defaultValue:@""];
    NSInteger jumpType = [SCDictionaryHelper integerFromDictionary:channelDict forKey:@"jumpType" defaultValue:0];

    if ([payChannel isEqualToString:@"IAP"] || [payChannel isEqualToString:@"GP"]) {
        // 苹果支付
        NSString *code = kSCCoinsCodeFromDict(coinsDict);
        NSString *invitationId = kSCCoinsInvitationIdFromDict(coinsDict);
        [self iAPWithProductCode:code source:invitationId entry:entry purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            dispatch_async(dispatch_get_main_queue(), ^{
                [fromVC.view hiddenLoading];
                kSCBlockExeNotNil(purchaseBlock,orderDict,error);
            });
        }];
    } else {
        // 创建订单
        NSString *code = kSCCoinsCodeFromDict(coinsDict);
        NSString *invitationId = kSCCoinsInvitationIdFromDict(coinsDict);
        [SCAPIServiceManager requestCreatOrderWithCode:code source:invitationId entry:entry payChannel:payChannel success:^(NSDictionary * _Nonnull orderDict) {
            // 成功创建订单
            [weakself sc_blank_empty];
            if (jumpType == 0) {
                // 打开内部网页 - 使用字典版本
                [weakself showWithFromVCPayWebDict:waitBlock orderDict:orderDict];

            } else if (jumpType == 1) {
                // 打开外部网页
                NSString *requestUrl = kSCOrderRequestUrlFromDict(orderDict);
                NSURL *url = [NSURL URLWithString:requestUrl];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                }
                //也是等待支付 - 直接使用字典
                kSCBlockExeNotNil(waitBlock,orderDict);
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            kSCBlockExeNotNil(purchaseBlock,nil, [[SCXErrorModel alloc] initWitMsg:@"Pay Fail".translateString]);
        }];

    }
}

- (void)showWithFromVCPayWebDict:(void(^)(NSDictionary * orderDict)) waitBlock orderDict:(NSDictionary * _Nonnull) orderDict{
    NSString *requestUrl = kSCOrderRequestUrlFromDict(orderDict);
    [SCThirdPartyPayWebViewController showWithFromVC:[UIViewController currentViewController] requestUrl:requestUrl callBack:^{
        kSCBlockExeNotNil(waitBlock,orderDict);
    }];
}



- (void)sc_blank_empty{

}



@end
