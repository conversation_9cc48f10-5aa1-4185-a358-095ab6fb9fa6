//
//  SCPayService.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCBaseAppService.h"
@class SCOrderResultModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCPayService : SCBaseAppService

//订单编号 监听订单状态， 但是不建议使用该属性用于窗口的关闭 建议使用callback统一处理
@property(nonatomic,strong,readonly) SCObservable<SCOrderResultModel *> * orderChangeObx;


#pragma mark - 字典版本API
@property(nonatomic,strong,readonly) SCObservable<NSDictionary *> * thirdPartCannelDictObx;
//刷新第三方支付渠道（字典版本）
- (void) remoteThirdPayCannelWithDictSuccess:(void(^_Nullable)(NSDictionary *channelDict)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

///刷新appStore商品信息
-(void) refreshProducts:(NSSet<NSString *> *)codes;

/// 调用苹果支付
- (void)iAPWithProductCode:(NSString *)code source:(NSString *)source entry:(NSString *)entry purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock;

/// 根据Channel字典支付
-(void) thirdPayWithCannelDict:(NSDictionary *)channelDict coinsDict:(NSDictionary *)coinsDict entry:(NSString *)entry fromVC:(UIViewController *)fromVC  purchaseBlock:(void(^)(NSDictionary * orderDict,SCXErrorModel *error)) purchaseBlock waitBlock:(void(^)(NSDictionary * orderDict)) waitBlock;

@end

NS_ASSUME_NONNULL_END
