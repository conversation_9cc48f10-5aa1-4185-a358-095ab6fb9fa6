//
//  SCCategoryAPIManagerPay.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerPay)

///创建订单
+(void) requestCreatOrderWithCode:(NSString *_Nonnull) code source:(NSString *_Nullable)source entry:(NSString *_Nullable)entry payChannel:(NSString *_Nonnull) channel success:(void (^_Nullable)(NSDictionary * _Nonnull orderDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///消费订单
+ (void)requestPaymentIpaWithOrderNo:(NSString * _Nonnull)orderNo payload:(NSString *_Nonnull)payload transactionId:(NSString *_Nonnull)transactionId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///第三方支付渠道
+(void) requestThirdPayCannelWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull payChannelDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

@end

NS_ASSUME_NONNULL_END
