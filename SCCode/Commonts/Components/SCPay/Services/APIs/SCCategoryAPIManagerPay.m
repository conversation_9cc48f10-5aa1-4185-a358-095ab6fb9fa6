//
//  SCCategoryAPIManagerPay.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCCategoryAPIManagerPay.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCXErrorModel.h"
#import "SCTrackingUtils.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerPay)


///创建订单
+(void) requestCreatOrderWithCode:(NSString *) code source:(NSString *)source entry:(NSString *_Nullable)entry payChannel:(NSString *) channel success:(void (^_Nullable)(NSDictionary * _Nonnull orderDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    NSMutableDictionary * params = [NSMutableDictionary dictionary];

    params[@"goodsCode"] = code;
    params[@"payChannel"] = channel;
    if(!kSCIsStrEmpty(source)){
        params[@"source"] = source;
    }
    if(!kSCIsStrEmpty(entry)){
        params[@"entry"] = entry;
    }

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICreateOrder method:SCNetMethodPOST parameters:params headers:nil success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        NSString *orderNo = [SCDictionaryHelper stringFromDictionary:result forKey:SCDictionaryKeys.shared.kSCDictKeyOrderNo defaultValue:nil];
        if(result && !kSCIsStrEmpty(orderNo) ){
            double payAmount = [SCDictionaryHelper longLongFromDictionary:result forKey:SCDictionaryKeys.shared.kSCDictKeyPayAmount defaultValue:0];
            [SCTrackingUtils trackStartPurchaseWithPrice:payAmount ?: 0];
            kSCBlockExeNotNil(success,result);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Create Order Fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///消费订单
+ (void)requestPaymentIpaWithOrderNo:(NSString *)orderNo payload:(NSString *)payload transactionId:(NSString *)transactionId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    NSDictionary *params = @{@"orderNo": orderNo, @"payload": payload, @"transactionId": transactionId, @"type": @(1)};
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIPaymentIpa method:SCNetMethodPOST parameters:params headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Payment Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///第三方支付渠道
+(void) requestThirdPayCannelWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull payChannelDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIPayChannel method:SCNetMethodGET parameters:nil headers:nil success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result){
            kSCBlockExeNotNil(success,result);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get Channer Fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


@end
