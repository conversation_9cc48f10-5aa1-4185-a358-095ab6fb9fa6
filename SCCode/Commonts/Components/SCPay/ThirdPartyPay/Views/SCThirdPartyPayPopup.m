//
//  SCThirdPartyPayPopup.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCThirdPartyPayPopup.h"
#import "SCContinueRechargePopup.h"
//View
#import "SCThirdPayCannelCell.h"
#import "SCThirdPayItemView.h"
//Model
//#import "SCThirdPayCannelGroupModel.h"
#import "SCThirdPayCannelDisplayModel.h"
#import "SCOrderResultModel.h"
#import "SCDictionaryHelper.h"
//支付服务
#import "SCPayService.h"
#import "SCSocketService.h"
#import "SCPopupManager.h"
#import "SCTrackingUtils.h"
#import "SCDictionaryHelper.h"

@interface SCThirdPartyPayPopup () <SCPopupManagerDelegate>
@property(nonatomic,weak)SCThirdPayItemView * headerView;
@property(nonatomic,weak) UIView *contentV;
@property(nonatomic,weak) UITableView *tableView;
@property(nonatomic,weak) UIButton * continueBtn;
@property(nonatomic,assign) BOOL isPaySuccess;

///回调  是否充值成功
@property(nonatomic,copy) void(^ _Nullable callback)(BOOL success);
@property (nonatomic, strong) NSString *entry;
@property (nonatomic, strong) NSString *source;

@property(nonatomic,strong) NSDictionary *itemDict; // 字典版本
@property(nonatomic,strong) NSArray<SCThirdPayCannelDisplayModel *> *canncels;
@property(nonatomic,assign) NSInteger selectIndex;
///当前是否活跃【没有退到后台并且，当前正在显示】
@property(nonatomic,assign) BOOL isActivite;
//是否已经关闭
@property(nonatomic,assign) BOOL isCloseed;
@end

@implementation SCThirdPartyPayPopup

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setIsHiddenSCNavigationBar:YES];
}
- (void)initUI{
    [super initUI];
    self.view.backgroundColor = UIColor.clearColor;
    
    NSMutableArray<SCThirdPayCannelDisplayModel *> * models = [NSMutableArray new];
    // 使用字典版本的观察者
    NSDictionary *channelGroupDict = SCAuthManager.instance.payService.thirdPartCannelDictObx.value;
    NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelGroupDict forKey:@"channelList" defaultValue:@[]];

    for (NSDictionary *channelDict in channelList) {
        if ([channelDict isKindOfClass:[NSDictionary class]]) {
            SCThirdPayCannelDisplayModel *m = [[SCThirdPayCannelDisplayModel alloc] init];
            m.channelDict = channelDict;
            [models addObject:m];
        }
    }
    self.canncels = models;
   
    
    UIView *contentV = [UIView new].setBackgroundColor(UIColor.scWhite);
    contentV.layer.cornerRadius = kSCBigCornerRadius;
    contentV.layer.masksToBounds = YES;
    contentV.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.view addSubview:contentV];
    _contentV = contentV;
    
    
    SCThirdPayItemView * headerView = [SCThirdPayItemView new];
    kSCAddTapGesture(headerView.closeBtn, self, onClose);
    [contentV addSubview:headerView];
    _headerView = headerView;
    
    UITableView *tableView = [UITableView tableViewWithFrame:self.contentV.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    tableView.backgroundColor = UIColor.scWhite;
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.rowHeight = 78;
    [contentV addSubview:tableView];
    _tableView = tableView;
    
    UIButton * continueBtn = [UIButton buttonWithTitle:@"Pay".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(20) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(onContinue)];
    [continueBtn sc_setThemeGradientBackground];
    [self.contentV addSubview:continueBtn];
    self.continueBtn = continueBtn;
    
    [self.contentV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.bottom.trailing.equalTo(self.view);
        make.height.mas_equalTo(628);
    }];
    
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self.contentV);
        //        make.height.mas_equalTo(60);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentV);
        make.top.equalTo(self.headerView.mas_bottom);
    }];
    
    [self.continueBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentV).offset(-20 - kSCSafeAreaBottomHeight);
        make.centerX.equalTo(self.contentV);
        make.size.mas_equalTo(CGSizeMake(291, 46));
        make.top.equalTo(self.tableView.mas_bottom).offset(20);
    }];
    
    [self updateItemView];
    
    
    kWeakSelf(self);
    

    
    //去掉监听原生支付订单变化，因为在 purchaseBlock 回调中已处理。

    //监听socket订单编号 - 使用字典处理
    [kSCAuthSocketService.orderEventObs afterSubscribe:^(NSDictionary * _Nonnull orderDict) {
        NSString *code = [SCDictionaryHelper stringFromDictionary:orderDict forKey:@"code" defaultValue:@""];
        if([code isEqualToString:[weakself itemCode]]){
            // TODO: 确认一下加在此处是否合适。
            NSString *orderNo = [SCDictionaryHelper stringFromDictionary:orderDict forKey:@"orderNo" defaultValue:@""];
            NSInteger payAmount = [SCDictionaryHelper integerFromDictionary:orderDict forKey:@"payAmount" defaultValue:0];
            [SCTrackingUtils trackPurchaseSuccessWithOrderNo:orderNo price:payAmount];
            NSString *userId = kSCCurrentUserID;
            NSString *key = [NSString stringWithFormat:@"%@%@", userId, code];
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:key];
            
            [weakself onSuccessClose];
        }
        
    } error:nil disposeBag:self.disposeBag];
    
    ///监听App前后台
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(willEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    
}



- (void)sc_blank_empty{
    
}

- (void)updateItemView {
    self.headerView.coinsL.text = [NSString stringWithFormat:@"%ld",[self itemExchangeCoin]];
    self.headerView.priceL.text = [NSString stringWithFormat:@"$%.2f",[self itemPrice]];
        
    if (self.canncels.count <= self.selectIndex) {
        return;
    }
    SCThirdPayCannelDisplayModel *displayModel = self.canncels[self.selectIndex];
    NSInteger r = [self calculateMaxBonusPercentageForChannelDict:displayModel.channelDict];

    NSInteger num = [self itemExchangeCoin] * r / 100;
    if (num <= 0) {
        self.headerView.extCoinsBtn.hidden = YES;
    } else {
        self.headerView.extCoinsBtn.hidden = NO;
        [self.headerView.extCoinsBtn setTitle:[NSString stringWithFormat:@"+%ld", (long)num] forState:UIControlStateNormal];
    }
}



- (NSInteger)calculateMaxBonusPercentageForChannelDict:(NSDictionary *)channelDict {
    // 数据验证
    if (![channelDict isKindOfClass:[NSDictionary class]]) {
        
        return 0;
    }

    NSString *payChannel = [SCDictionaryHelper stringFromDictionary:channelDict forKey:@"payChannel" defaultValue:@""];
    NSInteger presentCoinRatio = [SCDictionaryHelper integerFromDictionary:channelDict forKey:@"presentCoinRatio" defaultValue:0];
    NSInteger promotionPresentCoinRatio = [SCDictionaryHelper integerFromDictionary:channelDict forKey:@"promotionPresentCoinRatio" defaultValue:0];

    //新版活动促销->渠道弹窗额外比例展示规则
    if ([self isActivityPromotionItem]) {
        if ([payChannel isEqualToString:@"IAP"]) {
            //原生渠道显示的额外赠送比例，取原生渠道本身的额外赠送比例与商品的extraCoinPercent中最大一个
            return MAX(presentCoinRatio, [self activityPromotionExtraCoinPercent]);
        } else {
            //每个三方渠道显示的额外赠送比例，取三方渠道本身的赠送比例与thirdpartyCoinPercent中最大一个
            return MAX(presentCoinRatio, [self activityPromotionThirdpartyCoinPercent]);
        }
    } else {
        //非新版活动促销逻辑，即区分商店内促销商品和普通商品，商品自带赠送比例extraCoinPercent，则对比两者，取最大值显示
        NSInteger channelBonus = [self itemIsPromotion] ? promotionPresentCoinRatio : presentCoinRatio;
        return MAX(channelBonus, [self itemExtraCoinPercent]);
    }
}

#pragma mark -SCPopupManagerDelegate
- (void)handleBackgroundTapWithCompletion:(void (^)(BOOL))completion {
    //背景点击关闭
    SCContinueRechargePopup *rechargePopup = [[SCContinueRechargePopup alloc]init];
    [rechargePopup showInViewController:self closeBlock:^{
        completion(YES);
    } rechargeBlock:^{
        completion(NO);
    }];
}

-(void) onSuccessClose{
    self.isPaySuccess = true;
    if(self.isActivite){
        [self onClose];
    }
}
-(void) onClose{
    if (self.isPaySuccess) {
        // 支付成功关闭
        [self continueClose];
    } else {
        // 点击关闭
        SCContinueRechargePopup *rechargePopup = [[SCContinueRechargePopup alloc]init];
        kWeakSelf(self)
        [rechargePopup showInViewController:self closeBlock:^{
            [weakself continueClose];
        } rechargeBlock:nil];
    }
}

- (void)continueClose {
    if(!self.isCloseed){//防止重复关闭
        self.isCloseed = true;
        kSCBlockExeNotNil(self.callback,self.isPaySuccess);
        [[SCPopupManager shared] dismissPopup:self];
    }
}

-(void) onContinue{
    SCThirdPayCannelDisplayModel *channel = self.canncels[self.selectIndex];
    [self.view showLoading];
    kWeakSelf(self);
    [kScAuthMar.payService thirdPayWithCannelDict:channel.channelDict coinsDict:self.itemDict entry:self.entry fromVC:self purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
        //支付成功
        [weakself.view hiddenLoading];
        NSString *orderGoodsCode = kSCOrderGoodsCodeFromDict(orderDict);
        if (error == nil && [orderGoodsCode isEqualToString:[weakself itemCode]]) {
            //标记成功
            NSString *userId = kSCCurrentUserID;
            NSString *key = [NSString stringWithFormat:@"%@%@", userId, orderGoodsCode];
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:key];

            [weakself onSuccessClose];

        } else {
            [kSCKeyWindow toast: error.msg ?: @"Pay Fail".translateString];
        }
    } waitBlock:^(NSDictionary * _Nonnull orderDict) {
        //如果是 网页转跳的支付，那么当收到回调后，如果支付状态已经完成则关闭页面
        [weakself.view hiddenLoading];
        if(weakself.isPaySuccess){
            [weakself onSuccessClose];
        }else{

        }
    }];
}
- (void)willEnterForeground {
    //App退到后台
    self.isActivite = false;
}

- (void)didEnterBackground {
    //App恢复到前台
    self.isActivite = true;
    
    if(self.isPaySuccess){
        [self onSuccessClose];
    }
}
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.isActivite = true;
}
- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    if(self.isPaySuccess){
        [self onSuccessClose];
    }
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.isActivite = false;
}

#pragma mark - 字典访问辅助方法

- (NSString *)itemCode {
    return [SCDictionaryHelper stringFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsCodeKey defaultValue:@""];
}

- (NSInteger)itemExchangeCoin {
    return [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsExchangeCoinKey defaultValue:0];
}

- (CGFloat)itemPrice {
    return [SCDictionaryHelper floatFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsPriceKey defaultValue:0.0];
}

- (BOOL)itemIsPromotion {
    return [SCDictionaryHelper boolFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsIsPromotionKey defaultValue:NO];
}

- (NSInteger)itemExtraCoinPercent {
    return [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsExtraCoinPercentKey defaultValue:0];
}

- (BOOL)isActivityPromotionItem {
    // 通过检查活动促销特有字段来判断类型
    NSInteger extraCoin = [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCPromotionExtraCoinKey defaultValue:-1];
    NSInteger thirdpartyCoinPercent = [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCPromotionThirdpartyCoinPercentKey defaultValue:-1];
    return (extraCoin >= 0 || thirdpartyCoinPercent >= 0);
}

- (NSInteger)activityPromotionExtraCoinPercent {
    return [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCCoinsExtraCoinPercentKey defaultValue:0];
}

- (NSInteger)activityPromotionThirdpartyCoinPercent {
    return [SCDictionaryHelper integerFromDictionary:self.itemDict forKey:SCDictionaryKeys.shared.kSCPromotionThirdpartyCoinPercentKey defaultValue:0];
}

#pragma mark - TableView Delegate DataSource


- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCThirdPayCannelCell * cell = [SCThirdPayCannelCell initWithFormTableView:tableView];
    NSInteger row = indexPath.row;
    SCThirdPayCannelDisplayModel *model = self.canncels[row];
    NSInteger percent = [self calculateMaxBonusPercentageForChannelDict:model.channelDict];
    [cell configWitModel:model percent:percent isSelect:row == self.selectIndex];
    NSInteger itemType = [model itemType];
    cell.descL.text = itemType == 1 ? [model recommendReason] : @"";
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.canncels count];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 78;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    self.selectIndex = indexPath.row;
    [self.tableView reloadData];
    [self updateItemView];
}



#pragma mark - 路由

// 已废弃的 Model 版本方法
// +(void) showWithFromVC:(UIViewController *)fromVC coins:(SCCoinsModel *)coins source:(NSString *)source entry:(NSString *)entry callBack:(void(^ _Nullable)(BOOL success)) callback{
//     SCThirdPartyPayPopup *popup = [[SCThirdPartyPayPopup alloc] init];
//     popup.itemModel = coins;
//     popup.callback = callback;
//     popup.entry = entry;
//     popup.source = source;
//     [[SCPopupManager shared] showPopup:popup inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
// }

// 字典版本
+(void) showWithFromVC:(UIViewController *)fromVC coinsDict:(NSDictionary *)coinsDict source:(NSString *)source entry:(NSString *)entry callBack:(void(^ _Nullable)(BOOL success)) callback{
    SCThirdPartyPayPopup *popup = [[SCThirdPartyPayPopup alloc] init];
    popup.itemDict = coinsDict;
    popup.callback = callback;
    popup.entry = entry;
    popup.source = source;
    [[SCPopupManager shared] showPopup:popup inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
}

@end
