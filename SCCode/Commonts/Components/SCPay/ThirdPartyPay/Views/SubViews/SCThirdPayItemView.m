//
//  SCThirdPayItemView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCThirdPayItemView.h"
#import "SCIMUIConfig.h"
#import <Masonry/Masonry.h>
#import "SCFontManager.h"

@implementation SCThirdPayItemView

- (void)initUI{
    [super initUI];
    
    UILabel *titleL = [UILabel labelWithText:@"Payment method".translateString textColor:[UIColor scBlack] font:[SCFontManager boldItalicFontWithSize:20.0f] alignment:NSTextAlignmentLeft];
    [self addSubview:titleL];
    _titleL = titleL;
    
    
    UIButton *closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close"] ];
    [self addSubview:closeBtn];
    _closeBtn = closeBtn;
    
    UIView * contentV = [[UIView alloc] init].setCornerRadius(15.0f).setBackgroundColor([[UIColor colorWithHexString:@"#FFC0C1"] colorWithAlphaComponent:0.29]);
    [self addSubview:contentV];
    _contentV = contentV;
    UILabel * titleLeftL = [UILabel labelWithText:@"Item".translateString textColor:[UIColor colorWithHexString:@"#890000"] font:kScUIFontSemibold(14.0f) alignment:NSTextAlignmentLeft];
    [self.contentV addSubview:titleLeftL];
    _titleLeftL = titleLeftL;
    
    UILabel * titleRightL = [UILabel labelWithText:@"Price".translateString textColor:[UIColor colorWithHexString:@"#890000"] font:kScUIFontSemibold(14.0f) alignment:NSTextAlignmentRight];
    [self.contentV addSubview:titleRightL];
    _titleRightL = titleRightL;
    
    UIView *lineV = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#FA9797"]);
    [self.contentV addSubview:lineV];
    _lineV = lineV;
    
    ///创建图片
    UIImageView * iconImgV = [UIImageView new].setImageName(@"ic_coins_anchor_info");
    [self.contentV addSubview:iconImgV];
    _iconImgV = iconImgV;
    
    ///金币数量
    UILabel *coinsL = [UILabel labelWithText:@"100" textColor:[UIColor scBlack] font:kScUIFontSemibold(16) alignment:NSTextAlignmentLeft];
    [self.contentV addSubview:coinsL];
    _coinsL = coinsL;
    
    ///创建额外金币
    UIButton * extCoinsBtn= [UIButton buttonWithType:UIButtonTypeCustom];
    [extCoinsBtn setTitleColor:UIColor.scWhite forState:UIControlStateNormal];
    extCoinsBtn.titleLabel.font = kScUIFontSemibold(16.0f);
    extCoinsBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10.0f, 0, 10.0f);
    [extCoinsBtn sc_setThemeGradientBackgroundWithCornerRadius:8.0f];
    [self.contentV addSubview:extCoinsBtn];
    _extCoinsBtn = extCoinsBtn;
    
    ///创建价格
    UILabel * priceL = [UILabel labelWithText:@"$0.00" textColor:[UIColor scBlack] font:kScUIFontSemibold(14) alignment:NSTextAlignmentRight];
    [self.contentV addSubview:priceL];
    _priceL = priceL;
    
    
    //布局
    [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(21);
        make.top.mas_equalTo(25);
        make.height.mas_equalTo(30.0);
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self).mas_equalTo(0);
        make.top.equalTo(self).mas_equalTo(0);
        make.width.height.mas_equalTo(40.0);
    }];
    
    [self.contentV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).mas_equalTo(15);
        make.trailing.equalTo(self).mas_equalTo(-15);
        make.top.equalTo(titleL.mas_bottom).mas_equalTo(9.0f);
        make.height.mas_equalTo(113.0f);
        make.bottom.equalTo(self).mas_equalTo(-15);
    }];
    [self.lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentV);
        make.height.mas_equalTo(kSCDividingLineHeight);
        make.centerY.equalTo(self.contentV);
    }];
    [self.titleLeftL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentV).mas_equalTo(14);
        make.top.equalTo(self.contentV);
        make.bottom.equalTo(lineV.mas_top);
    }];
    
    [self.titleRightL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentV).mas_equalTo(-14);
        make.top.equalTo(self.contentV);
        make.bottom.equalTo(lineV.mas_top);
    }];
    
    [self.iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentV).mas_equalTo(14);
        make.centerY.equalTo(self.coinsL);
        make.size.mas_equalTo(CGSizeMake(18, 14));
    }];
    
    [self.coinsL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.iconImgV.mas_trailing).offset(3);
        make.centerY.equalTo(self.priceL);
        make.height.mas_equalTo(23.0f);
    }];
    
    [self.extCoinsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.coinsL.mas_trailing).offset(3);
        make.centerY.equalTo(self.coinsL);
        make.height.mas_equalTo(28.0f);
    }];
    
    [self.priceL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentV).mas_equalTo(-14);
        make.top.equalTo(lineV.mas_bottom).mas_equalTo(0);
        make.bottom.equalTo(self.contentV).mas_equalTo(0);
    }];
    
    
}

@end
