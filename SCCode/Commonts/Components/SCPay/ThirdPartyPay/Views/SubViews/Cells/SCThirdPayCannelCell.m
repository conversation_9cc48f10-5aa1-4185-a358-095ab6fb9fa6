//
//  SCThirdPayCannelCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/17.
//

#import "SCThirdPayCannelCell.h"
#import "SCThirdPayCannelDisplayModel.h"
// #import "SCCoinsModel.h" // 已移除，使用字典替代
#import "SCFontManager.h"

@interface SCThirdPayCannelCell()

@property (nonatomic, weak) UIView *containView;
///图标
@property(nonatomic,weak) UIImageView * iconImgV;
///标题
@property(nonatomic,weak) UILabel * titleL;
///额外金币图标
@property(nonatomic,weak) UIImageView * extCoinsImgV;
///额外金币
@property(nonatomic,weak) UILabel * extCoinsL;


@end

@implementation SCThirdPayCannelCell

- (void)initUI{
    [super initUI];
    
    UIView *containView = [[UIView alloc]init];
    containView.layer.borderWidth = 2.0f;
    containView.layer.borderColor = [UIColor colorWithHexString:@"#E0E0E0"].CGColor;
    containView.layer.cornerRadius = 10.0f;
    [self.contentView addSubview:containView];
    _containView = containView;
    
    UIImageView * iconImgV = [UIImageView new];
    iconImgV.contentMode = UIViewContentModeScaleAspectFit;
    [self.containView addSubview:iconImgV];
    _iconImgV = iconImgV;
    
    UILabel * titleL = [UILabel labelWithText:@"" textColor:[UIColor scBlack] font:[SCFontManager boldItalicFontWithSize:14.0f] alignment:NSTextAlignmentLeft];
    [self.containView addSubview:titleL];
    _titleL = titleL;
    
    UIImageView * extCoinsImgV = [UIImageView new].setImageName(@"ic_coins_middle");
    [self.containView addSubview:extCoinsImgV];
    _extCoinsImgV = extCoinsImgV;
    
    UILabel * extCoinsL = [UILabel labelWithText:@"" textColor:[UIColor colorWithHexString:@"#FF3000"] font:kScUIFontRegular(12) alignment:NSTextAlignmentLeft];
    [self.containView addSubview:extCoinsL];
    _extCoinsL = extCoinsL;
    
    UILabel * descL = [UILabel labelWithText:@"" textColor:[UIColor scRed] font:kScUIFontMedium(11) alignment:NSTextAlignmentRight];
    [self.containView addSubview:descL];
    _descL = descL;
    
    [_containView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16.0f);
        make.trailing.equalTo(self.contentView).offset(-16.0f);
        make.top.equalTo(self.contentView);
        make.height.mas_equalTo(68.0f);
    }];
    
    [iconImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.containView).offset(15);
        make.centerY.equalTo(self.containView);
        make.width.height.mas_equalTo(45.0f);
    }];
    
    [self.titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(iconImgV.mas_trailing).offset(10);
        make.centerY.equalTo(iconImgV).offset(-9);
        make.height.mas_equalTo(18);
    }];
    
    [self.extCoinsL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.titleL);
        make.centerY.equalTo(self.extCoinsImgV);
        make.height.mas_equalTo(16);
    }];
    
    [self.extCoinsImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.extCoinsL.mas_trailing).offset(2);
        make.top.equalTo(self.titleL.mas_bottom).offset(5);
        make.size.mas_equalTo(CGSizeMake(14, 11));
    }];
    
    [self.descL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.containView).offset(-10);
        make.centerY.equalTo(self.containView);
        make.height.mas_equalTo(16);
    }];
}
- (void)configWitModel:(SCThirdPayCannelDisplayModel *)model percent:(NSInteger)percent isSelect:(BOOL) isSelect{
    // 数据验证
    if (!model) {
        
        return;
    }

    // 使用便捷访问方法获取数据
    [self.iconImgV sc_setImageWithURL:[model iconUrl]];
    self.titleL.text = [model title];
       
    if (percent == 0) {
        self.extCoinsL.hidden = YES;
        self.extCoinsImgV.hidden = YES;
        [self.titleL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.iconImgV).offset(0);
        }];
    } else {
        [self.titleL mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self.iconImgV).offset(-9);
        }];
        self.extCoinsL.hidden = NO;
        self.extCoinsImgV.hidden = NO;
        
        NSString *coinsString = [@"+###% More Coins".translateString stringByReplacingOccurrencesOfString:@"###" withString:[NSString stringWithFormat:@"%ld", (long)percent]];;
        self.extCoinsL.text = coinsString;
    }
    
    self.containView.layer.borderColor = [UIColor colorWithHexString:isSelect ? @"#EB3A3C" : @"#E0E0E0"].CGColor;
    self.containView.layer.borderWidth = isSelect ? 3.0f : 2.0f;
}

@end
