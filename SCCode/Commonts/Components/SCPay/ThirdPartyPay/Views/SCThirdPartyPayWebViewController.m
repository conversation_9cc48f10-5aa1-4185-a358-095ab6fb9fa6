//
//  SCThirdPartyPayWebViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/17.
//

#import "SCThirdPartyPayWebViewController.h"
#import "SCIMService.h"
#import "SCConversationInfoViewController.h"
#import "SCBaseNavigationController.h"

@interface SCThirdPartyPayWebViewController ()
@property(nonatomic,copy) void(^callback)(void);
@end

@implementation SCThirdPartyPayWebViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.filterSound = YES;
    // Do any additional setup after loading the view.
}
- (WKWebViewConfiguration *)defaultConfiguration{
    WKWebViewConfiguration * config = [super defaultConfiguration];
    WKUserContentController *userContentController = config.userContentController;
    [userContentController addScriptMessageHandler:self name:@"openVipService"];
    config.userContentController = userContentController;
    return config;
}

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message{
    [super userContentController:userContentController didReceiveScriptMessage:message];
        
    if ([message.name isEqualToString:@"openVipService"]) {
        // 打开VIP服务客服
        // 检查客服账户字典是否存在
        if(kScAuthMar.imService.userServiceAccountObx.value == nil || [kScAuthMar.imService.userServiceAccountObx.value count] == 0){
            kWeakSelf(self)
            [kScAuthMar.imService remoteUserServiceAccountWithSuccess:^(NSDictionary * _Nonnull userServiceAccountDict) {
                [weakself sc_blank_empty];
                // 使用字典版本的方法打开会话页面
                NSString *userID = [SCDictionaryHelper stringFromDictionary:userServiceAccountDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
                [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:userServiceAccountDict];
            } failure:^(SCXErrorModel * _Nonnull error) {
                [weakself sc_blank_empty];
                [kSCKeyWindow toast:@"Unable to match customer service, please try again later".translateString];
            }];
        }else{
            // 使用字典版本的方法打开会话页面
            NSString *userID = [SCDictionaryHelper stringFromDictionary:kScAuthMar.imService.userServiceAccountObx.value forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:kScAuthMar.imService.userServiceAccountObx.value];
        }
    }
}

- (void)sc_blank_empty{
    
}

- (void)onBlack{
    [super onBlack];
    kSCBlockExeNotNil(self.callback);
}

- (void)dealloc {
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"openVipService"];
}

+(void) showWithFromVC:(UIViewController *)fromVC requestUrl:(NSString *) url callBack:(void(^)(void)) callback{
    SCThirdPartyPayWebViewController *webVC = [[SCThirdPartyPayWebViewController alloc] init];
    webVC.webAddress = url;
    webVC.callback = callback;
    
    
    SCBaseNavigationController *nv = [[SCBaseNavigationController alloc] initWithRootViewController:webVC];
    ///注意这里知道打开使用全屏，因为不全屏，上一个App不会调用即将显示和即将关闭的逻辑
    nv.modalPresentationStyle = UIModalPresentationFullScreen;
    nv.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
    [fromVC presentViewController:nv animated:YES completion:nil];
}

@end
