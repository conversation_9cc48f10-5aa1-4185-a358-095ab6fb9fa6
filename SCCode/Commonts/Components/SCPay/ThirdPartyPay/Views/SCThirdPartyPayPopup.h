//
//  SCThirdPartyPayPopup.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/16.
//

#import "SCBaseViewController.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCThirdPartyPayPopup<UITableViewDelegate,UITableViewDataSource> : SCBaseViewController


@end

#pragma mark - TableView Delegate DataSource

#pragma mark - 路由
@interface SCThirdPartyPayPopup(SCThirdRoute)
// 已废弃，使用字典版本
// +(void) showWithFromVC:(UIViewController *)fromVC coins:(SCCoinsModel *)coins source:(NSString *)source entry:(NSString *)entry callBack:(void(^ _Nullable)(BOOL success)) callback;

// 字典版本
+(void) showWithFromVC:(UIViewController *)fromVC coinsDict:(NSDictionary *)coinsDict source:(NSString *)source entry:(NSString *)entry callBack:(void(^ _Nullable)(BOOL success)) callback;

@end

NS_ASSUME_NONNULL_END
