//
//  SCThirdPayCannelDisplayModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/17.
//

#import "SCThirdPayCannelDisplayModel.h"
#import "SCDictionaryHelper.h"

@interface SCThirdPayCannelDisplayModel ()
// 缓存常用值以提高性能
@property (nonatomic, copy) NSString *cachedPayChannel;
@property (nonatomic, copy) NSString *cachedTitle;
@property (nonatomic, copy) NSString *cachedIconUrl;
@end

@implementation SCThirdPayCannelDisplayModel

#pragma mark - 便捷访问方法

- (NSString *)payChannel {
    if (!_cachedPayChannel) {
        _cachedPayChannel = [SCDictionaryHelper stringFromDictionary:self.channelDict forKey:@"payChannel" defaultValue:@""];
    }
    return _cachedPayChannel;
}

- (NSString *)title {
    if (!_cachedTitle) {
        _cachedTitle = [SCDictionaryHelper stringFromDictionary:self.channelDict forKey:@"title" defaultValue:@""];
    }
    return _cachedTitle;
}

- (NSString *)iconUrl {
    if (!_cachedIconUrl) {
        _cachedIconUrl = [SCDictionaryHelper stringFromDictionary:self.channelDict forKey:@"iconUrl" defaultValue:@""];
    }
    return _cachedIconUrl;
}

- (NSInteger)presentCoinRatio {
    return [SCDictionaryHelper integerFromDictionary:self.channelDict forKey:@"presentCoinRatio" defaultValue:0];
}

- (NSInteger)promotionPresentCoinRatio {
    return [SCDictionaryHelper integerFromDictionary:self.channelDict forKey:@"promotionPresentCoinRatio" defaultValue:0];
}

- (NSInteger)jumpType {
    return [SCDictionaryHelper integerFromDictionary:self.channelDict forKey:@"jumpType" defaultValue:0];
}

- (NSInteger)itemType {
    return [SCDictionaryHelper integerFromDictionary:self.channelDict forKey:@"itemType" defaultValue:0];
}

- (NSString *)recommendReason {
    return [SCDictionaryHelper stringFromDictionary:self.channelDict forKey:@"recommendReason" defaultValue:@""];
}

#pragma mark - Setter方法

- (void)setChannelDict:(NSDictionary *)channelDict {
    _channelDict = channelDict;
    // 清除缓存，强制重新计算
    _cachedPayChannel = nil;
    _cachedTitle = nil;
    _cachedIconUrl = nil;
}

@end
