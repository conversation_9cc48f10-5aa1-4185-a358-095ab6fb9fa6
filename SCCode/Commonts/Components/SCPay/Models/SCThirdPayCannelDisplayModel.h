//
//  SCThirdPayCannelDisplayModel.h
//  Supercall
//
//  Created by guanweihong on 2024/1/17.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

@interface SCThirdPayCannelDisplayModel : NSObject

///渠道数据字典（主要数据源）
@property(nonatomic,strong) NSDictionary * channelDict;

#pragma mark - 便捷访问方法
///获取支付渠道
- (NSString *)payChannel;
///获取标题
- (NSString *)title;
///获取图标URL
- (NSString *)iconUrl;
///获取赠送金币比例
- (NSInteger)presentCoinRatio;
///获取促销赠送金币比例
- (NSInteger)promotionPresentCoinRatio;
///获取跳转类型
- (NSInteger)jumpType;
///获取项目类型
- (NSInteger)itemType;
///获取推荐理由
- (NSString *)recommendReason;

@end

NS_ASSUME_NONNULL_END
