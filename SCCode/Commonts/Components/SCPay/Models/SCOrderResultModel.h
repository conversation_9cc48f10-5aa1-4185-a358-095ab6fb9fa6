//
//  SCOrderResultModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/17.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCOrderResultModel : NSObject
@property(nonatomic,assign) BOOL isSuccess;
@property(nonatomic,nullable,copy) NSString * msg;
@property(nonatomic,nonnull,copy) NSString * code;
@property(nonatomic,nonnull,copy) NSString * cannel;
@property(nonatomic,nullable,strong) NSDictionary * orderDict; // 订单数据

- (instancetype)initWithIsSuccess:(BOOL) isSuccess msg:(NSString *_Nullable)msg code:(NSString *_Nonnull)code cannel:(NSString *_Nonnull)cannel orderDict:(NSDictionary *_Nullable)orderDict;

@end

NS_ASSUME_NONNULL_END
