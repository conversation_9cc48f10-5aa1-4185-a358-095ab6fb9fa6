//
//  SCOrderResultModel.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/17.
//

#import "SCOrderResultModel.h"

@implementation SCOrderResultModel

///初始化
- (instancetype)initWithIsSuccess:(BOOL) isSuccess msg:(NSString *_Nullable)msg code:(NSString *_Nonnull)code cannel:(NSString *_Nonnull)cannel orderDict:(NSDictionary *_Nullable)orderDict
{
    self = [super init];
    if (self) {
        _isSuccess = isSuccess;
        _msg = msg;
        _code = code;
        _cannel = cannel;
        _orderDict = orderDict;
    }
    return self;
}

@end
