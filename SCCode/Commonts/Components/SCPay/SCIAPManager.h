//
//  SCIAPManager.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCIAPManager : NSObject<SKPaymentTransactionObserver, SKProductsRequestDelegate>



- (void)removeIAPObserver;
-(void)refreshProducts:(NSSet<NSString *> *)productIdentifiers;
- (void)requestProducts:(NSSet<NSString *> * _Nullable)productIdentifiers callBack:(void(^_Nullable)(NSDictionary<NSString *,SKProduct *> * _Nonnull productDic)) searchProductsBlock;
- (void)requestProductId:(NSString *)productIdentifier callBack:(void(^)(SKProduct * _Nullable product)) searchProductsBlock;

//支付
- (void)purchaseProduct:(SKProduct *)product source:(NSString *) source entry:(NSString *)entry purchaseBlock:(void(^ _Nullable )(NSDictionary * _Nullable orderDict ,SCXErrorModel * _Nullable error)) purchaseBlock;

@end

NS_ASSUME_NONNULL_END
