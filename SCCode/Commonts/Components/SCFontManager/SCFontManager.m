//
//  SCFontManager.m
//  Supercall
//
//  Created by sumengli<PERSON> on 2024/12/2.
//


#import "SCFontManager.h"

@implementation SCFontManager

// 字体名称常量
static NSString *const kFontNameRegular = @"BaiJamjuree-Regular";
static NSString *const kFontNameBold = @"BaiJamjuree-Bold";
static NSString *const kFontNameSemiBold = @"BaiJamjuree-SemiBold";
static NSString *const kFontNameItalic = @"BaiJamjuree-Italic";
static NSString *const kFontNameBoldItalic = @"BaiJamjuree-BoldItalic";
static NSString *const kFontNameSemiBoldItalic = @"BaiJamjuree-SemiBoldItalic";

#pragma mark - Public Methods

+ (UIFont *)regularFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameRegular size:size];
    return font ?: [UIFont systemFontOfSize:size];
}

+ (UIFont *)boldFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameBold size:size];
    return font ?: [UIFont boldSystemFontOfSize:size];
}

+ (UIFont *)semiBoldFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameSemiBold size:size];
    return font ?: [UIFont systemFontOfSize:size weight:UIFontWeightSemibold];
}

+ (UIFont *)italicFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameItalic size:size];
    return font ?: [UIFont italicSystemFontOfSize:size];
}

+ (UIFont *)boldItalicFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameBoldItalic size:size];
    if (!font) {
        // 如果找不到粗斜体，使用系统粗斜体
        UIFontDescriptor *descriptor = [UIFontDescriptor fontDescriptorWithFontAttributes:@{
            UIFontDescriptorFaceAttribute: @"Bold Italic"
        }];
        font = [UIFont fontWithDescriptor:descriptor size:size];
    }
    return font ?: [UIFont systemFontOfSize:size];
}

+ (UIFont *)semiBoldItalicFontWithSize:(CGFloat)size {
    UIFont *font = [UIFont fontWithName:kFontNameSemiBoldItalic size:size];
    if (!font) {
        // 如果找不到中粗斜体，使用系统中粗斜体
        UIFontDescriptor *descriptor = [[UIFontDescriptor fontDescriptorWithFontAttributes:@{
            UIFontDescriptorFaceAttribute: @"Semibold Italic"
        }] fontDescriptorWithSymbolicTraits:UIFontDescriptorTraitItalic];
        font = [UIFont fontWithDescriptor:descriptor size:size];
    }
    return font ?: [UIFont systemFontOfSize:size];
}

@end