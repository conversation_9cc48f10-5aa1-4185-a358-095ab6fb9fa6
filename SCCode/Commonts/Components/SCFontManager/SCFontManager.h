//
//  SCFontManager.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/12/2.
//


#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCFontManager : NSObject

/// Regular font (常规字体)
+ (UIFont *)regularFontWithSize:(CGFloat)size;

/// Bold font (粗体)
+ (UIFont *)boldFontWithSize:(CGFloat)size;

/// SemiBold font (中粗体)
+ (UIFont *)semiBoldFontWithSize:(CGFloat)size;

/// Italic font (斜体)
+ (UIFont *)italicFontWithSize:(CGFloat)size;

/// Bold Italic font (粗斜体)
+ (UIFont *)boldItalicFontWithSize:(CGFloat)size;

/// SemiBold Italic font (中粗斜体)
+ (UIFont *)semiBoldItalicFontWithSize:(CGFloat)size;

@end

NS_ASSUME_NONNULL_END