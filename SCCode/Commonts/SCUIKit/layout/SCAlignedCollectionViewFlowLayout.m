//
//  SCAlignedCollectionViewFlowLayout.m
//  Supercall
//
//  Created by sumengliu on 2024/10/23.
//

#import "SCAlignedCollectionViewFlowLayout.h"

@implementation SCAlignedCollectionViewFlowLayout

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    NSArray* attributesToReturn = [super layoutAttributesForElementsInRect:rect];
    for (UICollectionViewLayoutAttributes* attributes in attributesToReturn) {
        if (nil == attributes.representedElementKind) {
            NSIndexPath* indexPath = attributes.indexPath;
            attributes.frame = [self layoutAttributesForItemAtIndexPath:indexPath].frame;
        }
    }
    return attributesToReturn;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    UICollectionViewLayoutAttributes* currentItemAttributes = [super layoutAttributesForItemAtIndexPath:indexPath];
    UIEdgeInsets sectionInset = self.sectionInset;
    BOOL isRTL = kScAuthMar.isLanguageForce;
    
    if (indexPath.item == 0) { // 第一个元素
        CGRect frame = currentItemAttributes.frame;
        frame.origin.x = isRTL ? self.collectionView.bounds.size.width - sectionInset.right - frame.size.width : sectionInset.left;
        currentItemAttributes.frame = frame;
        return currentItemAttributes;
    }
    
    NSIndexPath* previousIndexPath = [NSIndexPath indexPathForItem:indexPath.item-1 inSection:indexPath.section];
    CGRect previousFrame = [self layoutAttributesForItemAtIndexPath:previousIndexPath].frame;
    
    CGRect currentFrame = currentItemAttributes.frame;
    CGRect strecthedCurrentFrame = CGRectMake(0,
                                              currentFrame.origin.y,
                                              self.collectionView.frame.size.width,
                                              currentFrame.size.height);
    
    if (!CGRectIntersectsRect(previousFrame, strecthedCurrentFrame)) { // 如果前一个元素和当前元素不在同一行
        CGRect frame = currentItemAttributes.frame;
        frame.origin.x = isRTL ? self.collectionView.bounds.size.width - sectionInset.right - frame.size.width : sectionInset.left;
        currentItemAttributes.frame = frame;
        return currentItemAttributes;
    }
    
    CGRect frame = currentItemAttributes.frame;
    if (isRTL) {
        frame.origin.x = previousFrame.origin.x - self.minimumInteritemSpacing - frame.size.width;
    } else {
        frame.origin.x = CGRectGetMaxX(previousFrame) + self.minimumInteritemSpacing;
    }
    currentItemAttributes.frame = frame;
    return currentItemAttributes;
}


@end
