//
//  SCCollectionViewLeftAlignedFlowLayout.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCCollectionViewLeftAlignedFlowLayout.h"

@implementation SCCollectionViewLeftAlignedFlowLayout

//左对齐
- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    NSArray<UICollectionViewLayoutAttributes *> *layoutAttributes = [super layoutAttributesForElementsInRect:rect];
    
    CGFloat leftMargin = self.sectionInset.left;
    CGFloat currentY = -1.0;
    
    for (UICollectionViewLayoutAttributes *attributes in layoutAttributes) {
        if (attributes.frame.origin.y != currentY) {
            leftMargin = self.sectionInset.left;
            currentY = attributes.frame.origin.y;
        }
        
        attributes.frame = CGRectMake(leftMargin, attributes.frame.origin.y, attributes.frame.size.width, attributes.frame.size.height);
        leftMargin += attributes.frame.size.width + self.minimumInteritemSpacing;
    }
    
    return layoutAttributes;
}


@end
