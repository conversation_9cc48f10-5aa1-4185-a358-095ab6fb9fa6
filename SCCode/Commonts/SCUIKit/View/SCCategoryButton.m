//
//  SCCategoryButton.m
//  Supercall
//
//  Created by sumengliu on 2024/11/15.
//

#import "SCCategoryButton.h"

@implementation UIButton (SCCategoryButton)

- (void)sc_setThemeGradientBackground {    
    [self sc_setThemeGradientBackgroundWithCornerRadius:kSCNormalCornerRadius];
}

- (void)sc_setThemeGradientBackgroundWithCornerRadius:(CGFloat)cornerRadius {
    NSArray *colors = @[
        [UIColor scGradientBtnStartColor],
        [UIColor scGradientBtnEndColor]
    ];
    
    NSArray *locations = @[@(0), @(1.0f)];
    CGPoint startPoint = CGPointMake(0, 0.5);
    CGPoint endPoint = CGPointMake(1, 0.5);
    
    // 先设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    
    // 设置渐变背景
    [self sc_setGradientBackgroundWithColors:colors
                          gradientLocations:locations
                               startPoint:startPoint
                                 endPoint:endPoint
                             cornerRadius:cornerRadius];
}

@end
