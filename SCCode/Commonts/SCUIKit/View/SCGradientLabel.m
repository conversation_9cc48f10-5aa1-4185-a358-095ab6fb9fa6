//
//  SCGradientLabel.m
//  Supercall
//
//  Created by sumengli<PERSON> on 2024/10/23.
//

#import "SCGradientLabel.h"

// GradientLabel.m
@interface SCGradientLabel()
@property (nonatomic, strong) UILabel *label;
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@end

@implementation SCGradientLabel

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    // 创建 Label
    self.label = [[UILabel alloc] init];
    self.label.numberOfLines = 0;
    self.label.font = [UIFont systemFontOfSize:16];
    self.label.textColor = [UIColor whiteColor]; // 添加默认文本颜色
    
    // 创建渐变层
    self.gradientLayer = [CAGradientLayer layer];
    self.gradientLayer.colors = @[
        (id)[[UIColor redColor] CGColor],
        (id)[[UIColor blueColor] CGColor]
    ];
    
    // 设置渐变方向（从左到右）
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.5);
    self.gradientLayer.endPoint = CGPointMake(1.0, 0.5);
    
    // 添加渐变层到最底层
    [self.layer addSublayer:self.gradientLayer];
    
    // 添加 label 到视图层级中
    [self addSubview:self.label];
    
    // 设置默认值
    self.contentInsets = UIEdgeInsetsZero;
    self.label.textAlignment = NSTextAlignmentLeft;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 计算可用的内容区域
    CGFloat availableWidth = self.bounds.size.width - self.contentInsets.left - self.contentInsets.right;
    CGSize maxSize = CGSizeMake(availableWidth, CGFLOAT_MAX);
    CGSize textSize = [self.label sizeThatFits:maxSize];
    
    // 根据对齐方式计算 x 坐标
    CGFloat labelX = self.contentInsets.left;
    if (self.textAlignment == NSTextAlignmentCenter) {
        labelX = self.contentInsets.left + (availableWidth - textSize.width) / 2;
    } else if (self.textAlignment == NSTextAlignmentRight) {
        labelX = self.bounds.size.width - self.contentInsets.right - textSize.width;
    }
    
    // 更新 label 的 frame
    self.label.frame = CGRectMake(labelX, 
                                 self.contentInsets.top,
                                 textSize.width,
                                 textSize.height);
    
    // 创建一个用于遮罩的 CATextLayer
    CATextLayer *textLayer = [CATextLayer layer];
    textLayer.string = self.label.text;
    textLayer.font = (__bridge CFTypeRef)self.label.font;
    textLayer.fontSize = self.label.font.pointSize;
    textLayer.alignmentMode = kCAAlignmentCenter;
    textLayer.frame = self.label.frame;
    textLayer.contentsScale = UIScreen.mainScreen.scale;
    
    // 更新渐变层的 frame 和遮罩
    self.gradientLayer.frame = self.bounds;
    self.gradientLayer.mask = textLayer;
    
    // 隐藏原始 label，因为我们使用 CATextLayer 来显示文本
    self.label.hidden = YES;
}

#pragma mark - Setters

- (void)setText:(NSString *)text {
    _text = [text copy];
    self.label.text = text;
    [self invalidateIntrinsicContentSize];
    [self setNeedsLayout];
}

- (void)setFont:(UIFont *)font {
    _font = font;
    self.label.font = font;
    [self invalidateIntrinsicContentSize];
    [self setNeedsLayout];
}

- (void)setNumberOfLines:(NSInteger)numberOfLines {
    _numberOfLines = numberOfLines;
    self.label.numberOfLines = numberOfLines;
    [self setNeedsLayout];
}

- (void)setGradientColors:(NSArray<UIColor *> *)colors {
    if (!colors.count) return;
    
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in colors) {
        [cgColors addObject:(id)color.CGColor];
    }
    self.gradientLayer.colors = cgColors;
    [self setNeedsLayout];
}

- (void)setTextAlignment:(NSTextAlignment)textAlignment {
    _textAlignment = textAlignment;
    self.label.textAlignment = textAlignment;
    [self setNeedsLayout];
}

- (void)setContentInsets:(UIEdgeInsets)contentInsets {
    _contentInsets = contentInsets;
    [self invalidateIntrinsicContentSize];
    [self setNeedsLayout];
}

#pragma mark - Intrinsic Content Size

- (CGSize)intrinsicContentSize {
    // 计算文本内容的大小
    CGFloat availableWidth = self.bounds.size.width;
    if (availableWidth <= 0) {
        // 如果还没有宽度，使用一个较大的值来计算
        availableWidth = UIScreen.mainScreen.bounds.size.width;
    }
    availableWidth -= (self.contentInsets.left + self.contentInsets.right);
    
    CGSize maxSize = CGSizeMake(availableWidth, CGFLOAT_MAX);
    CGSize textSize = [self.label sizeThatFits:maxSize];
    
    // 返回包含内边距的尺寸
    return CGSizeMake(textSize.width + self.contentInsets.left + self.contentInsets.right,
                     textSize.height + self.contentInsets.top + self.contentInsets.bottom);
}

@end
