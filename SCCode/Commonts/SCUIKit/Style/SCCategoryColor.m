//
//  SCCategoryColor.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/10.
//

#import "SCCategoryColor.h"

@implementation UIColor (SCCategoryColor)

+(UIColor *) scGlobalBgColor {
    return [UIColor colorWithHexString:@"#390C0C"];
}

+(UIColor *) scThemeRegColor{
    return [UIColor colorWithHexString:@"#FE7669"];
}

///深主题色
+ (UIColor *)scDarkTheme {
    return [UIColor colorWithHexString:@"#740A31"];
}
///主题色
+ (UIColor *)scTheme {
    return [UIColor colorWithHexString:@"#FF423E"];
}
///浅主题色
+ (UIColor *)scLightTheme {
    return [UIColor colorWithHexString:@"#FFEEC5"];
}

+ (UIColor *) scBlack{
    return [UIColor blackColor];
}

+ (UIColor *) scGray{
    return [UIColor colorWithHexString:@"#7C7C7C"];
}
//#979797

//#FF414141 灰色
+ (UIColor *) scFF414141{
    return [UIColor colorWithHexString:@"FF414141"];
}

+(UIColor *) scWhite{
    return [UIColor whiteColor];
}
+ (UIColor *)scLineColor{
    return [[UIColor scBlack] colorWithAlphaComponent:0.19];
}
//透明背景
+ (UIColor *) scTranBlackBGColor{
    return [[UIColor scBlack] colorWithAlphaComponent:0.2];;
}

//红色
+ (UIColor *) scRed{
    return [UIColor colorWithHexString:@"#F10F0F"];
}
//灰色按钮颜色
+ (UIColor *) scGrayBtn{
    return [UIColor colorWithHexString:@"#CECECE"];
}

/// 渐变按钮开始颜色
+ (UIColor *) scGradientBtnStartColor {
    return [UIColor colorWithHexString:@"#FF5450"];
}
/// 渐变按钮结束颜色
+ (UIColor *)scGradientBtnEndColor {
    return [UIColor colorWithHexString:@"#460645"];
}
+ (NSArray<UIColor*> *) scLevelColors {
    return @[
        [UIColor colorWithHexString:@"#A3A3A3"],
        [UIColor colorWithHexString:@"#806FB3"],
        [UIColor colorWithHexString:@"#E1882C"],
        [UIColor colorWithHexString:@"#FA9E5F"],
        [UIColor colorWithHexString:@"#FFBA3D"],
        [UIColor colorWithHexString:@"#FF6732"],
        [UIColor colorWithHexString:@"#FE4223"],
        [UIColor colorWithHexString:@"#EB3100"],
        [UIColor colorWithHexString:@"#FF0000"],
        [UIColor colorWithHexString:@"#CF0000"],
        [UIColor colorWithHexString:@"#B70000"],
    ];
}

+ (UIColor *) scChatBgColor {
    return [UIColor colorWithHexString:@"#1C0000"];
}

+ (UIColor *) sc50TranBlackBGColor {
    return [[UIColor scBlack] colorWithAlphaComponent:0.5];;
}


@end
