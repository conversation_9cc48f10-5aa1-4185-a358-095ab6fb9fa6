//
//  SCGradientColors.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/10.
//

#import "SCGradientColors.h"

@interface SCGradientColors ()
@property(nonatomic,strong) NSMutableArray<UIColor *> * colors;
@property(nonatomic,nonnull,strong,readonly) NSMutableArray<NSNumber *> * locations;
@end

@implementation SCGradientColors

- (instancetype)init
{
    self = [super init];
    if (self) {
        _colors = [NSMutableArray new];
        _locations = [NSMutableArray new];
        _orientation = SCGradientColorsOrientationVertical;
    }
    return self;
}

-(NSArray *) cgGolors{
    NSMutableArray * colorArray = [NSMutableArray new];
    for (UIColor *itemColor in self.colors) {
        [colorArray addObject:(__bridge id)itemColor.CGColor];
    }
    return [colorArray copy];
}
-(NSArray<NSNumber *> *) sclocations{
    return [_locations copy];
}

-(void) addWithColor:(UIColor *)color location:(CGFloat) location{
    [self.colors addObject:color];
    [_locations addObject:@(location)];
}

@end

@implementation SCGradientColors (SCUIKit)

+(SCGradientColors *) themeColorWithOrientation:(SCGradientColorsOrientation)orientation{
    SCGradientColors * c = [[SCGradientColors alloc] init];
    c->_orientation = orientation;
    [c addWithColor:[UIColor scTheme] location:0];
    [c addWithColor:[UIColor scDarkTheme] location:1];
    return c;
}
+(SCGradientColors *) darkThemeColorWithOrientation:(SCGradientColorsOrientation)orientation{
    SCGradientColors * c = [[SCGradientColors alloc] init];
    c->_orientation = orientation;
    [c addWithColor:[UIColor colorWithHexString:@"#F46E3F"] location:0];
    [c addWithColor:[UIColor scTheme] location:1];
    return c;
}

///邀请充值背景渐变色
+(SCGradientColors *) inviteCoinsBgColor{
    SCGradientColors * c = [[SCGradientColors alloc] init];
    c->_orientation = SCGradientColorsOrientationUpperLeftLowerRight;
    [c addWithColor:[UIColor colorWithHexString:@"#FFB598"] location:0];
    [c addWithColor:[UIColor colorWithHexString:@"#F7CC71"] location:1];
    return c;
}


+(SCGradientColors *)gradientWithColors:(NSArray<UIColor *> *)colors orientation:(SCGradientColorsOrientation)orientation{
    SCGradientColors * c = [[SCGradientColors alloc] init];
    c->_orientation = orientation;
    for (int i = 0; i < [colors count]; i++) {
        [c addWithColor:colors[i] location:i/(CGFloat)[colors count]];
    }
    
    return c;
}

//主题渐变色按钮
+(SCGradientColors *)scThemeGradient{
    //主题渐变色 目前只支持两个 改动的时候需要注意
    SCGradientColors * gradient = [[SCGradientColors alloc] init];
    [gradient addWithColor:[UIColor scDarkTheme] location:0];
    [gradient addWithColor:[UIColor scTheme] location:1];
    return gradient;
}

@end
