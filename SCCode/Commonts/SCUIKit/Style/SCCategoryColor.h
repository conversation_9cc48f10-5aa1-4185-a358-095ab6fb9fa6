//
//  SCCategoryColor.h
//  Supercall
//
//  Created by guanweihong on 2024/1/10.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIColor (SCCategoryColor)
/// 全局背景色
+(UIColor *) scGlobalBgColor;
///主题红
+(UIColor *) scThemeRegColor;
///深主题色
+ (UIColor *)scDarkTheme;
///主题色
+ (UIColor *)scTheme;
///浅主题色
+ (UIColor *)scLightTheme;
///黑色
+ (UIColor *)scBlack;
///灰色
+ (UIColor *) scGray;
//#FF414141 灰色
+ (UIColor *) scFF414141;
///白色
+ (UIColor *)scWhite;
///分割线颜色
+(UIColor *)scLineColor;
///半透明黑色背景
+ (UIColor *) scTranBlackBGColor;
///红色
+ (UIColor *) scRed;
///灰色圆角
+ (UIColor *) scGrayBtn;
/// 渐变按钮开始颜色
+ (UIColor *) scGradientBtnStartColor;
/// 渐变按钮结束颜色
+ (UIColor *)scGradientBtnEndColor;
// 等级颜色
+ (NSArray<UIColor*> *) scLevelColors;
/// 私聊页背景色
+ (UIColor *) scChatBgColor;
/// 50%黑色透明背景
+ (UIColor *) sc50TranBlackBGColor;

@end

NS_ASSUME_NONNULL_END
