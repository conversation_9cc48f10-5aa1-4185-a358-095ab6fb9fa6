//
//  SCGradientColors.h
//  Supercall  渐变色对象
//
//  Created by guanweihong on 2024/1/10.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
//渐变方向枚举
typedef enum : NSUInteger {
    ///水平方向
    SCGradientColorsOrientationHorizontal,
    ///垂直方向
    SCGradientColorsOrientationVertical,
    ///左上右下
    SCGradientColorsOrientationUpperLeftLowerRight,
    ///左下右上
    SCGradientColorsOrientationLowerLeftUpperRight,
} SCGradientColorsOrientation;


@interface SCGradientColors : NSObject
@property(nonatomic,nonnull,strong,readonly) NSArray * cgGolors;
@property(nonatomic,nonnull,strong,readonly) NSArray<NSNumber *> * sclocations;
@property(nonatomic,assign) SCGradientColorsOrientation orientation;
-(void) addWithColor:(UIColor *)color location:(CGFloat) location;
@end

@interface SCGradientColors (SCUIKit)
+(SCGradientColors *)scThemeGradient;
+(SCGradientColors *) themeColorWithOrientation:(SCGradientColorsOrientation)orientation;
+(SCGradientColors *) darkThemeColorWithOrientation:(SCGradientColorsOrientation)orientation;
///邀请充值背景渐变色
+(SCGradientColors *) inviteCoinsBgColor;
+(SCGradientColors *)gradientWithColors:(NSArray<UIColor *> *)colors orientation:(SCGradientColorsOrientation)orientation;
@end

NS_ASSUME_NONNULL_END
