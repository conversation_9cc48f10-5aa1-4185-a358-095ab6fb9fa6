//
//  SCAPIManage.h
//  Supercall
//
//  Created by juf<PERSON><PERSON> on 2025/1/16.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIManage : NSObject
+ (instancetype)shared;

//AJ
@property(nonatomic,copy)NSString *kAdjustToken;

@property(nonatomic,copy)NSString *kAdjustOrderToken;

@property(nonatomic,copy)NSString *kAdjustPurchaseToken;

@property(nonatomic,copy)NSString *kAdjustLoginToken;

@property(nonatomic,copy)NSString *kAdjustRegisterToken;

//k2 k3 k4
@property(nonatomic,copy)NSString *kEncryption2;

@property(nonatomic,copy)NSString *kEncryption3;

@property(nonatomic,copy)NSString *kEncryption4;



///登录接口
@property(nonatomic,copy)NSString *kSCAPILogin;
///退出登录
@property(nonatomic,copy)NSString *kSCAPILogout;
///删除账号
@property(nonatomic,copy)NSString *kSCAPIDeleteAccount;
///验证Token是否过期
@property(nonatomic,copy)NSString *kSCAPIVerifyToken;
///风控上报
@property(nonatomic,copy)NSString *kSCAPIUploadRisk;
/// AF 上传
@property(nonatomic,copy)NSString *kSCAPIAFUpload;
//获取App配置
@property(nonatomic,copy)NSString *kSCAPIAppConfig;
//获取配置
@property(nonatomic,copy)NSString *kSCAPIStrategy;
//获取主播列表数据
@property(nonatomic,copy)NSString *kSCAPIGetAnchorList;

//获取多个用户在线状态接口
@property(nonatomic,copy)NSString *kSCAPIGetUsersOnlineStatus;

//获取用户榜
@property(nonatomic,copy)NSString *kSCAPIGetUserRank;

//获取主播榜
@property(nonatomic,copy)NSString *kSCAPIGetAnchorRank;
///用户详情接口
@property(nonatomic,copy)NSString *kSCAPIGetUserInfo;
///获取用户基本信息【目前使用于IM列表】
@property(nonatomic,copy)NSString *kSCAPIGetUserBasic;
//获取主播是否在线
@property(nonatomic,copy)NSString *kSCAPIUserOnlineStatus;
///主播额外信息接口
@property(nonatomic,copy)NSString *kSCAPIGetUserExtraInfo;

///拉黑举报
@property(nonatomic,copy)NSString *kSCAPIBlockAndReport;
///取消拉黑
@property(nonatomic,copy)NSString *kSCAPIUnBlock;
///关注
@property(nonatomic,copy)NSString *kSCAPIFollow;
///取消关注
@property(nonatomic,copy)NSString *kSCAPIUnFollow;
///关注列表
@property(nonatomic,copy)NSString *kSCAPIFollowList;
///拉黑列表
@property(nonatomic,copy)NSString *kSCAPIBlockList;


///获取礼物列表
@property(nonatomic,copy)NSString *kSCAPIGiftList;
///活动礼物
@property(nonatomic,copy)NSString *kSCAPIActivityGiftList;
///获取用户礼物数量
@property(nonatomic,copy)NSString *kSCAPIGiftCounts;
///赠送礼物
@property(nonatomic,copy)NSString *kSCAPIGiveUserGifts;

/// Media 查询
@property(nonatomic,copy)NSString *kSCAPIMediaSearch;

///OSS
@property(nonatomic,copy)NSString *kSCAPIGetOSSPolicy;

//更新头像
@property(nonatomic,copy)NSString *kSCAPIUpdateAvatar;

//更新用户资源
@property(nonatomic,copy)NSString *kSCAPIUpdateMedia;

//更新用户信息
@property(nonatomic,copy)NSString *kSCAPIUpdateUserInfo;


#pragma mark - Banner
//获取Banner列表
@property(nonatomic,copy)NSString *kSCAPIGetBannerInfo;


#pragma mark - 金币和购买
///商品列表
@property(nonatomic,copy)NSString *kSCAPIGoodsList;
///邀请链接是否有效
@property(nonatomic,copy)NSString *kSCAPIInviteRechargeLinkIsValid;
///邀请链接获取商品信息
@property(nonatomic,copy)NSString *kSCAPIInviteRechargeLinkList;
///促销商品
@property(nonatomic,copy)NSString *kSCAPIPromotion;
///活动促销
@property(nonatomic,copy)NSString *kSCAPIActivityPromotion;
///创建订单
@property(nonatomic,copy)NSString *kSCAPICreateOrder;
/// 消费订单
@property(nonatomic,copy)NSString *kSCAPIPaymentIpa;
///支付渠道
@property(nonatomic,copy)NSString *kSCAPIPayChannel;
///查询当前用户金币数量
@property(nonatomic,copy)NSString *kSCAPIMyCoinsNum;
///注册奖励
@property(nonatomic,copy)NSString *kSCAPIRegisterReward;

#pragma mark - IM
///获取融云Token
@property(nonatomic,copy)NSString *kSCAPIGetRongCloudToken;
///前后台切换
@property(nonatomic,copy)NSString *kSCAPIModeSwitch;
///上报心跳
@property(nonatomic,copy)NSString *kSCAPIHeartbeat;

#pragma mark - Call

//创建通话
@property(nonatomic,copy)NSString *kSCAPICallCreateChannel;
//挂断
@property(nonatomic,copy)NSString *kSCAPICallHangUp;
///接听
@property(nonatomic,copy)NSString *kSCAPICallPickUp;
///加入频道
@property(nonatomic,copy)NSString *kSCAPICallJoinChannel;
///更新声网ID 在加入频道后更新
@property(nonatomic,copy)NSString *kSCAPICallUpdateAgoraUid;
//获取后置摄像头配置接口
@property(nonatomic,copy)NSString *kSCAPICallRealCameraConfig;
//打开后置摄像头
@property(nonatomic,copy)NSString *kSCAPICallOpenRealCamera;
/// 通话结果接口
@property(nonatomic,copy)NSString *kSCAPICallGetCallResult;
///主播评价
@property(nonatomic,copy)NSString *kSCAPICallAnchorEvaluate;

#pragma mark - 开关设置
//免打扰接口
@property(nonatomic,copy)NSString *kSCAPISwitchNotDisturb;
#pragma mark - 个人等级
///获取用户等级
@property(nonatomic,copy)NSString *kSCAPIGetUserLevel;
#pragma mark - 强化引导
///强化引导
@property(nonatomic,copy)NSString *kSCAPIGetStrongGuide;
#pragma mark - 客服
///客服问题集合
@property(nonatomic,copy)NSString *kSCAPIGetFAQ;

#pragma mark - 快速匹配
///随机推荐主播
@property(nonatomic,copy)NSString *kSCAPIRandomRecommendAnchor;
///快速匹配接口
@property(nonatomic,copy)NSString *kSCAPIFlashMatch;
///取消快速匹配
@property(nonatomic,copy)NSString *kSCAPIFlashMatchCancel;
///轮播话术
@property(nonatomic,copy)NSString *kSCAPIFlashChatConfig;



@end

NS_ASSUME_NONNULL_END
