//
//  SCPayEntry.h
//  SCCode
//
//  Created by AI Assistant
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCPayEntry : NSObject
+ (instancetype)shared;
/// 1. 匹配页面右上角商店
@property(nonatomic,copy)NSString *kPayEntrySourceMatchTitle;
/// 2. 个人页商店
@property(nonatomic,copy)NSString *kPayEntrySourceUserCenter;
/// 3. 匹配页面右下角悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceMatchFloat;
/// 4. 虚假机器人拨打界面引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceOnCallRobot;
/// 5. 视频通话中赠送礼物金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceChattingGift;
/// 6. 视频通话中主动点击getCoin引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceChatting;
/// 7. 被呼叫时候pickup但是由于金币不足引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceOnCall;
/// 8. 拨打电话时由于金币不足引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceCall;
/// 9. 首充弹窗直接充值
@property(nonatomic,copy)NSString *kPayEntrySourcePopNewbee;
/// 10. 主播/用户查找页右上角商店
@property(nonatomic,copy)NSString *kPayEntrySourceLookForTitle;
/// 11. 匹配页面左上角VIP入口
@property(nonatomic,copy)NSString *kPayEntrySourceSubscribeMatch;
/// 12. 个人页VIP入口
@property(nonatomic,copy)NSString *kPayEntrySourceSubscribeDetail;
/// 13. 视频聊天界面金币不足弹框
@property(nonatomic,copy)NSString *kPayEntrySourceCoinNotEnough;
/// 14. VIP订阅弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceSubscribeDialog;
/// 15. 限时充值弹窗
@property(nonatomic,copy)NSString *kPayEntrySourcePopPromotion;
/// 16. Vip等级页面升级按钮点击充值
@property(nonatomic,copy)NSString *kPayEntrySourceVipLevelUp;
/// 17. 充值链接充值弹框
@property(nonatomic,copy)NSString *kPayEntrySourceRechargeLink;
/// 18. 免费电话后诱导弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceAfterFreeCall;
/// 19. IM聊天页中赠送礼物金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceIMGift;
/// 20. IM聊天页中主动点击礼物弹框getCoin引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceIM;
/// 21. 主播个人中心拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorProfileCall;
/// 22. 主播视频详情页拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorProfileVideoCall;
/// 23. IM聊天界面右上角拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceConversation;
/// 24. 视频流拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceVideoflow;
/// 25. 匹配结果页面拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceMatchResultCall;
/// 26. 主播墙拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorWall;
/// 27. 主播详情页面付费照片/视频点击
@property(nonatomic,copy)NSString *kPayEntrySourceDetailMedia;
/// 28. 匹配功能金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceMatchCoinNotEnough;
/// 29. 后台推送优惠信息
@property(nonatomic,copy)NSString *kPayEntrySourcePushSpecialOffer;
/// 30. 消费升级弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceConsumeUpgradeDialog;
/// 31. IM聊天限制充值
@property(nonatomic,copy)NSString *kPayEntrySourceIMLimit;
/// 32. 玩老虎机金币不足充值
@property(nonatomic,copy)NSString *kPayEntrySourceSlotMachine;
/// 33. 动态送礼物金币不足充值
@property(nonatomic,copy)NSString *kPayEntrySourceMomentGift;
/// 34. 第三方支付充值
@property(nonatomic,copy)NSString *kPayEntrySourceTPP;
/// 35. 快速匹配充值
@property(nonatomic,copy)NSString *kPayEntrySourceFlash;
/// 36. 切换摄像头金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceSwitchCamera;
/// 37. 玩新版老虎机金币不足充值
@property(nonatomic,copy)NSString *kPayEntrySourceJackpotRecharge;
/// 38. 多人连线
@property(nonatomic,copy)NSString *kPayEntrySourceMultipleCall;
/// 39. 索要媒体礼物
@property(nonatomic,copy)NSString *kPayEntrySourceAskFor;
/// 40. 主播守护引起的金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorGuardian;
/// 41. 动态中主动点击礼物弹框getCoin引起悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceMoment;
/// 42. 关闭摄像头金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceCloseCamera;
/// 43. 音转文主动开启金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceVoiceToText;
/// 44. 视频通话中赠送索要礼物金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceVideoAskForGift;
/// 45. im中赠送索要礼物金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceIMAskForGift;
/// 46. 道具背包充值卡
@property(nonatomic,copy)NSString *kPayEntrySourceBackpackRechargeCard;
/// 47. 解锁关注人列表引起的充值
@property(nonatomic,copy)NSString *kPayEntrySourceUnlockFollowers;
/// 48. 解锁访客列表引起的充值
@property(nonatomic,copy)NSString *kPayEntrySourceUnlockVisitors;
/// 49. 访客列表金币不足引起的充值
@property(nonatomic,copy)NSString *kPayEntrySourceVisitorsCall;
/// 50. 关注/被关注列表金币不足引起的充值
@property(nonatomic,copy)NSString *kPayEntrySourceFollowCall;
/// 51. 访客列表进入主播详情金币不足引起的充值
@property(nonatomic,copy)NSString *kPayEntrySourceFollowedList;
/// 52. 主播图片详情页拨打电话金币不足引起的悬浮快捷弹窗
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorProfilePhotoCall;
/// 53. 主播详情查看未解锁图片
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorProfilePhotoUnlock;
/// 54. 主播详情查看未解锁视频
@property(nonatomic,copy)NSString *kPayEntrySourceAnchorProfileVideoUnlock;
/// 55. 通话记录呼叫主播金币不足
@property(nonatomic,copy)NSString *kPayEntrySourceCallsRecord;

@end

NS_ASSUME_NONNULL_END 
