#import "SCPayEntry.h"



@implementation SCPayEntry
static SCPayEntry *_instance = nil;
+ (instancetype)shared {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
}


- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setEntryKeys];
    }
    return self;
}

- (void)setEntryKeys{
    self.kPayEntrySourceMatchTitle = @"match_title";
    self.kPayEntrySourceUserCenter = @"user_center";
    self.kPayEntrySourceMatchFloat = @"match_float";
    self.kPayEntrySourceOnCallRobot = @"oncall_robot";
    self.kPayEntrySourceChattingGift = @"chatting_gift";
    self.kPayEntrySourceChatting = @"chatting";
    self.kPayEntrySourceOnCall = @"oncall";
    self.kPayEntrySourceCall = @"call";
    self.kPayEntrySourcePopNewbee = @"pop_newbee";
    self.kPayEntrySourceLookForTitle = @"look_for_title";
    self.kPayEntrySourceSubscribeMatch = @"subscribe_match";
    self.kPayEntrySourceSubscribeDetail = @"subscribe_detail";
    self.kPayEntrySourceCoinNotEnough = @"coin_not_enough";
    self.kPayEntrySourceSubscribeDialog = @"subscribe_dialog";
    self.kPayEntrySourcePopPromotion = @"pop_promotion";
    self.kPayEntrySourceVipLevelUp = @"vip_level_up";
    self.kPayEntrySourceRechargeLink = @"recharge_link";
    self.kPayEntrySourceAfterFreeCall = @"after_free_call";
    self.kPayEntrySourceIMGift = @"im_gift";
    self.kPayEntrySourceIM = @"im";
    self.kPayEntrySourceAnchorProfileCall = @"anchor_profile_call";
    self.kPayEntrySourceAnchorProfileVideoCall = @"anchor_profile_video_call";
    self.kPayEntrySourceConversation = @"conversation";
    self.kPayEntrySourceVideoflow = @"videoflow";
    self.kPayEntrySourceMatchResultCall = @"match_result_call";
    self.kPayEntrySourceAnchorWall = @"anchorWall";
    self.kPayEntrySourceDetailMedia = @"detail_media";
    self.kPayEntrySourceMatchCoinNotEnough = @"match_coin_not_enough";
    self.kPayEntrySourcePushSpecialOffer = @"push_special_offer";
    self.kPayEntrySourceConsumeUpgradeDialog = @"consume_upgrade_dialog";
    self.kPayEntrySourceIMLimit = @"im_limit";
    self.kPayEntrySourceSlotMachine = @"slot_machine";
    self.kPayEntrySourceMomentGift = @"moment_gift";
    self.kPayEntrySourceTPP = @"tpp";
    self.kPayEntrySourceFlash = @"flash";
    self.kPayEntrySourceSwitchCamera = @"switch_camera";
    self.kPayEntrySourceJackpotRecharge = @"jackpot_recharge";
    self.kPayEntrySourceMultipleCall = @"multiple_call";
    self.kPayEntrySourceAskFor = @"ask_for";
    self.kPayEntrySourceAnchorGuardian = @"anchor_guardian";
    self.kPayEntrySourceMoment = @"moment";
    self.kPayEntrySourceCloseCamera = @"close_camera";
    self.kPayEntrySourceVoiceToText = @"voice_to_text";
    self.kPayEntrySourceVideoAskForGift = @"video_ask_for_gift";
    self.kPayEntrySourceIMAskForGift = @"im_ask_for_gift";
    self.kPayEntrySourceBackpackRechargeCard = @"backpack_recharge_card";
    self.kPayEntrySourceUnlockFollowers = @"unlock_followers";
    self.kPayEntrySourceUnlockVisitors = @"unlock_visitors";
    self.kPayEntrySourceVisitorsCall = @"visitors_call";
    self.kPayEntrySourceFollowCall = @"follow_call";
    self.kPayEntrySourceFollowedList = @"followed_list";
    self.kPayEntrySourceAnchorProfilePhotoCall = @"anchor_profile_photo_call";
    self.kPayEntrySourceAnchorProfilePhotoUnlock = @"anchor_profile_photo_unlock";
    self.kPayEntrySourceAnchorProfileVideoUnlock = @"anchor_profile_video_unlock";
    self.kPayEntrySourceCallsRecord = @"calls_record";
}
@end
