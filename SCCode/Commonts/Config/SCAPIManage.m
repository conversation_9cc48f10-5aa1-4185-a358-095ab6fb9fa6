//
//  SCAPIManage.m
//  Supercall
//
//  Created by j<PERSON><PERSON><PERSON> on 2025/1/16.
//

#import "SCAPIManage.h"

@implementation SCAPIManage
static SCAPIManage *_instance = nil;
+ (instancetype)shared {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
}


- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setApis];
    }
    return self;
}

- (void)setApis{
    
    //AJ
    self.kAdjustToken = @"me8qkzddq2gw";
    self.kAdjustOrderToken = @"pkhzf1";
    self.kAdjustPurchaseToken = @"ak6kwp";
    self.kAdjustLoginToken = @"ak6kwp";
    self.kAdjustRegisterToken = @"8mlxq9";
    
    //k2 k3 k4
    self.kEncryption2 = @"Relaxed";
    self.kEncryption3 = @"Quantitative";
    self.kEncryption4 = @"Printed";
    
    ///登录接口
    self.kSCAPILogin = @"/security/oauth";
    ///退出登录
    self.kSCAPILogout = @"/security/logout";
    ///删除账号
    self.kSCAPIDeleteAccount = @"/user/deleteAccount";
    ///验证Token是否过期
    self.kSCAPIVerifyToken = @"/security/isValidToken";
    ///风控上报
    self.kSCAPIUploadRisk = @"/risk/info/upload";
    /// AF 上传
    self.kSCAPIAFUpload = @"/hit/ascribeRecordReqs";
    //获取App配置
    self.kSCAPIAppConfig = @"/config/getAppConfigPostV2";
    //获取配置
    self.kSCAPIStrategy = @"/config/getStrategyPostV2";
    //获取主播列表数据
    self.kSCAPIGetAnchorList = @"/broadcaster/wall/search";

    //获取多个用户在线状态接口
    self.kSCAPIGetUsersOnlineStatus = @"/user/getUserListOnlineStatusPostV2";

    //获取用户榜
    self.kSCAPIGetUserRank = @"/broadcaster/rank/search";

    //获取主播榜
    self.kSCAPIGetAnchorRank = @"/user/rank/search";
    ///用户详情接口
    self.kSCAPIGetUserInfo = @"/user/getUserInfoPostV2";
    ///获取用户基本信息【目前使用于IM列表】
    self.kSCAPIGetUserBasic = @"/user/getUsernameAndAvatarPostV2";
    //获取主播是否在线
    self.kSCAPIUserOnlineStatus = @"/user/getUserOnlineStatusPostV2";
    ///主播额外信息接口
    self.kSCAPIGetUserExtraInfo = @"/user/getBroadcasterExtraInfoPostV2";

    ///拉黑举报
    self.kSCAPIBlockAndReport = @"/report/complain/insertRecord";
    ///取消拉黑
    self.kSCAPIUnBlock = @"/report/complain/removeBlock";
    ///关注
    self.kSCAPIFollow = @"/user/addFriend";
    ///取消关注
    self.kSCAPIUnFollow = @"/user/unfriend";
    ///关注列表
    self.kSCAPIFollowList = @"/user/getFriendsListPage";
    ///拉黑列表
    self.kSCAPIBlockList = @"/report/complain/blockList";


    ///获取礼物列表
    self.kSCAPIGiftList = @"/gift/v2/listPostV2";
    ///活动礼物
    self.kSCAPIActivityGiftList = @"/gift/activity-only/askList";
    ///获取用户礼物数量
    self.kSCAPIGiftCounts = @"/gift/getGiftCount";
    ///赠送礼物
    self.kSCAPIGiveUserGifts = @"/user/giveUserGifts";

    /// Media 查询
    self.kSCAPIMediaSearch = @"/shortLink/media/searchPostV2";

    ///OSS
    self.kSCAPIGetOSSPolicy = @"/user/oss/policyPostV2";

    //更新头像
    self.kSCAPIUpdateAvatar = @"/user/updateAvatar";

    //更新用户资源
    self.kSCAPIUpdateMedia = @"/user/updateMedia";

    //更新用户信息
    self.kSCAPIUpdateUserInfo = @"/user/saveUserInfo";


    #pragma mark - Banner
    //获取Banner列表
    self.kSCAPIGetBannerInfo = @"/game/banner/info";


    #pragma mark - 金币和购买
    ///商品列表
    self.kSCAPIGoodsList = @"/coin/goods/search";
    ///邀请链接是否有效
    self.kSCAPIInviteRechargeLinkIsValid = @"/coin/recharge/checkBroadcasterInvitationPostV2";
    ///邀请链接获取商品信息
    self.kSCAPIInviteRechargeLinkList = @"/coin/goods/broadcasterInvitation";
    ///促销商品
    self.kSCAPIPromotion = @"/coin/goods/getPromotion";
    ///活动促销
    self.kSCAPIActivityPromotion = @"/coin/goods/getLastSpecialOfferV2";
    ///创建订单
    self.kSCAPICreateOrder = @"/coin/recharge/create";
    /// 消费订单
    self.kSCAPIPaymentIpa = @"/coin/recharge/payment/ipa";
    ///支付渠道
    self.kSCAPIPayChannel = @"/coin/payChannel/getPostV2";
    ///查询当前用户金币数量
    self.kSCAPIMyCoinsNum = @"/user/getUserCoinsPostV2";
    ///注册奖励
    self.kSCAPIRegisterReward = @"/coin/presented/getPostV2";

    #pragma mark - IM
    ///获取融云Token
    self.kSCAPIGetRongCloudToken = @"/user/rongcloud/tokenPostV2";
    ///前后台切换
    self.kSCAPIModeSwitch = @"/user/mode/switch";
    ///上报心跳
    self.kSCAPIHeartbeat = @"/user/activeing";

    #pragma mark - Call

    //创建通话
    self.kSCAPICallCreateChannel = @"/video-call/channel/create";
    //挂断
    self.kSCAPICallHangUp = @"/video-call/hangUp";
    ///接听
    self.kSCAPICallPickUp = @"/video-call/pickUp";
    ///加入频道
    self.kSCAPICallJoinChannel = @"/video-call/channel/join";
    ///更新声网ID 在加入频道后更新
    self.kSCAPICallUpdateAgoraUid = @"/user/updateAgoraUid";
    //获取后置摄像头配置接口
    self.kSCAPICallRealCameraConfig = @"/user/rearCamera/config";
    //打开后置摄像头
    self.kSCAPICallOpenRealCamera = @"/user/rearCamera/open";
    /// 通话结果接口
    self.kSCAPICallGetCallResult = @"/video-call/user/callResult";
    ///主播评价
    self.kSCAPICallAnchorEvaluate = @"/broadcaster/evaluate/submit";

    #pragma mark - 开关设置
    //免打扰接口
    self.kSCAPISwitchNotDisturb = @"/user/switchNotDisturb";
    #pragma mark - 个人等级
    ///获取用户等级
    self.kSCAPIGetUserLevel = @"/user/getLevelInfoPostV2";
    #pragma mark - 强化引导
    ///强化引导
    self.kSCAPIGetStrongGuide = @"/user/strongGuide/config";
    #pragma mark - 客服
    ///客服问题集合
    self.kSCAPIGetFAQ = @"/user/FAQ/get";

    #pragma mark - 快速匹配
    ///随机推荐主播
    self.kSCAPIRandomRecommendAnchor = @"/user/getRandomBroadcasterPostV2";
    ///快速匹配接口
    self.kSCAPIFlashMatch = @"/video-call/flash/chat";
    ///取消快速匹配
    self.kSCAPIFlashMatchCancel = @"/video-call/match/cancel";
    ///轮播话术
    self.kSCAPIFlashChatConfig = @"/config/content/search";
}

@end
