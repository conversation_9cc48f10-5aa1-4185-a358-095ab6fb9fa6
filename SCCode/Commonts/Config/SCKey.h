//
//  SCKey.h
//  Supercall
//  用于声明常规的Key  可以被混淆的
//  Created by guanweihong on 2024/10/26.
//

#ifndef SCKey_h
#define SCKey_h

//注意这里存放的都是可以被混淆的Key值，避免关联
// 例如： 保存数据的Key  , 一些自身业务的Key

#pragma mark - NSUserDefaults key
///登录用户保存Key
#define kSCLoginTokenKey @"k_SC_LOGIN_TOKEN_KEY"
/////保存App配置
//#define kSCAppConfigKey @"k_SC_APP_CONFIG_KEY"
///保存选择的语言
#define kSCLocalLanguageKey @"k_SC_LOCAL_LANGUAGE_KEY"
///翻译后的消息
#define kSCTranslateMessageKey @"k_SC_TRANSLATE_MESSAGE_KEY"
///旧的翻译语言
#define kSCOldTranslateLanguageKey @"k_SC_OLD_TRANSLATE_LANGUAGE_KEY"
///解压资源版本号
#define kSCUnZipResourceVersionKey @"k_SC_UNZIP_RESOURCE_VERSION_KEY"
///是否已经点击过后置摄像头
#define kSCIsClickBackCameraKey @"k_SC_IS_CLICK_BACK_CAMERA_KEY"
///是否从接口下发配置项设置过 Facebook
#define kSCIsSettingFacebook @"k_SC_IS_SETTING_FACEBOOK"
/// Facebook Id
#define kSCFacebookId @"k_SC_FACEBOOK_ID"
/// Facebook Client Token
#define KSCFacebookClientToken @"k_SC_FACEBOOK_CLIENT_TOKEN"

#pragma mark - 通知
///用户信息修改通知
#define kSCChangeUserInfoNoticationKey @"SC_CHANGE_USER_INFO_NOTICATION_KEY"


#pragma mark - 不可自动修改的Key
//Socket前后台切换防抖Key
#define kSCThrottleSocketForegroundChangeKey @"SC_THROTTLE_FOREGROUND_CHANGE_KEY"
//索要礼物，发送礼物防抖Key
#define kSCThrottleSendGiftKey @"SC_THROTTLE_SEND_GIFT_KEY"
//消息列表刷新防抖Key
#define kSCThrottleReloadConversationListKey @"SC_THROTTLE_RELOAD_CONVERSASTION_LIST_KEY"
//主播详情页信息刷新防抖
#define kSCThrottleRefreshAnchorInfoKey @"SC_THROTTLE_REFRESH_ANCHOR_INFO_KEY"

#endif /* SCKey_h */
