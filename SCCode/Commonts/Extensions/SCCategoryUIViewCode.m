//
//  SCCategoryUIViewCode.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCCategoryUIViewCode.h"
#import "MBProgressHUD.h"


#pragma mark - 加载框 和 提示框

@implementation UIView (SCCategoryUIViewCode)
-(void)showLoading{
    [self showLoading:nil];
}
-(void)showLoading:(NSString * _Nullable)text{
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self animated:YES];
    hud.mode = MBProgressHUDModeIndeterminate;
    hud.label.text = text;
    
}
-(void) hiddenLoading{
    void (^handleBlock)(void) = ^{
        [MBProgressHUD hideHUDForView:self animated:true];
    };
    if ([NSThread isMainThread]) {
        handleBlock();
    } else {
        dispatch_async(dispatch_get_main_queue(), handleBlock);
    }
}

-(void) toast:(NSString *)text{
    void (^handleBlock)(void) = ^{
        MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self animated:YES];
        hud.userInteractionEnabled = NO;
        hud.mode = MBProgressHUDModeText;
        hud.label.text = text;
        hud.label.numberOfLines = 0;
        ///两秒后关闭
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [hud hideAnimated:true];
        });
    };
    if ([NSThread isMainThread]) {
        handleBlock();
    } else {
        dispatch_async(dispatch_get_main_queue(), handleBlock);
    }
}
#pragma mark 静态 loading框和提示框

+ (void) showLoading{
    [UIView showLoading:nil];
}
+ (void) showLoading:(NSString * _Nullable)text{
    [kSCCodeMar.rootWindow showLoading:text];
}
+ (void) hiddenLoading{
    [kSCCodeMar.rootWindow hiddenLoading];
}
+ (void) toast:(NSString *)text{
    [kSCCodeMar.rootWindow toast:text];
}
#pragma mark - 获取ViewController
- (UIViewController *)viewController {
    //获取当前view的superView对应的控制器
    UIResponder *next = [self nextResponder];
    do {
        if ([next isKindOfClass:[UIViewController class]]) {
            return (UIViewController *)next;
        }
        next = [next nextResponder];
    } while (next != nil);
    return nil;
}



@end

#pragma mark - 工厂类扩展

@implementation UIView (SCCreaterView)

///根据背景色创建view
+ (UIView *) viewWithBackgroundColor:(UIColor *)color{
    UIView * view = [[UIView alloc] init];
    view.backgroundColor = color;
    return view;
}
//快速圆角
- (void)sc_setCornerRadius:(CGFloat)radius{
    self.layer.cornerRadius = radius;
    self.layer.masksToBounds = true;
}

@end

#pragma mark - 函数式
@implementation UIView (SCViewFunctional)

- (UIView * (^)(CGFloat))setX {
    return ^(CGFloat x) {
        CGRect frame = self.frame;
        frame.origin.x = x;
        self.frame = frame;
        return self;
    };
}

- (UIView * (^)(CGFloat))setY {
    return ^(CGFloat y) {
        CGRect frame = self.frame;
        frame.origin.y = y;
        self.frame = frame;
        return self;
    };
}

- (UIView * (^)(CGFloat))setWidth {
    return ^(CGFloat width) {
        CGRect frame = self.frame;
        frame.size.width = width;
        self.frame = frame;
        return self;
    };
}

- (UIView * (^)(CGFloat))setHeight {
    return ^(CGFloat height) {
        CGRect frame = self.frame;
        frame.size.height = height;
        self.frame = frame;
        return self;
    };
}

- (UIView * (^)(CGPoint))setOrigin {
    return ^(CGPoint origin) {
        CGRect frame = self.frame;
        frame.origin = origin;
        self.frame = frame;
        return self;
    };
}

- (UIView * (^)(CGSize))setSize {
    return ^(CGSize size) {
        CGRect frame = self.frame;
        frame.size = size;
        self.frame = frame;
        return self;
    };
}
- (UIView * (^)(CGFloat))setCenterY {
    return ^(CGFloat y) {
        self.scCenterY = y;
        return self;
    };
}
- (UIView * (^)(CGFloat))setCenterX {
    return ^(CGFloat x) {
        self.scCenterX = x;
        return self;
    };
}

- (UIView * (^)(CGFloat))setCornerRadius {
    return ^(CGFloat cornerRadius) {
        self.layer.cornerRadius = cornerRadius;
        self.layer.masksToBounds = YES;
        return self;
    };
}

- (UIView * (^)(UIColor *))setBackgroundColor {
    return ^(UIColor *color) {
        self.backgroundColor = color;
        return self;
    };
}

- (instancetype (^)(UIView * superView))addSuperView{
    return ^(UIView * superView) {
        [superView addSubview:self];
        return self;
    };
}


@end


@implementation UIView (SCFrame)


- (CGFloat)scTop {
    return self.frame.origin.y;
}

- (void)setScTop:(CGFloat)top {
    CGRect frame = self.frame;
    frame.origin.y = top;
    self.frame = frame;
}

- (CGFloat)scBottom {
    return CGRectGetMaxY(self.frame);
}

- (void)setScBottom:(CGFloat)bottom {
    CGRect frame = self.frame;
    frame.origin.y = bottom - CGRectGetHeight(frame);
    self.frame = frame;
}

- (CGFloat)scLeft {
    return self.frame.origin.x;
}

- (void)setScLeft:(CGFloat)left {
    CGRect frame = self.frame;
    frame.origin.x = left;
    self.frame = frame;
}

- (CGFloat)scRight {
    return CGRectGetMaxX(self.frame);
}

- (void)setScRight:(CGFloat)right {
    CGRect frame = self.frame;
    frame.origin.x = right - CGRectGetWidth(frame);
    self.frame = frame;
}

- (CGFloat)scWidth {
    return CGRectGetWidth(self.frame);
}

- (void)setScWidth:(CGFloat)width {
    CGRect frame = self.frame;
    frame.size.width = width;
    self.frame = frame;
}

- (CGFloat)scHeight {
    return CGRectGetHeight(self.frame);
}

- (void)setScHeight:(CGFloat)height {
    CGRect frame = self.frame;
    frame.size.height = height;
    self.frame = frame;
}
- (CGSize)scSize{
    return self.frame.size;
}

- (void)setScSize:(CGSize)scSize{
    CGRect frame = self.frame;
    frame.size = scSize;
    self.frame = frame;
}

- (CGFloat)scCenterX {
    return self.center.x;
}

- (void)setScCenterX:(CGFloat)centerX {
    self.center = CGPointMake(centerX, self.center.y);
}

- (CGFloat)scCenterY {
    return self.center.y;
}

- (void)setScCenterY:(CGFloat)centerY {
    self.center = CGPointMake(self.center.x, centerY);
}

@end
