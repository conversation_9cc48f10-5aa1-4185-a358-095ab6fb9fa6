//
//  SCCategoryUIImageViewCode.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/25.
//

#import "SCCategoryUIImageViewCode.h"
#import <SDWebImage/SDWebImage.h>

@implementation UIImageView (SCCategoryUIImageViewCode)

-(void) sc_setPlaceholderImage{
    self.setImageName(@"bg_global_image_placeholder");
}

-(void) sc_setImageWithURL:(NSString *)url {
    [self sc_setImageWithURL:url placeholderImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"]];
}

-(void) sc_setImageWithURL:(NSString *)url thumbURL:(NSString *)thumbURL{
    kWeakSelf(self);
    [self sd_setImageWithURL:[NSURL URLWithString:thumbURL] placeholderImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        kStrongSelf;
        if(error == nil){
            [strongSelf sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:image];
        }
    }];
    
}

///封装网络图片加载
-(void) sc_setImageWithURL:(NSString *)url placeholderImage:(UIImage *)placeholder{
    [self sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:placeholder];
}

- (void)sc_setImageWithURL:(NSString *)url completionBlock:(SDExternalCompletionBlock)completedBlock {
    [self sd_setImageWithURL:[NSURL URLWithString:url] placeholderImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"] completed:completedBlock];
}

@end
@implementation UIImageView (SCImageCodeUI)

///创建圆形头像
+(instancetype) imageViewWithRadius:(CGFloat)radius{
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.layer.cornerRadius = radius;
    imageView.layer.masksToBounds = YES;
    return imageView;
}

@end

@implementation UIImageView (SCImageFunction)

- (UIImageView * (^)(UIImage *))setImage {
    return ^(UIImage *image) {
        self.image = image;
        return self;
    };
}
- (UIImageView * _Nonnull (^)(NSString * _Nonnull))setImageName{
    return ^(NSString *imageName) {
        self.image = [SCResourceManager loadImageWithName:imageName];
        return self;
    };
}

- (UIImageView * (^)(UIColor *))setBackgroundColor {
    return ^(UIColor *color) {
        self.backgroundColor = color;
        return self;
    };
}

- (UIImageView * (^)(BOOL))setClipsToBounds {
    return ^(BOOL clipsToBounds) {
        self.clipsToBounds = clipsToBounds;
        return self;
    };
}

- (UIImageView * (^)(CGFloat))setCornerRadius {
    return ^(CGFloat cornerRadius) {
        self.layer.cornerRadius = cornerRadius;
        self.layer.masksToBounds = YES;
        return self;
    };
}
- (UIImageView * (^)(UIViewContentMode))setContentMode {
    return ^(UIViewContentMode contentMode) {
        self.contentMode = contentMode;
        return self;
    };
}
@end
