//
//  SCCategoryUIButtonCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIButton (SCCategoryUIButtonCode)

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundColor:(UIColor * _Nullable)backgroundColor
                 cornerRadius:(CGFloat) cornerRadius;

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundImage:(UIImage * _Nullable)backgroundImage
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action;


+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundColor:(UIColor * _Nullable)backgroundColor
                 cornerRadius:(CGFloat) cornerRadius
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action;
//根据图片创建
+ (UIButton *)buttonWithImageName:(NSString * _Nullable)imageName;
//根据图片创建
+ (UIButton *)buttonWithImage:(UIImage * _Nullable)image;
//根据图片创建
+ (UIButton *)buttonWithImageName:(NSString * _Nullable)imageName
                       target:(id _Nullable)target
                           action:(SEL _Nullable)action;
+ (UIButton *)buttonWithImage:(UIImage * _Nullable)image
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action;

// 创建带渐变背景的按钮
+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
              gradientColors:(NSArray<UIColor *> *)colors
             gradientLocations:(NSArray<NSNumber *> * _Nullable)locations
               gradientStart:(CGPoint)startPoint
                 gradientEnd:(CGPoint)endPoint
                cornerRadius:(CGFloat)cornerRadius
                     target:(id _Nullable)target
                     action:(SEL _Nullable)action;

// 设置按钮渐变背景
- (void)sc_setGradientBackgroundWithColors:(NSArray<UIColor *> *)colors
                         gradientLocations:(NSArray<NSNumber *> * _Nullable)locations
                              startPoint:(CGPoint)startPoint
                                endPoint:(CGPoint)endPoint
                            cornerRadius:(CGFloat)cornerRadius;


@end

NS_ASSUME_NONNULL_END
