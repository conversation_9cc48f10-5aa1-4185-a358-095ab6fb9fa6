//
//  SCCategoryUIImageCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/4.
//

#import <UIKit/UIKit.h>
@class SCGradientColors;
NS_ASSUME_NONNULL_BEGIN
typedef struct __attribute__((objc_boxable)) NS_SWIFT_SENDABLE SCCornerRadius {
    CGFloat topLeft, topRight, bottomLeft, bottomRight;
} SCCornerRadius;
UIKIT_STATIC_INLINE SCCornerRadius SCCornerRadiusMake(CGFloat topLeft, CGFloat topRight, CGFloat bottomLeft, CGFloat bottomRight) {
    SCCornerRadius insets = {topLeft, topRight, bottomLeft, bottomRight};
    return insets;
}
@interface UIImage (SCCategoryUIImageCode)
+(UIImage *)imageWithColor:(UIColor *) color;
+ (UIImage *)imageWithColor:(UIColor *)color imageSize:(CGSize)size;
+ (UIImage *)imageWithColor:(UIColor *)color imageSize:(CGSize)size cornerRadius:(CGFloat)cornerRadius;
///渐变色
+ (UIImage *)imageGradientWithSCGradientColors:(SCGradientColors *)colors size:(CGSize)size;
+ (UIImage *)imagGradientWithSCGradientColors:(SCGradientColors *)colors startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint size:(CGSize)size;
///圆角图片
- (UIImage *)imageWithRadius:(CGFloat)cornerRadius;
/// 图片圆角
- (UIImage *)imageWithRoundedCorners:(SCCornerRadius)cornerRadius;
@end

NS_ASSUME_NONNULL_END
