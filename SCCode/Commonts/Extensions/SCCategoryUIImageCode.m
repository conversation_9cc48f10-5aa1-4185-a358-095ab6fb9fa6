//
//  SCCategoryUIImageCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCCategoryUIImageCode.h"

@implementation UIImage (SCCategoryUIImageCode)
/**
 颜色转
 
 @param color 颜色值
 @return UIImage
 */
+(UIImage *)imageWithColor:(UIColor *) color{
    CGRect rect=CGRectMake(0,0, 1, 1);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *theImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return theImage;
}

+ (UIImage *)imageWithColor:(UIColor *)color imageSize:(CGSize)size {
    CGRect rect=CGRectMake(0,0, size.width, size.height);
    UIGraphicsBeginImageContext(rect.size);
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetFillColorWithColor(context, [color CGColor]);
    CGContextFillRect(context, rect);
    UIImage *theImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return theImage;
}

/**
 图片圆角
 */
+ (UIImage *)imageWithColor:(UIColor *)color imageSize:(CGSize)size cornerRadius:(CGFloat)cornerRadius {
    
    if(size.width == 0 || size.height == 0){
        size = CGSizeMake(10, 10);
    }
    // 1.利用绘图，建立上下文
    UIGraphicsBeginImageContextWithOptions(size, false, 0.0f);
    CGContextRef context = UIGraphicsGetCurrentContext();
    
    CGRect rect = CGRectMake(0, 0, size.width, size.height);
    
    
    // 3.利用 贝塞尔路径 实现 裁切 效果
    [[UIBezierPath bezierPathWithRoundedRect:rect
                                cornerRadius:cornerRadius] addClip];
    
    CGContextSetFillColorWithColor(context, color.CGColor);
    CGContextFillRect(context, rect);
    
    
    // 5.取得结果
    UIImage* result = UIGraphicsGetImageFromCurrentImageContext();
    
    // 6.关闭上下文
    UIGraphicsEndImageContext();
    return result;
}


+ (UIImage *)imageGradientWithSCGradientColors:(SCGradientColors *)colors size:(CGSize)size {
    CGPoint startPoin = CGPointMake(0, 0);
    CGPoint endPoin = CGPointMake(0, 0);
    switch (colors.orientation) {
        case SCGradientColorsOrientationVertical:
            startPoin = CGPointMake(0, 0);
            endPoin = CGPointMake(0, 1);
            break;
        case SCGradientColorsOrientationHorizontal:
            startPoin = CGPointMake(0, 0);
            endPoin = CGPointMake(1, 0);
            break;
        case SCGradientColorsOrientationUpperLeftLowerRight:
            startPoin = CGPointMake(0, 0);
            endPoin = CGPointMake(1, 1);
            break;
        case SCGradientColorsOrientationLowerLeftUpperRight:
            startPoin = CGPointMake(0, 1);
            endPoin = CGPointMake(1, 0);
            break;
        default:
            break;
    }
    
    return [UIImage imagGradientWithSCGradientColors:colors startPoint:startPoin endPoint:endPoin size:size];
}

+ (UIImage *)imagGradientWithSCGradientColors:(SCGradientColors *)colors startPoint:(CGPoint)startPoint endPoint:(CGPoint)endPoint size:(CGSize)size{
    
    NSArray *gradientColors = [colors cgGolors];
    NSUInteger count = [[colors sclocations] count];
    CGFloat gradientLocations[count];
    int i = 0;
    for (NSNumber *num in [colors sclocations]) {
        gradientLocations[i] = [num floatValue];
        i ++;
    }
    
    
    //渐变Layer
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    //设置大小
    gradientLayer.frame = CGRectMake(0, 0, size.width, size.height);
    gradientLayer.startPoint = startPoint;
    gradientLayer.endPoint = endPoint;
    
    //    gradientLayer.cornerRadius = cornerRadius;
    //颜色队列
    
    gradientLayer.colors = gradientColors;
    //开始画图
    UIGraphicsBeginImageContextWithOptions(gradientLayer.frame.size, gradientLayer.opaque, 0);
    [gradientLayer renderInContext:UIGraphicsGetCurrentContext()];
    UIImage *outputImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return outputImage;
    
    
}


- (UIImage *)imageWithRadius:(CGFloat)cornerRadius{
    return [self imageWithRoundedCorners:SCCornerRadiusMake(cornerRadius,cornerRadius,cornerRadius,cornerRadius)];
}
///生成圆角图片
- (UIImage *)imageWithRoundedCorners:(SCCornerRadius)cornerRadius {
    
    CGFloat topLeftRadius = cornerRadius.topLeft;
    CGFloat topRightRadius = cornerRadius.topRight;
    CGFloat bottomLeftRadius = cornerRadius.bottomLeft;
    CGFloat bottomRightRadius = cornerRadius.bottomRight;
    UIGraphicsBeginImageContextWithOptions(self.size, NO, self.scale);
    CGRect imageFrame = CGRectMake(0, 0, self.size.width, self.size.height);
    [[UIBezierPath bezierPathWithRoundedRect:imageFrame byRoundingCorners:UIRectCornerTopLeft cornerRadii:CGSizeMake(topLeftRadius, topLeftRadius)] addClip];
    [[UIBezierPath bezierPathWithRoundedRect:imageFrame byRoundingCorners:UIRectCornerBottomLeft cornerRadii:CGSizeMake(bottomLeftRadius, bottomLeftRadius)] addClip];
    [[UIBezierPath bezierPathWithRoundedRect:imageFrame byRoundingCorners:UIRectCornerTopRight cornerRadii:CGSizeMake(topRightRadius, topRightRadius)] addClip];
    [[UIBezierPath bezierPathWithRoundedRect:imageFrame byRoundingCorners:UIRectCornerBottomRight cornerRadii:CGSizeMake(bottomRightRadius, bottomRightRadius)] addClip];
    [self drawInRect:imageFrame];
    UIImage *roundedImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return roundedImage;
}

@end
