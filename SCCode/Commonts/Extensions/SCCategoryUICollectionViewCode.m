//
//  SCCategoryUICollectionViewCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/2.
//

#import "SCCategoryUICollectionViewCode.h"

@implementation UICollectionView (SCCategoryUICollectionViewCode)

///快速创建
+ (instancetype)collectionViewWithFrame:(CGRect)frame
                                 layout:(UICollectionViewLayout *)layout
                               delegate:(id<UICollectionViewDelegate>)delegate
                             dataSource:(id<UICollectionViewDataSource>)dataSource
{
    return [UICollectionView collectionViewWithFrame:frame layout:layout delegate:delegate dataSource:dataSource cellClass:nil forCellReuseIdentifier:nil];
}

///快速创建
+ (instancetype)collectionViewWithFrame:(CGRect)frame
                                 layout:(UICollectionViewLayout *)layout
                               delegate:(id<UICollectionViewDelegate>)delegate
                             dataSource:(id<UICollectionViewDataSource>)dataSource
                              cellClass:(nullable Class)cellClass
                       forCellReuseIdentifier:(NSString *_Nullable)identifier
{
    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:frame collectionViewLayout:layout];
    collectionView.delegate = delegate;
    collectionView.dataSource = dataSource;
    collectionView.backgroundColor = [UIColor whiteColor];
    collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    if(cellClass != nil && identifier != nil)
        [collectionView registerClass:cellClass forCellWithReuseIdentifier:identifier];
    return collectionView;
}

+ (instancetype) collectionViewWithLayout:(UICollectionViewLayout *)layout{
    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    return collectionView;;
}

@end
@implementation UICollectionView (SCCollectionFunctional)

- (UICollectionView * (^)(id<UICollectionViewDataSource> _Nonnull))setDataSource {
    return ^(id<UICollectionViewDataSource> dataSource) {
        self.dataSource = dataSource;
        return self;
    };
}

- (UICollectionView * (^)(id<UICollectionViewDelegate> _Nonnull))setDelegate {
    return ^(id<UICollectionViewDelegate> delegate) {
        self.delegate = delegate;
        return self;
    };
}
- (UICollectionView * (^)(Class _Nonnull class,NSString * _Nonnull cellIdentifier))registerCell{
    return ^(Class _Nonnull class,NSString * _Nonnull cellIdentifier) {
        [self registerClass:class forCellWithReuseIdentifier:cellIdentifier];
        return self;
    };
}

- (UICollectionView * (^)(UICollectionViewLayout * _Nonnull))setCollectionViewLayout {
    return ^(UICollectionViewLayout *layout) {
        [self setCollectionViewLayout:layout];
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setPagingEnabled {
    return ^(BOOL pagingEnabled) {
        self.pagingEnabled = pagingEnabled;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setScrollEnabled {
    return ^(BOOL scrollEnabled) {
        self.scrollEnabled = scrollEnabled;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setAllowsSelection {
    return ^(BOOL allowsSelection) {
        self.allowsSelection = allowsSelection;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setAllowsMultipleSelection {
    return ^(BOOL allowsMultipleSelection) {
        self.allowsMultipleSelection = allowsMultipleSelection;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setPrefetchingEnabled {
    return ^(BOOL prefetchingEnabled) {
        self.prefetchingEnabled = prefetchingEnabled;
        return self;
    };
}

- (UICollectionView * (^)(UIEdgeInsets))setContentInset {
    return ^(UIEdgeInsets contentInset) {
        self.contentInset = contentInset;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setShowsHorizontalScrollIndicator {
    return ^(BOOL showsHorizontalScrollIndicator) {
        self.showsHorizontalScrollIndicator = showsHorizontalScrollIndicator;
        return self;
    };
}

- (UICollectionView * (^)(BOOL))setShowsVerticalScrollIndicator {
    return ^(BOOL showsVerticalScrollIndicator) {
        self.showsVerticalScrollIndicator = showsVerticalScrollIndicator;
        return self;
    };
}
// 添加其他属性的设置函数

@end
