//
//  SCCategoryUITableViewCode.m
//  Supercall
//
//  Created by guan<PERSON>hong on 2023/12/26.
//

#import "SCCategoryUITableViewCode.h"

@implementation UITableView (SCCategoryUITableViewCode)


+ (instancetype)tableViewWithFrame:(CGRect)frame
                             style:(UITableViewStyle)style
                          delegate:(id<UITableViewDelegate>)delegate
                        dataSource:(id<UITableViewDataSource>)dataSource
                         {
    
    return [UITableView tableViewWithFrame:frame style:style delegate:delegate dataSource:dataSource cellClass:nil forCellReuseIdentifier:nil];
}
+ (instancetype)tableViewWithFrame:(CGRect)frame
                             style:(UITableViewStyle)style
                          delegate:(id<UITableViewDelegate>)delegate
                        dataSource:(id<UITableViewDataSource>)dataSource
                             cellClass:(nullable Class)cellClass forCellReuseIdentifier:(NSString *_Nullable)identifier
{
    UITableView *tableView = [[UITableView alloc] initWithFrame:frame style:style];
    tableView.delegate = delegate;
    tableView.dataSource = dataSource;
    tableView.backgroundColor = [UIColor whiteColor];
    tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    [tableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 0, 0);
    if(cellClass != nil && identifier != nil)
        [tableView registerClass:cellClass forCellReuseIdentifier:identifier];
    return tableView;
}
@end
