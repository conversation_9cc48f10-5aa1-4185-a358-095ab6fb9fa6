//
//  SCCategoryUIFontCode.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCCategoryUIFontCode.h"

@implementation UIFont (SCCategoryUIFontCode)

+ (UIFont *)scLight:(CGFloat)size {
    return [UIFont fontWithName:@"PingFangSC-Light" size:size];
}

+ (UIFont *)scRegular:(CGFloat)size {
    return [UIFont fontWithName:@"PingFangSC-Regular" size:size];
}

+ (UIFont *)scMedium:(CGFloat)size {
    return [UIFont fontWithName:@"PingFangSC-Medium" size:size];
}

+ (UIFont *)scSemibold:(CGFloat)size {
    return [UIFont fontWithName:@"PingFangSC-Semibold" size:size];
}

+ (UIFont *)scBoldFont:(CGFloat)size {
    return [UIFont fontWithName:@"PingFangSC-Bold" size:size];
}

@end
