//
//  SCCategoryNSStringCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCCategoryNSStringCode.h"
#import <CommonCrypto/CommonDigest.h>

@implementation NSString (SCCategoryNSStringCode)
+ (BOOL)isOCEmpty:(NSString *)string{
    BOOL isEmpty = (string == nil || [string isKindOfClass:[NSNull class]] || [string length] == 0 || [[string stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] length] == 0);
    return isEmpty;
}
///字符串截断，超过指定数量的显示...
- (NSString *)stringByTruncatingToLength:(NSUInteger)length {
    if (self.length <= length) {
        return self;
    }
    return [NSString stringWithFormat:@"%@...",[self substringToIndex:length]];
}
///截断10个字符以后的字符串
- (NSString *)replaceMoreThan10 {
    return [self stringByTruncatingToLength:10];
}

- (NSString *)replaceMoreThan8 {
    return [self stringByTruncatingToLength:8];
}

- (CGSize)sizeWithFont:(UIFont *)font maxSize:(CGSize)maxSize {
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGRect textRect = [self boundingRectWithSize:maxSize
                                         options:NSStringDrawingUsesLineFragmentOrigin
                                      attributes:attributes
                                         context:nil];
    return textRect.size;
}

///字符串MD5
- (NSString *)md5String{
    const char *str = [self UTF8String];
     unsigned char result[CC_MD5_DIGEST_LENGTH];
     CC_MD5(str, (CC_LONG)strlen(str), result);
     NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
     for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
         [md5String appendFormat:@"%02x", result[i]];
     }
     return md5String;
}
///使用秒数格式化成 xxHxxmxxs
+(NSString *) hmsWithSeconds:(NSInteger)seconds{
    NSInteger hours = seconds / 3600;
    NSInteger minutes = ((NSInteger)seconds % 3600) / 60;
    NSInteger remainingSeconds = (NSInteger)seconds % 60;

    NSMutableString *formattedString = [NSMutableString string];

    // 判断并拼接小时
    if (hours > 0) {
        [formattedString appendFormat:@"%ldh", (long)hours];
    }

    // 判断并拼接分钟
    if (minutes > 0 || hours > 0) {
        [formattedString appendFormat:@"%ldm", (long)minutes];
    }

    // 拼接秒
    [formattedString appendFormat:@"%lds", (long)remainingSeconds];

    return formattedString;
}

-(NSDictionary *) toScJson{
    NSData *jsonData = [self dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        
        return nil;
    }
    return dic;
}

-(BOOL)isBlank {
    if (self == nil || [self isKindOfClass:[NSNull class]]) {
        return YES;
    }
    
    if ([self isKindOfClass:[NSString class]]) {
        if ([[self stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]] length] == 0) {
            return YES;
        }
    }
    
    return NO;
}

- (NSString *)replaceMoreThan100 {
    if (self.length <= 100) {
        return self;
    }
    return [self substringToIndex:100];
}

@end

@implementation NSString (SCCodeURLEncode)

- (NSString *)urlencode {
    NSCharacterSet *allowedCharacterSet = [NSCharacterSet URLQueryAllowedCharacterSet];
    NSString *encodedString = [self stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacterSet];
    return encodedString;
}

- (NSString *)urldecode {
    NSString *decodedString = [self stringByRemovingPercentEncoding];
    return decodedString;
}



@end


 
@implementation NSString (SCStringEmojin)
 
- (BOOL)isEmoji {
    const unichar high = [self characterAtIndex: 0];
    
    // Surrogate pair (U+1D000-1F77F)
    if (0xd800 <= high && high <= 0xdbff) {
        const unichar low = [self characterAtIndex: 1];
        const int codepoint = ((high - 0xd800) * 0x400) + (low - 0xdc00) + 0x10000;
        
        return (0x1d000 <= codepoint && codepoint <= 0x1f77f);
        
    // Not surrogate pair (U+2100-27BF)
    } else {
        return (0x2100 <= high && high <= 0x27bf);
    }
}

 
- (instancetype)removedLastString {
    NSMutableString* __block buffer = [NSMutableString stringWithCapacity:[self length]];
       
        //是否已经移除过字符串
       __block BOOL removed = NO;
       [self enumerateSubstringsInRange:NSMakeRange(0, [self length])
                                options:NSStringEnumerationByComposedCharacterSequences
                             usingBlock: ^(NSString* substring, NSRange substringRange, NSRange enclosingRange, BOOL* stop) {
           //判断表情是否在最后
           BOOL flag = NO;
           if (substringRange.location + substringRange.length == [self length]) {
               flag = YES;
           }
           if(flag){
               if([substring isEmoji]){
                   //舍弃
                   removed = YES;
               }else{
                   [buffer appendString:substring];
               }
               
           }else{
               [buffer appendString: substring];
           }
           
       }];
    if(!removed){
        //移除最后一个字符
        if(buffer.length > 0)
            [buffer deleteCharactersInRange:NSMakeRange(buffer.length - 1, 1)];
    }
       
       return buffer;
    
}
 
@end
