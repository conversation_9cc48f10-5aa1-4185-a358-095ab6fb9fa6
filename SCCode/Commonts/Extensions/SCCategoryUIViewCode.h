//
//  SCCategoryUIViewCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
#pragma mark - 加载框 和 提示框
@interface UIView (SCCategoryUIViewCode)
-(void)showLoading;
-(void)showLoading:(NSString * _Nullable)text;
-(void) hiddenLoading;
-(void) toast:(NSString *_Nullable)text;
+ (void) showLoading;
+ (void) showLoading:(NSString *_Nullable)text;
+ (void) hiddenLoading;
+ (void) toast:(NSString *_Nullable)text;

#pragma mark - 获取ViewController
- (UIViewController *)viewController;
@end


#pragma mark - 工厂类扩展
@interface UIView (SCCreaterView)

@end

#pragma mark - 函数式
@interface UIView (SCViewFunctional)
- (UIView *(^)(CGFloat))setX;
- (UIView * (^)(CGFloat))setY;
- (UIView * (^)(CGFloat))setWidth;
- (UIView * (^)(CGFloat))setHeight;
- (UIView * (^)(CGPoint))setOrigin;
- (UIView * (^)(CGSize))setSize;
- (UIView * (^)(CGFloat))setCenterY;
- (UIView * (^)(CGFloat))setCenterX;
- (UIView * (^)(CGFloat))setCornerRadius;
- (UIView * (^)(UIColor *))setBackgroundColor;
- (instancetype (^)(UIView * superView))addSuperView;

@end

@interface UIView (SCFrame)

@property (nonatomic) CGFloat scTop;
@property (nonatomic) CGFloat scBottom;
@property (nonatomic) CGFloat scLeft;
@property (nonatomic) CGFloat scRight;
@property (nonatomic) CGFloat scWidth;
@property (nonatomic) CGFloat scHeight;
@property (nonatomic) CGSize scSize;
@property (nonatomic) CGFloat scCenterX;
@property (nonatomic) CGFloat scCenterY;


@end

NS_ASSUME_NONNULL_END
