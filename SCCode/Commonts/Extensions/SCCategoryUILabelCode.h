//
//  SCCategoryUILabelCode.h
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/4.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UILabel (SCCategoryUILabelCode)
+(instancetype)labelWithTextColor:(UIColor *)textColor font:(UIFont *)font;
+(instancetype)labelWithText:(NSString *)text textColor:(UIColor *)textColor font:(UIFont *)font;
+(instancetype)labelWithText:(NSString *)text textColor:(UIColor *)textColor font:(UIFont *)font alignment:(NSTextAlignment)alignment;


+(CGSize)labelSizeWithText:(NSString *)text font:(UIFont *)font maxSize:(CGSize)maxSize ;
@end


@interface UILabel (SCLBFunctional)

- (UILabel * (^)(NSString *))setText;
- (UILabel * (^)(UIFont *))setFont;
- (UILabel * (^)(CGFloat))setFontLightSize;
- (UILabel * (^)(CGFloat))setFontRegularSize;
- (UILabel * (^)(CGFloat))setFontMediumSize;
- (UILabel * (^)(CGFloat))setFontSemiboldSize;
- (UILabel * (^)(CGFloat))setFontBoldSize;

- (UILabel * (^)(UIColor *))setTextColor;
- (UILabel * (^)(NSTextAlignment))setTextAlignment;
- (UILabel * (^)(NSInteger))setNumberLines;

@end
NS_ASSUME_NONNULL_END
