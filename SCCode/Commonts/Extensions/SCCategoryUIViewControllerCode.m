//
//  SCCategoryUIViewControllerCode.m
//  Supercall
//
//  Created by guanweihong on 2024/1/25.
//

#import "SCCategoryUIViewControllerCode.h"

@implementation UIViewController (SCCategoryUIViewControllerCode)


+ (UIViewController *)currentViewController {
    UIViewController *currentViewController = nil;
    UIWindow *keyWindow = kSCKeyWindow;

    if (keyWindow.windowLevel == UIWindowLevelNormal) {
        UIViewController *rootViewController = keyWindow.rootViewController;
        
        if ([rootViewController isKindOfClass:[UITabBarController class]]) {
            UITabBarController *tabBarController = (UITabBarController *)rootViewController;
            currentViewController = tabBarController.selectedViewController;
        } else if ([rootViewController isKindOfClass:[UINavigationController class]]) {
            UINavigationController *navigationController = (UINavigationController *)rootViewController;
            currentViewController = navigationController.visibleViewController;
        } else {
            currentViewController = rootViewController;
        }
        
        while (currentViewController.presentedViewController != nil && !currentViewController.presentedViewController.isBeingDismissed) {
            currentViewController = currentViewController.presentedViewController;
        }
    }
    
    return currentViewController;
}
// 递归关闭present的视图控制器
- (void)dismissPresentedViewControllersWithCompletion:(void (^)(void))completion  {
    
    if (self.presentedViewController) {
        [self.presentedViewController dismissPresentedViewControllersWithCompletion:^{
            [self.presentedViewController dismissViewControllerAnimated:NO completion:^{
                completion();
            }];
        }];
        
    }else{
        completion();
    }
}

@end
