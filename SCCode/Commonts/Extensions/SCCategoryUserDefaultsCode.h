//
//  SCCategoryUserDefaultsCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>


@interface NSUserDefaults (SCCategoryUserDefaultsCode)
//+(BOOL) scSetObject:(NSObject * _Nullable)obj forKey:( NSString * _Nonnull )key;
//+(nullable id) scObjforKey:( NSString * _Nonnull )key modelClass:(Class _Nonnull)modelClass;
@end
#pragma mark - 宏
#define kSCUD [NSUserDefaults standardUserDefaults]
#define kSCUDSet(value, key) [[NSUserDefaults standardUserDefaults] setObject:value forKey:key]
#define kSCUDGet(key) [[NSUserDefaults standardUserDefaults] objectForKey:key]
#define kSCUDRemove(key) [[NSUserDefaults standardUserDefaults] removeObjectForKey:key]
#define kSCUDSynchronize [[NSUserDefaults standardUserDefaults] synchronize]
