//
//  NSString+SCCode.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (SCCategoryNSStringCode)

+ (BOOL)isOCEmpty:(NSString *)string;

- (NSString *)stringByTruncatingToLength:(NSUInteger)length;
///截断10个字符以后的字符串
- (NSString *)replaceMoreThan10;
- (NSString *)replaceMoreThan8;
- (CGSize)sizeWithFont:(UIFont *)font maxSize:(CGSize)maxSize ;

///字符串MD5
- (NSString *)md5String;
///使用秒数格式化成 xxHxxmxxs
+(NSString *) hmsWithSeconds:(NSInteger)seconds;

-(NSDictionary * _Nullable) toScJson;
/// 是否为空 nil 或者“”
-(BOOL)isBlank;
- (NSString *)replaceMoreThan100;
@end

@interface NSString (SCCodeURLEncode)

- (NSString *)urlencode;
- (NSString *)urldecode;

@end

@interface NSString (SCStringEmojin)

- (BOOL)isEmoji;

- (instancetype)removedLastString;

@end

NS_ASSUME_NONNULL_END
