//
//  SCCategoryUILabelCode.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/4.
//

#import "SCCategoryUILabelCode.h"

@implementation UILabel (SCCategoryUILabelCode)

+(instancetype)labelWithTextColor:(UIColor *)textColor font:(UIFont *)font{
    return [self labelWithText:@"" textColor:textColor font:font];
}
+(instancetype)labelWithText:(NSString *)text textColor:(UIColor *)textColor font:(UIFont *)font{
    return [self labelWithText:text textColor:textColor font:font alignment:NSTextAlignmentLeft];
}

+(instancetype)labelWithText:(NSString *)text textColor:(UIColor *)textColor font:(UIFont *)font alignment:(NSTextAlignment)alignment {
    UILabel *labeCool = [[self alloc] init];
    labeCool.text = text;
    labeCool.textAlignment = alignment;
    labeCool.textColor = textColor;
    labeCool.font = font;
    labeCool.numberOfLines = 0;
    return labeCool;
}

//计算文本宽高
+(CGSize)labelSizeWithText:(NSString *)text font:(UIFont *)font maxSize:(CGSize)maxSize {
    NSDictionary *attributes = @{NSFontAttributeName: font};
    CGRect rect = [text boundingRectWithSize:maxSize options:NSStringDrawingUsesLineFragmentOrigin attributes:attributes context:nil];
    return rect.size;
}


@end
@implementation UILabel (SCLBFunctional)

- (UILabel * (^)(NSString *))setText {
    return ^(NSString *text) {
        self.text = text;
        return self;
    };
}

- (UILabel * (^)(UIFont *))setFont {
    return ^(UIFont *font) {
        self.font = font;
        return self;
    };
}

- (UILabel * (^)(CGFloat))setFontLightSize {
    return ^(CGFloat fontSize) {
        self.setFont(kScUIFontLight(fontSize));
        return self;
    };
}
- (UILabel * (^)(CGFloat))setFontRegularSize {
    return ^(CGFloat fontSize) {
        self.setFont(kScUIFontRegular(fontSize));
        return self;
    };
}
- (UILabel * (^)(CGFloat))setFontMediumSize {
    return ^(CGFloat fontSize) {
        self.setFont(kScUIFontMedium(fontSize));
        return self;
    };
}
- (UILabel * (^)(CGFloat))setFontSemiboldSize {
    return ^(CGFloat fontSize) {
        self.setFont(kScUIFontSemibold(fontSize));
        return self;
    };
}

- (UILabel * (^)(CGFloat))setFontBoldSize {
    return ^(CGFloat fontSize) {
        self.setFont(kScUIFontBoldFont(fontSize));
        return self;
    };
}

- (UILabel * (^)(UIColor *))setTextColor {
    return ^(UIColor *color) {
        self.textColor = color;
        return self;
    };
}

- (UILabel * (^)(NSTextAlignment))setTextAlignment {
    return ^(NSTextAlignment alignment) {
        self.textAlignment = alignment;
        return self;
    };
}

- (UILabel * (^)(NSInteger))setNumberLines {
    return ^(NSInteger lines) {
        self.numberOfLines = lines;
        return self;
    };
}


@end
