//
//  SCCategoryUIButtonCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCCategoryUIButtonCode.h"
#import "SCCategoryUIImageCode.h"
///封装UIButton 快速创建的方法
@implementation UIButton (SCCategoryUIButtonCode)

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundColor:(UIColor * _Nullable)backgroundColor
                 cornerRadius:(CGFloat) cornerRadius{
    
    return [UIButton buttonWithTitle:title titleColor:titleColor font:font image:image backgroundColor:backgroundColor cornerRadius:cornerRadius target:nil action:nil];
}

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundColor:(UIColor * _Nullable)backgroundColor
                 cornerRadius:(CGFloat) cornerRadius
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action {
    UIImage *bgIV = nil;
    if(backgroundColor){
        bgIV = [[UIImage imageWithColor:backgroundColor imageSize:CGSizeMake(cornerRadius*2, cornerRadius*2) cornerRadius:cornerRadius] resizableImageWithCapInsets:UIEdgeInsetsMake(cornerRadius, cornerRadius, cornerRadius, cornerRadius) resizingMode:UIImageResizingModeStretch];
    }
    return [UIButton buttonWithTitle:title titleColor:titleColor font:font image:image backgroundImage:bgIV target:target action:action];
}

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
               backgroundImage:(UIImage * _Nullable)backgroundImage
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:titleColor forState:UIControlStateNormal];
    button.titleLabel.font = font;
    [button setImage:image forState:UIControlStateNormal];
    [button setBackgroundImage:backgroundImage forState:UIControlStateNormal];
    [button addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
    return button;
}


//根据图片创建
+ (UIButton *)buttonWithImageName:(NSString * _Nullable)imageName{
    return [UIButton buttonWithImage:[SCResourceManager loadImageWithName:imageName]];
}
//根据图片创建
+ (UIButton *)buttonWithImage:(UIImage * _Nullable)image{
    return [UIButton buttonWithImage:image target:nil action:nil];
}

//根据图片创建
+ (UIButton *)buttonWithImageName:(NSString * _Nullable)imageName
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action {
    return [UIButton buttonWithImage:[SCResourceManager loadImageWithName:imageName] target:target action:action];
}

//根据图片创建
+ (UIButton *)buttonWithImage:(UIImage * _Nullable)image
                       target:(id _Nullable)target
                       action:(SEL _Nullable)action {
    return [UIButton buttonWithTitle:nil titleColor:nil font:nil image:image backgroundImage:nil target:target action:action];
}

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
              gradientColors:(NSArray<UIColor *> *)colors
             gradientLocations:(NSArray<NSNumber *> * _Nullable)locations
               gradientStart:(CGPoint)startPoint
                 gradientEnd:(CGPoint)endPoint
                cornerRadius:(CGFloat)cornerRadius
                     target:(id _Nullable)target
                     action:(SEL _Nullable)action {
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:title forState:UIControlStateNormal];
    [button setTitleColor:titleColor forState:UIControlStateNormal];
    button.titleLabel.font = font;
    [button setImage:image forState:UIControlStateNormal];
    [button addTarget:target action:action forControlEvents:UIControlEventTouchUpInside];
    
    // 设置渐变背景
    [button sc_setGradientBackgroundWithColors:colors
                            gradientLocations:locations
                                  startPoint:startPoint
                                    endPoint:endPoint
                                cornerRadius:cornerRadius];
    
    return button;
}

- (void)sc_setGradientBackgroundWithColors:(NSArray<UIColor *> *)colors
                         gradientLocations:(NSArray<NSNumber *> * _Nullable)locations
                              startPoint:(CGPoint)startPoint
                                endPoint:(CGPoint)endPoint
                            cornerRadius:(CGFloat)cornerRadius {
    
    // 创建渐变图片
    CGFloat size = MAX(cornerRadius * 2, 1.0);
    
    // 创建渐变层
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.frame = CGRectMake(0, 0, size, size);
    
    // 设置渐变色
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in colors) {
        [cgColors addObject:(id)color.CGColor];
    }
    gradientLayer.colors = cgColors;
    
    // 设置渐变位置
    if (locations) {
        gradientLayer.locations = locations;
    }
    
    // 设置渐变方向
    gradientLayer.startPoint = startPoint;
    gradientLayer.endPoint = endPoint;
    
    // 使用 UIGraphicsImageRenderer 创建图片
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:gradientLayer.frame.size];
    UIImage *gradientImage = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull rendererContext) {
        [gradientLayer renderInContext:rendererContext.CGContext];
    }];
    
    // 创建可拉伸的背景图片（不设置圆角）
    UIImage *stretchableImage = [gradientImage resizableImageWithCapInsets:UIEdgeInsetsMake(0, 0, 0, 0)
                                                            resizingMode:UIImageResizingModeStretch];
    
    // 设置按钮背景图片
    [self setBackgroundImage:stretchableImage forState:UIControlStateNormal];
    
    // 设置按钮圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
}

+ (UIButton *)buttonWithTitle:(NSString * _Nullable)title
                   titleColor:(UIColor * _Nullable)titleColor
                         font:(UIFont * _Nullable)font
                        image:(UIImage * _Nullable)image
              gradientColors:(NSArray<UIColor *> *)colors
               gradientStart:(CGPoint)startPoint
                 gradientEnd:(CGPoint)endPoint
                cornerRadius:(CGFloat)cornerRadius
                     target:(id _Nullable)target
                     action:(SEL _Nullable)action {
    
    return [self buttonWithTitle:title
                     titleColor:titleColor
                          font:font
                         image:image
                gradientColors:colors
             gradientLocations:nil
                 gradientStart:startPoint
                   gradientEnd:endPoint
                  cornerRadius:cornerRadius
                       target:target
                       action:action];
}

- (void)sc_setGradientBackgroundWithColors:(NSArray<UIColor *> *)colors
                              startPoint:(CGPoint)startPoint
                                endPoint:(CGPoint)endPoint
                            cornerRadius:(CGFloat)cornerRadius {
    
    [self sc_setGradientBackgroundWithColors:colors
                          gradientLocations:nil
                               startPoint:startPoint
                                 endPoint:endPoint
                             cornerRadius:cornerRadius];
}

@end
