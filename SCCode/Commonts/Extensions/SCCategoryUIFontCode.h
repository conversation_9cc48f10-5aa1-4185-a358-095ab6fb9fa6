//
//  SCCategoryUIFontCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <UIKit/UIKit.h>



NS_ASSUME_NONNULL_BEGIN

@interface UIFont (SCCategoryUIFontCode)
+ (U<PERSON><PERSON> *)scLight:(CGFloat)size;
+ (UIFont *)scRegular:(CGFloat)size;
+ (UIFont *)scMedium:(CGFloat)size;
+ (UIFont *)scSemibold:(CGFloat)size;
+ (UIFont *)scBoldFont:(CGFloat)size;
@end

NS_ASSUME_NONNULL_END

#pragma mark - 宏定义

#define kScUIFontLight(size) [UIFont scLight:size]
#define kScUIFontRegular(size) [UIFont scRegular:size]
#define kScUIFontMedium(size) [UIFont scMedium:size]
#define kScUIFontSemibold(size) [UIFont scSemibold:size]
#define kSc<PERSON><PERSON>ontBoldFont(size) [UIFont scBoldFont:size]
