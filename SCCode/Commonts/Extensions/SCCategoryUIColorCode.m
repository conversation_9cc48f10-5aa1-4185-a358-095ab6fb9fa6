//
//  SCCategoryUIColorCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCCategoryUIColorCode.h"

@implementation UIColor (SCCategoryUIColorCode)



+ (UIColor *)colorWithHexString:(NSString *)hexString {
    NSString *colorString = [hexString stringByReplacingOccurrencesOfString:@"#" withString:@""];
    NSUInteger length = colorString.length;
    
    unsigned int hexValue = 0;
    NSScanner *scanner = [NSScanner scannerWithString:colorString];
    [scanner scanHexInt:&hexValue];
    
    CGFloat red, green, blue, alpha;
    
    if (length == 6) { // HEX字符串
        red = ((hexValue & 0xFF0000) >> 16)/255.0;
        green = ((hexValue & 0x00FF00) >> 8)/255.0;
        blue = (hexValue & 0x0000FF)/255.0;
        alpha = 1.0;
    } else if (length == 8) { // AHEX字符串
        alpha  = ((hexValue & 0xFF000000) >> 24)/255.0;
        red = ((hexValue & 0x00FF0000) >> 16)/255.0;
        green  = ((hexValue & 0x0000FF00) >> 8)/255.0;
        blue = (hexValue & 0x000000FF)/255.0;
    } else {
        return nil; // 非法字符串
    }
    
    return [UIColor colorWithRed:red green:green blue:blue alpha:alpha];
}


@end
