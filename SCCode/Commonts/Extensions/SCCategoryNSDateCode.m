//
//  SCCategoryNSDateCode.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCCategoryNSDateCode.h"
#import "SCLanguageManager.h"

@implementation NSDate (SCCategoryNSDateCode)

+(NSDate *) dataWithYYMMDDString:(NSString *)dateString;{
    return [self dataWithDateString:dateString dateFormat:@"yyyy-MM-dd"];
}

+(NSDate *) dataWithDateString:(NSString *)dateString dateFormat:(NSString *)dateFormat{
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    //使用应用内选择的语言
    NSString *selectedLanguageCode = [SCLanguageManager getDeviceLanguage];
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:selectedLanguageCode];
    formatter.locale = locale;

    [formatter setDateFormat:dateFormat];
    return [formatter dateFromString:dateString];
}

///Date转yyyy-MM-dd
- (NSString *)stringWithYYMMDD{
    return [self stringWithDateFormat:@"yyyy-MM-dd"];
}

///Date转String
- (NSString *)stringWithDateFormat:(NSString *)dateFormat{
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    //使用应用内选择的语言
    NSString *selectedLanguageCode = [SCLanguageManager getDeviceLanguage];
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:selectedLanguageCode];
    formatter.locale = locale;
    
    [formatter setDateFormat:dateFormat];
    return [formatter stringFromDate:self];
}

///获取100年前的当天时间
+ (NSDate *)dateWithNumYearsAgo:(int)numYearsAgo{
    NSDate *date = [NSDate date];
    NSDateComponents *comps = [[NSDateComponents alloc] init];
    [comps setYear:-numYearsAgo];
    NSCalendar *calendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
    
    NSString *selectedLanguageCode = [SCLanguageManager getDeviceLanguage];
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:selectedLanguageCode];
    calendar.locale = locale;
    
    NSDate *date100YearsAgo = [calendar dateByAddingComponents:comps toDate:date options:0];
    return date100YearsAgo;
}

///格式化为消息显示的时间格式
- (NSString *)formattedMessageTime {
    NSCalendar *calendar = [NSCalendar currentCalendar];
    //使用应用内选择的语言
//    NSString *selectedLanguageCode = [SCLanguageManager getDeviceLanguage];
//    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:selectedLanguageCode];
//    calendar.locale = locale;
    
    NSDateComponents *selfComponents = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:self];
    NSDateComponents *nowComponents = [calendar components:NSCalendarUnitYear|NSCalendarUnitMonth|NSCalendarUnitDay fromDate:[NSDate date]];
    
    if (selfComponents.year != nowComponents.year) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
//        formatter.locale = locale;
        formatter.dateFormat = @"yyyy-MM-dd HH:mm";
        return [formatter stringFromDate:self];
    } else if (selfComponents.month != nowComponents.month || selfComponents.day != nowComponents.day) {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        formatter.dateFormat = @"MM-dd HH:mm";
//        formatter.locale = locale;
        return [formatter stringFromDate:self];
    } else {
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
//        formatter.locale = locale;
        formatter.dateFormat = @"HH:mm";
        return [formatter stringFromDate:self];
    }
}

///格式化为相对时间（now, xx m ago, xx h ago）
- (NSString *)formattedTimeAgo {
    NSTimeInterval timeInterval = -[self timeIntervalSinceNow];
    
    if (timeInterval < 60) { // 小于1分钟
        return @"now";
    }
    
    if (timeInterval < 3600) { // 小于1小时
        int minutes = (int)timeInterval / 60;
        return [NSString stringWithFormat:@"%d m ago", minutes];
    }
    
    if (timeInterval < 86400) { // 小于1天
        int hours = (int)timeInterval / 3600;
        return [NSString stringWithFormat:@"%d h ago", hours];
    }
    
    // 如果超过1天，返回正常的日期格式
    return [self formattedMessageTime];
}

@end
