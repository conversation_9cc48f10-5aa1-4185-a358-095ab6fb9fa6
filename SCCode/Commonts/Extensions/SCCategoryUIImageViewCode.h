//
//  SCCategoryUIImageViewCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import <UIKit/UIKit.h>
#import <SDWebImage/SDWebImage.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIImageView (SCCategoryUIImageViewCode)
-(void) sc_setPlaceholderImage;
-(void) sc_setImageWithURL:(NSString *)url;
-(void) sc_setImageWithURL:(NSString *)url thumbURL:(NSString *)thumbURL;
-(void) sc_setImageWithURL:(NSString *)url placeholderImage:(UIImage *)placeholder;
-(void) sc_setImageWithURL:(NSString *)url completionBlock:(nullable SDExternalCompletionBlock)completedBlock;

@end

@interface UIImageView (SCImageCodeUI)

///创建圆形头像
+(instancetype) imageViewWithRadius:(CGFloat)radius;

@end

@interface UIImageView (SCImageFunction)

- (UIImageView * (^)(UIImage *))setImage;
- (UIImageView * _Nonnull (^)(NSString * _Nonnull))setImageName;
- (UIImageView * (^)(UIViewContentMode))setContentMode;
- (UIImageView * (^)(UIColor *))setBackgroundColor;
- (UIImageView * (^)(BOOL))setClipsToBounds;
- (UIImageView * (^)(CGFloat))setCornerRadius;

@end

NS_ASSUME_NONNULL_END
