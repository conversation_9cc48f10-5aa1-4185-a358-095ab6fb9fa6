//
//  SCCategoryNSDateCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSDate (SCCategoryNSDateCode)
+(NSDate *) dataWithYYMMDDString:(NSString *)dateString;
+(NSDate *) dataWithDateString:(NSString *)dateString dateFormat:(NSString *)dateFormat;

- (NSString *)stringWithYYMMDD;
- (NSString *)stringWithDateFormat:(NSString *)dateFormat;

///获取100年前的当天时间
+ (NSDate *)dateWithNumYearsAgo:(int)numYearsAgo;

///格式化为消息显示的时间格式
- (NSString *)formattedMessageTime;

///格式化为相对时间（now, xx m ago, xx h ago）
- (NSString *)formattedTimeAgo;
@end

NS_ASSUME_NONNULL_END
