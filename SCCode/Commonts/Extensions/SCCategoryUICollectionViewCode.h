//
//  SCCategoryUICollectionViewCode.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/2.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UICollectionView (SCCategoryUICollectionViewCode)
///快速创建
+ (instancetype)collectionViewWithFrame:(CGRect)frame
                                 layout:(UICollectionViewLayout *)layout
                               delegate:(id<UICollectionViewDelegate>)delegate
                             dataSource:(id<UICollectionViewDataSource>)dataSource;

///快速创建
+ (instancetype)collectionViewWithFrame:(CGRect)frame
                                 layout:(UICollectionViewLayout *)layout
                               delegate:(id<UICollectionViewDelegate>)delegate
                             dataSource:(id<UICollectionViewDataSource>)dataSource
                              cellClass:(nullable Class)cellClass
                 forCellReuseIdentifier:(NSString *_Nullable)identifier;

+ (instancetype) collectionViewWithLayout:(UICollectionViewLayout *)layout;
@end

@interface UICollectionView (SCCollectionFunctional)


- (UICollectionView * (^)(id<UICollectionViewDataSource> _Nonnull))setDataSource;

- (UICollectionView * (^)(id<UICollectionViewDelegate> _Nonnull))setDelegate;

- (UICollectionView * (^)(Class _Nonnull class,NSString * _Nonnull cellIdentifier))registerCell;

- (UICollectionView * (^)(UICollectionViewLayout * _Nonnull))setCollectionViewLayout;

- (UICollectionView * (^)(BOOL))setPagingEnabled;

- (UICollectionView * (^)(BOOL))setScrollEnabled;

- (UICollectionView * (^)(BOOL))setAllowsSelection;

- (UICollectionView * (^)(BOOL))setAllowsMultipleSelection;

- (UICollectionView * (^)(BOOL))setPrefetchingEnabled;

- (UICollectionView * (^)(UIEdgeInsets))setContentInset;
- (UICollectionView * (^)(BOOL))setShowsHorizontalScrollIndicator;
- (UICollectionView * (^)(BOOL))setShowsVerticalScrollIndicator;
@end


NS_ASSUME_NONNULL_END
