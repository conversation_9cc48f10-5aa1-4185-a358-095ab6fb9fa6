//
//  SCCategoryNSNumberCode.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCCategoryNSNumberCode.h"

@implementation NSNumber (SCCategoryNSNumberCode)

///秒数 转 HH:mm:ss
-(NSString *) toHHMMSS{
    NSInteger seconds = [self integerValue];
    NSInteger hour = seconds/3600;
    NSInteger minute = (seconds - hour*3600)/60;
    NSInteger second = seconds - hour*3600 - minute*60;
    return [NSString stringWithFormat:@"%02ld:%02ld:%02ld",(long)hour,(long)minute,(long)second];
}

@end
