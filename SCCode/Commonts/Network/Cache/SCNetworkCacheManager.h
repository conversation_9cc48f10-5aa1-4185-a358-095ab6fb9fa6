//
//  SCNetworkCacheManager.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/8.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCNetworkCacheManager : NSObject

+ (instancetype)sharedManager ;
- (void)cacheData:(NSData *)data forKey:(NSString *)key;
-(void) cacheObj:(id) obj forKey:(NSString *)key;
- (NSData *)getCachedDataForKey:(NSString *)key ;

//根据 用户ID,url,参数 生成key
+ (NSString *)cacheKeyWithUserId:(NSString *)userId url:(NSString *)url params:(NSDictionary *)params;

@end

NS_ASSUME_NONNULL_END
