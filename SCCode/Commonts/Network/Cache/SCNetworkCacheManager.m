//
//  SCNetworkCacheManager.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/8.
//

#import "SCNetworkCacheManager.h"
#import "SCDataConverter.h"

@interface SCNetworkCacheManager ()
@property (nonatomic, strong) NSCache *memoryCache;
@end

@implementation SCNetworkCacheManager


+ (instancetype)sharedManager {
    static SCNetworkCacheManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[SCNetworkCacheManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _memoryCache = [[NSCache alloc] init];
        _memoryCache.totalCostLimit = 10 * 1024 * 1024; // 设置内存缓存的限制为10MB
    }
    return self;
}

-(void) cacheObj:(id) obj forKey:(NSString *)key {
    // 这个方法现在已经不推荐使用，建议直接使用cacheData:forKey:
    // 如果obj是字典，转换为JSON数据进行缓存
    NSData *data = nil;
    if ([obj isKindOfClass:[NSDictionary class]] || [obj isKindOfClass:[NSArray class]]) {
        data = [SCDataConverter JSONDataFromObject:obj];
    }
    if (data) {
        [self cacheData:data forKey:key];
    }
}

- (void)cacheData:(NSData *)data forKey:(NSString *)key {
    //默认缓存2天
    [self cacheData:data forKey:key expirationInterval:60*60*24*2];
}

- (void)cacheData:(NSData *)data forKey:(NSString *)key expirationInterval:(NSTimeInterval)expirationInterval {
    if (data && key) {
        // 存储数据到内存缓存
        [self.memoryCache setObject:data forKey:key];
        
        // 存储数据到磁盘
        NSString *cachePath = [self cachePathForKey:key];
        [data writeToFile:cachePath atomically:YES];
        
        // 保存缓存的有效期
        NSDictionary *expirationInfo = @{@"expirationInterval": @(expirationInterval)};
        [[NSUserDefaults standardUserDefaults] setObject:expirationInfo forKey:key];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}


- (NSData *)getCachedDataForKey:(NSString *)key {
    // 尝试从内存缓存中获取数据
    NSData *memoryCachedData = [self.memoryCache objectForKey:key];
    if (memoryCachedData) {
        return memoryCachedData;
    }
    
    // 尝试从磁盘中获取数据
    if (key && ![self isCacheExpiredForKey:key expirationInterval:[self expirationIntervalForKey:key]]) {
        NSString *cachePath = [self cachePathForKey:key];
        return [NSData dataWithContentsOfFile:cachePath];
    }
    return nil;
}

- (BOOL)isCacheExpiredForKey:(NSString *)key expirationInterval:(NSTimeInterval)expirationInterval {
    NSDictionary *expirationInfo = [[NSUserDefaults standardUserDefaults] objectForKey:key];
    if (expirationInfo) {
        NSTimeInterval cachedTimeInterval = [[expirationInfo objectForKey:@"expirationInterval"] doubleValue];
        NSTimeInterval currentTimeInterval = [[NSDate date] timeIntervalSince1970];
        if (cachedTimeInterval > 0 && currentTimeInterval - cachedTimeInterval > expirationInterval) {
            // 缓存已过期
            return YES;
        }
    }
    return NO;
}

- (NSTimeInterval)expirationIntervalForKey:(NSString *)key {
    // 这里可以根据实际需求返回对应的缓存有效期，例如根据不同的API接口设置不同的有效期
    return 60*60*24*2; // 默认缓存有效期为1小时
}
///缓存地址
- (NSString *)cachePathForKey:(NSString *)key {
    NSString *cacheDirectory = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES) firstObject];
    NSString *cachePath = [cacheDirectory stringByAppendingPathComponent:key];
    return cachePath;
}

//根据 用户ID,url,参数 生成key
+ (NSString *)cacheKeyWithUserId:(NSString *)userId url:(NSString *)url params:(NSDictionary *)params {
    //params 根据key 排序 然后key=value&拼接
    NSMutableString *paramsString = [NSMutableString string];
    if(params != nil && params.count > 0){
        NSArray *keys = [params allKeys];
        NSArray *sortedKeys = [keys sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
            return [obj1 compare:obj2 options:NSNumericSearch];
        }];
        
        for (NSString *key in sortedKeys) {
            [paramsString appendFormat:@"%@=%@&", key, [params objectForKey:key]];
        }
    }
    NSString *cacheKey = [NSString stringWithFormat:@"%@#%@#%@", userId, url, paramsString];
    return cacheKey;
}

@end
