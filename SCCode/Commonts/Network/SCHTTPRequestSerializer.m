//
//  SCHTTPRequestSerializer.m
//  Supercall
//
//  Created by j<PERSON><PERSON><PERSON> on 2024/10/24.
//

#import "SCHTTPRequestSerializer.h"

#import "SCCryptoUtils.h"
#import "SCAuthManager.h"
//#import "SCAppConfigModel.h"
@implementation SCHTTPRequestSerializer

- (nullable NSMutableURLRequest *)requestWithMethod:(NSString *)method
                                          URLString:(NSString *)URLString
                                         parameters:(nullable id)parameters
                                              error:(NSError * _Nullable __autoreleasing *)error{
    
    NSMutableURLRequest *request = [super requestWithMethod:method URLString:URLString parameters:parameters error:error];
    
    NSMutableDictionary *newParameters = [[NSMutableDictionary alloc] init];
    if (parameters) {
        [newParameters addEntriesFromDictionary:parameters];
    }
    newParameters[@"http_headers"] = self.headers;
    
    if ([URLString containsString:[SCAPIManage shared].kSCAPIAppConfig]) {
        NSString *key = [SCHTTPRequestSerializer getAppEncryptKey];
        [self prepareRequest:request parameters:newParameters key:key];
    }else {
        
        // 从配置字典中获取加密密钥
        NSString *key = [SCDictionaryHelper configItemDataFromDict:kScAuthMar.appConfig itemName:SCDictionaryKeys.shared.kSCAppConfigEncryptKeyKey];
        if (![key isEqualToString:@""]) {
            [self prepareRequest:request parameters:newParameters key:key];
        }
    }
    
    return request;
}

- (void)prepareRequest:(NSMutableURLRequest *)request parameters:(NSDictionary *)newParameters key:(NSString *)key {
    NSError *error = nil;

    // 将字典序列化为 JSON 数据
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:newParameters options:0 error:&error];

    if (!error && jsonData) {
        // 将 JSON 数据转换为字符串
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];

//        key = @"test-app.mikome.xyz0000000000000";
//        jsonString = @"{\"http_headers\":{\"device-id\":\"6E8C8C2E-94AE-4194-8F48-ECD7AABE0997\",\"platform\":\"iOS\",\"model\":\"arm64\",\"pkg\":\"com.testovo.ios\",\"lang\":\"zh-tw\",\"device_country\":\"CN\",\"ver\":\"1.0\",\"sys_lan\":\"zh-Hans\",\"time_zone\":\"Asia\\/Shanghai\",\"device_lang\":\"zh-Hans\",\"platform_ver\":\"17.5\",\"is_anchor\":\"false\",\"utm-source\":\"\"}}";
        // 使用 AES 加密
        NSString *aesStr = [SCCryptoUtils encryptECB:jsonString key:key];

        // 将加密后的字符串转换为 NSData
        if (aesStr) {
            request.HTTPBody = [aesStr dataUsingEncoding:NSUTF8StringEncoding];
        }
    } else {
        
    }
}

+ (NSString *)getAppEncryptKey {
    NSString *apiHostURL = kSCCodeMar.apiHost; // 替换为实际的 APIHOSTURL
    NSURL *url = [NSURL URLWithString:apiHostURL];
    NSString *host = url.host ? : @"";
    
    // 判断 host 是否超过32位
    if (host.length < 32) {
        // 填充0达到32位
        return [host stringByPaddingToLength:32 withString:@"0" startingAtIndex:0];
    } else {
        // 截取前32位
        return [host substringToIndex:32];
    }
}


@end
