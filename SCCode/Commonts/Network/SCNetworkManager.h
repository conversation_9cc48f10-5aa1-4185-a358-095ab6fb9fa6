//
//  SCNetworkManager.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
@class SCXErrorModel,SCProgress;
typedef NS_ENUM(NSInteger, SCNetMethod) {
        SCNetMethodGET,
        SCNetMethodPOST,
        SCNetMethodPOSTFORM,
};

///缓存策略

typedef NS_ENUM(NSInteger, SCNetCachePolicy) {
    //不缓存数据
    SCNetCachePolicyNotCache,
    //只读取缓存，如果有缓存就不去请求数据
    SCNetCachePolicyOnlyCache,
    //优先获取缓存数据，并且刷新接口
    SCNetCachePolicyCacheAndRefresh,
    //注意可能会导致请求回调两次 缓存和接口请求都回调
    SCNetCachePolicyCacheAndRefreshCallback,
};
@interface SCNetworkManager : NSObject

+(NSDictionary<NSString *,NSString *> *_Nonnull) baseHeader;

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure;

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
                 token:(NSString * _Nullable)token
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure;

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
           cachePolicy:(SCNetCachePolicy)cachePolicy
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure;

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
                 token:(NSString * _Nullable)token
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
           cachePolicy:(SCNetCachePolicy)cachePolicy
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure;

//图片上传
+ (NSURLSessionDataTask *_Nonnull)uploadMultipleWithURL:(NSString *_Nonnull)urlString
                                      imageData:(NSData *_Nonnull)imageData
                                       fileName:(NSString *_Nonnull)fileName
                   parameters:(NSDictionary *_Nullable)parameters
                      headers:(NSDictionary *_Nullable)headers
               uploadProgress:( void (^_Nullable)(SCProgress * _Nonnull uploadProgress)) uploadProgressBlock
                      success:(void (^_Nullable)(id _Nullable responseObject))success
                                        failure:(void (^_Nullable)(SCXErrorModel * _Nullable error))failure;
@end

