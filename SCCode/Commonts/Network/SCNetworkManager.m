//
//  SCNetworkManager.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCNetworkManager.h"
#import <AFNetworking/AFNetworking.h>
#import "SCXErrorModel.h"
#import "SCNetWorkResponseModel.h"
#import "SCNetworkConstant.h"
#import "SCAppUtils.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

#import "SCAuthManager.h"
// #import "SCTokenModel.h" // 已移除，使用字典替代
#import "SCProgress.h"
#import "SCNetworkCacheManager.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
// #import "SCAppConfigModel.h" // 已迁移到字典
#import "SCHTTPRequestSerializer.h"
#import "SCCryptoUtils.h"
#import "SCSafetyUtils.h"
#import "SCTrackingUtils.h"
#import "SCLanguageManager.h"
#import "SCThrottle.h"
#import "SCAPIManage.h"
static AFHTTPSessionManager *_session;
@implementation SCNetworkManager

+(AFHTTPSessionManager *) session{
    if(_session == nil){
        _session = [AFHTTPSessionManager manager];
        
        _session.requestSerializer = [AFHTTPRequestSerializer serializer];
        _session.requestSerializer.timeoutInterval = 30.f;
        
        
        _session.responseSerializer = [AFHTTPResponseSerializer serializer];
    }
    return _session;
}

+(NSDictionary<NSString *,NSString *> *) baseHeader{
    return @{
        @"ver": [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"], // app版本
        @"device-id": SCAppUtils.identifier, // 设备唯一标识
        @"model": [SCSafetyUtils getDeviceModel], // 设备型号
        @"lang": [SCLanguageManager getDeviceLanguage], // 系统语言
        @"sys_lan": [SCAppUtils getCountryLang] ?: @"en",
        @"is_anchor": @"false",
        @"pkg": [[NSBundle mainBundle] bundleIdentifier],
        @"platform": @"iOS",
        @"platform_ver": [[UIDevice currentDevice] systemVersion],
        @"device_lang": [SCAppUtils getCountryLang],
        @"device_country": [SCAppUtils getCountryCode],
        @"time_zone": [SCSafetyUtils getTimeZone]
    };
}

+(NSMutableDictionary *)handleHeaderWithHeader:(NSDictionary *)headers{
    // 设置请求头
    NSMutableDictionary *newHeader = @{}.mutableCopy;
    if(headers){
        newHeader = headers.mutableCopy;
    }else{
        newHeader = @{}.mutableCopy;
    }
    
    NSDictionary<NSString*,NSString *> * baseHeader = [SCNetworkManager baseHeader];
    for (NSString *key in baseHeader) {
        newHeader[key] = baseHeader[key];
    }
    
    // 从配置字典中获取融云区域代码
    NSString *rcAreaCode = [SCDictionaryHelper rcAreaCodeFromConfigDict:kScAuthMar.appConfig];
    if (!rcAreaCode.isBlank) {
        newHeader[@"rc_type"] = rcAreaCode;
    }
    
    if([kScAuthMar isLogin]){
        NSString *token = [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];
        newHeader[@"Authorization"] = [NSString stringWithFormat:@"Bearer %@",token];
    }
    
    NSDictionary *afHeaders = SCTrackingUtils.shared.appsFlyerHeaders;
    if (afHeaders.count > 0) {
        [newHeader addEntriesFromDictionary:afHeaders];
    }
    
    return newHeader;
}

+ (NSURLSessionDataTask *)uploadMultipleWithURL:(NSString *)urlString
                                      imageData:(NSData *)imageData
                                       fileName:(NSString *)fileName
                                     parameters:(NSDictionary *)parameters
                                        headers:(NSDictionary *)headers
                                 uploadProgress:(nullable void (^)(SCProgress *uploadProgress)) uploadProgressBlock
                                        success:(void (^)(id responseObject))success
                                        failure:(void (^)(SCXErrorModel * error))failure{
    if(![urlString hasPrefix:@"http"]){
        urlString = [NSString stringWithFormat:@"%@%@",kSCCodeMar.apiHost,urlString];
    }
    AFHTTPSessionManager * session = [SCNetworkManager session];
    session.responseSerializer = [AFJSONResponseSerializer serializer];
    NSMutableDictionary *newHeader = [SCNetworkManager handleHeaderWithHeader:headers];
#ifdef DEBUG
    
    
    
    
#endif
    NSURLSessionDataTask *task;
    NSString *token = [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];
    NSString  *requestToken = [NSString stringWithFormat:@"Bearer %@",token];
    
    task = [session POST:urlString parameters:parameters headers:newHeader constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        [formData appendPartWithFileData:imageData name:@"file" fileName:fileName mimeType:@"image/jpeg"];
    } progress:^(NSProgress * _Nonnull uploadProgress) {
        
        SCProgress *progress = [[SCProgress alloc] init];
        progress.total = uploadProgress.totalUnitCount;
        progress.progress = uploadProgress.completedUnitCount;
        kSCBlockExeNotNil(uploadProgressBlock,progress);
    } success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        [SCNetworkManager handlewithUrl:urlString requestToken:requestToken response:responseObject task:task cachePolicy:SCNetCachePolicyNotCache cacheKey:nil success:success failure:failure];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [SCNetworkManager handleToken:requestToken error:[[NSError alloc] initWithDomain:@"Please check your network connection".translateString code:SCNetworkResponstCodeUnknownError userInfo:@{}] task:task failure:failure];
    }];
    return task;
}

+ (void)requestWithURL:(NSString *)urlString
                method:(SCNetMethod )method
            parameters:(NSDictionary *)parameters
               headers:(NSDictionary *)headers
               success:(void (^)(id responseObject))success
               failure:(void (^)(SCXErrorModel * error))failure {
    [SCNetworkManager requestWithURL:urlString method:method token:nil parameters:parameters headers:headers cachePolicy:SCNetCachePolicyNotCache success:success failure:failure];
}

+ (void)requestWithURL:(NSString *)urlString
                method:(SCNetMethod )method
                 token:(NSString *)token
            parameters:(NSDictionary *)parameters
               headers:(NSDictionary *)headers
               success:(void (^)(id responseObject))success
               failure:(void (^)(SCXErrorModel * error))failure {
    [SCNetworkManager requestWithURL:urlString method:method token:token parameters:parameters headers:headers cachePolicy:SCNetCachePolicyNotCache success:success failure:failure];
}

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
           cachePolicy:(SCNetCachePolicy)cachePolicy
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure {
    [SCNetworkManager requestWithURL:urlString method:method token:nil parameters:parameters headers:headers cachePolicy:cachePolicy success:success failure:failure];
}

+ (void)requestWithURL:(NSString * _Nonnull)urlString
                method:(SCNetMethod )method
                 token:(NSString * _Nullable)token
            parameters:(NSDictionary * _Nullable)parameters
               headers:(NSDictionary * _Nullable)headers
           cachePolicy:(SCNetCachePolicy)cachePolicy
               success:(void (^ _Nullable)(id _Nonnull responseObject))success
               failure:(void (^ _Nullable)(SCXErrorModel * _Nonnull error))failure {
    urlString = [NSString stringWithFormat:@"%@%@",kSCCodeMar.apiHost,urlString];
    AFHTTPSessionManager * session = [SCNetworkManager session];
    //    session.responseSerializer = [AFJSONResponseSerializer serializer];
    NSMutableDictionary *newHeader = [SCNetworkManager handleHeaderWithHeader:headers];
    if (!kSCIsStrEmpty(token)) {
        newHeader[@"Authorization"] = [NSString stringWithFormat:@"Bearer %@", token];
    }
#ifdef DEBUG
    
    
    
    
#endif
    NSString *cacheKey ;
    if(method == SCNetMethodGET || method == SCNetMethodPOST){
        if(cachePolicy != SCNetCachePolicyNotCache){
            cacheKey = [SCNetworkCacheManager cacheKeyWithUserId:kSCCurrentUserID url:urlString params:parameters];
            NSData *cacheData = [[SCNetworkCacheManager sharedManager] getCachedDataForKey:cacheKey];
            if(cacheData){
                NSDictionary *responseDict = [SCDataConverter dictionaryFromJSONData:cacheData];
                if(responseDict){
                    NSInteger code = [SCDictionaryHelper integerFromDictionary:responseDict forKey:SCDictionaryKeys.shared.kSCResponseCodeKey defaultValue:0];
                    if(code == SCNetworkResponstCodeSuccess || code == SCNetworkResponstCodeCallSuccess){
                        id data = [SCDictionaryHelper dictionaryFromDictionary:responseDict forKey:SCDictionaryKeys.shared.kSCResponseDataKey defaultValue:nil];
                        if(data != nil){
                            kSCBlockExeNotNil(success,data);
                            ///如果为只读缓存，那么需要中断下一步
                            if(cachePolicy == SCNetCachePolicyOnlyCache)
                                return;
                            //只有 需要缓存的并且不为 SCNetCachePolicyCacheAndRefreshCallback 的才清空接口访问回调
                            if(cachePolicy != SCNetCachePolicyCacheAndRefreshCallback)
                                success = nil;
                        }
                    }
                }
            }
        }
    }
    
    SCHTTPRequestSerializer *requestSerializer =[SCHTTPRequestSerializer serializer];
    requestSerializer.headers = newHeader;
    session.requestSerializer = requestSerializer;
    session.responseSerializer = [AFHTTPResponseSerializer serializer];
    NSMutableURLRequest *request = [requestSerializer requestWithMethod:@"POST" URLString:urlString parameters:parameters error:nil];
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:@"application/json" forHTTPHeaderField:@"Accept"];

    NSURLSessionDataTask *task = nil;
    
    NSString  *requestToken = [NSString stringWithFormat:@"Bearer %@",[SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""]];
    if(!kSCIsStrEmpty(token)){
        requestToken = [NSString stringWithFormat:@"Bearer %@", token];
    }
    
    task = [session dataTaskWithRequest:request uploadProgress:nil downloadProgress:nil completionHandler:^(NSURLResponse * _Nonnull response, id  _Nullable responseObject, NSError * _Nullable error) {
        if (error) {
            [SCNetworkManager handleToken:requestToken error:[[NSError alloc] initWithDomain:@"Please check your network connection".translateString code:SCNetworkResponstCodeUnknownError userInfo:@{}] task:task failure:failure];
        } else {
            [SCNetworkManager handlewithUrl:urlString requestToken:requestToken response:responseObject task:task cachePolicy:cachePolicy cacheKey:cacheKey  success:success failure:failure];
        }
    }];
    [task resume];
}

+ (void) handlewithUrl:(NSString *)url requestToken:(NSString *)requestToken response:(nullable id)responseObject task:(nonnull NSURLSessionDataTask *)task cachePolicy:(SCNetCachePolicy)cachePolicy cacheKey:(NSString *)cacheKey success:(void (^)(id responseObject))success failure:(void (^)( SCXErrorModel * _Nonnull ))failure{
    NSDictionary *newResponseObject = nil;
    if ([responseObject isKindOfClass:[NSDictionary class]]) {
        newResponseObject = responseObject;
    }else {
        NSData *newData = [SCNetworkManager preprocessUrl:url data:responseObject];
        NSError *jsonError = nil;
        newResponseObject = [NSJSONSerialization JSONObjectWithData:newData options:0 error:&jsonError];
    }
    
    
    
    // 直接使用字典处理响应数据
    NSDictionary *responseDict = [SCDataConverter safeDictionaryFromResponseObject:newResponseObject];
    if(responseDict){
        NSInteger code = [SCDictionaryHelper integerFromDictionary:responseDict forKey:SCDictionaryKeys.shared.kSCResponseCodeKey defaultValue:0];
        if(code == SCNetworkResponstCodeSuccess || code == SCNetworkResponstCodeCallSuccess){
            id data = responseDict[SCDictionaryKeys.shared.kSCResponseDataKey];
            if(data != nil ){
                if(cacheKey != SCNetCachePolicyNotCache){
                    SCNetworkCacheManager * cacheManager = [SCNetworkCacheManager sharedManager];
                    // 缓存整个响应字典
                    NSData *cacheData = [SCDataConverter JSONDataFromDictionary:responseDict];
                    if(cacheData){
                        [cacheManager cacheData:cacheData forKey:cacheKey];
                    }
                }

                if(success){
                    success(data);
                }

#ifdef DEBUG
                
                
                
#endif
                return;

            }
            //JSON解析失败
            [SCNetworkManager handleToken:requestToken error:[[NSError alloc] initWithDomain:@"" code:SCNetworkResponstCodejsonParsingError userInfo:@{}] task:task failure:failure];
        }else{
            NSString *msg = [SCDictionaryHelper stringFromDictionary:responseDict forKey:SCDictionaryKeys.shared.kSCResponseMessageKey defaultValue:@""];
            [SCNetworkManager handleToken:requestToken error:[[NSError alloc] initWithDomain:msg code:code userInfo:@{}] task:task failure:failure];
        }
    }else{
        //响应数据解析失败
        [SCNetworkManager handleToken:requestToken error:[[NSError alloc] initWithDomain:@"Response parsing failed" code:SCNetworkResponstCodejsonParsingError userInfo:@{}] task:task failure:failure];
    }
    
    
}
+ (void) handleToken:(NSString *)requestToken error:(nonnull NSError *  )error task:(nonnull NSURLSessionDataTask *)task failure:(nonnull void (^)( SCXErrorModel * _Nonnull ))failure{
#ifdef DEBUG
    
    
#endif
    if(error.code == SCNetworkResponstCodeNotToken ||
       error.code == SCNetworkResponstCodeFailToken ||
       error.code == SCNetworkResponstCodeOtherLogin ||
       error.code == SCNetworkResponstCodeValidToken ||
       error.code == SCNetworkResponstCodeExpiredToken ||
       error.code == SCNetworkResponstCodeBlock){
        NSString *currentToken = [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];
        if ([requestToken isEqualToString:[NSString stringWithFormat:@"Bearer %@",currentToken]] &&
            (![currentToken isEqualToString:@""] || currentToken != nil)) {
            
            //Token失效返回登录页面
            [kScAuthMar doLogoutNotRequest:nil];
            //主线程执行
            dispatch_async(dispatch_get_main_queue(), ^{
                [SCThrottle throttleWithTag:@"token_expired" onExecute:^{
                    //Toast Token失效
                    [kSCKeyWindow toast:@"Token expired, login again".translateString];
                }];
                
            });
        }
        
        
    }
    SCXErrorModel * errorModel = [[SCXErrorModel alloc] initWithCode:error.code msg:error.domain userInfo:@{}];
    
    if(failure){
        failure(errorModel);
    }
    
}

+ (NSData *)preprocessUrl:(NSString *)url data:(NSData *)data {
    NSMutableDictionary *responseMap = [NSMutableDictionary dictionary];
    NSString *responseString = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    
    
    
    // 假设 url 是一个 NSString 变量
    if ([url containsString:[SCAPIManage shared].kSCAPIAppConfig]) {
        NSString *key = [SCHTTPRequestSerializer getAppEncryptKey];
        // 解密 AppConfig
        NSString *jsonString = [SCCryptoUtils decryptECB:responseString key:key];
        
        
        if (jsonString) {
            // 解析 JSON
            NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
            NSError *jsonError = nil;
            NSDictionary *map = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&jsonError];
            
            if (!jsonError && [map isKindOfClass:[NSDictionary class]]) {
                responseMap = [map mutableCopy];
                
                NSDictionary *dataDict = map[@"data"];
                if ([dataDict isKindOfClass:[NSDictionary class]]) {
                    NSString *key = [NSString stringWithFormat:@"%@%@", [SCNetworkManager decodeBase64:dataDict[SCAPIManage.shared.kEncryption2] ?: @""], [SCNetworkManager decodeBase64:dataDict[SCAPIManage.shared.kEncryption3] ?: @""]];
                    
                    NSString *k4 = [SCCryptoUtils decryptECB:[SCNetworkManager decodeBase64:dataDict[SCAPIManage.shared.kEncryption4] ?: @""] key:key];
                    if (k4) {
                        
                        NSData *k4Data = [k4 dataUsingEncoding:NSUTF8StringEncoding];
                        NSDictionary *k4Map = [NSJSONSerialization JSONObjectWithData:k4Data options:0 error:nil];
                        if (k4Map) {
                            responseMap[@"data"] = k4Map;
                        }
                    }
                }
            }
        }
        
    } else {
        // 判断 responseString 是否为 JSON 格式
        // 从配置字典中获取加密密钥
        NSString *encryptKey = [SCDictionaryHelper configItemDataFromDict:kScAuthMar.appConfig itemName:SCDictionaryKeys.shared.kSCAppConfigEncryptKeyKey];
        NSString *jsonString = [SCCryptoUtils decryptECB:responseString key:encryptKey];
        if (!jsonString) {
            return data;
        }
        
        
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        NSError *jsonError = nil;
        NSDictionary *map = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&jsonError];
        
        if (!jsonError && [map isKindOfClass:[NSDictionary class]]) {
            responseMap = [map mutableCopy];
        }
    }
    
    if (responseMap.count > 0) {
        // responseMap 转 JSON 然后转 Data
        NSError *error = nil;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:responseMap options:0 error:&error];
        if (!error) {
            return jsonData;
        }
    }
    
    return data;
}

+ (NSString *)decodeBase64:(NSString *)text {
    NSData *data = [[NSData alloc] initWithBase64EncodedString:text options:NSDataBase64DecodingIgnoreUnknownCharacters];
    if (data) {
        return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    }
    return nil;
}

@end
