#import "SCNetworkStatusManager.h"
#import "AFNetworkReachabilityManager.h"

@interface SCNetworkStatusManager()

@property (nonatomic, assign) BOOL isNetworkReachable;

@end

@implementation SCNetworkStatusManager

+ (instancetype)shared {
    static SCNetworkStatusManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

- (instancetype)init {
    if (self = [super init]) {
        _isNetworkReachable = YES;
    }
    return self;
}

- (void)startMonitoring {
    [[AFNetworkReachabilityManager sharedManager] startMonitoring];
    
    kWeakSelf(self);
    [[AFNetworkReachabilityManager sharedManager] setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        weakself.isNetworkReachable = (status != AFNetworkReachabilityStatusNotReachable);
    }];
}

- (void)stopMonitoring {
    [[AFNetworkReachabilityManager sharedManager] stopMonitoring];
}

- (void)dealloc {
    [self stopMonitoring];
}

@end 
