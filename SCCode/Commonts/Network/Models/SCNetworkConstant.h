//
//  SCConstant.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>


@interface SCNetworkConstant : NSObject

@end

typedef NS_ENUM(NSInteger, SCNetworkResponstCode) {
    SCNetworkResponstCodeSuccess = 200,
    SCNetworkResponstCodeCallSuccess = 0,
    SCNetworkResponstCodeUnknownError = -1,
    SCNetworkResponstCodejsonParsingError = -3,
    SCNetworkResponstCodeTimeOut = -2,
    SCNetworkResponstCodeExpiredToken = 10010303,
    SCNetworkResponstCodeValidToken = 100103,
    SCNetworkResponstCodeNotToken = 10010301,
    SCNetworkResponstCodeFailToken = 10010302,
    SCNetworkResponstCodeOtherLogin = 10010304,
    SCNetworkResponstCodeBlock = 10010003,
    //表示没有数据，属于正常
    SCNetworkResponstCodeNoData = 1,
};
