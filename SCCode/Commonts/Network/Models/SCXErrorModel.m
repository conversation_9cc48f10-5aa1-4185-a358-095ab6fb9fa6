//
//  SCError.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCXErrorModel.h"
#import "SCNetworkConstant.h"

@interface SCXErrorModel()

@end

@implementation SCXErrorModel
#pragma mark 构造方法
- (nullable instancetype)initWitMsg:(nullable NSString *)msg{
    self = [super init];
    if(self){
        [self _initWithCode:SCNetworkResponstCodeUnknownError msg:msg userInfo:nil];
    }
    return self;
}
- (nullable instancetype)initWithCode:(NSInteger)code msg:(nullable NSString *)msg{
    self = [super init];
    if(self){
        [self _initWithCode:code msg:msg userInfo:nil];
    }
    return self;
}
- (nullable instancetype)initWithCode:(NSInteger)code msg:(nullable NSString *)msg userInfo:(nullable NSDictionary*)userInfo{
    self = [super init];
    if(self){
        [self _initWithCode:code msg:msg userInfo:userInfo];
    }
    return self;
}
-(void) _initWithCode:(NSInteger)code msg:(nullable NSString *)msg userInfo:(nullable NSDictionary*)userInfo{
    _code = code;
    _msg = msg;
    _userInfo = userInfo;
}

+ (nonnull instancetype)errorWith:(nullable NSError *)error{
    if(error == nil){
        return [[SCXErrorModel alloc] initWithCode:error.code msg:error.domain userInfo:error.userInfo];
    }else{
        return [[SCXErrorModel alloc] initWithCode:error.code msg:error.domain userInfo:error.userInfo];
    }

}

@end
