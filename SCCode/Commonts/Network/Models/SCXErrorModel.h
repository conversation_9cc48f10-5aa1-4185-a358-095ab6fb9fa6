//
//  SCError.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
#import "SCNetworkConstant.h"


@interface SCXErrorModel : NSObject
//
@property(nonatomic,readonly) NSInteger code;
@property(nonatomic,readonly,nullable) NSString * msg;
@property(nonatomic,readonly,nullable) NSDictionary *userInfo;

- (nullable instancetype)initWitMsg:(nullable NSString *)msg;
- (nullable instancetype)initWithCode:(NSInteger)code msg:(nullable NSString *)msg;
- (nullable instancetype)initWithCode:(NSInteger)code msg:(nullable NSString *)msg userInfo:(nullable NSDictionary*)userInfo;

+ (nonnull instancetype)errorWith:(nullable NSError *)error;

@end

#define kTokenExpiredError [[SCXErrorModel alloc] initWitMsg:@"Token expired, log in again"]
#define kLoginFailError [[SCXErrorModel alloc] initWitMsg:@"Login Fail"]
