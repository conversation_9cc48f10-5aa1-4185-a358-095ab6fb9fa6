//
//  SCCodeBlockDefine.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/7.
//

#ifndef SCCodeBlockDefine_h
#define SCCodeBlockDefine_h


#pragma mark - Block 弱引用
#define kWeakSelf(type) __weak typeof(type) weak##type = type;
#define kStrongSelf __strong typeof(weakself) strongSelf = weakself;

#pragma mark - 闭包相关
///执行闭包判断是否为空
#define kSCBlockExeNotNil(block, ...) \
do { \
    if (block) { \
        block(__VA_ARGS__); \
    } \
} while (0)

#pragma mark - 判断类
///判断是否空字符串
#define kSCIsStrEmpty(string) ([NSString isOCEmpty:string])


#pragma mark - 事件
///添加 tap
#define kSCAddTapGesture(tagView, tag,tagSelector) \
    [tagView setUserInteractionEnabled:YES]; \
    [tagView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:tag action:@selector(tagSelector)]];


#endif /* SCCodeBlockDefine_h */
