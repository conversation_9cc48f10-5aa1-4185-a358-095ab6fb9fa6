//
//  SCCodeLayoutDefine.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/7.
//

#ifndef SCCodeLayoutDefine_h
#define SCCodeLayoutDefine_h

//屏幕宽度高度
#define kSCScreenWidth [UIScreen mainScreen].bounds.size.width
///屏幕高度
#define kSCScreenHeight [UIScreen mainScreen].bounds.size.height
//相对宽度高度
///相对宽度
#define kSCScaleWidth(width) (width / 375.0) * kSCScreenWidth
///相对高度
#define kSCScaleHeight(height) (height / 812.0) * kSCScreenHeight

///安全区高度
#define kSCSafeAreaTopHeight \
^CGFloat() { \
    UIWindow *window = [[[UIApplication sharedApplication] windows] firstObject]; \
    return window.safeAreaInsets.top; \
}()
///状态栏高度
#define kSCStatusBarHeight \
^CGFloat() { \
    UIWindow *window = UIApplication.sharedApplication.windows.firstObject;\
    UIWindowScene *windowScene = window.windowScene;\
    CGRect statusBarFrame = windowScene.statusBarManager.statusBarFrame;\
    return CGRectGetHeight(statusBarFrame); \
}()
///底部安全区高度
#define kSCSafeAreaBottomHeight \
^CGFloat() { \
    UIWindow *window = [[[UIApplication sharedApplication] windows] firstObject]; \
    return window.safeAreaInsets.bottom; \
}()
// tabBar高度
#define kSCTabBarHeight 49.f
//底部导航栏全部高度
#define kSCTabBarFullHeight 49.f+kSCSafeAreaBottomHeight
//顶部导航栏高度
#define kSCNavBarHeight 44.f
#define kSCNavBarFullHeight 44.f + kSCSafeAreaTopHeight


#endif /* SCCodeLayoutDefine_h */
