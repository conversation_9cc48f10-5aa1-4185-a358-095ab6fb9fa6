//
//  SCBaseView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/9.
//

#import <UIKit/UIKit.h>
@class SCBaseView;
NS_ASSUME_NONNULL_BEGIN
@protocol SCBaseViewDelegate <NSObject>
/**
 view 事件回调

 @param view 视图
 @param info 数据信息
 */
-(void)scBaseView:(SCBaseView *)view callBackInfo:(id)info;

@end
@interface SCBaseView : UIView

@property(nonatomic, weak) id<SCBaseViewDelegate> scDelegate;
@property(nonatomic, copy) void (^scEventBlock) (id);

- (void)initData;

- (void)initUI;

/**
 触发事件回调
 
 @param info 数据信息
 */
-(void)eventCallBackWithInfo:(id)info;


@end

NS_ASSUME_NONNULL_END
