//
//  SCBaseTableViewCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import <UIKit/UIKit.h>
@class SCBaseTableViewCell;
NS_ASSUME_NONNULL_BEGIN
@protocol SCBaseTableViewCellDelegate <NSObject>
/**
 cell事件回调
 */
-(void)scCell:(SCBaseTableViewCell *)cell eventInfo:(id)info;

@end

@interface SCBaseTableViewCell : UITableViewCell
/**
 代理
 */
@property(nonatomic, weak) id<SCBaseTableViewCellDelegate> theDelegate;
+ (NSString *)registeredIdentifier;
/**
 初始化
 */
+ (instancetype)initWithFormTableView:(UITableView *)tableView;

//配置默认数据
- (void)initData;
//创建子视图
- (void)initUI;
///更新布局
-(void) updateLayouts;

/**
 触发事件回调
 */
-(void)scSendEvent:(id)info;
@end

NS_ASSUME_NONNULL_END
