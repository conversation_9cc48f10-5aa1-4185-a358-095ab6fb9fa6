//
//  SCBaseTableViewCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCBaseTableViewCell.h"

@implementation SCBaseTableViewCell


+ (NSString *)registeredIdentifier {
    return NSStringFromClass([self class]);
}


/**
 初始化
 */
+ (instancetype)initWithFormTableView:(UITableView *)tableView {
    SCBaseTableViewCell *cell=[tableView dequeueReusableCellWithIdentifier:[self registeredIdentifier]];
    if (cell==nil) {
        cell = [[self alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:[self registeredIdentifier]];
    }
    return  cell;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self initialize];
    }
    return self;
}
- (void)layoutSubviews{
    [super layoutSubviews];
    [self updateLayouts];
}
/**
 初始化数据和视图【供子类调用】
 */
- (void)initialize {
    //去除选中效果
    self.selectionStyle =  UITableViewCellSelectionStyleNone;
    [self initData];
    [self initUI];
}

/**
 设置默认值【供子类重写】
 */
- (void)initData {
    
}

/**
 初始化视图
 */
- (void)initUI {
    
}
///更新布局
-(void) updateLayouts{
    
}
/**
 事件回调
 */
- (void)scSendEvent:(id)info{
    if ([self.theDelegate respondsToSelector:@selector(scCell:eventInfo:)]) {
        [self.theDelegate scCell:self eventInfo:info];
    }
}

@end
