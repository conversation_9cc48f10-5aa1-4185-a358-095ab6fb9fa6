//
//  SCBaseViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <UIKit/UIKit.h>
@class SCNavigationBar;
NS_ASSUME_NONNULL_BEGIN

@interface SCBaseViewController : UIViewController
/// 自定义导航栏
@property(nonatomic, strong, readonly) SCNavigationBar *scNavigationBar;
@property(nonatomic, assign) BOOL isHiddenSCNavigationBar;
/// 导航栏下面的布局 预留方便对齐
@property(nonatomic, nonnull, strong, readonly) UIView *scContentView;

///用于关闭订阅
@property(nonatomic,strong,readonly) SCDisposeBag * disposeBag;

/// 初始化数据
- (void)initData;

/// 初始化UI
- (void)initUI;

/// 初始化事件
- (void)initEventAction;

/// 返回键
- (void)onBlack;
@end

NS_ASSUME_NONNULL_END
