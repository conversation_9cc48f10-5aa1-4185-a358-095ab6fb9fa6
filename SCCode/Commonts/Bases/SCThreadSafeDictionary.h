//
//  SCThreadSafeDictionary.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCThreadSafeDictionary<__covariant KeyType, __covariant ObjectType> : NSObject
@property (nonatomic, strong) NSMutableDictionary *dictionary;
@property (nonatomic, strong) NSLock *lock;


- (id)objectForKey:(KeyType)key ;

- (void)setObject:(ObjectType)object forKey:(KeyType)key ;

- (void)removeObjectForKey:(KeyType)key;

@end

NS_ASSUME_NONNULL_END
