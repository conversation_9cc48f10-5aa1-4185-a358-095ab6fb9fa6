//
//  SCThreadSafeDictionary.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
//

#import "SCThreadSafeDictionary.h"

@implementation SCThreadSafeDictionary

- (instancetype)init {
    self = [super init];
    if (self) {
        self.dictionary = [NSMutableDictionary dictionary];
        self.lock = [[NSLock alloc] init];
    }
    return self;
}

- (id)objectForKey:(id)key {
    [self.lock lock];
    id object = [self.dictionary objectForKey:key];
    [self.lock unlock];
    return object;
}

- (void)setObject:(id)object forKey:(id)key {
    [self.lock lock];
    [self.dictionary setObject:object forKey:key];
    [self.lock unlock];
}

- (void)removeObjectForKey:(id)key {
    [self.lock lock];
    [self.dictionary removeObjectForKey:key];
    [self.lock unlock];
}
@end
