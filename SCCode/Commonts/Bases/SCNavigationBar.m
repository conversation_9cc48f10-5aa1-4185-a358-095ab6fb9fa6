//
//  SCNavigationBar.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCNavigationBar.h"
#import <Masonry/Masonry.h>


@implementation SCNavigationBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setNavigationBarStyle:SCNavigationBarStyleDefault]; // 默认样式
    }
    return self;
}

- (void)setupUI {
    // 创建高度为44的子视图
    UIView * contentView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width, 44)];
//    contentView.backgroundColor = [UIColor scGlobalBgColor];
    [self addSubview:contentView];
    _contentView = contentView;
    
    
    // 添加和布局子视图，设置样式和约束
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.textColor = [UIColor blackColor];
    titleLabel.font = [UIFont systemFontOfSize:23 weight:UIFontWeightSemibold];
    [self.contentView addSubview:titleLabel];
    _titleLabel = titleLabel;
    
    UIButton *backButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.backButton setImageEdgeInsets:UIEdgeInsetsMake(0, 10, 0, 0)];
    [self.backButton setContentHorizontalAlignment:UIControlContentHorizontalAlignmentLeft];
    
    [self.backButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [self.contentView addSubview:backButton];
    _backButton = backButton;
    // 添加底部线条
    UIView *lineView = [[UIView alloc] init];
    lineView.backgroundColor = [UIColor blackColor];
    [self addSubview:lineView];
    _lineView = lineView;
    [self.lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.height.equalTo(@0.0);
    }];

    // 添加阴影
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowOpacity = 0.1;
    self.layer.shadowRadius = 2;
    self.layer.shadowRadius = 2;
    [self updateShadow];
    // 使用Masonry设置约束
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.height.equalTo(@44);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(contentView);
    }];
    
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentView).offset(0);
        make.centerY.equalTo(contentView);
        make.width.equalTo(@44);
        make.height.equalTo(@44);
    }];
}

- (void)setNavigationBarStyle:(SCNavigationBarStyle)style {
    switch (style) {
        case SCNavigationBarStyleDefault:
            self.backgroundColor = [UIColor scGlobalBgColor];
            self.titleLabel.textColor = [UIColor blackColor];
            [self.backButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
            [self.backButton setImage:[SCResourceManager loadImageWithName:@"ic_black_arr" isAutoForce:YES] forState:UIControlStateNormal];
            
            break;
        case SCNavigationBarStyleTransparent:
            self.backgroundColor = [UIColor clearColor];
            self.titleLabel.textColor = [UIColor whiteColor];
            [self.backButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            [self.backButton setImage:[SCResourceManager loadImageWithName:@"ic_white_arr" isAutoForce:YES] forState:UIControlStateNormal];
  
            break;
        default:
            break;
    }
}
- (void)setIsShadow:(BOOL)isShadow{
    _isShadow = isShadow;
    [self updateShadow];
}
-(void)updateShadow{
    if(self.isShadow){
        self.layer.shadowColor = [UIColor blackColor].CGColor;
    }else{
        self.layer.shadowColor = [UIColor clearColor].CGColor;
    }
}


@end
