//
//  SCBaseNavigationController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/20.
//

#import "SCBaseNavigationController.h"
#import "SCBaseViewController.h"

@interface SCBaseNavigationController ()

@end

@implementation SCBaseNavigationController


- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated{
    if([viewController isKindOfClass:[SCBaseViewController class]]){
        [self.navigationController setNavigationBarHidden:NO animated:animated];
    }
    
    if ([NSStringFromClass([viewController class]) isEqualToString:@"RCFilePreviewViewController"]) {
            [viewController.navigationController setNavigationBarHidden:NO animated:NO];
            
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                __weak typeof(self) weakSelf = self;
                [viewController.navigationController setNavigationBarHidden:NO animated:NO];
                UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
                [button setImage:[SCResourceManager loadImageWithName:@"ic_white_arr" isAutoForce:YES] forState:UIControlStateNormal];
                [button addTarget:self action:@selector(navPop) forControlEvents:UIControlEventTouchUpInside];
                button.frame = CGRectMake(0, 0, 30, 30);
                UIBarButtonItem *back = [[UIBarButtonItem alloc] initWithCustomView:button];
                viewController.navigationItem.leftBarButtonItem = back;
            });
        }
    
    [super pushViewController:viewController animated:animated];

}

- (void)navPop{
    [self popViewControllerAnimated:YES];
}

- (void)setNavigationBarHidden:(BOOL)hidden animated:(BOOL)animated {
    [super setNavigationBarHidden:hidden animated:animated];
    self.interactivePopGestureRecognizer.delegate = self;
}

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    if (self.viewControllers.count > 1) {
        return YES;
    }
    return NO;
}

@end
