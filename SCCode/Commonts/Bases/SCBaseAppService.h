//
//  SCBaseAppService.h
//  Supercall 用于用户全局服务
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCBaseAppService : NSObject
@property(nonatomic,copy,readonly) NSString *userId;
//初始化
- (instancetype)initWithUserId:(NSString *)userId;
///销毁服务 【用于处理服务停止需要销毁的内容】
- (void)destroyService;
@end

NS_ASSUME_NONNULL_END
