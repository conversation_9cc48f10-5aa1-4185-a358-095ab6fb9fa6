//
//  SCNavigationBar.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/21.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, SCNavigationBarStyle) {
    SCNavigationBarStyleDefault,
    SCNavigationBarStyleTransparent
};

@interface SCNavigationBar : UIView
@property (nonatomic, weak,readonly) UILabel *titleLabel;
@property (nonatomic, weak,readonly) UIButton *backButton;
@property (nonatomic, weak,readonly) UIView * contentView;
@property (nonatomic, weak,readonly) UIView * lineView;
@property (nonatomic, assign) BOOL isShadow;

- (void)setNavigationBarStyle:(SCNavigationBarStyle)style;
@end
NS_ASSUME_NONNULL_END
