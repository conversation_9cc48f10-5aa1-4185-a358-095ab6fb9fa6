//
//  SCBaseCollectionCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/9.
//

#import "SCBaseCollectionCell.h"

@implementation SCBaseCollectionCell


+ (NSString *)cellIdentifier{
    return NSStringFromClass([self class]);
}
/**
 初始化
 */
+ (instancetype)initWithFormCollectionView:(UICollectionView *)collctionView forIndexPath:(nonnull NSIndexPath *)indexPath {

    SCBaseCollectionCell *cell=[collctionView dequeueReusableCellWithReuseIdentifier:[self cellIdentifier] forIndexPath:indexPath];
    if (cell==nil) {
        cell = [[self alloc] initWithFrame:CGRectZero];
    }
    return  cell;
}
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self initData];
        [self initialize];
    }
    return self;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    [self updateLayouts];
}
/**
 初始化数据和视图【供子类调用】
 */
- (void)initialize {
    //去除选中效果
    [self initData];
    [self initUI];
}

/**
 设置默认值【供子类重写】
 */
- (void)initData {
    
}

/**
 初始化视图
 */
- (void)initUI {
    
}
///更新布局
-(void) updateLayouts{
    
}


@end
