//
//  SCBaseView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/9.
//

#import "SCBaseView.h"

@implementation SCBaseView

-(instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self _initialize];
    }
    return self;
}

/**
 初始化数据和视图
 */
- (void)_initialize {
    [self initData];
    [self initUI];
}
- (void)initData {
    
}
- (void)initUI{
    
}

/**
事件回调
 
 @param info 数据信息
 */
-(void)eventCallBackWithInfo:(id)info {
    if ([self.scDelegate respondsToSelector:@selector(scBaseView:callBackInfo:)]) {
        [self.scDelegate scBaseView:self callBackInfo:info];
    }
    ///触发回调
    kSCBlockExeNotNil(self.scEventBlock,info);
}

- (void)setScDelegate:(id<SCBaseViewDelegate>)scDelegate{
    _scDelegate = scDelegate;
    ///防止多次回调，如果设置了Delegate那么需要清理Block
    if ([_scDelegate respondsToSelector:@selector(scBaseView:callBackInfo:)]) {
        _scEventBlock = nil;
    }
}

- (void)setScEventBlock:(void (^)(id _Nonnull))scEventBlock{
    _scEventBlock = scEventBlock;
    ///防止多次回调，如果设置了Block那么需要清理Delegate
    if (_scEventBlock) {
        _scDelegate = nil;
    }
}

@end
