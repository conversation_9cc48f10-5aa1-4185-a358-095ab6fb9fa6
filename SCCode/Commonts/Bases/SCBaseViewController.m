//
//  SCBaseViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/20.
//

#import "SCBaseViewController.h"
#import "SCNavigationBar.h"
#import <Masonry/Masonry.h>

@interface SCBaseViewController ()

@end

@implementation SCBaseViewController

- (void)dealloc{
    [_disposeBag dispose];
    _disposeBag = nil;
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent; // 返回白色状态栏样式
}

//重新标题设置
- (void)setTitle:(NSString *)title{
    [super setTitle:title];
    self.scNavigationBar.titleLabel.text = title;
}

- (void)setIsHiddenSCNavigationBar:(BOOL)isHiddenSCNavigationBar{
    _isHiddenSCNavigationBar = isHiddenSCNavigationBar;
    [self.scNavigationBar setHidden:self.isHiddenSCNavigationBar];
    [self.scContentView setHidden:self.isHiddenSCNavigationBar];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self configurePopGesture];
    //隐藏系统导航栏
    [self.navigationController setNavigationBarHidden:YES animated:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _disposeBag = [[SCDisposeBag alloc]init];
    
    [self configurePopGesture];

    [self initData];

    [self.view setBackgroundColor:[UIColor scGlobalBgColor]];
    
    _scContentView = [[UIView alloc] init];
    _scContentView.backgroundColor = [UIColor scGlobalBgColor];
    [self.view addSubview:_scContentView];
    
    _scNavigationBar = [[SCNavigationBar alloc] init];
    self.scNavigationBar.titleLabel.text = self.title;
    [self.view addSubview:_scNavigationBar];
    
    [self.scNavigationBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.view).offset(0);
        make.height.equalTo(@(kSCNavBarFullHeight));
    }];
    [self.scContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view).offset(0);
        make.top.equalTo(self.scNavigationBar.mas_bottom).offset(0);
    }];
    [self.scNavigationBar.backButton addTarget:self action:@selector(onBlack) forControlEvents:UIControlEventTouchUpInside];
    [self.scNavigationBar setHidden:self.isHiddenSCNavigationBar];
    [self.scContentView setHidden:self.isHiddenSCNavigationBar];
    [self initUI];
    [self initEventAction];
    
    // 监听语言切换通知
    kWeakSelf(self)
    [kScAuthMar.languageObx subscribe:^(NSNumber * _Nullable value) {
        [weakself configurePopGesture];
    } error:nil disposeBag:_disposeBag];
}

- (void)configurePopGesture {
    self.navigationController.view.semanticContentAttribute = UIView.appearance.semanticContentAttribute;
    self.navigationController.navigationBar.semanticContentAttribute = UIView.appearance.semanticContentAttribute;
}


///初始化数据
-(void) initData{
    
}
///初始化UI
- (void) initUI{
    
}
///初始化事件
-(void)initEventAction{
    
}

///返回键
-(void)onBlack{
    if(self.navigationController == nil || [self.navigationController.viewControllers count] <= 1){
        [self dismissViewControllerAnimated:YES completion:nil];
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
    
}



@end

