//
//  SCSafetyUtils.h
//  Supercall
//
//  Created by sumeng<PERSON>u on 2024/10/21.
//


#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCSafetyUtils : NSObject

+ (BOOL)isEnabledVPN;
+ (BOOL)isProxyEnabled;
+ (NSString *)getTimeZone;
+ (BOOL)isAutoTimeZone;
+ (BOOL)isSimulator;
+ (BOOL)isJailbroken;
+ (NSDictionary *)info;
+ (nullable NSString *)encryptInfo;
+ (NSString *)getDeviceModel;

/// 登录接口和风控接口额外的请求头
+ (NSDictionary *)extraHeaders;

@end

NS_ASSUME_NONNULL_END
