//
//  SCAVAudioSessionUtils.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/11/16.
//

#import "SCAVAudioSessionUtils.h"
#import <AudioToolbox/AudioToolbox.h>

@implementation SCAVAudioSessionUtils
+ (void)configAVAudioSessionWithCategory:(AVAudioSessionCategory) category error:(NSError **)error{
    // 设置音频会话
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    NSError *errorTemp = nil;
    // 设置音频会话类别为播放和录音
    [audioSession setCategory:category error:&errorTemp];
    if (errorTemp) {
        
        if(error){
            *error = errorTemp;
        }
        
        return;
    }
    // 激活音频会话
    [audioSession setActive:YES error:error];
    if (errorTemp) {
        
        if(error){
            *error = errorTemp;
        }
        return;
    }
    
}
+ (void)configAVAudioSessionWithCategory:(AVAudioSessionCategory) category withMode:(AVAudioSessionMode) mode error:(NSError **)error{
    // 设置音频会话
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];
    NSError *errorTemp = nil;
    // 设置音频会话类别为播放和录音
    [audioSession setCategory:category error:&errorTemp];
    if (errorTemp) {
        
        if(error){
            *error = errorTemp;
        }
        
        return;
    }
    [audioSession setMode:mode error:&errorTemp];
    if (errorTemp) {
        
        if(error){
            *error = errorTemp;
        }
        
        return;
    }
    // 激活音频会话
    [audioSession setActive:YES error:error];
    if (errorTemp) {
        
        if(error){
            *error = errorTemp;
        }
        return;
    }
}
@end
