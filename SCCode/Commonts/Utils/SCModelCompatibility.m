//
//  SCModelCompatibility.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  Model兼容层实现
//

#import "SCModelCompatibility.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
// #import "SCVideoCallResultModel.h" // 已移除，使用字典替代

// 注意：字典键常量定义已移至 SCDictionaryKeys.m 中统一管理
// 以下常量现在从 SCDictionaryKeys.h 中导入：
// - kSCUserIDKey, kSCUserNicknameKey, kSCUserAgeKey, kSCUserCountryKey, kSCUserStatusKey, kSCUserCoinsKey
// - kSCGiftCodeKey, kSCGiftNameKey, kSCGiftDescKey
// - kSCMediaTypeKey, kSCMediaSortKey

// 仅保留 SCModelCompatibility 特有的常量定义
@implementation SCModelCompatibility

#pragma mark - Model转字典方法

+ (nullable NSDictionary *)dictionaryFromModel:(id)model {
    if (!model) {
        return nil;
    }

    // YYModel已移除，这个方法现在返回nil
    // 如果需要Model转字典功能，应该在Model类中实现toDictionary方法
    
    return nil;
}

+ (nullable NSArray<NSDictionary *> *)dictionaryArrayFromModelArray:(NSArray *)modelArray {
    if (!modelArray || ![modelArray isKindOfClass:[NSArray class]]) {
        return nil;
    }
    
    NSMutableArray<NSDictionary *> *dictionaryArray = [NSMutableArray arrayWithCapacity:modelArray.count];
    
    for (id model in modelArray) {
        NSDictionary *dictionary = [self dictionaryFromModel:model];
        if (dictionary) {
            [dictionaryArray addObject:dictionary];
        }
    }
    
    return [dictionaryArray copy];
}

#pragma mark - 字典转Model方法（临时使用）

+ (nullable id)modelFromDictionary:(NSDictionary *)dictionary modelClass:(Class)modelClass {
    if (!dictionary || ![dictionary isKindOfClass:[NSDictionary class]] || !modelClass) {
        return nil;
    }

    // YYModel已移除，这个方法现在返回nil
    // 应该直接使用字典数据而不是转换为Model对象
    
    return nil;
}

#pragma mark - 消息相关转换方法

+ (nullable NSDictionary *)dictionaryFromMessage:(id)message {
    if (!message) {
        return nil;
    }

    // 使用通用的Model转字典方法
    return [self dictionaryFromModel:message];
}

+ (nullable id)messageFromDictionary:(NSDictionary *)messageDict {
    if (!messageDict || ![messageDict isKindOfClass:[NSDictionary class]]) {
        return nil;
    }

    // 这里需要根据实际的消息Model类来创建
    // 暂时使用通用方法，具体的消息类需要在调用时指定
    // 例如：[self modelFromDictionary:messageDict modelClass:[SCNativeMessageModel class]]
    return messageDict; // 暂时返回字典，具体实现需要根据消息系统的结构调整
}

+ (nullable NSArray<NSDictionary *> *)dictionaryArrayFromMessages:(NSArray *)messages {
    if (!messages || ![messages isKindOfClass:[NSArray class]]) {
        return nil;
    }

    NSMutableArray<NSDictionary *> *dictArray = [NSMutableArray array];
    for (id message in messages) {
        NSDictionary *messageDict = [self dictionaryFromMessage:message];
        if (messageDict) {
            [dictArray addObject:messageDict];
        }
    }

    return [dictArray copy];
}

#pragma mark - 数据结构映射

+ (NSDictionary *)standardizedUserDictionary:(NSDictionary *)userDict {
    if (!userDict || ![userDict isKindOfClass:[NSDictionary class]]) {
        return @{};
    }
    
    NSMutableDictionary *standardDict = [NSMutableDictionary dictionary];
    
    // 标准化用户ID - 支持多种键名
    NSString *userID = [SCDictionaryHelper userIDFromDictionary:userDict];
    if (userID.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserIDKey] = userID;
    }
    
    // 标准化其他字段
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"nickname" defaultValue:@""];
    if (nickname.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserNicknameKey] = nickname;
    }
    
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"avatarUrl" defaultValue:@""];
    if (avatarUrl.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserAvatarKey] = avatarUrl;
    }
    
    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:@"age" defaultValue:0];
    if (age > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserAgeKey] = @(age);
    }
    
    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"country" defaultValue:@""];
    if (country.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserCountryKey] = country;
    }
    
    NSString *status = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"status" defaultValue:@""];
    if (status.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCUserStatusKey] = status;
    }
    
    // 尝试多种可能的金币键名以保持兼容性
    NSInteger coins = [SCDictionaryHelper integerFromDictionary:userDict forKey:@"availableCoins" defaultValue:0];
    if (coins == 0) {
        coins = [SCDictionaryHelper integerFromDictionary:userDict forKey:@"coins" defaultValue:0];
    }
    standardDict[SCDictionaryKeys.shared.kSCUserCoinsKey] = @(coins);
    
    // 保留原始字典中的其他字段
    for (NSString *key in userDict.allKeys) {
        if (!standardDict[key]) {
            standardDict[key] = userDict[key];
        }
    }
    
    return [standardDict copy];
}

+ (NSDictionary *)standardizedGiftDictionary:(NSDictionary *)giftDict {
    if (!giftDict || ![giftDict isKindOfClass:[NSDictionary class]]) {
        return @{};
    }
    
    NSMutableDictionary *standardDict = [NSMutableDictionary dictionary];
    
    // 标准化礼物字段
    NSString *code = [SCDictionaryHelper stringFromDictionary:giftDict forKey:@"code" defaultValue:@""];
    if (code.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCGiftCodeKey] = code;
    }
    
    NSString *name = [SCDictionaryHelper stringFromDictionary:giftDict forKey:@"name" defaultValue:@""];
    if (name.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCGiftNameKey] = name;
    }
    
    NSInteger price = [SCDictionaryHelper integerFromDictionary:giftDict forKey:@"coinPrice" defaultValue:0];
    standardDict[SCDictionaryKeys.shared.kSCGiftPriceKey] = @(price);
    
    NSString *iconPath = [SCDictionaryHelper stringFromDictionary:giftDict forKey:@"iconPath" defaultValue:@""];
    if (iconPath.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCGiftIconKey] = iconPath;
    }
    
    NSString *giftDesc = [SCDictionaryHelper stringFromDictionary:giftDict forKey:@"giftDesc" defaultValue:@""];
    if (giftDesc.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCGiftDescKey] = giftDesc;
    }
    
    // 保留原始字典中的其他字段
    for (NSString *key in giftDict.allKeys) {
        if (!standardDict[key]) {
            standardDict[key] = giftDict[key];
        }
    }
    
    return [standardDict copy];
}

+ (NSDictionary *)standardizedMediaDictionary:(NSDictionary *)mediaDict {
    if (!mediaDict || ![mediaDict isKindOfClass:[NSDictionary class]]) {
        return @{};
    }
    
    NSMutableDictionary *standardDict = [NSMutableDictionary dictionary];
    
    // 标准化媒体字段
    NSString *mediaUrl = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"mediaUrl" defaultValue:@""];
    if (mediaUrl.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCMediaURLKey] = mediaUrl;
    }
    
    NSString *mediaType = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"mediaType" defaultValue:@""];
    if (mediaType.length > 0) {
        standardDict[SCDictionaryKeys.shared.kSCMediaTypeKey] = mediaType;
    }
    
    NSInteger sort = [SCDictionaryHelper integerFromDictionary:mediaDict forKey:@"sort" defaultValue:0];
    standardDict[SCDictionaryKeys.shared.kSCMediaSortKey] = @(sort);
    
    // 保留原始字典中的其他字段
    for (NSString *key in mediaDict.allKeys) {
        if (!standardDict[key]) {
            standardDict[key] = mediaDict[key];
        }
    }
    
    return [standardDict copy];
}

#pragma mark - 便捷访问方法

+ (NSString *)userIDFromDictionary:(NSDictionary *)dictionary {
    return [SCDictionaryHelper userIDFromDictionary:dictionary];
}

+ (NSString *)nicknameFromDictionary:(NSDictionary *)dictionary {
    return [SCDictionaryHelper stringFromDictionary:dictionary forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
}

+ (NSString *)avatarURLFromDictionary:(NSDictionary *)dictionary {
    return [SCDictionaryHelper stringFromDictionary:dictionary forKey:SCDictionaryKeys.shared.kSCUserAvatarKey defaultValue:@""];
}

+ (NSInteger)ageFromDictionary:(NSDictionary *)dictionary {
    return [SCDictionaryHelper integerFromDictionary:dictionary forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
}

+ (NSString *)countryFromDictionary:(NSDictionary *)dictionary {
    return [SCDictionaryHelper stringFromDictionary:dictionary forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
}

+ (NSInteger)coinsFromDictionary:(NSDictionary *)dictionary {
    // 尝试多种可能的金币键名以保持兼容性
    NSInteger coins = [SCDictionaryHelper integerFromDictionary:dictionary forKey:@"availableCoins" defaultValue:0];
    if (coins == 0) {
        coins = [SCDictionaryHelper integerFromDictionary:dictionary forKey:@"coins" defaultValue:0];
    }
    return coins;
}

// 已移除通话结果相关转换方法，完全使用字典替代
// #pragma mark - 通话结果相关转换方法
// + (nullable id)videoCallResultModelFromDict:(NSDictionary *)resultDict

@end
