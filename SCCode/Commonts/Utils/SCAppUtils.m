//
//  SCAppUtils.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <Security/Security.h>
#import "SCKeychainUtils.h"
#import "SCAppUtils.h"

@implementation SCAppUtils


+ (NSString *)identifier {
    
    NSString * uuid = [SCKeychainUtils readValueForKey:@"sc_app_key"];
    
    if(uuid == nil || uuid == NULL || uuid.length == 0){
        uuid = [[NSUserDefaults standardUserDefaults] objectForKey:@"sc_app_key"];
        if(uuid == nil || uuid == NULL || uuid.length == 0){
            // 生成新的唯一标识
            uuid = [[NSUUID UUID] UUIDString];
            [SCKeychainUtils writeValue:uuid forKey:@"sc_app_key"];
            [[NSUserDefaults standardUserDefaults] setObject:uuid forKey:@"sc_app_key"];
        }
        
    }
    return uuid;
}

+ (void)resetIdentifier {
    // 生成新的唯一标识
    NSString *uuid = [[NSUUID UUID] UUIDString];
    [SCKeychainUtils writeValue:uuid forKey:@"sc_app_key"];
}

+ (nullable NSString *)version {
    return [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"];
}

+ (NSString *)deviceModel {
    return [[UIDevice currentDevice] model];
}

+ (NSString *)packageName {
    return [[NSBundle mainBundle] bundleIdentifier];
}

+ (NSString *)platform {
    return @"iOS";
}

+ (NSString *)getAllInputLanguages {
    NSArray<UITextInputMode *> *inputModes = [UITextInputMode activeInputModes];
    NSMutableArray<NSString *> *languages = [NSMutableArray array];

    // 遍历输入模式，并提取 primaryLanguage
    for (UITextInputMode *inputMode in inputModes) {
        if (inputMode.primaryLanguage != nil) {
            [languages addObject:inputMode.primaryLanguage];
        }
    }

    // 如果没有语言，返回空字符串
    if (languages.count == 0) {
        return @"";
    }

    // 用逗号分隔语言
    return [languages componentsJoinedByString:@","];
}

+ (NSString *)getCountryLang {
    // 获取首选语言
    NSString *preferredLanguage = [NSLocale preferredLanguages].firstObject;
    if (!preferredLanguage) {
        return nil;
    }
    
    // 根据首选语言创建 Locale 对象
    NSLocale *locale = [[NSLocale alloc] initWithLocaleIdentifier:preferredLanguage];
    
    // 获取语言代码
    NSString *languageCode = locale.languageCode;
    
    // 获取当前地区的 NSLocale 对象
    NSLocale *currentLocale = [NSLocale currentLocale];
    
    // 获取国家代码
    NSString *countryCode = [currentLocale objectForKey:NSLocaleCountryCode];
    
    // 获取脚本代码
    NSString *scriptCode = locale.scriptCode;
    
    // 构建最终的语言标识符
    NSString *language = languageCode;
    
    // 如果有脚本代码，拼接到语言代码后
    if (scriptCode != nil && scriptCode.length > 0) {
        language = [NSString stringWithFormat:@"%@-%@", language, scriptCode];
    }
    
    // 如果有国家代码，拼接到语言代码后
    if (countryCode != nil && countryCode.length > 0) {
        language = [NSString stringWithFormat:@"%@-%@", language, countryCode];
    }
    
    return language;
}

+ (NSString *)getCountryCode {
    // 获取当前地区的 NSLocale 对象
    NSLocale *currentLocale = [NSLocale currentLocale];
    
    // 获取国家代码
    NSString *countryCode = [currentLocale objectForKey:NSLocaleCountryCode];
    
    // 如果获取失败，返回默认值
    return countryCode ?: @"US";
}

@end
