//
//  SCDictionaryKeys.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  字典键常量定义 - 统一管理所有字典访问的键名
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - 网络响应通用键
@interface SCDictionaryKeys : NSObject
+ (instancetype)shared;
/// 响应数据键
@property(nonatomic,copy)NSString *kSCResponseDataKey;
/// 响应代码键
@property(nonatomic,copy)NSString *kSCResponseCodeKey;
/// 响应消息键
@property(nonatomic,copy)NSString *kSCResponseMessageKey;
/// 响应成功标识键
@property(nonatomic,copy)NSString *kSCResponseSuccessKey;
/// 响应失败标识键
@property(nonatomic,copy)NSString *kSCResponseFailKey;

#pragma mark - 用户信息相关键

/// 用户ID键（主要）
@property(nonatomic,copy)NSString *kSCUserIDKey;
/// 用户ID键（备选1）
@property(nonatomic,copy)NSString *kSCUserIdKey;
/// 用户ID键（备选2）
@property(nonatomic,copy)NSString *kSCUser_IdKey;
/// 用户昵称键
@property(nonatomic,copy)NSString *kSCUserNicknameKey;
/// 用户头像URL键
@property(nonatomic,copy)NSString *kSCUserAvatarUrlKey;
/// 用户头像缩略图URL键
@property(nonatomic,copy)NSString *kSCUserAvatarThumbUrlKey;
/// 用户性别键
@property(nonatomic,copy)NSString *kSCUserGenderKey;
/// 用户年龄键
@property(nonatomic,copy)NSString *kSCUserAgeKey;
/// 用户金币键
@property(nonatomic,copy)NSString *kSCUserCoinsKey;
/// 用户国家键
@property(nonatomic,copy)NSString *kSCUserCountryKey;
/// 用户注册国家键
@property(nonatomic,copy)NSString *kSCUserRegisterCountryKey;
/// 用户状态键
@property(nonatomic,copy)NSString *kSCUserStatusKey;
/// 用户显示状态键
@property(nonatomic,copy)NSString *kSCUserDisplayStatusKey;
/// 用户可用金币键
@property(nonatomic,copy)NSString *kSCUserAvailableCoinsKey;
/// 用户是否充值键
@property(nonatomic,copy)NSString *kSCUserIsRechargeKey;
/// 用户通话费用键
@property(nonatomic,copy)NSString *kSCUserCallCoinsKey;
/// 用户单价键
@property(nonatomic,copy)NSString *kSCUserUnitPriceKey;
/// 用户媒体列表键
@property(nonatomic,copy)NSString *kSCUserMediaListKey;
/// 用户头像响应列表键
@property(nonatomic,copy)NSString *kSCUserAvatarRespListKey;
/// 用户标签详情键
@property(nonatomic,copy)NSString *kSCUserTagDetailsKey;
/// 用户扩展信息标签列表键
@property(nonatomic,copy)NSString *kSCUserExtraLabelsListKey;
/// 用户融云Token键
@property(nonatomic,copy)NSString *kSCUserRongcloudTokenKey;
/// 置顶官方用户ID列表键
@property(nonatomic,copy)NSString *kSCTopOfficialUserIdsKey;
/// 主播关注官方用户ID列表键
@property(nonatomic,copy)NSString *kSCBroadcasterFollowOfficialUserIdsKey;
/// 用户服务账号ID键
@property(nonatomic,copy)NSString *kSCUserServiceAccountIdKey;
/// 用户媒体类型键
@property(nonatomic,copy)NSString *kSCUserMediaTypeKey;
/// 用户媒体URL键
@property(nonatomic,copy)NSString *kSCUserMediaUrlKey;
/// 用户生日键
@property(nonatomic,copy)NSString *kSCUserBirthdayKey;
/// 用户语言键
@property(nonatomic,copy)NSString *kSCUserLanguageKey;
/// 用户等级键
@property(nonatomic,copy)NSString *kSCUserLevelKey;
/// 用户是否VIP键
@property(nonatomic,copy)NSString *kSCUserIsVipKey;
/// 用户关于信息键
@property(nonatomic,copy)NSString *kSCUserAboutKey;
/// 用户头像映射路径键
@property(nonatomic,copy)NSString *kSCUserAvatarMapPathKey;

@property(nonatomic,copy)NSString *kSCUserAvatarKey;
@property(nonatomic,copy)NSString *kSCGiftPriceKey;
@property(nonatomic,copy)NSString *kSCGiftIconKey;
@property(nonatomic,copy)NSString *kSCMediaURLKey;
@property(nonatomic,copy)NSString *kSCDataKey;
@property(nonatomic,copy)NSString *kSCCodeKey;
@property(nonatomic,copy)NSString *kSCMessageKey;
@property(nonatomic,copy)NSString *kSCSuccessKey;

#pragma mark - 认证相关键

/// 登录令牌键
@property(nonatomic,copy)NSString *kSCTokenKey;
/// 用户信息键
@property(nonatomic,copy)NSString *kSCUserInfoKey;
/// 是否首次注册键
@property(nonatomic,copy)NSString *kSCIsFirstRegisterKey;

#pragma mark - 礼物相关键

/// 礼物代码键
@property(nonatomic,copy)NSString *kSCGiftCodeKey;
/// 礼物名称键
@property(nonatomic,copy)NSString *kSCGiftNameKey;
/// 礼物金币价格键
@property(nonatomic,copy)NSString *kSCGiftCoinPriceKey;
/// 礼物排序号键
@property(nonatomic,copy)NSString *kSCGiftSortNoKey;
/// 礼物描述键
@property(nonatomic,copy)NSString *kSCGiftDescKey;

#pragma mark - 消息相关键
/// 消息ID键
@property(nonatomic,copy)NSString *kSCMessageIdKey;
/// 消息内容键
@property(nonatomic,copy)NSString *kSCMessageContentKey;
/// 消息类型键
@property(nonatomic,copy)NSString *kSCMessageTypeKey;
/// 消息发送者ID键
@property(nonatomic,copy)NSString *kSCMessageFromUserIdKey;
/// 消息接收者ID键
@property(nonatomic,copy)NSString *kSCMessageToUserIdKey;
/// 消息时间戳键
@property(nonatomic,copy)NSString *kSCMessageTimestampKey;

/// 礼物图标路径键
@property(nonatomic,copy)NSString *kSCGiftIconPathKey;
/// 礼物缩略图路径键
@property(nonatomic,copy)NSString *kSCGiftIconThumbPathKey;
/// 礼物数量键
@property(nonatomic,copy)NSString *kSCGiftCountKey;

#pragma mark - 媒体相关键

/// 媒体路径键
@property(nonatomic,copy)NSString *kSCMediaPathKey;
/// 媒体URL键
@property(nonatomic,copy)NSString *kSCMediaUrlKey;
/// 媒体类型键
@property(nonatomic,copy)NSString *kSCMediaTypeKey;
/// 媒体排序键
@property(nonatomic,copy)NSString *kSCMediaSortKey;

#pragma mark - 应用配置相关键

/// 配置版本键
@property(nonatomic,copy)NSString *kSCAppConfigVerKey;
/// 配置项目列表键
@property(nonatomic,copy)NSString *kSCAppConfigItemsKey;
/// 配置项目名称键
@property(nonatomic,copy)NSString *kSCAppConfigItemNameKey;
/// 配置项目数据键
@property(nonatomic,copy)NSString *kSCAppConfigItemDataKey;
/// 融云应用键名
@property(nonatomic,copy)NSString *kSCAppConfigRcAppKeyName;
/// 融云区域代码键名
@property(nonatomic,copy)NSString *kSCAppConfigRcAreaCodeName;
/// 谷歌翻译键名
@property(nonatomic,copy)NSString *kSCAppConfigGoogleTranslationKeyName;
/// Facebook应用ID键名
@property(nonatomic,copy)NSString *kSCAppConfigFBIdName;
/// Facebook客户端令牌键名
@property(nonatomic,copy)NSString *kSCAppConfigFBClientTokenName;
/// 应用扩展数据键名
@property(nonatomic,copy)NSString *kSCAppConfigExtDataName;

#pragma mark - 用户等级相关键

/// 当前等级键
@property(nonatomic,copy)NSString *kSCUserLevelCurrentKey;
/// 充值金币键
@property(nonatomic,copy)NSString *kSCUserLevelRechargedCoinsKey;
/// 需要充值金币键
@property(nonatomic,copy)NSString *kSCUserLevelNeedRechargeCoinsKey;
/// 用户等级列表键
@property(nonatomic,copy)NSString *kSCUserLevelListKey;
/// 等级键
@property(nonatomic,copy)NSString *kSCLevelKey;
/// 等级充值金币键
@property(nonatomic,copy)NSString *kSCLevelRechargedCoinsKey;

#pragma mark - 横幅相关键

/// 横幅ID键
@property(nonatomic,copy)NSString *kSCBannerIdKey;
/// 横幅标题键
@property(nonatomic,copy)NSString *kSCBannerTitleKey;
/// 横幅图片URL键
@property(nonatomic,copy)NSString *kSCBannerImageUrlKey;
/// 横幅跳转URL键
@property(nonatomic,copy)NSString *kSCBannerJumpUrlKey;
/// 横幅类型键
@property(nonatomic,copy)NSString *kSCBannerTypeKey;

#pragma mark - 通话相关键

/// 通话频道名称键
@property(nonatomic,copy)NSString *kSCCallChannelNameKey;
/// 通话令牌键
@property(nonatomic,copy)NSString *kSCCallTokenKey;
/// 通话用户ID键
@property(nonatomic,copy)NSString *kSCCallUserIdKey;
/// 通话类型键
@property(nonatomic,copy)NSString *kSCCallTypeKey;
/// 通话来源键
@property(nonatomic,copy)NSString *kSCCallSourceKey;
/// 通话会话ID键
@property(nonatomic,copy)NSString *kSCCallSessionIdKey;

/// 频道名称键（简化版）
@property(nonatomic,copy)NSString *kSCDictKeyChannelName;
/// 订单号键（简化版）
@property(nonatomic,copy)NSString *kSCDictKeyOrderNo;
/// 支付金额键（简化版）
@property(nonatomic,copy)NSString *kSCDictKeyPayAmount;

#pragma mark - 应用配置相关键

/// 融云应用Key键
@property(nonatomic,copy)NSString *kSCDictKeyRck;
/// 融云区域代码键
@property(nonatomic,copy)NSString *kSCDictKeyRcAreaCode;
/// 融云应用Key键（备选）
@property(nonatomic,copy)NSString *kSCDictKeyRcAppKey;
/// 谷歌翻译Key键
@property(nonatomic,copy)NSString *kSCDictKeyGoogleTranslationKey;
/// 应用版本键
@property(nonatomic,copy)NSString *kSCDictKeyVer;

#pragma mark - 礼物相关键

/// 礼物接收数量键（用于用户礼物数量显示）
@property(nonatomic,copy)NSString *kSCGiftNumKey;

/// 金币商品相关键
/// 商品ID键
@property(nonatomic,copy)NSString *kSCCoinsGoodsIdKey;
/// 商品编号键
@property(nonatomic,copy)NSString *kSCCoinsCodeKey;
/// 图标键
@property(nonatomic,copy)NSString *kSCCoinsIconKey;
/// 类型键
@property(nonatomic,copy)NSString *kSCCoinsTypeKey;
/// 标签键
@property(nonatomic,copy)NSString *kSCCoinsTagsKey;
/// 折扣键
@property(nonatomic,copy)NSString *kSCCoinsDiscountKey;
/// 原价键
@property(nonatomic,copy)NSString *kSCCoinsOriginalPriceKey;
/// 当前价格键
@property(nonatomic,copy)NSString *kSCCoinsPriceKey;
/// 兑换金币数键
@property(nonatomic,copy)NSString *kSCCoinsExchangeCoinKey;
/// 原价(卢比)键
@property(nonatomic,copy)NSString *kSCCoinsOriginalPriceRupeeKey;
/// 当前价格(卢比)键
@property(nonatomic,copy)NSString *kSCCoinsPriceRupeeKey;
/// 本地支付价格(卢比)键
@property(nonatomic,copy)NSString *kSCCoinsLocalPaymentPriceRupeeKey;
/// 是否促销键
@property(nonatomic,copy)NSString *kSCCoinsIsPromotionKey;
/// 本地支付原价键
@property(nonatomic,copy)NSString *kSCCoinsLocalPayOriginalPriceKey;
/// 本地支付价格键
@property(nonatomic,copy)NSString *kSCCoinsLocalPayPriceKey;
/// 额外金币比例键
@property(nonatomic,copy)NSString *kSCCoinsExtraCoinPercentKey;
/// 邀请链接键
@property(nonatomic,copy)NSString *kSCCoinsInvitationIdKey;
/// 子类型键
@property(nonatomic,copy)NSString *kSCCoinsSubTypeKey;

#pragma mark - 通话相关键（扩展）

/// 通话免费时长键
@property(nonatomic,copy)NSString *kSCCallFreeSecondsKey;
/// 通话发起用户ID键
@property(nonatomic,copy)NSString *kSCCallFromUserIdKey;
/// 通话接收用户ID键
@property(nonatomic,copy)NSString *kSCCallToUserIdKey;
/// 通话RTC令牌键
@property(nonatomic,copy)NSString *kSCCallRtcTokenKey;
/// 通话视频SDK选择键
@property(nonatomic,copy)NSString *kSCCallChooseVideoSdkKey;
/// 通话绿色模式键
@property(nonatomic,copy)NSString *kSCCallIsGreenModeKey;
/// 通话单价键
@property(nonatomic,copy)NSString *kSCCallUnitPriceKey;
/// 通话时长键
@property(nonatomic,copy)NSString *kSCCallDurationKey;
/// 通话状态键
@property(nonatomic,copy)NSString *kSCCallStatusKey;
/// 通话是否免费键
@property(nonatomic,copy)NSString *kSCCallIsFreeKey;
/// 通话挂断原因键
@property(nonatomic,copy)NSString *kSCCallHangUpReasonKey;
/// 通话结束类型键
@property(nonatomic,copy)NSString *kSCCallEndTypeKey;
/// 通话创建时间键
@property(nonatomic,copy)NSString *kSCCallCreatAtKey;
/// 通话客户端会话ID键
@property(nonatomic,copy)NSString *kSCCallClientSessionIdKey;
/// 通话主播单价键
@property(nonatomic,copy)NSString *kSCCallBroadcasterUnitPriceKey;
/// 通话是否匹配键
@property(nonatomic,copy)NSString *kSCCallIsMatchKey;

#pragma mark - Socket事件相关键

/// Socket命令键
@property(nonatomic,copy)NSString *kSCSocketCommandKey;
/// Socket命令ID键
@property(nonatomic,copy)NSString *kSCSocketCommandIdKey;
/// Socket数据键
@property(nonatomic,copy)NSString *kSCSocketDataKey;
/// Socket时间戳键
@property(nonatomic,copy)NSString *kSCSocketTimestampKey;

#pragma mark - 排行榜相关键

/// 排行榜月份名称键
@property(nonatomic,copy)NSString *kSCRankMonthNameKey;
/// 排行榜数据键
@property(nonatomic,copy)NSString *kSCRankDataKey;
/// 排行榜排序号键
@property(nonatomic,copy)NSString *kSCRankSortNoKey;
/// 排行榜项头像键
@property(nonatomic,copy)NSString *kSCRankItemAvatarKey;
/// 排行榜项头像映射路径键
@property(nonatomic,copy)NSString *kSCRankItemAvatarMapPathKey;
/// 排行榜项昵称键
@property(nonatomic,copy)NSString *kSCRankItemNicknameKey;
/// 排行榜项排序键
@property(nonatomic,copy)NSString *kSCRankItemSortKey;
/// 排行榜项用户ID键
@property(nonatomic,copy)NSString *kSCRankItemUserIDKey;

/// 主播排行榜主播ID键
@property(nonatomic,copy)NSString *kSCAnchorRankBroadcasterIdKey;
/// 主播排行榜主播名称键
@property(nonatomic,copy)NSString *kSCAnchorRankBroadcasterNameKey;
/// 主播排行榜在线时间键
@property(nonatomic,copy)NSString *kSCAnchorRankOnlineTimeKey;
/// 主播排行榜公会ID键
@property(nonatomic,copy)NSString *kSCAnchorRankGuildIdKey;
/// 主播排行榜公会名称键
@property(nonatomic,copy)NSString *kSCAnchorRankGuildNameKey;
/// 主播排行榜总收入金币键
@property(nonatomic,copy)NSString *kSCAnchorRankTotalIncomeKey;

/// 排行榜类型键
@property(nonatomic,copy)NSString *kSCRankingTypeKey;
/// 排行榜标题键
@property(nonatomic,copy)NSString *kSCRankingTitleKey;

#pragma mark - 个人设置相关键

/// 个人设置项标题键
@property(nonatomic,copy)NSString *kSCPersonalItemTitleKey;
/// 个人设置项副标题键
@property(nonatomic,copy)NSString *kSCPersonalItemSubTitleKey;
/// 个人设置项图标键
@property(nonatomic,copy)NSString *kSCPersonalItemImageKey;
/// 个人设置项样式键
@property(nonatomic,copy)NSString *kSCPersonalItemStyleKey;
/// 个人设置项开关状态键
@property(nonatomic,copy)NSString *kSCPersonalItemIsOnKey;

#pragma mark - 订单相关键

/// 订单号键
@property(nonatomic,copy)NSString *kSCOrderNoKey;
/// 订单金额键
@property(nonatomic,copy)NSString *kSCOrderAmountKey;
/// 支付金额键
@property(nonatomic,copy)NSString *kSCPayAmountKey;
/// 订单状态键
@property(nonatomic,copy)NSString *kSCOrderStatusKey;
/// 商品代码键
@property(nonatomic,copy)NSString *kSCOrderGoodsCodeKey;
/// 商品名称键
@property(nonatomic,copy)NSString *kSCOrderGoodsNameKey;
/// 已支付金额键
@property(nonatomic,copy)NSString *kSCOrderPaidAmountKey;
/// 支付货币键
@property(nonatomic,copy)NSString *kSCOrderPaidCurrencyKey;
/// 请求URL键
@property(nonatomic,copy)NSString *kSCOrderRequestUrlKey;

#pragma mark - 等级相关键

/// 等级名称键
@property(nonatomic,copy)NSString *kSCLevelNameKey;
/// 等级图标键
@property(nonatomic,copy)NSString *kSCLevelIconKey;

#pragma mark - 标签相关键

/// 标签ID键
@property(nonatomic,copy)NSString *kSCTagIdKey;
/// 标签名称键
@property(nonatomic,copy)NSString *kSCTagNameKey;
/// 标签颜色键
@property(nonatomic,copy)NSString *kSCTagColorKey;
/// 子标签列表键
@property(nonatomic,copy)NSString *kSCSubTagListKey;

#pragma mark - OSS策略相关键

/// OSS访问密钥ID键
@property(nonatomic,copy)NSString *kSCOSSPolicyAccessKeyIdKey;
/// OSS策略键
@property(nonatomic,copy)NSString *kSCOSSPolicyPolicyKey;
/// OSS签名键
@property(nonatomic,copy)NSString *kSCOSSPolicySignatureKey;
/// OSS目录键
@property(nonatomic,copy)NSString *kSCOSSPolicyDirKey;
/// OSS回调键
@property(nonatomic,copy)NSString *kSCOSSPolicyCallbackKey;
/// OSS主机地址键
@property(nonatomic,copy)NSString *kSCOSSPolicyHostKey;

#pragma mark - Banner相关键

/// Banner图片URL键
@property(nonatomic,copy)NSString *kSCBannerPicKey;
/// Banner封面URL键（发现页专用）
@property(nonatomic,copy)NSString *kSCBannerCoverUrlKey;
/// Banner业务类型键
@property(nonatomic,copy)NSString *kSCBannerBizTypeKey;

// 强引导相关Key常量
@property(nonatomic,copy)NSString *kSCStrongGuideContentKey;
@property(nonatomic,copy)NSString *kSCStrongGuideInPkgNameKey;
@property(nonatomic,copy)NSString *kSCStrongGuideInviteCodeKey;
@property(nonatomic,copy)NSString *kSCStrongGuideNativeRechargeRedirectKey;

// 促销相关Key常量
@property(nonatomic,copy)NSString *kSCPromotionCodeKey;
@property(nonatomic,copy)NSString *kSCPromotionSurplusMillisecondKey;
@property(nonatomic,copy)NSString *kSCPromotionRemainMillisecondsKey;
@property(nonatomic,copy)NSString *kSCPromotionThirdpartyCoinPercentKey;
@property(nonatomic,copy)NSString *kSCPromotionExchangeCoinKey;
@property(nonatomic,copy)NSString *kSCPromotionExtraCoinKey;
@property(nonatomic,copy)NSString *kSCPromotionCapableRechargeNumKey;
@property(nonatomic,copy)NSString *kSCPromotionRechargeNumKey;

// 通话结果相关Key常量
@property(nonatomic,copy)NSString *kSCVideoCallResultChannelNameKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultBroadcasterIdKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultDurationKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultTagListKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultBadTagListKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultRecommendListKey;
@property(nonatomic,copy)NSString *kSCVideoCallResultIsClubServiceKey;

// 应用配置相关Key常量
@property(nonatomic,copy)NSString *kSCAppConfigRckKey;
@property(nonatomic,copy)NSString *kSCAppConfigRtckKey;
@property(nonatomic,copy)NSString *kSCAppConfigTppOpenTypeKey;
@property(nonatomic,copy)NSString *kSCAppConfigEncryptKeyKey;
@property(nonatomic,copy)NSString *kSCAppConfigRiskControlInfoConfigKey;

// 风险控制配置Key常量
@property(nonatomic,copy)NSString *kSCRiskControlKFactorKey;
@property(nonatomic,copy)NSString *kSCRiskControlKFactorNumKey;
@property(nonatomic,copy)NSString *kSCRiskControlKIntervalKey;

// 扩展数据Key常量
@property(nonatomic,copy)NSString *kSCAppExtDataBannerEnabledKey;
@property(nonatomic,copy)NSString *kSCAppExtDataBannersKey;

// 发现页横幅Key常量
@property(nonatomic,copy)NSString *kSCDiscoverBannerJumpUrlKey;
@property(nonatomic,copy)NSString *kSCDiscoverBannerCoverUrlKey;
@property(nonatomic,copy)NSString *kSCDiscoverBannerTypeKey;

// 主播网速检测Key常量
@property(nonatomic,copy)NSString *kSCAnchorNetspeedSizeLimitKey;
@property(nonatomic,copy)NSString *kSCAnchorNetspeedIntervalKey;
@property(nonatomic,copy)NSString *kSCAnchorNetspeedUrlKey;

@property(nonatomic,copy)NSString *kSCRobotQuestionSetQuestionIdKey;
@property(nonatomic,copy)NSString *kSCRobotQuestionSetContentKey;
@property(nonatomic,copy)NSString *kSCRobotQuestionSetFaqInfoListKey;

// SCRobotCustomerFAQInfoModel 相关键名
@property(nonatomic,copy)NSString *kSCRobotFAQInfoFaqIdKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoCodeKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoQuestionKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoTypeKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoImageUrlKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoMessageAnswerKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoIsViewExampleKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoHandleTypeKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoToUrlKey;
@property(nonatomic,copy)NSString *kSCRobotFAQInfoIsLikeKey;

// SCRobotCustomerMessageAnswerModel 相关键名
@property(nonatomic,copy)NSString *kSCRobotMessageAnswerContentKey;
@property(nonatomic,copy)NSString *kSCRobotMessageAnswerEventHandleListKey;

// SCRobotCustomerAnswerEventHandleModel 相关键名
@property(nonatomic,copy)NSString *kSCRobotAnswerEventHandleTypeKey;
@property(nonatomic,copy)NSString *kSCRobotAnswerEventMatchStrKey;
@property(nonatomic,copy)NSString *kSCRobotAnswerEventToUrlKey;

// SCMyQuestionModel 相关键名
@property(nonatomic,copy)NSString *kSCMyQuestionCodeKey;
@property(nonatomic,copy)NSString *kSCMyQuestionQuestionKey;


@end
#pragma mark - 便捷访问宏定义

/// 安全获取字符串值的宏
#define SC_SAFE_STRING(dict, key, defaultValue) \
    [SCDictionaryHelper stringFromDictionary:dict forKey:key defaultValue:defaultValue]

/// 安全获取整数值的宏
#define SC_SAFE_INTEGER(dict, key, defaultValue) \
    [SCDictionaryHelper integerFromDictionary:dict forKey:key defaultValue:defaultValue]

/// 安全获取布尔值的宏
#define SC_SAFE_BOOL(dict, key, defaultValue) \
    [SCDictionaryHelper boolFromDictionary:dict forKey:key defaultValue:defaultValue]

/// 安全获取数组的宏
#define SC_SAFE_ARRAY(dict, key, defaultValue) \
    [SCDictionaryHelper arrayFromDictionary:dict forKey:key defaultValue:defaultValue]

/// 安全获取字典的宏
#define SC_SAFE_DICTIONARY(dict, key, defaultValue) \
    [SCDictionaryHelper dictionaryFromDictionary:dict forKey:key defaultValue:defaultValue]

/// 安全获取用户ID的宏
#define SC_SAFE_USER_ID(dict) \
    [SCDictionaryHelper userIDFromDictionary:dict]

#pragma mark - 当前登录用户便捷访问宏

// 当前登录用户信息便捷访问宏（需要先import SCAuthManager.h）
#define kSCCurrentUserInfoDict kScAuthMar.loginUserDict[@"userInfo"]
#define kSCCurrentUserID [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""]
#define kSCCurrentUserNickname [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""]
#define kSCCurrentUserAvatarUrl [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""]
#define kSCCurrentUserAvatarThumbUrl [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserAvatarThumbUrlKey defaultValue:@""]
#define kSCCurrentUserGender [SCDictionaryHelper integerFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserGenderKey defaultValue:0]
#define kSCCurrentUserAge [SCDictionaryHelper integerFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0]
#define kSCCurrentUserCoins [SCDictionaryHelper integerFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserCoinsKey defaultValue:0]
#define kSCCurrentUserStatus [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""]
#define kSCCurrentUserIsSwitchNotDisturbIm [SCDictionaryHelper boolFromDictionary:kSCCurrentUserInfoDict forKey:@"isSwitchNotDisturbIm" defaultValue:NO]
#define kSCCurrentUserIsSwitchNotDisturbCall [SCDictionaryHelper boolFromDictionary:kSCCurrentUserInfoDict forKey:@"isSwitchNotDisturbCall" defaultValue:NO]

// 用户信息字典便捷访问宏（需要传入userDict参数）
#define kSCUserIDFromDict(userDict) [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""]
#define kSCUserNicknameFromDict(userDict) [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""]
#define kSCUserAvatarUrlFromDict(userDict) [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""]
#define kSCUserAvatarThumbUrlFromDict(userDict) [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarThumbUrlKey defaultValue:@""]
#define kSCUserGenderFromDict(userDict) [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserGenderKey defaultValue:0]
#define kSCUserAgeFromDict(userDict) [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0]
#define kSCUserCoinsFromDict(userDict) [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCoinsKey defaultValue:0]
#define kSCUserStatusFromDict(userDict) [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""]

// 礼物字典便捷访问宏（需要传入giftDict参数）
#define kSCGiftCodeFromDict(giftDict) [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:@""]
#define kSCGiftNameFromDict(giftDict) [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftNameKey defaultValue:@""]
#define kSCGiftCoinPriceFromDict(giftDict) [SCDictionaryHelper integerFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCoinPriceKey defaultValue:0]
#define kSCGiftIconPathFromDict(giftDict) [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftIconPathKey defaultValue:@""]
#define kSCGiftIconThumbPathFromDict(giftDict) [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftIconThumbPathKey defaultValue:@""]
#define kSCGiftDescFromDict(giftDict) [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftDescKey defaultValue:@""]
#define kSCGiftNumFromDict(giftDict) [SCDictionaryHelper integerFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftNumKey defaultValue:0]

// 金币字典便捷访问宏（需要传入coinsDict参数）
#define kSCCoinsGoodsIdFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsGoodsIdKey defaultValue:@""]
#define kSCCoinsCodeFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsCodeKey defaultValue:@""]
#define kSCCoinsIconFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsIconKey defaultValue:@""]
#define kSCCoinsTypeFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsTypeKey defaultValue:@""]
#define kSCCoinsTagsFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsTagsKey defaultValue:@""]
#define kSCCoinsDiscountFromDict(coinsDict) [SCDictionaryHelper floatFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsDiscountKey defaultValue:0.0]
#define kSCCoinsOriginalPriceFromDict(coinsDict) [SCDictionaryHelper floatFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsOriginalPriceKey defaultValue:0.0]
#define kSCCoinsPriceFromDict(coinsDict) [SCDictionaryHelper floatFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsPriceKey defaultValue:0.0]
#define kSCCoinsExchangeCoinFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsExchangeCoinKey defaultValue:0]
#define kSCCoinsOriginalPriceRupeeFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsOriginalPriceRupeeKey defaultValue:0]
#define kSCCoinsPriceRupeeFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsPriceRupeeKey defaultValue:0]
#define kSCCoinsLocalPaymentPriceRupeeFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsLocalPaymentPriceRupeeKey defaultValue:0]
#define kSCCoinsIsPromotionFromDict(coinsDict) [SCDictionaryHelper boolFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsIsPromotionKey defaultValue:NO]
#define kSCCoinsLocalPayOriginalPriceFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsLocalPayOriginalPriceKey defaultValue:0]
#define kSCCoinsLocalPayPriceFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsLocalPayPriceKey defaultValue:0]
#define kSCCoinsExtraCoinPercentFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsExtraCoinPercentKey defaultValue:0]
#define kSCCoinsInvitationIdFromDict(coinsDict) [SCDictionaryHelper stringFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsInvitationIdKey defaultValue:@""]
#define kSCCoinsSubTypeFromDict(coinsDict) [SCDictionaryHelper integerFromDictionary:coinsDict forKey:SCDictionaryKeys.shared.kSCCoinsSubTypeKey defaultValue:0]

// 消息字典便捷访问宏（需要传入messageDict参数）
#define kSCMessageIdFromDict(messageDict) [SCDictionaryHelper stringFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageIdKey defaultValue:@""]
#define kSCMessageContentFromDict(messageDict) [SCDictionaryHelper stringFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageContentKey defaultValue:@""]
#define kSCMessageTypeFromDict(messageDict) [SCDictionaryHelper integerFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageTypeKey defaultValue:0]
#define kSCMessageFromUserIdFromDict(messageDict) [SCDictionaryHelper stringFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageFromUserIdKey defaultValue:@""]
#define kSCMessageToUserIdFromDict(messageDict) [SCDictionaryHelper stringFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageToUserIdKey defaultValue:@""]
#define kSCMessageTimestampFromDict(messageDict) [SCDictionaryHelper doubleFromDictionary:messageDict forKey:SCDictionaryKeys.shared.kSCMessageTimestampKey defaultValue:0.0]

// 订单字典便捷访问宏（需要传入orderDict参数）
#define kSCOrderGoodsCodeFromDict(orderDict) [SCDictionaryHelper stringFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderGoodsCodeKey defaultValue:@""]
#define kSCOrderGoodsNameFromDict(orderDict) [SCDictionaryHelper stringFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderGoodsNameKey defaultValue:@""]
#define kSCOrderNoFromDict(orderDict) [SCDictionaryHelper stringFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderNoKey defaultValue:@""]
#define kSCOrderPayAmountFromDict(orderDict) [SCDictionaryHelper doubleFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCPayAmountKey defaultValue:0.0]
#define kSCOrderPaidAmountFromDict(orderDict) [SCDictionaryHelper doubleFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderPaidAmountKey defaultValue:0.0]
#define kSCOrderPaidCurrencyFromDict(orderDict) [SCDictionaryHelper stringFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderPaidCurrencyKey defaultValue:@""]
#define kSCOrderRequestUrlFromDict(orderDict) [SCDictionaryHelper stringFromDictionary:orderDict forKey:SCDictionaryKeys.shared.kSCOrderRequestUrlKey defaultValue:@""]

NS_ASSUME_NONNULL_END
