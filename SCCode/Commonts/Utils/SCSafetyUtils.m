//
//  SCSafetyUtils.m
//  Supercall
//
//  Created by sumengliu on 2024/10/21.
//


#import "SCSafetyUtils.h"
#import <NetworkExtension/NetworkExtension.h>
#import <CFNetwork/CFNetwork.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import <CoreTelephony/CTCarrier.h>
#import <SystemConfiguration/SystemConfiguration.h>
//#import "SCAppConfigModel.h"
#import "SCAppUtils.h"
#import "SCCryptoUtils.h"
#import <sys/utsname.h>

@implementation SCSafetyUtils

+ (BOOL)isEnabledVPN {
    BOOL flag = NO;
    NSString *version = [UIDevice currentDevice].systemVersion;
    
    NSArray *truncatedString = [version componentsSeparatedByString:@"."];
    double versionNum = [version doubleValue];
    if (truncatedString.count > 0) {
        versionNum = [truncatedString.firstObject doubleValue];
    }
    
    if (versionNum >= 9.0) {
        NSDictionary *dict = (__bridge NSDictionary *)CFNetworkCopySystemProxySettings();
        NSDictionary *keys = dict[@"__SCOPED__"];
        for (NSString *key in keys.allKeys) {
            if ([key rangeOfString:@"tap"].location != NSNotFound ||
                [key rangeOfString:@"tun"].location != NSNotFound ||
                [key rangeOfString:@"ipsec"].location != NSNotFound ||
                [key rangeOfString:@"ppp"].location != NSNotFound) {
                flag = YES;
                break;
            }
        }
    }
    
    return flag;
}

+ (BOOL)isProxyEnabled {
    NSDictionary *proxySettings = (__bridge NSDictionary *)CFNetworkCopySystemProxySettings();
    NSNumber *proxyType = proxySettings[(NSString *)kCFNetworkProxiesHTTPEnable];
    return [proxyType boolValue];
}

+ (NSString *)getTimeZone {
    return [NSTimeZone systemTimeZone].name;
}

+ (BOOL)isAutoTimeZone {
    return [NSTimeZone systemTimeZone].daylightSavingTime;
}

+ (BOOL)isSimulator {
#if TARGET_IPHONE_SIMULATOR
    return YES;
#else
    return NO;
#endif
}

+ (BOOL)isJailbroken {
    NSArray *jailbreakFilePaths = @[
        @"/Applications/Cydia.app",
        @"/Library/MobileSubstrate/MobileSubstrate.dylib",
        @"/bin/bash",
        @"/usr/sbin/sshd",
        @"/etc/apt"
    ];
    
    for (NSString *path in jailbreakFilePaths) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
            return YES;
        }
    }
    
    return NO;
}

+ (NSDictionary *)info {
    NSMutableDictionary *deviceInfoMap = [NSMutableDictionary dictionary];
    deviceInfoMap[@"platform"] = @"iOS";
    deviceInfoMap[@"pkg"] = [[NSBundle mainBundle] bundleIdentifier];
    deviceInfoMap[@"ver"] = [[[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"] replaceMoreThan100];
    deviceInfoMap[@"platform_ver"] = [UIDevice currentDevice].systemVersion;
    deviceInfoMap[@"model"] = [[self getDeviceModel] replaceMoreThan100];
    NSString * userId = kSCCurrentUserID;
    if (!kSCIsStrEmpty(userId)) {
        deviceInfoMap[@"user_id"] = [userId replaceMoreThan100];
    }
    deviceInfoMap[@"device_id"] = [SCAppUtils.identifier replaceMoreThan100];
    deviceInfoMap[@"is_enable_vpn"] = [self isEnabledVPN] ? @"1" : @"0";
    deviceInfoMap[@"is_enable_proxy"] = [self isProxyEnabled] ? @"1" : @"0";
    deviceInfoMap[@"system_language"] = [SCAppUtils getCountryLang] ?: @"en";
    deviceInfoMap[@"time_zone"] = [[self getTimeZone] replaceMoreThan100];
    deviceInfoMap[@"is_jailbreaking"] = [self isJailbroken] ? @"1" : @"0";
    deviceInfoMap[@"is_emulator"] = [self isSimulator] ? @"1" : @"0";
    deviceInfoMap[@"input_language"] = [SCAppUtils getAllInputLanguages];
    
    
    
    return [deviceInfoMap copy];
}

+ (nullable NSString *)encryptInfo {
    // 从配置字典中获取风险控制配置的k_factor
    NSDictionary *riskControlConfig = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.appConfig forKey:@"riskControlInfoConfig" defaultValue:@{}];
    NSString *aesKey = [SCDictionaryHelper stringFromDictionary:riskControlConfig forKey:@"k_factor" defaultValue:nil];
    NSString *jsonString = nil;
    
    if (aesKey) {
        NSError *error;
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:[self info] options:NSJSONWritingPrettyPrinted error:&error];
        if (!error) {
            NSString *jsonString2 = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            jsonString = [SCCryptoUtils encryptECB:jsonString2 key:aesKey];
            if (jsonString) {
                jsonString = [SCCryptoUtils convertToRFC4648URLSafe:jsonString];
            }
        } else {
            
        }
    }
    
    return jsonString;
}

+ (NSString *)getDeviceModel {
    static NSString *deviceModel;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        struct utsname systemInfo;
        uname(&systemInfo);
        deviceModel = [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
    });
    return deviceModel;
}

+ (NSDictionary *)extraHeaders {
    NSDictionary *headers = @{
        @"device_lang": [SCAppUtils getCountryLang],
        @"device_country": [SCAppUtils getCountryCode],
        @"time_zone": [SCSafetyUtils getTimeZone]
    };
    
    return headers;
}

@end
