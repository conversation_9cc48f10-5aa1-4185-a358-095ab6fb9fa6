//
//  SCCryptoUtils.m
//  Supercall
//
//  Created by sum<PERSON><PERSON><PERSON> on 2024/10/21.
//

#import "SCCryptoUtils.h"
#import <CommonCrypto/CommonCrypto.h>
@implementation SCCryptoUtils

+ (NSString *)encryptECB:(NSString *)cleartext key:(NSString *)aesKey {
    // 检查密钥长度，AES 支持的密钥长度为 16、24 或 32 字节
    if (aesKey.length != kCCKeySizeAES128 && aesKey.length != kCCKeySizeAES192 && aesKey.length != kCCKeySizeAES256) {
        
        return nil;
    }
    NSData *keyData = [aesKey dataUsingEncoding:NSUTF8StringEncoding];
    NSData *plainData = [cleartext dataUsingEncoding:NSUTF8StringEncoding];
    // 设置加密参数
    size_t bufferSize = cleartext.length + kCCBlockSizeAES128; // 申请足够的空间
    void *buffer = malloc(bufferSize);
    size_t bytesEncrypted = 0;

    // 执行加密
    CCCryptorStatus status = CCCrypt(kCCEncrypt,                // 操作
                                     kCCAlgorithmAES,          // 算法
                                     kCCOptionPKCS7Padding | kCCOptionECBMode, // 选项
                                     keyData.bytes,             // 密钥
                                     keyData.length,            // 密钥长度
                                     NULL,                     // IV，ECB 模式下为 NULL
                                     plainData.bytes,          // 输入数据
                                     plainData.length,         // 输入长度
                                     buffer,                   // 输出缓冲区
                                     bufferSize,               // 输出缓冲区大小
                                     &bytesEncrypted);         // 输出长度

    if (status == kCCSuccess) {
        // 创建加密数据
        NSData *encryptedData = [NSData dataWithBytesNoCopy:buffer length:bytesEncrypted];

        // 返回 Base64 编码的字符串
        return [encryptedData base64EncodedStringWithOptions:0];
    }

    free(buffer); // 释放缓冲区
    
    return nil;
}

//+ (nullable NSString *)encryptECB:(NSString *)plainText key:(NSString *)key {
//    NSData *plainData = [plainText dataUsingEncoding:NSUTF8StringEncoding];
//    NSData *keyData = [key dataUsingEncoding:NSUTF8StringEncoding];
//    
//    if (!plainData || !keyData) {
//        return nil;
//    }
//    
//    size_t outLength;
//    NSMutableData *cipherData = [NSMutableData dataWithLength:plainData.length + kCCBlockSizeAES128];
//    
//    CCCryptorStatus result = CCCrypt(kCCEncrypt,
//                                     kCCAlgorithmAES,
//                            kCCOptionPKCS7Padding,
//                                     keyData.bytes,
//                                     kCCKeySizeAES128,
//                                     NULL,
//                                     plainData.bytes,
//                                     plainData.length,
//                                     cipherData.mutableBytes,
//                                     cipherData.length,
//                                     &outLength);
//    
//    if (result == kCCSuccess) {
//        cipherData.length = outLength;
//        
//        return [cipherData base64Encoding];
//    }
//    
//    return nil;
//}

+ (NSString *)convertToRFC4648URLSafe:(NSString *)base64String {
    NSString *urlSafeString = [base64String stringByReplacingOccurrencesOfString:@"+" withString:@"-"];
    urlSafeString = [urlSafeString stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    urlSafeString = [urlSafeString stringByReplacingOccurrencesOfString:@"=" withString:@""];
    return urlSafeString;
}


+ (NSString *)decryptECB:(NSString *)cipherText key:(NSString *)key {
    NSString *replacedText = [cipherText stringByReplacingOccurrencesOfString:@"\r\n" withString:@""];
    NSData *encryptedData = [[NSData alloc] initWithBase64EncodedString:replacedText options:0];
    NSData *aesKey = [key dataUsingEncoding:NSUTF8StringEncoding];
    
    // 检查密钥长度，AES 支持的密钥长度为 16、24 或 32 字节
    if (aesKey.length != kCCKeySizeAES128 && aesKey.length != kCCKeySizeAES192 && aesKey.length != kCCKeySizeAES256) {
        
        return nil;
    }

    size_t bufferSize = encryptedData.length + kCCBlockSizeAES128; // 申请足够的空间
    void *buffer = malloc(bufferSize);
    size_t bytesDecrypted = 0;

    CCCryptorStatus status = CCCrypt(kCCDecrypt,                // 操作
                                     kCCAlgorithmAES,          // 算法
                                     kCCOptionPKCS7Padding | kCCOptionECBMode, // 选项
                                     aesKey.bytes,             // 密钥
                                     aesKey.length,            // 密钥长度
                                     NULL,                     // IV，ECB 模式下为 NULL
                                     encryptedData.bytes,      // 输入数据
                                     encryptedData.length,     // 输入长度
                                     buffer,                   // 输出缓冲区
                                     bufferSize,               // 输出缓冲区大小
                                     &bytesDecrypted);         // 输出长度

    if (status == kCCSuccess) {
        NSData *decryptedData = [NSData dataWithBytesNoCopy:buffer length:bytesDecrypted];
        return [[NSString alloc] initWithData:decryptedData encoding:NSUTF8StringEncoding];
    }

    free(buffer);
    
    return nil;
}


+ (nullable NSData *)aesEncrypt:(NSData *)data key:(NSData *)key iv:(NSData *)iv {
    NSUInteger dataLength = [data length];
    size_t bufferSize = dataLength + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    
    size_t numBytesEncrypted = 0;
    CCCryptorStatus cryptStatus = CCCrypt(kCCEncrypt,
                                          kCCAlgorithmAES,
                                          kCCOptionPKCS7Padding,
                                          [key bytes],
                                          kCCKeySizeAES128,
                                          [iv bytes],
                                          [data bytes],
                                          dataLength,
                                          buffer,
                                          bufferSize,
                                          &numBytesEncrypted);
    
    if (cryptStatus == kCCSuccess) {
        return [NSData dataWithBytesNoCopy:buffer length:numBytesEncrypted];
    }
    
    free(buffer);
    return nil;
}

+ (nullable NSData *)decryptAES:(NSData *)data key:(NSData *)key iv:(NSData *)iv {
    NSUInteger dataLength = [data length];
    size_t bufferSize = dataLength + kCCBlockSizeAES128;
    void *buffer = malloc(bufferSize);
    
    size_t numBytesDecrypted = 0;
    CCCryptorStatus cryptStatus = CCCrypt(kCCDecrypt,
                                          kCCAlgorithmAES,
                                          kCCOptionPKCS7Padding,
                                          [key bytes],
                                          kCCKeySizeAES128,
                                          [iv bytes],
                                          [data bytes],
                                          dataLength,
                                          buffer,
                                          bufferSize,
                                          &numBytesDecrypted);
    
    if (cryptStatus == kCCSuccess) {
        return [NSData dataWithBytesNoCopy:buffer length:numBytesDecrypted];
    }
    
    free(buffer);
    return nil;
}

+ (NSData *)dataFromHexString:(NSString *)hexStr {
    NSString *hexStr1 = hexStr;
    if (hexStr.length % 2 != 0) {
        hexStr1 = [@"0" stringByAppendingString:hexStr];
    }
    NSArray<NSNumber *> *bytes = [self bytesFromHexString:hexStr1];
    
    NSMutableData *data = [NSMutableData dataWithCapacity:bytes.count];
    for (NSNumber *byte in bytes) {
        uint8_t byteValue = byte.unsignedCharValue;
        [data appendBytes:&byteValue length:1];
    }
    
    return data;
}

+ (NSArray<NSNumber *> *)bytesFromHexString:(NSString *)hexStr {
    NSCAssert(hexStr.length % 2 == 0, @"输入字符串格式不对，8位代表一个字符");
    NSMutableArray<NSNumber *> *bytes = [NSMutableArray array];
    NSUInteger sum = 0;
    
    for (NSUInteger i = 0; i < hexStr.length; i++) {
        unichar c = [hexStr characterAtIndex:i];
        NSInteger intC = 0;
        
        if (c >= '0' && c <= '9') {
            intC = c - '0';
        } else if (c >= 'a' && c <= 'f') {
            intC = c - 'a' + 10;
        } else if (c >= 'A' && c <= 'F') {
            intC = c - 'A' + 10;
        } else {
            NSCAssert(NO, @"输入字符串格式不对，每个字符都需要在0~9，a~f，A~F内");
        }
        
        sum = sum * 16 + intC;
        
        if (i % 2 != 0) {
            [bytes addObject:@(sum)];
            sum = 0;
        }
    }
    
    
    return [bytes copy];
}

+ (nullable NSString *)fileMD5:(NSURL *)fileURL {
    NSError *error = nil;
    NSData *fileData = [NSData dataWithContentsOfURL:fileURL options:NSDataReadingMappedIfSafe error:&error];
    
    if (error) {
        
        return nil;
    }
    
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5(fileData.bytes, (CC_LONG)fileData.length, digest);
    
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", digest[i]];
    }
    
    return [md5String copy];
}

+ (nullable NSString *)strMD5:(NSString *)str {
    NSData *data = [str dataUsingEncoding:NSUTF8StringEncoding];
    
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5(data.bytes, (CC_LONG)data.length, digest);
    
    NSMutableString *md5String = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [md5String appendFormat:@"%02x", digest[i]];
    }
    
    return [md5String copy];
}

@end
