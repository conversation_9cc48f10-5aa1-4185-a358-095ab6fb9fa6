//
//  SCDataConverter.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  数据转换工具类 - 处理JSON与字典的转换
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCDataConverter : NSObject

#pragma mark - JSON转换方法

/**
 * 将JSON字符串转换为字典
 * @param jsonString JSON字符串
 * @return 转换后的字典，失败时返回nil
 */
+ (nullable NSDictionary *)dictionaryFromJSONString:(NSString *)jsonString;

/**
 * 将JSON数据转换为字典
 * @param jsonData JSON数据
 * @return 转换后的字典，失败时返回nil
 */
+ (nullable NSDictionary *)dictionaryFromJSONData:(NSData *)jsonData;

/**
 * 将字典转换为JSON字符串
 * @param dictionary 源字典
 * @return JSON字符串，失败时返回nil
 */
+ (nullable NSString *)JSONStringFromDictionary:(NSDictionary *)dictionary;

/**
 * 将字典转换为JSON字符串（别名方法）
 * @param dictionary 源字典
 * @return JSON字符串，失败时返回nil
 */
+ (nullable NSString *)jsonStringFromDictionary:(NSDictionary *)dictionary;

/**
 * 安全地从JSON字符串转换为字典
 * @param jsonString JSON字符串
 * @return 转换后的字典，失败时返回nil
 */
+ (nullable NSDictionary *)safeDictionaryFromJSONString:(NSString *)jsonString;

/**
 * 将字典转换为JSON数据
 * @param dictionary 源字典
 * @return JSON数据，失败时返回nil
 */
+ (nullable NSData *)JSONDataFromDictionary:(NSDictionary *)dictionary;

#pragma mark - 数组转换方法

/**
 * 将JSON字符串转换为数组
 * @param jsonString JSON字符串
 * @return 转换后的数组，失败时返回nil
 */
+ (nullable NSArray *)arrayFromJSONString:(NSString *)jsonString;

/**
 * 将JSON数据转换为数组
 * @param jsonData JSON数据
 * @return 转换后的数组，失败时返回nil
 */
+ (nullable NSArray *)arrayFromJSONData:(NSData *)jsonData;

/**
 * 将数组转换为JSON字符串
 * @param array 源数组
 * @return JSON字符串，失败时返回nil
 */
+ (nullable NSString *)JSONStringFromArray:(NSArray *)array;

/**
 * 将数组转换为JSON数据
 * @param array 源数组
 * @return JSON数据，失败时返回nil
 */
+ (nullable NSData *)JSONDataFromArray:(NSArray *)array;

#pragma mark - 通用转换方法

/**
 * 将任意对象转换为JSON字符串
 * @param object 源对象（字典、数组等）
 * @return JSON字符串，失败时返回nil
 */
+ (nullable NSString *)JSONStringFromObject:(id)object;

/**
 * 将任意对象转换为JSON数据
 * @param object 源对象（字典、数组等）
 * @return JSON数据，失败时返回nil
 */
+ (nullable NSData *)JSONDataFromObject:(id)object;

/**
 * 将JSON字符串或数据转换为对象
 * @param json JSON字符串或NSData对象
 * @return 转换后的对象（字典或数组），失败时返回nil
 */
+ (nullable id)objectFromJSON:(id)json;

#pragma mark - 数据验证方法

/**
 * 验证字符串是否为有效的JSON格式
 * @param jsonString 待验证的字符串
 * @return 是否为有效JSON
 */
+ (BOOL)isValidJSONString:(NSString *)jsonString;

/**
 * 验证数据是否为有效的JSON格式
 * @param jsonData 待验证的数据
 * @return 是否为有效JSON
 */
+ (BOOL)isValidJSONData:(NSData *)jsonData;

#pragma mark - 错误处理

/**
 * 获取最后一次转换的错误信息
 * @return 错误信息，无错误时返回nil
 */
+ (nullable NSError *)lastConversionError;

#pragma mark - 便捷方法

/**
 * 安全地从响应对象中提取字典
 * 支持直接的字典对象或需要JSON解析的数据
 * @param responseObject 响应对象
 * @return 字典对象，失败时返回nil
 */
+ (nullable NSDictionary *)safeDictionaryFromResponseObject:(id)responseObject;

/**
 * 安全地从响应对象中提取数组
 * 支持直接的数组对象或需要JSON解析的数据
 * @param responseObject 响应对象
 * @return 数组对象，失败时返回nil
 */
+ (nullable NSArray *)safeArrayFromResponseObject:(id)responseObject;

@end

NS_ASSUME_NONNULL_END
