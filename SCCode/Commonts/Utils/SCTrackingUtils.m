//
//  SCTrackingUtils.m
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/3/21.
//

#import "SCTrackingUtils.h"
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "SCAuthManager.h"
#import "SCAppUtils.h"
//#import "SCAppConfigModel.h"
#import "SCAPIServiceManager.h"

@interface SCTrackingUtils()

@property (nonatomic, strong) NSMutableDictionary *afHeaders;
@property (nonatomic, strong) ADJAttribution *attribution; // Adjust归因对象
@property (nonatomic, assign) BOOL isHaveUp;
@end

@implementation SCTrackingUtils

+ (instancetype)shared {
    static SCTrackingUtils *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

#pragma mark - Initialization Methods

// 处理 Facebook SDK 设置
- (void)setupFacebookSDKWithCompletion:(void(^)(BOOL success))completion {
    BOOL isSettingFB = [[NSUserDefaults standardUserDefaults] boolForKey:kSCIsSettingFacebook];
    
    // 检查是否需要更新 Facebook 配置
    if (!isSettingFB && [self isValidFacebookConfig]) {
        [self saveFacebookConfig];
    }
    
    // 配置 Facebook SDK
    NSString *appFBId = [[NSUserDefaults standardUserDefaults] objectForKey:kSCFacebookId];
    NSString *appFBClientToken = [[NSUserDefaults standardUserDefaults] objectForKey:KSCFacebookClientToken];
    
    if (!kSCIsStrEmpty(appFBId) && !kSCIsStrEmpty(appFBClientToken)) {
        FBSDKSettings.sharedSettings.appID = appFBId;
        FBSDKSettings.sharedSettings.clientToken = appFBClientToken;
        FBSDKSettings.sharedSettings.appURLSchemeSuffix = appFBId;
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kSCIsSettingFacebook];
        if (completion) completion(YES);
        return;
    }
    
    if (completion) completion(NO);
}

// 检查 Facebook 配置是否有效
- (BOOL)isValidFacebookConfig {
    // 从配置字典中获取Facebook配置
    NSString *fbId = [SCDictionaryHelper appFBIdFromConfigDict:kScAuthMar.appConfig];
    NSString *fbClientToken = [SCDictionaryHelper appFBClientTokenFromConfigDict:kScAuthMar.appConfig];
    return !kSCIsStrEmpty(fbId) && !kSCIsStrEmpty(fbClientToken);
}

// 保存 Facebook 配置到本地
- (void)saveFacebookConfig {
    // 从配置字典中获取Facebook配置
    NSString *fbId = [SCDictionaryHelper appFBIdFromConfigDict:kScAuthMar.appConfig];
    NSString *fbClientToken = [SCDictionaryHelper appFBClientTokenFromConfigDict:kScAuthMar.appConfig];

    [[NSUserDefaults standardUserDefaults] setObject:fbId forKey:kSCFacebookId];
    [[NSUserDefaults standardUserDefaults] setObject:fbClientToken forKey:KSCFacebookClientToken];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)initAppsFlyerisDebug:(BOOL)isDebug {
    // 启用Adjust SDK初始化
    ADJConfig *adjustConfig = [ADJConfig configWithAppToken:SCAPIManage.shared.kAdjustToken environment:ADJEnvironmentProduction];
    adjustConfig.delegate = self;
    adjustConfig.logLevel = ADJLogLevelVerbose;
    adjustConfig.sendInBackground = YES;
    adjustConfig.attConsentWaitingInterval = 30;

    [Adjust appDidLaunch:adjustConfig];

    // 获取当前归因信息
    ADJAttribution *currentAttribution = [Adjust attribution];
    if (currentAttribution) {
        self.attribution = currentAttribution;
        [self updateAppsFlyerHeaders];
    }
}

- (void)adjustAttributionChanged:(ADJAttribution *)attribution {
    self.attribution = attribution;
    [self updateAppsFlyerHeaders];

    // 归因信息更新后自动上报
    NSString *currentToken = [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];
    [self reportAscribeRecordWithToken:currentToken userId:kSCCurrentUserID];
}

- (void)reportAscribeRecordWithToken:(NSString *)token userId:(NSString *)userId {
    if (userId != nil && ![userId isEqualToString:@""] && token != nil && ![token isEqualToString:@""] && !self.isHaveUp) {
        
        // Handle non-organic install
        NSDictionary *params = @{
            @"adgroupId": self.attribution.adgroup ?: @"",
            @"adset": @"",
            @"adsetId": self.attribution.creative ?: @"",
            @"afChannel": @"",
            @"afStatus": @"",
            @"agency": @"",
            @"campaign": @"",
            @"campaignId": self.attribution.campaign ?: @"",
            @"createTime": @([[NSDate date] timeIntervalSince1970]),
            @"deviceId": SCAppUtils.identifier,
            @"id": userId ?: @"",
            @"installUtmSource": @"",
            @"pkg": [[NSBundle mainBundle] bundleIdentifier] ?: @"",
            @"userId": userId ?: @"",
            @"utmSource": self.attribution.network ?: @"",
            @"ver": [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"]
        };
        
        [SCAPIServiceManager requestUploadAFWithParam:params token:token success:^{
            self.isHaveUp = YES;
        } failure:nil];
    }
    
}

#pragma mark - Utility Methods

- (NSString *)checkValue:(id)value {
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return @"";
    }
    NSString *strValue = [NSString stringWithFormat:@"%@", value];
    return [strValue stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
}

#pragma mark - Class Methods

+ (void)requestATTPermission {
    if (@available(iOS 14.0, *)) {
        [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
            if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
                FBSDKSettings.sharedSettings.isAdvertiserTrackingEnabled = YES;
            }
        }];
    } else {
        FBSDKSettings.sharedSettings.isAdvertiserTrackingEnabled = YES;
    }
}

+ (void)activateFacebookApp {
    [FBSDKAppEvents.shared activateApp];
}

//+ (void)startAppsFlyer {
//    [[AppsFlyerLib shared] start];
//}

#pragma mark - Event Tracking

+ (void)trackProductClickWithProductId:(NSString *)productId price:(double)price originPrice:(double)originPrice {
    // Facebook Events
    NSDictionary *parameters = @{
        @"product_id": productId,
        @"product_price": @(price),
        @"product_origin_price": @(originPrice)
    };
    [FBSDKAppEvents.shared logEvent:@"click_pay" parameters:parameters];
    
    // AppsFlyer Events
    
}

+ (void)trackPurchaseSuccessWithOrderNo:(NSString *)orderNo price:(double)price {
    [self trackBasePurchaseSuccessWithPrice:price];
}

+ (void)trackStartPurchaseWithPrice:(double)price {
    [self trackBaseStartPurchaseWithPrice:price];
}

+ (void)trackEventWithName:(NSString *)name params:(NSDictionary *)params {
    [FBSDKAppEvents.shared logEvent:name parameters:params];
}

#pragma mark - Private Methods

+ (double)getRevenueFactor {
    // Implement revenue factor logic based on your configuration
    return 1.0;
}

+ (void)trackBaseStartPurchaseWithPrice:(double)price {
    // Facebook
    [FBSDKAppEvents.shared logEvent:FBSDKAppEventNameInitiatedCheckout valueToSum:price parameters:@{
        FBSDKAppEventParameterNameCurrency: @"USD"
    }];
    
    // AppsFlyer
    ADJEvent *event = [ADJEvent eventWithEventToken:SCAPIManage.shared.kAdjustOrderToken];
    [event setRevenue:price currency:@"USD"];
    [Adjust trackEvent:event];
}

+ (void)trackBasePurchaseSuccessWithPrice:(double)price {
    // Facebook
    [FBSDKAppEvents.shared logPurchase:price currency:@"USD" parameters:nil];
    
    // AppsFlyer
    
    
    ADJEvent *event = [ADJEvent eventWithEventToken:SCAPIManage.shared.kAdjustPurchaseToken];
    [event setRevenue:price currency:@"USD"];
    [Adjust trackEvent:event];
}
+ (void)trackAjEvent:(NSString *)eventname{
    ADJEvent *event = [ADJEvent eventWithEventToken:eventname];
    [Adjust trackEvent:event];
}


- (instancetype)init {
    self = [super init];
    if (self) {
        _afHeaders = [NSMutableDictionary dictionary];
    }
    return self;
}

// 更新 AF 头部信息
- (void)updateAppsFlyerHeaders{
    @synchronized (self.afHeaders) {
        self.afHeaders[@"utm-source"] = self.attribution.network ?: @"";
        self.afHeaders[@"af_adgroup_id"] = self.attribution.adgroup ?: @"";
        self.afHeaders[@"af_adset"] = @"";
        self.afHeaders[@"af_adset_id"] = self.attribution.creative ?: @"";
        self.afHeaders[@"af_status"] = @"";
        self.afHeaders[@"af_agency"] = @"";
        self.afHeaders[@"af_channel"] = @"";
        self.afHeaders[@"campaign"] = @"";
        self.afHeaders[@"campaign_id"] = self.attribution.campaign ?: @"";
    }
}

// 获取 AF 头部信息的只读属性
- (NSDictionary *)appsFlyerHeaders {
    @synchronized (self.afHeaders) {
        return [self.afHeaders copy];
    }
}

@end 
