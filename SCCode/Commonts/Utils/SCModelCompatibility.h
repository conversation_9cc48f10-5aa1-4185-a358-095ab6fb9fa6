//
//  SCModelCompatibility.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  Model兼容层 - 过渡期间model对象与字典的转换
//

#import <Foundation/Foundation.h>
#import "SCDictionaryKeys.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCModelCompatibility : NSObject

#pragma mark - Model转字典方法

/**
 * 将任意model对象转换为字典
 * 使用YYModel的yy_modelToJSONObject方法
 * @param model model对象
 * @return 字典对象，失败时返回nil
 */
+ (nullable NSDictionary *)dictionaryFromModel:(id)model;

/**
 * 将model对象数组转换为字典数组
 * @param modelArray model对象数组
 * @return 字典数组，失败时返回nil
 */
+ (nullable NSArray<NSDictionary *> *)dictionaryArrayFromModelArray:(NSArray *)modelArray;

#pragma mark - 字典转Model方法（临时使用）

/**
 * 将字典转换为指定类型的model对象
 * 使用YYModel的yy_modelWithJSON方法
 * @param dictionary 字典对象
 * @param modelClass model类
 * @return model对象，失败时返回nil
 */
+ (nullable id)modelFromDictionary:(NSDictionary *)dictionary modelClass:(Class)modelClass;

#pragma mark - 消息相关转换方法
/**
 * 将消息Model转换为字典
 * @param message 消息Model对象
 * @return 字典对象，失败时返回nil
 */
+ (nullable NSDictionary *)dictionaryFromMessage:(id)message;

/**
 * 从字典创建消息Model
 * @param messageDict 消息字典
 * @return 消息Model对象，失败时返回nil
 */
+ (nullable id)messageFromDictionary:(NSDictionary *)messageDict;

/**
 * 将消息Model数组转换为字典数组
 * @param messages 消息Model数组
 * @return 字典数组，失败时返回nil
 */
+ (nullable NSArray<NSDictionary *> *)dictionaryArrayFromMessages:(NSArray *)messages;

#pragma mark - 数据结构映射

/**
 * 获取用户信息字典的标准键映射
 * 将各种可能的用户ID键名统一为标准格式
 * @param userDict 用户信息字典
 * @return 标准化后的用户信息字典
 */
+ (NSDictionary *)standardizedUserDictionary:(NSDictionary *)userDict;

/**
 * 获取礼物信息字典的标准键映射
 * @param giftDict 礼物信息字典
 * @return 标准化后的礼物信息字典
 */
+ (NSDictionary *)standardizedGiftDictionary:(NSDictionary *)giftDict;

/**
 * 获取媒体信息字典的标准键映射
 * @param mediaDict 媒体信息字典
 * @return 标准化后的媒体信息字典
 */
+ (NSDictionary *)standardizedMediaDictionary:(NSDictionary *)mediaDict;

#pragma mark - 常用字典键常量

// 注意：大部分字典键常量已移至 SCDictionaryKeys.h 中统一管理
// 请导入 SCDictionaryKeys.h 来使用以下常量：
// - kSCUserIDKey, kSCUserNicknameKey, kSCUserAgeKey, kSCUserCountryKey, kSCUserStatusKey, kSCUserCoinsKey
// - kSCGiftCodeKey, kSCGiftNameKey, kSCGiftDescKey
// - kSCMediaTypeKey, kSCMediaSortKey

// SCModelCompatibility 特有的常量


#pragma mark - 便捷访问方法

/**
 * 从字典中安全获取用户ID
 * 支持多种键名格式
 * @param dictionary 字典
 * @return 用户ID字符串
 */
+ (NSString *)userIDFromDictionary:(NSDictionary *)dictionary;

/**
 * 从字典中安全获取用户昵称
 * @param dictionary 字典
 * @return 用户昵称
 */
+ (NSString *)nicknameFromDictionary:(NSDictionary *)dictionary;

/**
 * 从字典中安全获取头像URL
 * @param dictionary 字典
 * @return 头像URL字符串
 */
+ (NSString *)avatarURLFromDictionary:(NSDictionary *)dictionary;

/**
 * 从字典中安全获取用户年龄
 * @param dictionary 字典
 * @return 年龄
 */
+ (NSInteger)ageFromDictionary:(NSDictionary *)dictionary;

/**
 * 从字典中安全获取用户国家
 * @param dictionary 字典
 * @return 国家名称
 */
+ (NSString *)countryFromDictionary:(NSDictionary *)dictionary;

/**
 * 从字典中安全获取用户金币数
 * @param dictionary 字典
 * @return 金币数
 */
+ (NSInteger)coinsFromDictionary:(NSDictionary *)dictionary;

@end

NS_ASSUME_NONNULL_END
