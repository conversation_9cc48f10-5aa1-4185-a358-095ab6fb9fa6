//
//  SCDictionaryHelper.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  字典工具类 - 提供类型安全的字典访问方法
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <RongIMLibCore/RongIMLibCore.h>

NS_ASSUME_NONNULL_BEGIN



// 主播状态枚举定义
typedef NS_ENUM(NSInteger, SCAnchorStatus) {
    AnchorStatusUnknown,
    AnchorStatusOffline,
    AnchorStatusOnline,
    AnchorStatusBusy,
    AnchorStatusIncall
};

@interface SCDictionaryHelper : NSObject

#pragma mark - 基础类型安全取值方法

/**
 * 安全获取字符串值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值，当key不存在或值为nil时返回
 * @return 字符串值
 */
+ (NSString *)stringFromDictionary:(NSDictionary *)dictionary 
                            forKey:(NSString *)key 
                      defaultValue:(nullable NSString *)defaultValue;

/**
 * 安全获取整数值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 整数值
 */
+ (NSInteger)integerFromDictionary:(NSDictionary *)dictionary 
                            forKey:(NSString *)key 
                      defaultValue:(NSInteger)defaultValue;

/**
 * 安全获取长整数值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 长整数值
 */
+ (long long)longLongFromDictionary:(NSDictionary *)dictionary 
                             forKey:(NSString *)key 
                       defaultValue:(long long)defaultValue;

/**
 * 安全获取浮点数值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 浮点数值
 */
+ (CGFloat)floatFromDictionary:(NSDictionary *)dictionary 
                        forKey:(NSString *)key 
                  defaultValue:(CGFloat)defaultValue;

/**
 * 安全获取双精度浮点数值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 双精度浮点数值
 */
+ (double)doubleFromDictionary:(NSDictionary *)dictionary 
                        forKey:(NSString *)key 
                  defaultValue:(double)defaultValue;

/**
 * 安全获取布尔值
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 布尔值
 */
+ (BOOL)boolFromDictionary:(NSDictionary *)dictionary 
                    forKey:(NSString *)key 
              defaultValue:(BOOL)defaultValue;

#pragma mark - 复合类型取值方法

/**
 * 安全获取数组
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 数组
 */
+ (NSArray *)arrayFromDictionary:(NSDictionary *)dictionary 
                          forKey:(NSString *)key 
                    defaultValue:(nullable NSArray *)defaultValue;

/**
 * 安全获取字典
 * @param dictionary 目标字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 字典
 */
+ (NSDictionary *)dictionaryFromDictionary:(NSDictionary *)dictionary 
                                    forKey:(NSString *)key 
                              defaultValue:(nullable NSDictionary *)defaultValue;

#pragma mark - 嵌套访问方法

/**
 * 通过路径获取嵌套字典中的值
 * @param dictionary 目标字典
 * @param keyPath 键路径，用"."分隔，如"user.profile.name"
 * @param defaultValue 默认值
 * @return 字符串值
 */
+ (NSString *)stringFromDictionary:(NSDictionary *)dictionary 
                           keyPath:(NSString *)keyPath 
                      defaultValue:(NSString *)defaultValue;

/**
 * 通过路径获取嵌套字典中的整数值
 * @param dictionary 目标字典
 * @param keyPath 键路径
 * @param defaultValue 默认值
 * @return 整数值
 */
+ (NSInteger)integerFromDictionary:(NSDictionary *)dictionary 
                           keyPath:(NSString *)keyPath 
                      defaultValue:(NSInteger)defaultValue;

#pragma mark - 数据验证方法

/**
 * 验证字典是否包含必需的键
 * @param dictionary 目标字典
 * @param requiredKeys 必需的键数组
 * @return 是否包含所有必需键
 */
+ (BOOL)dictionary:(NSDictionary *)dictionary containsRequiredKeys:(NSArray<NSString *> *)requiredKeys;

/**
 * 验证字典中指定键的值是否为指定类型
 * @param dictionary 目标字典
 * @param key 键名
 * @param expectedClass 期望的类型
 * @return 是否为期望类型
 */
+ (BOOL)dictionary:(NSDictionary *)dictionary key:(NSString *)key isKindOfClass:(Class)expectedClass;

#pragma mark - 便捷方法

/**
 * 安全获取用户ID（支持多种可能的键名）
 * @param dictionary 目标字典
 * @return 用户ID字符串
 */
+ (NSString *)userIDFromDictionary:(NSDictionary *)dictionary;

/**
 * 安全获取URL字符串并验证格式
 * @param dictionary 目标字典
 * @param key 键名
 * @return 有效的URL字符串，无效时返回nil
 */
+ (nullable NSString *)validURLStringFromDictionary:(NSDictionary *)dictionary forKey:(NSString *)key;

#pragma mark - 用户状态处理方法

/**
 * 将状态字符串转换为枚举值
 * @param statusString 状态字符串
 * @return SCAnchorStatus枚举值
 */
+ (SCAnchorStatus)anchorStatusFromString:(NSString *)statusString;

/**
 * 将枚举值转换为状态字符串
 * @param status SCAnchorStatus枚举值
 * @return 状态字符串
 */
+ (NSString *)stringFromAnchorStatus:(SCAnchorStatus)status;

/**
 * 根据状态获取点颜色
 * @param status SCAnchorStatus枚举值
 * @return 对应的颜色
 */
+ (UIColor *)dotColorForAnchorStatus:(SCAnchorStatus)status;

/**
 * 根据状态获取文字颜色
 * @param status SCAnchorStatus枚举值
 * @return 对应的颜色
 */
+ (UIColor *)textColorForAnchorStatus:(SCAnchorStatus)status;

#pragma mark - 用户媒体处理方法

/**
 * 从用户字典中获取照片媒体URL数组
 * @param userDict 用户信息字典
 * @return 照片URL数组
 */
+ (NSArray<NSString *> *)photoMediaUrlsFromUserDict:(NSDictionary *)userDict;

/**
 * 从用户字典中获取照片媒体URL数组（带默认头像）
 * @param userDict 用户信息字典
 * @return 照片URL数组，如果为空则返回头像URL
 */
+ (NSArray<NSString *> *)photoMediaUrlsDefaultAvatarFromUserDict:(NSDictionary *)userDict;

/**
 * 从用户字典中获取照片媒体对象数组
 * @param userDict 用户信息字典
 * @return 照片媒体字典数组
 */
+ (NSArray<NSDictionary *> *)photoMediasFromUserDict:(NSDictionary *)userDict;

/**
 * 从用户字典中获取照片媒体对象数组（带默认头像）
 * @param userDict 用户信息字典
 * @return 照片媒体字典数组，如果为空则包含头像信息
 */
+ (NSArray<NSDictionary *> *)photoMediasDefaultAvatarFromUserDict:(NSDictionary *)userDict;

/**
 * 从用户字典中获取视频媒体对象数组
 * @param userDict 用户信息字典
 * @return 视频媒体字典数组
 */
+ (NSArray<NSDictionary *> *)videoMediasFromUserDict:(NSDictionary *)userDict;

#pragma mark - 应用配置处理方法

/**
 * 从配置字典中获取指定名称的配置项数据
 * @param configDict 配置字典
 * @param itemName 配置项名称
 * @return 配置项数据，未找到时返回nil
 */
+ (nullable id)configItemDataFromDict:(NSDictionary *)configDict itemName:(NSString *)itemName;

/**
 * 从配置字典中获取融云应用键
 * @param configDict 配置字典
 * @return 融云应用键字符串
 */
+ (NSString *)rcAppKeyFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取融云区域代码
 * @param configDict 配置字典
 * @return 融云区域代码字符串
 */
+ (NSString *)rcAreaCodeFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取谷歌翻译键
 * @param configDict 配置字典
 * @return 谷歌翻译键字符串
 */
+ (NSString *)googleTranslationKeyFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取Facebook应用ID
 * @param configDict 配置字典
 * @return Facebook应用ID字符串
 */
+ (NSString *)appFBIdFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取Facebook客户端令牌
 * @param configDict 配置字典
 * @return Facebook客户端令牌字符串
 */
+ (NSString *)appFBClientTokenFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取融云区域代码枚举
 * @param configDict 配置字典
 * @return RCAreaCode枚举值
 */
+ (RCAreaCode)rcAreaCodeModelFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取应用扩展数据
 * @param configDict 配置字典
 * @return 扩展数据字典
 */
+ (nullable NSDictionary *)appExtDataFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取rck配置
 * @param configDict 配置字典
 * @return rck字符串
 */
+ (NSString *)rckFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取rtck配置
 * @param configDict 配置字典
 * @return rtck字符串
 */
+ (NSString *)rtckFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取第三方开放类型
 * @param configDict 配置字典
 * @return 第三方开放类型
 */
+ (NSInteger)tppOpenTypeFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取加密密钥
 * @param configDict 配置字典
 * @return 加密密钥字符串
 */
+ (NSString *)encryptKeyFromConfigDict:(NSDictionary *)configDict;

/**
 * 从配置字典中获取风险控制配置
 * @param configDict 配置字典
 * @return 风险控制配置字典
 */
+ (nullable NSDictionary *)riskControlInfoConfigFromConfigDict:(NSDictionary *)configDict;

/**
 * 从风险控制配置字典中获取k_factor
 * @param riskControlDict 风险控制配置字典
 * @return k_factor字符串
 */
+ (NSString *)kFactorFromRiskControlDict:(NSDictionary *)riskControlDict;

/**
 * 从风险控制配置字典中获取k_factor_num
 * @param riskControlDict 风险控制配置字典
 * @return k_factor_num字符串
 */
+ (NSString *)kFactorNumFromRiskControlDict:(NSDictionary *)riskControlDict;

/**
 * 从风险控制配置字典中获取k_interval
 * @param riskControlDict 风险控制配置字典
 * @return k_interval字符串
 */
+ (NSString *)kIntervalFromRiskControlDict:(NSDictionary *)riskControlDict;

/**
 * 从扩展数据字典中获取横幅启用状态
 * @param extDataDict 扩展数据字典
 * @return 横幅是否启用
 */
+ (BOOL)bannerEnabledFromExtDataDict:(NSDictionary *)extDataDict;

/**
 * 从扩展数据字典中获取横幅数组
 * @param extDataDict 扩展数据字典
 * @return 横幅字典数组
 */
+ (NSArray<NSDictionary *> *)bannersFromExtDataDict:(NSDictionary *)extDataDict;

/**
 * 从横幅字典中获取跳转URL
 * @param bannerDict 横幅字典
 * @return 跳转URL字符串
 */
+ (NSString *)jumpUrlFromBannerDict:(NSDictionary *)bannerDict;

/**
 * 从横幅字典中获取封面URL
 * @param bannerDict 横幅字典
 * @return 封面URL字符串
 */
+ (NSString *)coverUrlFromBannerDict:(NSDictionary *)bannerDict;

/**
 * 从横幅字典中获取类型
 * @param bannerDict 横幅字典
 * @return 横幅类型
 */
+ (NSInteger)typeFromBannerDict:(NSDictionary *)bannerDict;

/**
 * 从网速检测配置字典中获取大小限制
 * @param netspeedDict 网速检测配置字典
 * @return 大小限制
 */
+ (NSInteger)sizeLimitFromNetspeedDict:(NSDictionary *)netspeedDict;

/**
 * 从网速检测配置字典中获取间隔
 * @param netspeedDict 网速检测配置字典
 * @return 间隔
 */
+ (NSInteger)intervalFromNetspeedDict:(NSDictionary *)netspeedDict;

/**
 * 从网速检测配置字典中获取URL
 * @param netspeedDict 网速检测配置字典
 * @return URL字符串
 */
+ (NSString *)urlFromNetspeedDict:(NSDictionary *)netspeedDict;

#pragma mark - 用户等级处理方法

/**
 * 计算下一个等级
 * @param levelDict 等级字典，包含当前等级和等级列表
 * @return 下一个等级，如果已是最高等级返回-1
 */
+ (NSInteger)nextLevelFromLevelDict:(NSDictionary *)levelDict;

#pragma mark - 通话会话字典访问方法

/**
 * 从通话会话字典中获取状态
 * @param sessionDict 通话会话字典
 * @return 通话状态
 */
+ (NSInteger)callStatusFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取是否免费
 * @param sessionDict 通话会话字典
 * @return 是否免费
 */
+ (BOOL)isFreeFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取免费时长
 * @param sessionDict 通话会话字典
 * @return 免费时长（秒）
 */
+ (NSInteger)callFreeSecondsFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取发起用户ID
 * @param sessionDict 通话会话字典
 * @return 发起用户ID
 */
+ (NSString *)fromUserIdFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取接收用户ID
 * @param sessionDict 通话会话字典
 * @return 接收用户ID
 */
+ (NSString *)toUserIdFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取RTC Token
 * @param sessionDict 通话会话字典
 * @return RTC Token字符串
 */
+ (NSString *)rtcTokenFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取客户端会话ID
 * @param sessionDict 通话会话字典
 * @return 客户端会话ID
 */
+ (NSString *)clientSessionIdFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取视频SDK选择
 * @param sessionDict 通话会话字典
 * @return 视频SDK选择（1-声网，2-bigo）
 */
+ (NSInteger)chooseVideoSdkFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取是否绿色模式
 * @param sessionDict 通话会话字典
 * @return 是否绿色模式
 */
+ (BOOL)isGreenModeFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取频道名称
 * @param sessionDict 通话会话字典
 * @return 频道名称
 */
+ (NSString *)channelNameFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取单价
 * @param sessionDict 通话会话字典
 * @return 单价
 */
+ (NSInteger)unitPriceFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取主播单价
 * @param sessionDict 通话会话字典
 * @return 主播单价
 */
+ (NSInteger)broadcasterUnitPriceFromSessionDict:(NSDictionary *)sessionDict;

/**
 * 从通话会话字典中获取是否匹配
 * @param sessionDict 通话会话字典
 * @return 是否匹配
 */
+ (BOOL)isMatchFromSessionDict:(NSDictionary *)sessionDict;

#pragma mark - 排行榜字典访问方法

/**
 * 从排行榜字典中获取月份名称
 * @param rankDict 排行榜字典
 * @return 月份名称
 */
+ (NSString *)rankMonthNameFromDict:(NSDictionary *)rankDict;

/**
 * 从排行榜字典中获取排行数据
 * @param rankDict 排行榜字典
 * @return 排行数据数组
 */
+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict;

/**
 * 从排行榜字典中获取排序号
 * @param rankDict 排行榜字典
 * @return 排序号
 */
+ (NSString *)rankSortNoFromDict:(NSDictionary *)rankDict;

/**
 * 从排行榜项字典中获取头像
 * @param rankItemDict 排行榜项字典
 * @return 头像URL
 */
+ (NSString *)rankItemAvatarFromDict:(NSDictionary *)rankItemDict;

/**
 * 从排行榜项字典中获取昵称
 * @param rankItemDict 排行榜项字典
 * @return 昵称
 */
+ (NSString *)rankItemNicknameFromDict:(NSDictionary *)rankItemDict;

/**
 * 从排行榜项字典中获取排序
 * @param rankItemDict 排行榜项字典
 * @return 排序
 */
+ (NSInteger)rankItemSortFromDict:(NSDictionary *)rankItemDict;

/**
 * 从排行榜项字典中获取用户ID
 * @param rankItemDict 排行榜项字典
 * @return 用户ID
 */
+ (NSString *)rankItemUserIDFromDict:(NSDictionary *)rankItemDict;

/**
 * 从主播排行榜项字典中获取主播ID
 * @param anchorRankItemDict 主播排行榜项字典
 * @return 主播ID
 */
+ (NSInteger)anchorRankBroadcasterIdFromDict:(NSDictionary *)anchorRankItemDict;

/**
 * 从主播排行榜项字典中获取总收入金币
 * @param anchorRankItemDict 主播排行榜项字典
 * @return 总收入金币
 */
+ (NSInteger)anchorRankTotalIncomeFromDict:(NSDictionary *)anchorRankItemDict;

#pragma mark - 个人设置字典访问方法

/**
 * 从个人设置项字典中获取标题
 * @param personalItemDict 个人设置项字典
 * @return 标题
 */
+ (NSString *)personalItemTitleFromDict:(NSDictionary *)personalItemDict;

/**
 * 从个人设置项字典中获取副标题
 * @param personalItemDict 个人设置项字典
 * @return 副标题
 */
+ (NSString *)personalItemSubTitleFromDict:(NSDictionary *)personalItemDict;

/**
 * 从个人设置项字典中获取图标
 * @param personalItemDict 个人设置项字典
 * @return 图标名称
 */
+ (NSString *)personalItemImageFromDict:(NSDictionary *)personalItemDict;

/**
 * 从个人设置项字典中获取样式
 * @param personalItemDict 个人设置项字典
 * @return 样式值
 */
+ (NSInteger)personalItemStyleFromDict:(NSDictionary *)personalItemDict;

/**
 * 从个人设置项字典中获取开关状态
 * @param personalItemDict 个人设置项字典
 * @return 开关状态
 */
+ (BOOL)personalItemIsOnFromDict:(NSDictionary *)personalItemDict;

#pragma mark - 订单字典处理方法

/**
 * 创建订单字典
 * @param goodsCode 商品代码
 * @param goodsName 商品名称
 * @param orderNo 订单号
 * @param payAmount 支付金额
 * @param paidAmount 已支付金额
 * @param paidCurrency 支付货币
 * @param requestUrl 请求URL
 * @return 订单字典
 */
+ (NSDictionary *)createOrderDictWithGoodsCode:(NSString *)goodsCode
                                     goodsName:(NSString *)goodsName
                                       orderNo:(NSString *)orderNo
                                     payAmount:(double)payAmount
                                    paidAmount:(double)paidAmount
                                  paidCurrency:(NSString *)paidCurrency
                                    requestUrl:(NSString *)requestUrl;

/**
 * 从订单Model转换为订单字典（兼容性方法）
 * @param orderModel 订单Model实例（使用KVC访问属性）
 * @return 订单字典
 */
+ (NSDictionary *)orderDictFromModel:(id)orderModel;

@end

NS_ASSUME_NONNULL_END
