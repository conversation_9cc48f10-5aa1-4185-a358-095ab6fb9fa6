//
//  SCDictionaryHelper.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  字典工具类实现
//

#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"



@implementation SCDictionaryHelper

#pragma mark - 基础类型安全取值方法

+ (NSString *)stringFromDictionary:(NSDictionary *)dictionary 
                            forKey:(NSString *)key 
                      defaultValue:(NSString *)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        return value;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value stringValue];
    }
    
    return defaultValue;
}

+ (NSInteger)integerFromDictionary:(NSDictionary *)dictionary 
                            forKey:(NSString *)key 
                      defaultValue:(NSInteger)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value integerValue];
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        return [value integerValue];
    }
    
    return defaultValue;
}

+ (long long)longLongFromDictionary:(NSDictionary *)dictionary 
                             forKey:(NSString *)key 
                       defaultValue:(long long)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value longLongValue];
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        return [value longLongValue];
    }
    
    return defaultValue;
}

+ (CGFloat)floatFromDictionary:(NSDictionary *)dictionary 
                        forKey:(NSString *)key 
                  defaultValue:(CGFloat)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value floatValue];
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        return [value floatValue];
    }
    
    return defaultValue;
}

+ (double)doubleFromDictionary:(NSDictionary *)dictionary 
                        forKey:(NSString *)key 
                  defaultValue:(double)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value doubleValue];
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        return [value doubleValue];
    }
    
    return defaultValue;
}

+ (BOOL)boolFromDictionary:(NSDictionary *)dictionary 
                    forKey:(NSString *)key 
              defaultValue:(BOOL)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value boolValue];
    }
    
    if ([value isKindOfClass:[NSString class]]) {
        NSString *stringValue = [(NSString *)value lowercaseString];
        if ([stringValue isEqualToString:@"true"] || [stringValue isEqualToString:@"1"]) {
            return YES;
        }
        if ([stringValue isEqualToString:@"false"] || [stringValue isEqualToString:@"0"]) {
            return NO;
        }
    }
    
    return defaultValue;
}

#pragma mark - 复合类型取值方法

+ (NSArray *)arrayFromDictionary:(NSDictionary *)dictionary 
                          forKey:(NSString *)key 
                    defaultValue:(nullable NSArray *)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSArray class]]) {
        return value;
    }
    
    return defaultValue;
}

+ (NSDictionary *)dictionaryFromDictionary:(NSDictionary *)dictionary 
                                    forKey:(NSString *)key 
                              defaultValue:(nullable NSDictionary *)defaultValue {
    if (!dictionary || !key) {
        return defaultValue;
    }
    
    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return defaultValue;
    }
    
    if ([value isKindOfClass:[NSDictionary class]]) {
        return value;
    }
    
    return defaultValue;
}

#pragma mark - 嵌套访问方法

+ (NSString *)stringFromDictionary:(NSDictionary *)dictionary 
                           keyPath:(NSString *)keyPath 
                      defaultValue:(NSString *)defaultValue {
    if (!dictionary || !keyPath) {
        return defaultValue;
    }
    
    NSArray *keys = [keyPath componentsSeparatedByString:@"."];
    id currentValue = dictionary;
    
    for (NSString *key in keys) {
        if (![currentValue isKindOfClass:[NSDictionary class]]) {
            return defaultValue;
        }
        currentValue = currentValue[key];
        if (!currentValue || [currentValue isKindOfClass:[NSNull class]]) {
            return defaultValue;
        }
    }
    
    if ([currentValue isKindOfClass:[NSString class]]) {
        return currentValue;
    }
    
    if ([currentValue isKindOfClass:[NSNumber class]]) {
        return [currentValue stringValue];
    }
    
    return defaultValue;
}

+ (NSInteger)integerFromDictionary:(NSDictionary *)dictionary 
                           keyPath:(NSString *)keyPath 
                      defaultValue:(NSInteger)defaultValue {
    if (!dictionary || !keyPath) {
        return defaultValue;
    }
    
    NSArray *keys = [keyPath componentsSeparatedByString:@"."];
    id currentValue = dictionary;
    
    for (NSString *key in keys) {
        if (![currentValue isKindOfClass:[NSDictionary class]]) {
            return defaultValue;
        }
        currentValue = currentValue[key];
        if (!currentValue || [currentValue isKindOfClass:[NSNull class]]) {
            return defaultValue;
        }
    }
    
    if ([currentValue isKindOfClass:[NSNumber class]]) {
        return [currentValue integerValue];
    }
    
    if ([currentValue isKindOfClass:[NSString class]]) {
        return [currentValue integerValue];
    }

    return defaultValue;
}

#pragma mark - 数据验证方法

+ (BOOL)dictionary:(NSDictionary *)dictionary containsRequiredKeys:(NSArray<NSString *> *)requiredKeys {
    if (!dictionary || !requiredKeys) {
        return NO;
    }

    for (NSString *key in requiredKeys) {
        if (!dictionary[key] || [dictionary[key] isKindOfClass:[NSNull class]]) {
            return NO;
        }
    }

    return YES;
}

+ (BOOL)dictionary:(NSDictionary *)dictionary key:(NSString *)key isKindOfClass:(Class)expectedClass {
    if (!dictionary || !key || !expectedClass) {
        return NO;
    }

    id value = dictionary[key];
    if (!value || [value isKindOfClass:[NSNull class]]) {
        return NO;
    }

    return [value isKindOfClass:expectedClass];
}

#pragma mark - 便捷方法

+ (NSString *)userIDFromDictionary:(NSDictionary *)dictionary {
    // 尝试多种可能的用户ID键名
    NSArray *possibleKeys = @[@"userID", @"userId", @"user_id", @"id"];

    for (NSString *key in possibleKeys) {
        NSString *userID = [self stringFromDictionary:dictionary forKey:key defaultValue:nil];
        if (userID && userID.length > 0) {
            return userID;
        }
    }

    return @"";
}

+ (nullable NSString *)validURLStringFromDictionary:(NSDictionary *)dictionary forKey:(NSString *)key {
    NSString *urlString = [self stringFromDictionary:dictionary forKey:key defaultValue:nil];

    if (!urlString || urlString.length == 0) {
        return nil;
    }

    // 基本URL格式验证
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        return nil;
    }

    return urlString;
}

#pragma mark - 用户状态处理方法

+ (SCAnchorStatus)anchorStatusFromString:(NSString *)statusString {
    NSString *lowercaseString = [statusString lowercaseString];
    if ([lowercaseString isEqualToString:@"online"]) {
        return AnchorStatusOnline;
    } else if ([lowercaseString isEqualToString:@"busy"]) {
        return AnchorStatusBusy;
    } else if ([lowercaseString isEqualToString:@"offline"]) {
        return AnchorStatusOffline;
    } else if ([lowercaseString isEqualToString:@"incall"]) {
        return AnchorStatusIncall;
    } else {
        return AnchorStatusUnknown; // 未知
    }
}

+ (NSString *)stringFromAnchorStatus:(SCAnchorStatus)status {
    switch (status) {
        case AnchorStatusOnline:
            return @"Online";
        case AnchorStatusBusy:
            return @"Busy";
        case AnchorStatusUnknown:
        case AnchorStatusOffline:
            return @"Offline";
        case AnchorStatusIncall:
            return @"InCall";
    }
}

+ (UIColor *)dotColorForAnchorStatus:(SCAnchorStatus)status {
    switch (status) {
        case AnchorStatusOnline:
            return [UIColor colorWithHexString:@"FF80FF57"];
        case AnchorStatusBusy:
            return [UIColor colorWithHexString:@"FFFFB057"];
        case AnchorStatusUnknown:
        case AnchorStatusOffline:
            return [UIColor colorWithHexString:@"FFD1D1D1"];
        case AnchorStatusIncall:
            return [UIColor colorWithHexString:@"FFFF5757"];
    }
}

+ (UIColor *)textColorForAnchorStatus:(SCAnchorStatus)status {
    switch (status) {
        case AnchorStatusOnline:
            return [UIColor scWhite];
        case AnchorStatusBusy:
            return [UIColor scWhite];
        case AnchorStatusUnknown:
        case AnchorStatusOffline:
            return [UIColor scWhite];
        case AnchorStatusIncall:
            return [UIColor scWhite];
    }
}

#pragma mark - 用户媒体处理方法

+ (NSArray<NSString *> *)photoMediaUrlsFromUserDict:(NSDictionary *)userDict {
    return [self mediaUrlsFromUserDict:userDict mediaType:@"photo"];
}

+ (NSArray<NSString *> *)photoMediaUrlsDefaultAvatarFromUserDict:(NSDictionary *)userDict {
    NSArray<NSString *> *list = [self photoMediaUrlsFromUserDict:userDict];
    if (list == nil || [list count] == 0) {
        NSString *avatarUrl = [self stringFromDictionary:userDict forKey:@"avatarUrl" defaultValue:@""];
        return @[avatarUrl];
    } else {
        return list;
    }
}

+ (NSArray<NSDictionary *> *)photoMediasFromUserDict:(NSDictionary *)userDict {
    return [self mediasFromUserDict:userDict mediaType:@"photo"];
}

+ (NSArray<NSDictionary *> *)photoMediasDefaultAvatarFromUserDict:(NSDictionary *)userDict {
    NSArray<NSDictionary *> *list = [self photoMediasFromUserDict:userDict];
    if (list == nil || [list count] == 0) {
        NSString *avatarUrl = [self stringFromDictionary:userDict forKey:@"avatarUrl" defaultValue:@""];
        if (avatarUrl.length > 0) {
            NSDictionary *avatarMedia = @{
                @"mediaType": @"photo",
                @"mediaUrl": avatarUrl
            };
            return @[avatarMedia];
        }
        return @[];
    } else {
        return list;
    }
}

+ (NSArray<NSDictionary *> *)videoMediasFromUserDict:(NSDictionary *)userDict {
    return [self mediasFromUserDict:userDict mediaType:@"video"];
}

#pragma mark - 私有辅助方法

+ (NSArray<NSString *> *)mediaUrlsFromUserDict:(NSDictionary *)userDict mediaType:(NSString *)type {
    NSMutableArray<NSString *> *mediaUrls = [NSMutableArray new];
    NSArray *mediaList = [self arrayFromDictionary:userDict forKey:@"mediaList" defaultValue:@[]];

    for (id mediaObj in mediaList) {
        if ([mediaObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *mediaDict = (NSDictionary *)mediaObj;
            NSString *mediaType = [self stringFromDictionary:mediaDict forKey:@"mediaType" defaultValue:@""];
            if ([mediaType.lowercaseString isEqualToString:type]) {
                NSString *mediaUrl = [self stringFromDictionary:mediaDict forKey:@"mediaUrl" defaultValue:@""];
                if (mediaUrl.length > 0) {
                    [mediaUrls addObject:mediaUrl];
                }
            }
        }
    }

    return mediaUrls;
}

+ (NSArray<NSDictionary *> *)mediasFromUserDict:(NSDictionary *)userDict mediaType:(NSString *)type {
    NSMutableArray<NSDictionary *> *medias = [NSMutableArray new];
    NSArray *mediaList = [self arrayFromDictionary:userDict forKey:@"mediaList" defaultValue:@[]];

    for (id mediaObj in mediaList) {
        if ([mediaObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *mediaDict = (NSDictionary *)mediaObj;
            NSString *mediaType = [self stringFromDictionary:mediaDict forKey:@"mediaType" defaultValue:@""];
            if ([mediaType.lowercaseString isEqualToString:type]) {
                [medias addObject:mediaDict];
            }
        }
    }

    return medias;
}

#pragma mark - 应用配置处理方法

+ (nullable id)configItemDataFromDict:(NSDictionary *)configDict itemName:(NSString *)itemName {
    NSArray *items = [self arrayFromDictionary:configDict forKey:@"items" defaultValue:@[]];

    for (id itemObj in items) {
        if ([itemObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *itemDict = (NSDictionary *)itemObj;
            NSString *name = [self stringFromDictionary:itemDict forKey:@"name" defaultValue:@""];
            if ([name isEqualToString:itemName]) {
                return itemDict[@"data"];
            }
        }
    }

    return nil;
}

+ (NSString *)rcAppKeyFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"rc_app_key"];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSString *)rcAreaCodeFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"rc_area_code"];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSString *)googleTranslationKeyFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"google_translation_key"];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSString *)appFBIdFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"app_fb_id"];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSString *)appFBClientTokenFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"app_fb_client_token"];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (RCAreaCode)rcAreaCodeModelFromConfigDict:(NSDictionary *)configDict {
    NSString *rcCode = [self rcAreaCodeFromConfigDict:configDict];
    if (rcCode.length == 0) {
        return RCAreaCodeInvalid;
    }
    if ([rcCode isEqualToString:@"NA"]) {
        return RCAreaCodeNA;
    }
    if ([rcCode isEqualToString:@"SA"]) {
        return RCAreaCodeSA;
    }
    if ([rcCode isEqualToString:@"SG"]) {
        return RCAreaCodeSG;
    }
    if ([rcCode isEqualToString:@"SG_B"]) {
        return RCAreaCodeSG_B;
    }

    return RCAreaCodeBJ;
}

+ (NSString *)rckFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:SCDictionaryKeys.shared.kSCAppConfigRckKey];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSString *)rtckFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:SCDictionaryKeys.shared.kSCAppConfigRtckKey];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (NSInteger)tppOpenTypeFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:SCDictionaryKeys.shared.kSCAppConfigTppOpenTypeKey];
    if ([data isKindOfClass:[NSString class]]) {
        return [data integerValue];
    } else if ([data isKindOfClass:[NSNumber class]]) {
        return [data integerValue];
    }
    return 0;
}

+ (NSString *)encryptKeyFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:SCDictionaryKeys.shared.kSCAppConfigEncryptKeyKey];
    if ([data isKindOfClass:[NSString class]]) {
        return data;
    }
    return @"";
}

+ (nullable NSDictionary *)riskControlInfoConfigFromConfigDict:(NSDictionary *)configDict {
    id data = [self dictionaryFromDictionary:configDict forKey:SCDictionaryKeys.shared.kSCAppConfigRiskControlInfoConfigKey defaultValue:nil];
    if ([data isKindOfClass:[NSDictionary class]]) {
        return data;
    }
    return nil;
}

+ (NSString *)kFactorFromRiskControlDict:(NSDictionary *)riskControlDict {
    return [self stringFromDictionary:riskControlDict forKey:SCDictionaryKeys.shared.kSCRiskControlKFactorKey defaultValue:@""];
}

+ (NSString *)kFactorNumFromRiskControlDict:(NSDictionary *)riskControlDict {
    return [self stringFromDictionary:riskControlDict forKey:SCDictionaryKeys.shared.kSCRiskControlKFactorNumKey defaultValue:@""];
}

+ (NSString *)kIntervalFromRiskControlDict:(NSDictionary *)riskControlDict {
    return [self stringFromDictionary:riskControlDict forKey:SCDictionaryKeys.shared.kSCRiskControlKIntervalKey defaultValue:@""];
}

+ (nullable NSDictionary *)appExtDataFromConfigDict:(NSDictionary *)configDict {
    id data = [self configItemDataFromDict:configDict itemName:@"app_ext_data"];
    if ([data isKindOfClass:[NSDictionary class]]) {
        return data;
    }
    return nil;
}

+ (BOOL)bannerEnabledFromExtDataDict:(NSDictionary *)extDataDict {
    return [self boolFromDictionary:extDataDict forKey:SCDictionaryKeys.shared.kSCAppExtDataBannerEnabledKey defaultValue:NO];
}

+ (NSArray<NSDictionary *> *)bannersFromExtDataDict:(NSDictionary *)extDataDict {
    NSArray *banners = [self arrayFromDictionary:extDataDict forKey:SCDictionaryKeys.shared.kSCAppExtDataBannersKey defaultValue:@[]];
    NSMutableArray<NSDictionary *> *result = [NSMutableArray array];
    for (id item in banners) {
        if ([item isKindOfClass:[NSDictionary class]]) {
            [result addObject:item];
        }
    }
    return [result copy];
}

+ (NSString *)jumpUrlFromBannerDict:(NSDictionary *)bannerDict {
    return [self stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCDiscoverBannerJumpUrlKey defaultValue:@""];
}

+ (NSString *)coverUrlFromBannerDict:(NSDictionary *)bannerDict {
    return [self stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCDiscoverBannerCoverUrlKey defaultValue:@""];
}

+ (NSInteger)typeFromBannerDict:(NSDictionary *)bannerDict {
    return [self integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCDiscoverBannerTypeKey defaultValue:0];
}

+ (NSInteger)sizeLimitFromNetspeedDict:(NSDictionary *)netspeedDict {
    return [self integerFromDictionary:netspeedDict forKey:SCDictionaryKeys.shared.kSCAnchorNetspeedSizeLimitKey defaultValue:0];
}

+ (NSInteger)intervalFromNetspeedDict:(NSDictionary *)netspeedDict {
    return [self integerFromDictionary:netspeedDict forKey:SCDictionaryKeys.shared.kSCAnchorNetspeedIntervalKey defaultValue:0];
}

+ (NSString *)urlFromNetspeedDict:(NSDictionary *)netspeedDict {
    return [self stringFromDictionary:netspeedDict forKey:SCDictionaryKeys.shared.kSCAnchorNetspeedUrlKey defaultValue:@""];
}

#pragma mark - 用户等级处理方法

+ (NSInteger)nextLevelFromLevelDict:(NSDictionary *)levelDict {
    NSInteger currentLevel = [self integerFromDictionary:levelDict forKey:@"level" defaultValue:0];
    NSArray *userLevelList = [self arrayFromDictionary:levelDict forKey:@"userLevelList" defaultValue:@[]];

    NSInteger maxLevel = 0;
    for (id levelObj in userLevelList) {
        if ([levelObj isKindOfClass:[NSDictionary class]]) {
            NSDictionary *levelItemDict = (NSDictionary *)levelObj;
            NSInteger level = [self integerFromDictionary:levelItemDict forKey:@"level" defaultValue:0];
            if (level > maxLevel) {
                maxLevel = level;
            }
        }
    }

    if (maxLevel <= currentLevel) {
        return -1; // 已是最高等级
    }

    return currentLevel + 1;
}

#pragma mark - 通话会话字典访问方法

+ (NSInteger)callStatusFromSessionDict:(NSDictionary *)sessionDict {
    return [self integerFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallStatusKey defaultValue:0];
}

+ (BOOL)isFreeFromSessionDict:(NSDictionary *)sessionDict {
    return [self boolFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallIsFreeKey defaultValue:NO];
}

+ (NSInteger)callFreeSecondsFromSessionDict:(NSDictionary *)sessionDict {
    return [self integerFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0];
}

+ (NSString *)fromUserIdFromSessionDict:(NSDictionary *)sessionDict {
    return [self stringFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:@""];
}

+ (NSString *)toUserIdFromSessionDict:(NSDictionary *)sessionDict {
    return [self stringFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:@""];
}

+ (NSString *)rtcTokenFromSessionDict:(NSDictionary *)sessionDict {
    return [self stringFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""];
}

+ (NSString *)clientSessionIdFromSessionDict:(NSDictionary *)sessionDict {
    return [self stringFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallClientSessionIdKey defaultValue:@""];
}

+ (NSInteger)chooseVideoSdkFromSessionDict:(NSDictionary *)sessionDict {
    return [self integerFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:1];
}

+ (BOOL)isGreenModeFromSessionDict:(NSDictionary *)sessionDict {
    return [self boolFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO];
}

+ (NSString *)channelNameFromSessionDict:(NSDictionary *)sessionDict {
    return [self stringFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:@""];
}

+ (NSInteger)unitPriceFromSessionDict:(NSDictionary *)sessionDict {
    return [self integerFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallUnitPriceKey defaultValue:0];
}

+ (NSInteger)broadcasterUnitPriceFromSessionDict:(NSDictionary *)sessionDict {
    return [self integerFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallBroadcasterUnitPriceKey defaultValue:0];
}

+ (BOOL)isMatchFromSessionDict:(NSDictionary *)sessionDict {
    return [self boolFromDictionary:sessionDict forKey:SCDictionaryKeys.shared.kSCCallIsMatchKey defaultValue:NO];
}

#pragma mark - 排行榜字典访问方法

+ (NSString *)rankMonthNameFromDict:(NSDictionary *)rankDict {
    return [self stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankMonthNameKey defaultValue:@""];
}

+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict {
    return [self arrayFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankDataKey defaultValue:@[]];
}

+ (NSString *)rankSortNoFromDict:(NSDictionary *)rankDict {
    return [self stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankSortNoKey defaultValue:@""];
}

+ (NSString *)rankItemAvatarFromDict:(NSDictionary *)rankItemDict {
    return [self stringFromDictionary:rankItemDict forKey:SCDictionaryKeys.shared.kSCRankItemAvatarKey defaultValue:@""];
}

+ (NSString *)rankItemNicknameFromDict:(NSDictionary *)rankItemDict {
    return [self stringFromDictionary:rankItemDict forKey:SCDictionaryKeys.shared.kSCRankItemNicknameKey defaultValue:@""];
}

+ (NSInteger)rankItemSortFromDict:(NSDictionary *)rankItemDict {
    return [self integerFromDictionary:rankItemDict forKey:SCDictionaryKeys.shared.kSCRankItemSortKey defaultValue:0];
}

+ (NSString *)rankItemUserIDFromDict:(NSDictionary *)rankItemDict {
    return [self stringFromDictionary:rankItemDict forKey:SCDictionaryKeys.shared.kSCRankItemUserIDKey defaultValue:@""];
}

+ (NSInteger)anchorRankBroadcasterIdFromDict:(NSDictionary *)anchorRankItemDict {
    return [self integerFromDictionary:anchorRankItemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankBroadcasterIdKey defaultValue:0];
}

+ (NSInteger)anchorRankTotalIncomeFromDict:(NSDictionary *)anchorRankItemDict {
    return [self integerFromDictionary:anchorRankItemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankTotalIncomeKey defaultValue:0];
}

#pragma mark - 个人设置字典访问方法

+ (NSString *)personalItemTitleFromDict:(NSDictionary *)personalItemDict {
    return [self stringFromDictionary:personalItemDict forKey:SCDictionaryKeys.shared.kSCPersonalItemTitleKey defaultValue:@""];
}

+ (NSString *)personalItemSubTitleFromDict:(NSDictionary *)personalItemDict {
    return [self stringFromDictionary:personalItemDict forKey:SCDictionaryKeys.shared.kSCPersonalItemSubTitleKey defaultValue:@""];
}

+ (NSString *)personalItemImageFromDict:(NSDictionary *)personalItemDict {
    return [self stringFromDictionary:personalItemDict forKey:SCDictionaryKeys.shared.kSCPersonalItemImageKey defaultValue:@""];
}

+ (NSInteger)personalItemStyleFromDict:(NSDictionary *)personalItemDict {
    return [self integerFromDictionary:personalItemDict forKey:SCDictionaryKeys.shared.kSCPersonalItemStyleKey defaultValue:0];
}

+ (BOOL)personalItemIsOnFromDict:(NSDictionary *)personalItemDict {
    return [self boolFromDictionary:personalItemDict forKey:SCDictionaryKeys.shared.kSCPersonalItemIsOnKey defaultValue:NO];
}

#pragma mark - 订单字典处理方法

+ (NSDictionary *)createOrderDictWithGoodsCode:(NSString *)goodsCode
                                     goodsName:(NSString *)goodsName
                                       orderNo:(NSString *)orderNo
                                     payAmount:(double)payAmount
                                    paidAmount:(double)paidAmount
                                  paidCurrency:(NSString *)paidCurrency
                                    requestUrl:(NSString *)requestUrl {
    NSMutableDictionary *orderDict = [NSMutableDictionary dictionary];

    if (goodsCode) orderDict[SCDictionaryKeys.shared.kSCOrderGoodsCodeKey] = goodsCode;
    if (goodsName) orderDict[SCDictionaryKeys.shared.kSCOrderGoodsNameKey] = goodsName;
    if (orderNo) orderDict[SCDictionaryKeys.shared.kSCOrderNoKey] = orderNo;
    orderDict[SCDictionaryKeys.shared.kSCPayAmountKey] = @(payAmount);
    orderDict[SCDictionaryKeys.shared.kSCOrderPaidAmountKey] = @(paidAmount);
    if (paidCurrency) orderDict[SCDictionaryKeys.shared.kSCOrderPaidCurrencyKey] = paidCurrency;
    if (requestUrl) orderDict[SCDictionaryKeys.shared.kSCOrderRequestUrlKey] = requestUrl;

    return [orderDict copy];
}

+ (NSDictionary *)orderDictFromModel:(id)orderModel {
    if (!orderModel) return @{};

    // 使用KVC安全获取属性值
    NSString *goodsCode = [orderModel valueForKey:@"goodsCode"] ?: @"";
    NSString *goodsName = [orderModel valueForKey:@"goodsName"] ?: @"";
    NSString *orderNo = [orderModel valueForKey:@"orderNo"] ?: @"";
    NSNumber *payAmount = [orderModel valueForKey:@"payAmount"] ?: @0;
    NSNumber *paidAmount = [orderModel valueForKey:@"paidAmount"] ?: @0;
    NSString *paidCurrency = [orderModel valueForKey:@"paidCurrency"] ?: @"";
    NSString *requestUrl = [orderModel valueForKey:@"requestUrl"] ?: @"";

    return [self createOrderDictWithGoodsCode:goodsCode
                                    goodsName:goodsName
                                      orderNo:orderNo
                                    payAmount:[payAmount doubleValue]
                                   paidAmount:[paidAmount doubleValue]
                                 paidCurrency:paidCurrency
                                   requestUrl:requestUrl];
}

@end
