//
//  SCCryptoUtils.h
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/10/21.
//


#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCCryptoUtils : NSObject

+ (nullable NSString *)encryptECB:(NSString *)plainText key:(NSString *)key;
+ (NSString *)convertToRFC4648URLSafe:(NSString *)base64String;
+ (nullable NSString *)decryptECB:(NSString *)plainText key:(NSString *)key;
+ (nullable NSData *)aesEncrypt:(NSData *)data key:(NSData *)key iv:(NSData *)iv;
+ (nullable NSData *)decryptAES:(NSData *)data key:(NSData *)key iv:(NSData *)iv;
+ (NSData *)dataFromHexString:(NSString *)hexStr;
+ (NSArray<NSNumber *> *)bytesFromHexString:(NSString *)hexStr;
+ (nullable NSString *)fileMD5:(NSURL *)fileURL;
+ (nullable NSString *)strMD5:(NSString *)str;

@end

NS_ASSUME_NONNULL_END
