//
//  SCDataConverter.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  数据转换工具类实现
//

#import "SCDataConverter.h"

static NSError *_lastConversionError = nil;

@implementation SCDataConverter

#pragma mark - JSON转换方法

+ (nullable NSDictionary *)dictionaryFromJSONString:(NSString *)jsonString {
    if (!jsonString || jsonString.length == 0) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    return [self dictionaryFromJSONData:jsonData];
}

+ (nullable NSDictionary *)dictionaryFromJSONData:(NSData *)jsonData {
    if (!jsonData || jsonData.length == 0) {
        return nil;
    }
    
    NSError *error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData 
                                                    options:NSJSONReadingMutableContainers 
                                                      error:&error];
    
    _lastConversionError = error;
    
    if (error || ![jsonObject isKindOfClass:[NSDictionary class]]) {
        return nil;
    }
    
    return jsonObject;
}

+ (nullable NSString *)JSONStringFromDictionary:(NSDictionary *)dictionary {
    NSData *jsonData = [self JSONDataFromDictionary:dictionary];
    if (!jsonData) {
        return nil;
    }
    
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

+ (nullable NSData *)JSONDataFromDictionary:(NSDictionary *)dictionary {
    if (!dictionary) {
        return nil;
    }
    
    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictionary 
                                                       options:NSJSONWritingPrettyPrinted 
                                                         error:&error];
    
    _lastConversionError = error;
    
    if (error) {
        return nil;
    }
    
    return jsonData;
}

+ (nullable NSString *)jsonStringFromDictionary:(NSDictionary *)dictionary {
    return [self JSONStringFromDictionary:dictionary];
}

+ (nullable NSDictionary *)safeDictionaryFromJSONString:(NSString *)jsonString {
    return [self dictionaryFromJSONString:jsonString];
}

#pragma mark - 数组转换方法

+ (nullable NSArray *)arrayFromJSONString:(NSString *)jsonString {
    if (!jsonString || jsonString.length == 0) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    return [self arrayFromJSONData:jsonData];
}

+ (nullable NSArray *)arrayFromJSONData:(NSData *)jsonData {
    if (!jsonData || jsonData.length == 0) {
        return nil;
    }
    
    NSError *error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData 
                                                    options:NSJSONReadingMutableContainers 
                                                      error:&error];
    
    _lastConversionError = error;
    
    if (error || ![jsonObject isKindOfClass:[NSArray class]]) {
        return nil;
    }
    
    return jsonObject;
}

+ (nullable NSString *)JSONStringFromArray:(NSArray *)array {
    NSData *jsonData = [self JSONDataFromArray:array];
    if (!jsonData) {
        return nil;
    }
    
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

+ (nullable NSData *)JSONDataFromArray:(NSArray *)array {
    if (!array) {
        return nil;
    }
    
    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:array 
                                                       options:NSJSONWritingPrettyPrinted 
                                                         error:&error];
    
    _lastConversionError = error;
    
    if (error) {
        return nil;
    }
    
    return jsonData;
}

#pragma mark - 通用转换方法

+ (nullable NSString *)JSONStringFromObject:(id)object {
    NSData *jsonData = [self JSONDataFromObject:object];
    if (!jsonData) {
        return nil;
    }
    
    return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
}

+ (nullable NSData *)JSONDataFromObject:(id)object {
    if (!object) {
        return nil;
    }
    
    // 检查对象是否可以序列化为JSON
    if (![NSJSONSerialization isValidJSONObject:object]) {
        return nil;
    }
    
    NSError *error = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:object 
                                                       options:NSJSONWritingPrettyPrinted 
                                                         error:&error];
    
    _lastConversionError = error;
    
    if (error) {
        return nil;
    }
    
    return jsonData;
}

+ (nullable id)objectFromJSON:(id)json {
    if (!json) {
        return nil;
    }
    
    NSData *jsonData = nil;
    
    if ([json isKindOfClass:[NSString class]]) {
        jsonData = [json dataUsingEncoding:NSUTF8StringEncoding];
    } else if ([json isKindOfClass:[NSData class]]) {
        jsonData = json;
    } else {
        return nil;
    }
    
    if (!jsonData || jsonData.length == 0) {
        return nil;
    }
    
    NSError *error = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData 
                                                    options:NSJSONReadingMutableContainers 
                                                      error:&error];
    
    _lastConversionError = error;
    
    if (error) {
        return nil;
    }
    
    return jsonObject;
}

#pragma mark - 数据验证方法

+ (BOOL)isValidJSONString:(NSString *)jsonString {
    if (!jsonString || jsonString.length == 0) {
        return NO;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    return [self isValidJSONData:jsonData];
}

+ (BOOL)isValidJSONData:(NSData *)jsonData {
    if (!jsonData || jsonData.length == 0) {
        return NO;
    }
    
    NSError *error = nil;
    [NSJSONSerialization JSONObjectWithData:jsonData 
                                     options:0 
                                       error:&error];
    
    return error == nil;
}

#pragma mark - 错误处理

+ (nullable NSError *)lastConversionError {
    return _lastConversionError;
}

#pragma mark - 便捷方法

+ (nullable NSDictionary *)safeDictionaryFromResponseObject:(id)responseObject {
    if (!responseObject) {
        return nil;
    }
    
    // 如果已经是字典，直接返回
    if ([responseObject isKindOfClass:[NSDictionary class]]) {
        return responseObject;
    }
    
    // 尝试从JSON字符串或数据转换
    if ([responseObject isKindOfClass:[NSString class]] || 
        [responseObject isKindOfClass:[NSData class]]) {
        id jsonObject = [self objectFromJSON:responseObject];
        if ([jsonObject isKindOfClass:[NSDictionary class]]) {
            return jsonObject;
        }
    }
    
    return nil;
}

+ (nullable NSArray *)safeArrayFromResponseObject:(id)responseObject {
    if (!responseObject) {
        return nil;
    }
    
    // 如果已经是数组，直接返回
    if ([responseObject isKindOfClass:[NSArray class]]) {
        return responseObject;
    }
    
    // 尝试从JSON字符串或数据转换
    if ([responseObject isKindOfClass:[NSString class]] || 
        [responseObject isKindOfClass:[NSData class]]) {
        id jsonObject = [self objectFromJSON:responseObject];
        if ([jsonObject isKindOfClass:[NSArray class]]) {
            return jsonObject;
        }
    }
    
    return nil;
}

@end
