//
//  SCKeychainUtils.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCKeychainUtils.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <Security/Security.h>

@implementation SCKeychainUtils

+ (NSMutableDictionary *)getKeychainQueryForKey:(NSString *)key {
    NSString *serviceName = [[NSBundle mainBundle] bundleIdentifier];
    
    NSMutableDictionary *query = [@{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: serviceName,
        (__bridge id)kSecAttrAccount: key,
        (__bridge id)kSecReturnData: @(YES)
    } mutableCopy];
    
    return query;
}

+ (NSString *)readValueForKey:(NSString *)key {
    NSMutableDictionary *query = [self getKeychainQueryForKey:key];
    
    CFTypeRef result = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, &result);
    
    if (status == errSecSuccess && result != NULL) {
        NSData *data = (__bridge_transfer NSData *)result;
        NSString *value = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        return value;
    } else {
        return nil;
    }
}

+ (BOOL)writeValue:(NSString *)value forKey:(NSString *)key {
    NSMutableDictionary *query = [self getKeychainQueryForKey:key];
    
    NSData *data = [value dataUsingEncoding:NSUTF8StringEncoding];
    query[(__bridge id)kSecValueData] = data;
    
    OSStatus status = SecItemAdd((__bridge CFDictionaryRef)query, NULL);
    
    if (status == errSecSuccess) {
        return YES;
    } else if (status == errSecDuplicateItem) {
        return [self updateValue:value forKey:key];
    } else {
        return NO;
    }
}

+ (BOOL)updateValue:(NSString *)value forKey:(NSString *)key {
    NSMutableDictionary *query = [self getKeychainQueryForKey:key];
    
    NSDictionary *update = @{
        (__bridge id)kSecValueData: [value dataUsingEncoding:NSUTF8StringEncoding]
    };
    
    OSStatus status = SecItemUpdate((__bridge CFDictionaryRef)query, (__bridge CFDictionaryRef)update);
    
    if (status == errSecSuccess) {
        return YES;
    } else {
        return NO;
    }
}

+ (BOOL)deleteValueForKey:(NSString *)key {
    NSMutableDictionary *query = [self getKeychainQueryForKey:key];
    
    OSStatus status = SecItemDelete((__bridge CFDictionaryRef)query);
    
    if (status == errSecSuccess) {
        return YES;
    } else {
        return NO;
    }
}

@end
