//
//  SCTrackingUtils.h
//  Supercall
//
//  Created by sumengliu on 2024/3/21.
//

#import <Foundation/Foundation.h>
#import <Adjust/Adjust.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCTrackingUtils : NSObject <AdjustDelegate>

+ (instancetype)shared;

// Initialize AppsFlyer
- (void)initAppsFlyerisDebug:(BOOL)isDebug;

// FacebookSDK
- (void)setupFacebookSDKWithCompletion:(void(^)(BOOL success))completion;

// Tracking methods
+ (void)requestATTPermission;
+ (void)activateFacebookApp;
//+ (void)startAppsFlyer;

// Event tracking
+ (void)trackProductClickWithProductId:(NSString *)productId
                             price:(double)price
                      originPrice:(double)originPrice;
+ (void)trackPurchaseSuccessWithOrderNo:(NSString *)orderNo
                              price:(double)price;
+ (void)trackStartPurchaseWithPrice:(double)price;

+ (void)trackAjEvent:(NSString *)eventname;

+ (void)trackEventWithName:(NSString *)name
                    params:(NSDictionary *)params;
- (void)reportAscribeRecordWithToken:(NSString *)token userId:(NSString *)userId;
// 获取 AppsFlyer 头部信息
@property (nonatomic, strong, readonly) NSDictionary *appsFlyerHeaders;

@end

NS_ASSUME_NONNULL_END 
