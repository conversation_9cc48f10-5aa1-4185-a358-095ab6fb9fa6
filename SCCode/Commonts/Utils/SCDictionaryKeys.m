//
//  SCDictionaryKeys.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/21.
//  字典键常量定义实现
//

#import "SCDictionaryKeys.h"

#pragma mark - 网络响应通用键
@implementation SCDictionaryKeys

static SCDictionaryKeys *_instance = nil;
+ (instancetype)shared {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
}


- (instancetype)init
{
    self = [super init];
    if (self) {
        [self setKeys];
    }
    return self;
}

- (void)setKeys{
    
    self.kSCResponseDataKey = @"data";
    self.kSCResponseCodeKey = @"code";
    self.kSCResponseMessageKey = @"msg";
    self.kSCResponseSuccessKey = @"success";
    self.kSCResponseFailKey = @"fail";
    
#pragma mark - 用户信息相关键
    
    self.kSCUserIDKey = @"userId";
    self.kSCUserIdKey = @"userId";
    self.kSCUser_IdKey = @"user_id";
    self.kSCUserNicknameKey = @"nickname";
    self.kSCUserAvatarUrlKey = @"avatarUrl";
    self.kSCUserAvatarThumbUrlKey = @"avatarThumbUrl";
    self.kSCUserGenderKey = @"gender";
    self.kSCUserAgeKey = @"age";
    self.kSCUserCoinsKey = @"coins";
    self.kSCUserCountryKey = @"country";
    self.kSCUserRegisterCountryKey = @"registerCountry";
    self.kSCUserStatusKey = @"status";
    self.kSCUserDisplayStatusKey = @"displayStatus";
    self.kSCUserAvailableCoinsKey = @"availableCoins";
    self.kSCUserIsRechargeKey = @"isRecharge";
    self.kSCUserCallCoinsKey = @"callCoins";
    self.kSCUserUnitPriceKey = @"unitPrice";
    self.kSCUserMediaListKey = @"mediaList";
    self.kSCUserAvatarRespListKey = @"avatarRespList";
    self.kSCUserTagDetailsKey = @"tagDetails";
    self.kSCUserExtraLabelsListKey = @"labelsList";
    /// 用户融云Token键
    self.kSCUserRongcloudTokenKey = @"rongcloudToken";
    /// 置顶官方用户ID列表键
    self.kSCTopOfficialUserIdsKey = @"topOfficialUserIds";
    /// 主播关注官方用户ID列表键
    self.kSCBroadcasterFollowOfficialUserIdsKey = @"broadcasterFollowOfficialUserIds";
    /// 用户服务账号ID键
    self.kSCUserServiceAccountIdKey = @"userServiceAccountId";
    /// 用户媒体类型键
    self.kSCUserMediaTypeKey = @"mediaType";
    /// 用户媒体URL键
    self.kSCUserMediaUrlKey = @"mediaUrl";
    /// 用户生日键
    self.kSCUserBirthdayKey = @"birthday";
    /// 用户语言键
    self.kSCUserLanguageKey = @"language";
    /// 用户等级键
    self.kSCUserLevelKey = @"level";
    /// 用户是否VIP键
    self.kSCUserIsVipKey = @"isVip";
    /// 用户关于信息键
    self.kSCUserAboutKey = @"about";
    /// 用户头像映射路径键
    self.kSCUserAvatarMapPathKey = @"avatarMapPath";
    self.kSCUserAvatarKey = @"avatarUrl";
    self.kSCGiftPriceKey = @"coinPrice";
    self.kSCGiftIconKey = @"iconPath";
    self.kSCMediaURLKey = @"mediaUrl";
    self.kSCDataKey = @"data";
    self.kSCCodeKey = @"code";
    self.kSCMessageKey = @"msg";
    self.kSCSuccessKey = @"success";
#pragma mark - 认证相关键
    
    self.kSCTokenKey = @"token";
    self.kSCUserInfoKey = @"userInfo";
    self.kSCIsFirstRegisterKey = @"isFirstRegister";
    
#pragma mark - 礼物相关键
    
    self.kSCGiftCodeKey = @"code";
    self.kSCGiftNameKey = @"name";
    self.kSCGiftCoinPriceKey = @"coinPrice";
    self.kSCGiftSortNoKey = @"sortNo";
    self.kSCGiftDescKey = @"giftDesc";
    
    // 消息相关键实现
    self.kSCMessageIdKey = @"messageId";
    self.kSCMessageContentKey = @"content";
    self.kSCMessageTypeKey = @"type";
    self.kSCMessageFromUserIdKey = @"fromUserId";
    self.kSCMessageToUserIdKey = @"toUserId";
    self.kSCMessageTimestampKey = @"timestamp";
    
    self.kSCGiftIconPathKey = @"iconPath";
    self.kSCGiftIconThumbPathKey = @"iconThumbPath";
    self.kSCGiftCountKey = @"count";
    self.kSCGiftNumKey = @"giftNum";
    
    // 金币商品相关键实现
    self.kSCCoinsGoodsIdKey = @"goodsId";
    self.kSCCoinsCodeKey = @"code";
    self.kSCCoinsIconKey = @"icon";
    self.kSCCoinsTypeKey = @"type";
    self.kSCCoinsTagsKey = @"tags";
    self.kSCCoinsDiscountKey = @"discount";
    self.kSCCoinsOriginalPriceKey = @"originalPrice";
    self.kSCCoinsPriceKey = @"price";
    self.kSCCoinsExchangeCoinKey = @"exchangeCoin";
    self.kSCCoinsOriginalPriceRupeeKey = @"originalPriceRupee";
    self.kSCCoinsPriceRupeeKey = @"priceRupee";
    self.kSCCoinsLocalPaymentPriceRupeeKey = @"localPaymentPriceRupee";
    self.kSCCoinsIsPromotionKey = @"isPromotion";
    self.kSCCoinsLocalPayOriginalPriceKey = @"localPayOriginalPrice";
    self.kSCCoinsLocalPayPriceKey = @"localPayPrice";
    self.kSCCoinsExtraCoinPercentKey = @"extraCoinPercent";
    self.kSCCoinsInvitationIdKey = @"invitationId";
    self.kSCCoinsSubTypeKey = @"subType";
    
#pragma mark - 媒体相关键
    
    self.kSCMediaPathKey = @"mediaPath";
    self.kSCMediaUrlKey = @"mediaUrl";
    self.kSCMediaTypeKey = @"mediaType";
    self.kSCMediaSortKey = @"sort";
    
#pragma mark - 应用配置相关键
    
    self.kSCAppConfigVerKey = @"ver";
    self.kSCAppConfigItemsKey = @"items";
    self.kSCAppConfigItemNameKey = @"name";
    self.kSCAppConfigItemDataKey = @"data";
    self.kSCAppConfigRcAppKeyName = @"rc_app_key";
    self.kSCAppConfigRcAreaCodeName = @"rc_area_code";
    self.kSCAppConfigGoogleTranslationKeyName = @"google_translation_key";
    self.kSCAppConfigFBIdName = @"app_fb_id";
    self.kSCAppConfigFBClientTokenName = @"app_fb_client_token";
    self.kSCAppConfigExtDataName = @"app_ext_data";
    
#pragma mark - 用户等级相关键
    
    self.kSCUserLevelCurrentKey = @"level";
    self.kSCUserLevelRechargedCoinsKey = @"rechargedCoins";
    self.kSCUserLevelNeedRechargeCoinsKey = @"needRechargeCoins";
    self.kSCUserLevelListKey = @"userLevelList";
    //self.kSCLevelKey = @"level";
    self.kSCLevelRechargedCoinsKey = @"rechargedCoins";
    
#pragma mark - 横幅相关键
    
    self.kSCBannerIdKey = @"id";
    self.kSCBannerTitleKey = @"title";
    self.kSCBannerImageUrlKey = @"imageUrl";
    self.kSCBannerJumpUrlKey = @"jumpUrl";
    self.kSCBannerTypeKey = @"type";
    
#pragma mark - 通话相关键
    
    self.kSCCallChannelNameKey = @"channelName";
    self.kSCCallTokenKey = @"token";
    self.kSCCallUserIdKey = @"userId";
    self.kSCCallTypeKey = @"callType";
    self.kSCCallSourceKey = @"callSource";
    self.kSCCallSessionIdKey = @"sessionId";
    
    self.kSCDictKeyChannelName = @"channelName";
    self.kSCDictKeyOrderNo = @"orderNo";
    self.kSCDictKeyPayAmount = @"payAmount";
    
#pragma mark - 应用配置相关键实现
    
    self.kSCDictKeyRck = @"rck";
    self.kSCDictKeyRcAreaCode = @"rcAreaCodeModel";
    self.kSCDictKeyRcAppKey = @"rcAppKey";
    self.kSCDictKeyGoogleTranslationKey = @"googleTranslationKey";
    self.kSCDictKeyVer = @"ver";
    
#pragma mark - 礼物相关键实现
    
    //self.kSCGiftCodeKey = @"code";
    //self.kSCGiftCoinPriceKey = @"coinPrice";
    //self.kSCGiftSortNoKey = @"sortNo";
    //self.kSCGiftDescKey = @"giftDesc";
    //self.kSCGiftIconPathKey = @"iconPath";
    //self.kSCGiftIconThumbPathKey = @"iconThumbPath";
    //self.kSCGiftCountKey = @"count";
    //self.kSCGiftNameKey = @"name";
    
#pragma mark - 通话相关键实现（扩展）
    
    self.kSCCallFreeSecondsKey = @"callFreeSeconds";
    self.kSCCallFromUserIdKey = @"fromUserId";
    self.kSCCallToUserIdKey = @"toUserId";
    self.kSCCallRtcTokenKey = @"rtcToken";
    self.kSCCallChooseVideoSdkKey = @"chooseVideoSdk";
    self.kSCCallIsGreenModeKey = @"isGreenMode";
    self.kSCCallUnitPriceKey = @"unitPrice";
    self.kSCCallDurationKey = @"duration";
    self.kSCCallStatusKey = @"status";
    self.kSCCallIsFreeKey = @"isFree";
    self.kSCCallHangUpReasonKey = @"hangUpReason";
    self.kSCCallEndTypeKey = @"endType";
    self.kSCCallCreatAtKey = @"creatAt";
    self.kSCCallClientSessionIdKey = @"clientSessionId";
    self.kSCCallBroadcasterUnitPriceKey = @"broadcasterUnitPrice";
    self.kSCCallIsMatchKey = @"isMatch";
    
#pragma mark - Socket事件相关键
    
    self.kSCSocketCommandKey = @"command";
    self.kSCSocketCommandIdKey = @"commandId";
    self.kSCSocketDataKey = @"data";
    self.kSCSocketTimestampKey = @"timestamp";
    
#pragma mark - 排行榜相关键定义
    self.kSCRankMonthNameKey = @"monthName";
    self.kSCRankDataKey = @"rankData";
    self.kSCRankSortNoKey = @"sortNo";
    self.kSCRankItemAvatarKey = @"avatar";
    self.kSCRankItemAvatarMapPathKey = @"avatarMapPath";
    self.kSCRankItemNicknameKey = @"nickname";
    self.kSCRankItemSortKey = @"sort";
    self.kSCRankItemUserIDKey = @"userId";
    
    self.kSCAnchorRankBroadcasterIdKey = @"broadcasterId";
    self.kSCAnchorRankBroadcasterNameKey = @"broadcasterName";
    self.kSCAnchorRankOnlineTimeKey = @"broadcasterOnlineTime";
    self.kSCAnchorRankGuildIdKey = @"guildId";
    self.kSCAnchorRankGuildNameKey = @"guildName";
    self.kSCAnchorRankTotalIncomeKey = @"totalIncomeCoins";
    
    self.kSCRankingTypeKey = @"rankType";
    self.kSCRankingTitleKey = @"title";
    
#pragma mark - 个人设置相关键定义
    self.kSCPersonalItemTitleKey = @"title";
    self.kSCPersonalItemSubTitleKey = @"subTitle";
    self.kSCPersonalItemImageKey = @"image";
    self.kSCPersonalItemStyleKey = @"style";
    self.kSCPersonalItemIsOnKey = @"isON";
    
#pragma mark - 订单相关键
    
    self.kSCOrderNoKey = @"orderNo";
    self.kSCOrderAmountKey = @"amount";
    self.kSCPayAmountKey = @"payAmount";
    self.kSCOrderStatusKey = @"status";
    self.kSCOrderGoodsCodeKey = @"goodsCode";
    self.kSCOrderGoodsNameKey = @"goodsName";
    self.kSCOrderPaidAmountKey = @"paidAmount";
    self.kSCOrderPaidCurrencyKey = @"paidCurrency";
    self.kSCOrderRequestUrlKey = @"requestUrl";
    
#pragma mark - 等级相关键
    
    self.kSCLevelKey = @"level";
    self.kSCLevelNameKey = @"levelName";
    self.kSCLevelIconKey = @"levelIcon";
    
#pragma mark - 标签相关键
    
    self.kSCTagIdKey = @"tagId";
    self.kSCTagNameKey = @"tagName";
    self.kSCTagColorKey = @"tagColor";
    self.kSCSubTagListKey = @"subTagList";
    
#pragma mark - OSS策略相关键
    
    self.kSCOSSPolicyAccessKeyIdKey = @"accessKeyId";
    self.kSCOSSPolicyPolicyKey = @"policy";
    self.kSCOSSPolicySignatureKey = @"signature";
    self.kSCOSSPolicyDirKey = @"dir";
    self.kSCOSSPolicyCallbackKey = @"callback";
    self.kSCOSSPolicyHostKey = @"host";
    
#pragma mark - Banner相关键
    
    self.kSCBannerPicKey = @"pic";
    self.kSCBannerCoverUrlKey = @"coverUrl";
    //self.kSCBannerTypeKey = @"type";
    //self.kSCBannerJumpUrlKey = @"jumpUrl";
    self.kSCBannerBizTypeKey = @"bizType";
    
    
    // 强引导相关Key常量定义
    self.kSCStrongGuideContentKey = @"content";
    self.kSCStrongGuideInPkgNameKey = @"inPkgName";
    self.kSCStrongGuideInviteCodeKey = @"inviteCode";
    self.kSCStrongGuideNativeRechargeRedirectKey = @"nativeRechargeRedirect";

    // 促销相关Key常量定义
    self.kSCPromotionCodeKey = @"code";
    self.kSCPromotionSurplusMillisecondKey = @"surplusMillisecond";
    self.kSCPromotionRemainMillisecondsKey = @"remainMilliseconds";
    self.kSCPromotionThirdpartyCoinPercentKey = @"thirdpartyCoinPercent";
    self.kSCPromotionExchangeCoinKey = @"exchangeCoin";
    self.kSCPromotionExtraCoinKey = @"extraCoin";
    self.kSCPromotionCapableRechargeNumKey = @"capableRechargeNum";
    self.kSCPromotionRechargeNumKey = @"rechargeNum";

    // 通话结果相关Key常量定义
    self.kSCVideoCallResultChannelNameKey = @"channelName";
    self.kSCVideoCallResultBroadcasterIdKey = @"broadcasterId";
    self.kSCVideoCallResultDurationKey = @"duration";
    self.kSCVideoCallResultTagListKey = @"tagList";
    self.kSCVideoCallResultBadTagListKey = @"badTagList";
    self.kSCVideoCallResultRecommendListKey = @"recommendList";
    self.kSCVideoCallResultIsClubServiceKey = @"isClubService";

    // 应用配置相关Key常量定义
    self.kSCAppConfigRckKey = @"rck";
    self.kSCAppConfigRtckKey = @"rtck";
    self.kSCAppConfigTppOpenTypeKey = @"tpp_open_type";
    self.kSCAppConfigEncryptKeyKey = @"encrypt_key";
    self.kSCAppConfigRiskControlInfoConfigKey = @"riskControlInfoConfig";

    // 风险控制配置Key常量定义
    self.kSCRiskControlKFactorKey = @"k_factor";
    self.kSCRiskControlKFactorNumKey = @"k_factor_num";
    self.kSCRiskControlKIntervalKey = @"k_interval";

    // 扩展数据Key常量定义
    self.kSCAppExtDataBannerEnabledKey = @"banner_enabled";
    self.kSCAppExtDataBannersKey = @"banners";

    // 发现页横幅Key常量定义
    self.kSCDiscoverBannerJumpUrlKey = @"jump_url";
    self.kSCDiscoverBannerCoverUrlKey = @"cover_url";
    self.kSCDiscoverBannerTypeKey = @"type";

    // 主播网速检测Key常量定义
    self.kSCAnchorNetspeedSizeLimitKey = @"sizeLimit";
    self.kSCAnchorNetspeedIntervalKey = @"interval";
    self.kSCAnchorNetspeedUrlKey = @"url";
    
    // SCRobotCustomerQuestionSetModel 相关键名
    self.kSCRobotQuestionSetQuestionIdKey = @"questionId";
    self.kSCRobotQuestionSetContentKey = @"content";
    self.kSCRobotQuestionSetFaqInfoListKey = @"faqInfoList";

    // SCRobotCustomerFAQInfoModel 相关键名
    self.kSCRobotFAQInfoFaqIdKey = @"faqId";
    self.kSCRobotFAQInfoCodeKey = @"code";
    self.kSCRobotFAQInfoQuestionKey = @"question";
    self.kSCRobotFAQInfoTypeKey = @"type";
    self.kSCRobotFAQInfoImageUrlKey = @"imageUrl";
    self.kSCRobotFAQInfoMessageAnswerKey = @"messageAnswer";
    self.kSCRobotFAQInfoIsViewExampleKey = @"isViewExample";
    self.kSCRobotFAQInfoHandleTypeKey = @"handleType";
    self.kSCRobotFAQInfoToUrlKey = @"toUrl";
    self.kSCRobotFAQInfoIsLikeKey = @"isLike";

    // SCRobotCustomerMessageAnswerModel 相关键名
    self.kSCRobotMessageAnswerContentKey = @"content";
    self.kSCRobotMessageAnswerEventHandleListKey = @"answerEventHandleList";

    // SCRobotCustomerAnswerEventHandleModel 相关键名
    self.kSCRobotAnswerEventHandleTypeKey = @"handleType";
    self.kSCRobotAnswerEventMatchStrKey = @"matchStr";
    self.kSCRobotAnswerEventToUrlKey = @"toUrl";

    // SCMyQuestionModel 相关键名
    self.kSCMyQuestionCodeKey = @"code";
    self.kSCMyQuestionQuestionKey = @"question";
}
@end
