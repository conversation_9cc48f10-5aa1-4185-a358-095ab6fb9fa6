//
//  SCSocketService.m
//  Supercall
//
//  Created by 关伟洪 on 2024/1/21.
//

#import "SCSocketService.h"
#import "SCCategorySocket.h"


#import "SCCoinsService.h"

#import "SCXErrorModel.h"
#import "SCDictionaryHelper.h"
#import "SCDataConverter.h"
#import "SCDictionaryKeys.h"
#import "SCCallService.h"
#import "SCStoreScoreAlertViewController.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCThrottle.h"

@import SocketIO;
@interface SCSocketService()

@property (nonatomic,strong) SocketIOClient *socket;
@property (nonatomic,strong) SocketManager *socketManager;
//当前是前台还是后台
@property (nonatomic, assign) Boolean isForeground;
//记录离开时间
@property (nonatomic, assign) NSTimeInterval leaveTime;

@end

@implementation SCSocketService{
    
    NSTimer *_heartBeatTimer;
}

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        [self _init];
    }
    return self;
}

-(void) _init{
    _orderEventObs = [[SCObservable<NSDictionary *> alloc] initWithValue:nil];
    _isForeground = YES;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(willEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
}


- (void)willEnterForeground {
    _isForeground = YES;
    [self foregroundChangeHandel];
}

- (void)didEnterBackground {
    _isForeground = NO;
    _leaveTime = [[NSDate date] timeIntervalSince1970];
    [self foregroundChangeHandel];
}

- (void)foregroundChangeHandel {
    kWeakSelf(self)
    [SCThrottle throttleWithTag:kSCThrottleSocketForegroundChangeKey onAfter:^{
        kStrongSelf
        
        //判断是否登录
        if (!kScAuthMar.isLogin) {
            return;
        }
        //判断是否连接
        if (![strongSelf isConnected]) {
            //判断离开时间是否超过2分钟
            if ([[NSDate date] timeIntervalSince1970] - strongSelf.leaveTime > 120){
                //重新链接
                [strongSelf connect];
            }
        }else{
            [strongSelf connect];
        }
        // 前后台切换上报 mode (integer, optional): 模式（0-前台 1-后台）
        [weakself requestSwitchWithMode:strongSelf.isForeground ? 0:1];
        //更新促销数据
        [kSCAuthCoinsService remotePromotionWithSuccess:nil failure:nil];
        [kSCAuthCoinsService remoteActivityPromotionWithSuccess:nil failure:nil];
        
    }];
    
}

- (void)requestSwitchWithMode:(NSInteger)mode {
    [SCAPIServiceManager requestSwitchWithMode:mode success:nil failure:nil];
}

- (void)destroyService{
    [super destroyService];
    [self destoryHeartBeat];
    [self disconnect];
}

- (BOOL)isConnected {
    return [_socket status] == SocketIOStatusConnected;
}
- (void)disconnect {
    [self.socketManager disconnect];
    _socket = nil;
}
- (void)connect{
    if (self.socketManager != nil) {
        [self disconnect];
    }
    NSString *token = [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];

    NSDictionary *config = @{
        @"log": @(YES),
        @"compress": @(YES),
        @"reconnects": @(YES),
        @"reconnectWait": @(2),
        @"reconnectAttempts": @(-1),
        @"forceNew": @(YES),
        @"forceWebsockets": @(YES),
        //           @"version": @(SocketIOClientVersion2),
        @"connectParams": @{@"token": token}
    };
       
       _socketManager = [[SocketManager alloc] initWithSocketURL:[NSURL URLWithString:kSCCodeMar.imHost] config:config];
       _socket = _socketManager.defaultSocket;
       
       [self listeningStatus];
       [self listeningMsgEvent];
       
       [_socket connect];
    ///开始心跳
    [self startHeartBeat];
}
#pragma mark - 心跳
//开始发送心跳
- (void)startHeartBeat {
    [self destoryHeartBeat];
    kWeakSelf(self)
    _heartBeatTimer = [NSTimer scheduledTimerWithTimeInterval:60 repeats:YES block:^(NSTimer * _Nonnull timer) {
        [weakself sendHeartBeat];
    }];
    [[NSRunLoop currentRunLoop] addTimer:_heartBeatTimer forMode:NSRunLoopCommonModes];
}

//取消心跳
- (void)destoryHeartBeat {
    if (_heartBeatTimer) {
        [_heartBeatTimer invalidate];
        _heartBeatTimer = nil;
    }
}
//发送心跳
- (void)sendHeartBeat {
    if (_socket.status != SocketIOStatusConnected) {
        [self connect];
    }
    [SCAPIServiceManager requestHeartbeat:0 success:nil failure:nil];
}

#pragma mark - 监听状态
- (void)sc_blank_empty{}
//监听socket状态
- (void)listeningStatus{
    kWeakSelf(self)
    /// Emitted when the client connects. This is also called on a successful reconnection. A connect event gets one
    /// data item: the namespace that was connected to.
    [self.socket on:@"connect" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
        //链接成功
        ///获取注册奖励
        [kSCAuthCoinsService checkRegisterReward];
    }];
    
    /// Emitted when the socket has disconnected and will not attempt to try to reconnect.
    [self.socket on:@"disconnect" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    
    //Emitted when an error occurs.
    [self.socket on:@"error" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    
    ///Emitted whenever the engine sends a ping.
    [self.socket on:@"ping" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    
    ///Emitted whenever the engine gets a pong.
    [self.socket on:@"pong" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    
    //Emitted when the client begins the reconnection process.
    [self.socket on:@"reconnect" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    //Emitted each time the client tries to reconnect to the server.
    [self.socket on:@"reconnectAttempt" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    
    [self.socket on:@"statusChange" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
    [self.socket on:@"websocketUpgrade" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        [weakself sc_blank_empty];
        
    }];
}

//监听时间
-(void) listeningMsgEvent{
    kWeakSelf(self);
    
    
    
    // "responseEvent"事件处理
    [self.socket on:@"responseEvent" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull socketAckEmitter) {
        
        
        NSDictionary *result = data.firstObject;
        [weakself handleEvent:result];
        
    }];
    
    // "messageEvent"事件处理
    [self.socket on:@"messageEvent" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull socketAckEmitter) {
        
        
        NSDictionary *result = data.firstObject;
        [weakself handleEvent:result];
    }];
    
    //索要礼物
    [self.socket on:@"onGiftAsk" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull socketAckEmitter) {
        
        NSDictionary *result = data.firstObject;
        [weakself handleGiftAskEvent:result];
    }];
    
}

///统一处理消息
-(void) handleEvent:(NSDictionary *) eventDic{
    NSString *eventJsonString = [SCDataConverter jsonStringFromDictionary:eventDic];
    
    

    void (^handleBlock)(void) = ^{
        // 直接使用字典处理事件，不再创建Model对象
        NSString *command = [SCDictionaryHelper stringFromDictionary:eventDic forKey:@"command" defaultValue:@""];
        NSString *commandId = [SCDictionaryHelper stringFromDictionary:eventDic forKey:@"commandId" defaultValue:@""];
        NSString *code = [SCDictionaryHelper stringFromDictionary:eventDic forKey:@"code" defaultValue:@""];
        NSInteger timestamp = [SCDictionaryHelper integerFromDictionary:eventDic forKey:@"timestamp" defaultValue:0];
        id data = [eventDic objectForKey:@"data"];

        // 创建字典格式的事件对象传递给CallService
        NSDictionary *eventDict = @{
            @"commandId": commandId,
            @"command": command,
            @"code": code,
            @"timestamp": @(timestamp),
            @"data": data ?: [NSNull null]
        };

        if([kSCAuthCallService handleEventDict:eventDict])
            return;

        if([command isEqualToString:@"availableCoins"]){
            //金币刷新
            [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
        }else if ([command isEqualToString:@"flashChatFreeTimes"]){
            ///刷新免费次数
            if(data != nil  && [data isKindOfClass:[NSDictionary class]]){
                NSDictionary * tempData = (NSDictionary *)data;
                NSInteger times = [tempData[@"freeTimes"] integerValue];

                // 从策略字典中获取闪聊配置，更新免费次数
                NSMutableDictionary *mutableStrategyDict = [kScAuthMar.strategyObs.value mutableCopy];
                NSMutableDictionary *flashChatConfig = [[SCDictionaryHelper dictionaryFromDictionary:mutableStrategyDict forKey:@"flashChatConfig" defaultValue:@{}] mutableCopy];

                flashChatConfig[@"residueFreeCallTimes"] = @(times);
                if(times > 0){
                    flashChatConfig[@"isFreeCall"] = @(YES);
                }else{
                    flashChatConfig[@"isFreeCall"] = @(NO);
                }

                mutableStrategyDict[@"flashChatConfig"] = flashChatConfig;
                //通知刷新
                kScAuthMar.strategyObs.value = [mutableStrategyDict copy];
            }
            
        }else if([command isEqualToString:@"rechargeOrderStatus"]){
            //订单状态 - 直接使用字典数据
            NSDictionary *orderData = nil;
            if ([data isKindOfClass:[NSDictionary class]]) {
                orderData = (NSDictionary *)data;
            }
            self.orderEventObs.value = orderData;
            
            //检测是否弹出对话框
            NSInteger status = [SCDictionaryHelper integerFromDictionary:orderData forKey:@"status" defaultValue:0];
            if(status == 2){ // SCSocketOrderStautsSuccess = 2
                //充值成功刷新金币
                [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
                
                [SCStoreScoreAlertViewController checkIsCanOpenStoreScoreAlertWithCompletion:nil];
            }
        }
    };
    
    if ([NSThread isMainThread]) {
        handleBlock();
    } else {
        dispatch_async(dispatch_get_main_queue(), handleBlock);
    }
}

-(void) handleGiftAskEvent:(NSDictionary *) eventDic{
    [self handleEvent:eventDic];
}

#pragma mark - 发送消息

///发送消息
- (void)sendMessageWithCommand:(NSString *) command data:(NSDictionary *)data success:(void(^)(void))success failure:(void(^)(SCXErrorModel *error))failure{
    
    if (![self isConnected]) {
        kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Not Connected Socket".translateString]);
        return;
    }
    [self.socket emit:command with:@[data] completion:^{
        kSCBlockExeNotNil(success);
    }];
}



@end
