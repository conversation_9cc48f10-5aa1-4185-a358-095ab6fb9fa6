//
//  SCSocketCommandModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/22.
//

#import <Foundation/Foundation.h>




NS_ASSUME_NONNULL_BEGIN
@interface SCSocketEventModel : NSObject
// 长连消息id， 用于判断消息的唯一性，相同id不做二次处理
@property (nonatomic, copy)           NSString *commandId;
// 长连消息名称
@property (nonatomic, copy)           NSString *command;
// 长连消息状态
@property (nonatomic, copy)           NSString *code;
/// 长连消息时间戳
@property (nonatomic, assign)         NSInteger timestamp;
// 长连消息数据
@property (nonatomic, strong) id data;
@end

NS_ASSUME_NONNULL_END
