//
//  SCCategorySocket.m
//  Supercall
//
//  Created by 关伟洪 on 2024/1/21.
//

#import "SCCategorySocket.h"


@implementation SCAPIServiceManager (SCCategorySocket)

+(void) requestSwitchWithMode:(NSInteger)mode success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIModeSwitch method:SCNetMethodPOST parameters:@{@"mode":@(mode)} headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [((NSNumber *)responseObject) boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"switch fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requestHeartbeat:(NSInteger)mode success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIHeartbeat method:SCNetMethodPOST parameters:nil headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [((NSNumber *)responseObject) boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Heart Beat fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
@end
