//
//  SCCategorySocket.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/21.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategorySocket)

+(void) requestSwitchWithMode:(NSInteger)mode success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
+(void) requestHeartbeat:(NSInteger)mode success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
