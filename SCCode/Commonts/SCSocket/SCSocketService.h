//
//  SCSocketService.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/21.
//

#import "SCBaseAppService.h"
#import "SCSocketEventModel.h"


// 移除Model类前向声明，改为使用字典
NS_ASSUME_NONNULL_BEGIN



@interface SCSocketService : SCBaseAppService

///链接socket
- (void)connect;
///当前是否已经链接
- (BOOL)isConnected;

#pragma mark - 心跳

//开始发送心跳
- (void)startHeartBeat;
//取消心跳
- (void)destoryHeartBeat;

#pragma mark - 发送消息

///发送消息
- (void)sendMessageWithCommand:(NSString *) command data:(NSDictionary *)data success:(void(^)(void))success failure:(void(^)(SCXErrorModel *error))failure;

#pragma mark - 分发事件

@property(nonatomic,strong) SCObservable<NSDictionary *> *orderEventObs;

///统一处理消息
-(void) handleEvent:(NSDictionary *) eventDic;
///处理礼物索要礼物逻辑
-(void) handleGiftAskEvent:(NSDictionary *) eventDic;
@end

NS_ASSUME_NONNULL_END
