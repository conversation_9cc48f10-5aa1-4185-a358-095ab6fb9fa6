# 语音播放器架构文档

## 概述

本文档描述了语音播放器的新架构设计，主要解决了Cell复用导致的messageId不一致问题。

## 问题背景

### 原有问题

在原有的架构中，`SCVoicePlayerManager` 使用单一代理模式，存在以下问题：

1. **Cell复用冲突**：UICollectionView的Cell会被复用，当Cell被复用时，新的Cell会覆盖之前的代理设置
2. **messageId不一致**：语音下载和播放是异步操作，当回调触发时，Cell可能已经显示了不同的消息
3. **状态混乱**：导致语音播放状态显示错误，用户体验不佳

### 具体场景

```
用户点击消息A的语音 -> Cell1开始播放
Cell1被复用显示消息B -> Cell1.model.messageId = B的ID  
消息A的播放回调到达 -> if (self.model.messageId != messageId) return; // 被忽略
```

## 新架构设计

### 核心思路

采用**通知机制**替代单一代理模式，所有需要监听语音播放状态的组件都可以注册通知监听器。

### 架构组件

#### 1. SCVoicePlayerManager（语音播放管理器）

**职责**：
- 管理语音消息的下载、缓存和播放
- 发送播放状态变化通知
- 保持向后兼容的代理接口

**关键方法**：
```objc
// 发送状态变化通知
- (void)postStateChangeNotificationWithState:(SCVoicePlayState)state 
                                    messageId:(long)messageId 
                                        error:(NSError *)error;

// 发送进度更新通知  
- (void)postProgressUpdateNotificationWithProgress:(float)progress 
                                          messageId:(long)messageId;
```

#### 2. SCVoiceMessageCell（语音消息Cell）

**职责**：
- 监听语音播放状态通知
- 根据messageId过滤相关消息
- 更新UI显示

**生命周期管理**：
```objc
// 初始化时注册通知监听
- (instancetype)initWithFrame:(CGRect)frame {
    // ...
    [self setupNotificationObservers];
}

// 销毁时自动注销通知监听
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
```

### 通知机制

#### 通知类型

1. **SCVoicePlayerStateDidChangeNotification**
   - 播放状态变化通知
   - UserInfo包含：messageId, state, error(可选)

2. **SCVoicePlayerDownloadProgressDidUpdateNotification**
   - 下载进度更新通知
   - UserInfo包含：messageId, progress

#### 通知处理流程

```objc
- (void)handleVoicePlayerStateChange:(NSNotification *)notification {
    long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];
    
    // 关键：根据messageId过滤
    if (self.model.messageId != messageId) {
        return;
    }
    
    // 处理状态变化
    SCVoicePlayState state = [notification.userInfo[SCVoicePlayerNotificationStateKey] integerValue];
    // 更新UI...
}
```

## 优势分析

### 1. 解决Cell复用问题
- 所有Cell都能接收到通知
- 每个Cell根据messageId自行判断是否响应
- 避免了代理被覆盖的问题

### 2. 提高系统稳定性
- 通知机制是iOS标准模式，稳定可靠
- 自动内存管理，避免内存泄漏
- 解耦组件，提高可维护性

### 3. 保持向后兼容
- 保留原有代理接口
- 现有代码无需大幅修改
- 渐进式迁移

### 4. 性能优化
- 通知机制开销极小
- 支持多个监听器
- 异步处理，不阻塞主线程

## 使用指南

### 监听语音播放状态

```objc
// 1. 注册通知监听
[[NSNotificationCenter defaultCenter] addObserver:self
                                         selector:@selector(handleStateChange:)
                                             name:SCVoicePlayerStateDidChangeNotification
                                           object:nil];

// 2. 处理通知
- (void)handleStateChange:(NSNotification *)notification {
    long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];
    if (self.model.messageId != messageId) return;
    
    SCVoicePlayState state = [notification.userInfo[SCVoicePlayerNotificationStateKey] integerValue];
    // 处理状态变化...
}

// 3. 注销监听
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
```

### 监听下载进度

```objc
[[NSNotificationCenter defaultCenter] addObserver:self
                                         selector:@selector(handleProgress:)
                                             name:SCVoicePlayerDownloadProgressDidUpdateNotification
                                           object:nil];

- (void)handleProgress:(NSNotification *)notification {
    long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];
    if (self.model.messageId != messageId) return;
    
    float progress = [notification.userInfo[SCVoicePlayerNotificationProgressKey] floatValue];
    // 更新进度UI...
}
```

## 测试策略

### 1. 单元测试
- 通知发送和接收功能测试
- messageId过滤机制测试
- 多状态变化测试

### 2. 性能测试
- 大量通知发送性能测试
- 多监听器性能影响测试
- Cell复用场景性能测试

### 3. 内存泄漏检测
- 通知监听器内存泄漏测试
- Cell内存泄漏测试
- 大量创建销毁测试

## 迁移指南

### 现有代码迁移

1. **Cell类修改**：
   - 移除代理设置代码
   - 添加通知监听注册
   - 添加通知处理方法
   - 添加dealloc方法

2. **Manager类修改**：
   - 添加通知发送代码
   - 保留原有代理调用

3. **测试验证**：
   - 运行单元测试
   - 进行集成测试
   - 性能和内存测试

### 注意事项

1. **内存管理**：确保在dealloc中注销通知监听
2. **线程安全**：通知处理在主线程进行
3. **错误处理**：妥善处理通知中的错误信息
4. **向后兼容**：保留原有接口，确保平滑迁移

## 总结

新的通知机制架构彻底解决了Cell复用导致的messageId不一致问题，提供了更稳定、更可维护的语音播放解决方案。通过保持向后兼容性，确保了平滑的迁移过程。
