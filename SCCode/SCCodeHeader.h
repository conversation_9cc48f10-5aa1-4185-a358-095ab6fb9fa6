//
//  SCCodeHeader.h
//  Supercall
//
//  Created by 关伟洪 on 2024/1/7.
//

#ifndef SCCodeHeader_h
#define SCCodeHeader_h


#pragma mark - 扩展


//颜色
#import "SCCategoryUIColorCode.h"
//字体
#import "SCCategoryUIFontCode.h"
//SCCategoryUIViewCode
#import "SCCategoryUIViewCode.h"
//SCCategoryUITableViewCode.h
#import "SCCategoryUITableViewCode.h"
#import "SCCategoryUICollectionViewCode.h"
#import "SCCategoryUIButtonCode.h"
//SCCategoryUIImageViewCode
#import "SCCategoryUIImageViewCode.h"
//SCCategoryUILabelCode
#import "SCCategoryUILabelCode.h"
//字符串扩展
#import "SCCategoryNSStringCode.h"
#import "SCCategoryNSDateCode.h"
#import "SCCategoryUIImageCode.h"
#import "SCCategoryNSNumberCode.h"
#import "SCCategoryUIViewControllerCode.h"

#pragma mark - 代码块
#import "SCCodeBlockDefine.h"

#pragma mark - 核心函数
//SCAuthManager类单例
#import "SCAuthManager.h"
#define kScAuthMar [SCAuthManager instance]
//SCAppIntegrationManager
#import "SCAppIntegrationManager.h"
#define kSCCodeMar [SCAppIntegrationManager shared]

#pragma mark - 资源
#import "SCResourceManager.h"

#pragma mark - 布局
#import "SCCodeLayoutDefine.h"
#pragma mark - 常量
//常量
#import "SCConstant.h"
//SCUIKit 导入
#import "SCUIKit.h"

#pragma mark - 字典工具类
#import "SCDictionaryHelper.h"
#import "SCDataConverter.h"
#import "SCModelCompatibility.h"
#import "SCDictionaryKeys.h"
#pragma mark - 常用数据模型
// 已移除Model类，改为使用字典
// #import "SCTokenModel.h"
// #import "SCUserInfoModel.h"
// 支付来源
#import "SCPayEntry.h"

#pragma mark - 订阅
#import "SCObservable.h"
#import "SCAPIManage.h"


//加载所有model
#import "SCHomeViewController.h"
#import "SCCountryModel.h"
#import "SCCountryPickerViewModel.h"
// #import "SCOrderModel.h" // 已移除，使用字典替代
#import "SCOrderResultModel.h"

#import "SCThirdPayCannelDisplayModel.h"
//#import "SCThirdPayCannelGroupModel.h"
// 已移除Model类，改为使用字典
// #import "SCUserExtraInfoModel.h"
// #import "SCUserInfoBaseModel.h"
// #import "SCUserInfoModel.h"
// #import "SCAppConfigItemModel.h"
// #import "SCAppConfigModel.h"
// #import "SCAvatarRespListModel.h"
// #import "SCBannerModel.h"
// #import "SCGiftCountItemModel.h"
// #import "SCGiftCountModel.h"
// #import "SCGiftModel.h"

// #import "SCMyLevelModel.h"
// #import "SCOSSPolicyModel.h"
// #import "SCStrategyModel.h"
// #import "SCStrongGuideModel.h"
// #import "SCTagDetailModel.h"
// #import "SCTokenModel.h"
// #import "SCUserLevelModel.h"
#import "SCNetWorkResponseModel.h"
#import "SCXErrorModel.h"
#import "SCSocketEventModel.h"
//#import "SCSocketOrderEventModel.h"

#import "SCUserBoolChangeModel.h"
// #import "SCUserGiftNumDisplayModel.h" // 已移除，依赖于已删除的SCGiftModel
#import "SCAnchorInfoViewModel.h"
#import "SCUserRankModel.h"
#import "SCAnchorViewModel.h"
#import "SCAnchorSubTitleJXCategoryTitleCellModel.h"
#import "SCCallSessionModel.h"
#import "SCCallSessionStatus.h"
#import "SCRearCameraConfigModel.h"
#import "SCVideoCallChatMessageModel.h"
// #import "SCVideoCallResultModel.h" // 已移除，使用字典替代
#import "SCDBActionModel.h"
// #import "SCGiftSendNumModel.h" // 已移除，依赖于已删除的SCGiftModel
// #import "SCGiftSendNumTipDisplayModel.h" // 已移除，依赖于已删除的SCGiftModel
// #import "SCConversationDisplayModel.h" // 已移除，依赖于已删除的SCUserInfoBaseModel
// #import "SCMessageDisplayModel.h" // 已移除，依赖于已删除的SCUserInfoBaseModel
#import "SCMessageTitleItemDisplayModel.h"
#import "SCRechargeCardMessageContentModel.h"
#import "SCNativeMessageModel.h"
#import "SCNativeConversationModel.h"


#import "SCPersonalEditViewModel.h"
#import "SCPersonalItemDisplayModel.h"
#import "SCPersonalViewModel.h"
#import "SCLanguageModel.h"
// 已移除充值相关Model类，改为使用字典
// #import "SCActivityPromotionDisplayModel.h"
// #import "SCActivityPromotionModel.h"
// #import "SCCoinsModel.h"
// #import "SCPromotionDisplayModel.h"
// 已移除SCPromotionModel，使用字典替代
// #import "SCRegisterRewardModel.h"
#import "SCCoinsStoreViewModel.h"

#pragma mark - LOG

#ifdef DEBUG
#define IS_DEBUG_ENVIRONMENT 1
#else
#define IS_DEBUG_ENVIRONMENT 0
#endif

#if IS_DEBUG_ENVIRONMENT
#define SCLog(...) NSLog(__VA_ARGS__)
#else
#define SCLog(...)
#endif

#pragma mark - 不同包需要修改的值【需要注意必现混淆前配置】
#pragma mark 资源
//密码
#define kSCAssterPwd @"adbbdb0df8b647e82fb6f2f4236699b1"
//资源名字
#define kSCAssetNameKey @"supercall_data"


#pragma mark  网络
//#define kSCAPIHOTH @"http://test-app.kihoo.xyz"
//#define kSCAPIIMHOTH @"wss://test-im.kihoo.xyz"
//static NSString * SCAPIPrivacyPolicyUrl = @"http://h5.kihoo.xyz/privacyPolicy.html";
//static NSString * SCAPITermConditionsUrl = @"http://h5.kihoo.xyz/termConditions.html";


#endif /* SCCodeHeader_h */


