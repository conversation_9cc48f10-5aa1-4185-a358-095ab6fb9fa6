
//

#import "SCAppIntegrationManager+FlutterMerge.h"
//#import "Runner-Swift.h"

//#import "GeneratedPluginRegistrant.h"
@implementation SCAppIntegrationManager (FlutterMerge)
- (void)flutter_mergeWithApplication:(UIApplication *)application didFinishLaunchingWithOptions:(nullable NSDictionary *)launchOptions{
    
    //合并flutter的时候打开
    
//    FlutterAppDelegate *appdelegate = (FlutterAppDelegate *)[UIApplication sharedApplication].delegate;
//    FlutterViewController *controller;
//    //修改这里时间戳，大于才能进入b面逻辑，否则进A面
//    if ([appdelegate.window.rootViewController isKindOfClass:[FlutterViewController class]]) {
//            controller = (FlutterViewController *)appdelegate.window.rootViewController;
//
//        }
//
//    [kSCCodeMar configWithApiHost:@"http://test-app.zinggaby.club" imHost:@"wss://test-im.zinggaby.club" termConditionsUrl:@"https://app-h5.zinggaby.club/privacy.html" privacyPolicyUrl:@"https://app-h5.zinggaby.club/terms.html"];
//
//    [kSCCodeMar initWith:appdelegate.window toLogin:^{
//        dispatch_async(dispatch_get_main_queue(), ^{
//           // A 面
//        });
//
//    } toLogout:^{
//        //处理A面的清空逻辑
//        dispatch_async(dispatch_get_main_queue(), ^{
//
//        });
//    } launchImageViewCallback:^(UIImageView * _Nonnull imageView) {
//        imageView.image = [UIImage imageNamed:@"LaunchImage"];
//    } applogoCallback:^UIImage * _Nullable{
//        return [UIImage imageNamed:@"zing_app_logo"];
//    }];
//
//    [kSCCodeMar application:application didFinishLaunchingWithOptions:launchOptions];
//    [kSCCodeMar checkLoginWithAuditModeHandler:^(NSString * _Nullable loginStr) {
//        //修改这里时间戳，大于则闪退
//        if (controller) {
//            appdelegate.window.rootViewController = controller;
//        }
//
//    }];

    
}
@end
