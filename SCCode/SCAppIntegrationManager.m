//
//  SCAppIntegrationManager.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/20.
//

#import "SCAppIntegrationManager.h"
#import "SCAuthManager.h"
#import "SCNetworkManager.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import <FBSDKCoreKit/FBSDKCoreKit.h>
#import "SCTrackingUtils.h"
#import "SCAppUtils.h"
#import "SCNetworkStatusManager.h"
#import "SCLaunchVCViewController.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAppIntegrationManager()

@property (nonatomic, copy) SCDeleteAccountCallback deleteAccountCallback;
@property (nonatomic, copy) SCAuditModeHandler auditModeHandler;
@property (nonatomic, copy) SCImageViewCallBack launchImageViewCallback;
@property (nonatomic, assign) BOOL isCourage;

@property (nonatomic, strong) UIApplication *application;
@property (nonatomic, strong) NSDictionary *launchOptions;
@property (nonatomic, strong) NSTimer *checkTimer;

//审核接口获取完毕回调，内部退出登录时需要该回调。
@property (nonatomic, copy) SCVoidCallback strategySuccessCallback;

@end

@implementation SCAppIntegrationManager

#pragma mark - 单例
static SCAppIntegrationManager *_instance = nil;
+ (instancetype)shared {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _instance = [[self alloc] init];
    });
    return _instance;
}

- (void)configWithApiHost:(NSString *)apiHost imHost:(NSString *)imHost termConditionsUrl:(NSString *)termConditionsUrl privacyPolicyUrl:(NSString *)privacyPolicyUrl {
    self.apiHost = apiHost;
    self.imHost = imHost;
    self.termConditionsUrl = termConditionsUrl;
    self.privacyPolicyUrl = privacyPolicyUrl;
    
}
#pragma mark - 初始化配置
- (void)initWith:(UIWindow *)window
         toLogin:(void(^)(void))toLogin
        toLogout:(void(^)(void))toLogout
launchImageViewCallback:(nonnull SCImageViewCallBack)launchImageViewCallback
 applogoCallback:(nonnull SCImageCallBack)applogoCallback {
    self.rootWindow = window;
    self.toLogin = toLogin;
    self.toLogout = toLogout;
    self.launchImageViewCallback = launchImageViewCallback;
    self.applogoCallback = applogoCallback;
    
    [self setupLaunchVC];
    // 初始化其他必要配置
    [self setupBasicConfigurations];
}

- (void)setupLaunchVC {
    SCLaunchVCViewController *viewController = [[SCLaunchVCViewController alloc] init];
    if (self.launchImageViewCallback) {
        self.launchImageViewCallback(viewController.launchImageView);
    }
    self.rootWindow.rootViewController = viewController;
}

- (void)setupBasicConfigurations {
    // 初始化基础配置，如键盘管理等
    [[SCNetworkStatusManager shared] startMonitoring];
    // 其他基础配置...
}

// 获取appconfig后调用
- (void)setupFaceBookSDK {
    //Facebook
    [[SCTrackingUtils shared] setupFacebookSDKWithCompletion:^(BOOL success) {
        //配置成功后执行
        if (success) {
            [FBSDKApplicationDelegate.sharedInstance application:self->_application didFinishLaunchingWithOptions:self->_launchOptions];
        } else {
            #warning 如果配置失败也执行初始化，要确保 info.plist 有配置 Facebook，如果appconfig接口没有返回，info.plist也没有，初始化会崩溃。
//            [FBSDKApplicationDelegate.sharedInstance application:application didFinishLaunchingWithOptions:launchOptions];
        }
    }];
}

#pragma mark - 回调设置
- (void)setDeleteAccountCallback:(SCDeleteAccountCallback)callback {
    self.deleteAccountCallback = callback;
}

#pragma mark - App生命周期
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(nullable NSDictionary *)launchOptions {
    _application = application;
    _launchOptions = launchOptions;
    
    // 处理启动配置
    [SCResourceManager loadLanguageJson];
    //AppFlyer
#if DEBUG
        [[SCTrackingUtils shared] initAppsFlyerisDebug:YES];
#else
        [[SCTrackingUtils shared] initAppsFlyerisDebug:NO];
#endif       
    return YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    // 处理进入前台逻辑
    //Facebook
    [SCTrackingUtils activateFacebookApp];
    // 延迟 1 秒执行
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [SCTrackingUtils requestATTPermission];
//        [SCTrackingUtils startAppsFlyer];
    });
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    // 处理URL Scheme
    [FBSDKApplicationDelegate.sharedInstance application:app openURL:url options:options];
    return YES;
}

#pragma mark - 用户相关
- (void)deleteAccount {
    // 调用删除账号API
    [kScAuthMar deleteAccount:^(SCXErrorModel * _Nullable error) {
        if (error == nil) {
            if (self.deleteAccountCallback) {
                self.deleteAccountCallback();
            }
        } else {
            //TODO:
        }
    }];
}

- (NSString *)UUID {
    return SCAppUtils.identifier;
}

- (NSString *)userToken {
    // 优先使用字典数据
    NSDictionary *loginUserDict = [kScAuthMar loginUserDict];
    if (loginUserDict) {
        return [SCDictionaryHelper stringFromDictionary:loginUserDict forKey:@"token" defaultValue:@""];
    }
    return [SCDictionaryHelper stringFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCTokenKey defaultValue:@""];
}

- (BOOL)isLogin {
    return kScAuthMar.isLogin;
}

/// 获取策略配置
/// @param token 用户token
/// @param callback 回调策略参数
/// @param failure 失败回调
- (void)getStrategyWithToken:(NSString *)token callback:(SCStrategyCallback)callback failure:(void(^)(SCXErrorModel *error))failure {
    // 定义获取策略的通用处理块
    void (^fetchStrategy)(void) = ^{
        [kScAuthMar remoteStrategWithToken:token success:^(NSDictionary * _Nonnull strategyDict) {
            BOOL isEnergy = [SCDictionaryHelper boolFromDictionary:strategyDict forKey:@"isEnergy" defaultValue:NO];
            self.isCourage = isEnergy;

            // 获取flashChatConfig
            NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:strategyDict forKey:@"flashChatConfig" defaultValue:@{}];
            BOOL isSwitch = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isSwitch" defaultValue:NO];

            if (callback) {
                callback(isEnergy, isSwitch);
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            if (failure) {
                failure(error);
            }
        }];
    };
    
    // 如果没有 appConfig，先获取配置再获取策略
    if (kScAuthMar.appConfig == nil || [kScAuthMar.appConfig count] == 0) {
        [kScAuthMar remoteAppConfig:^(NSDictionary * _Nonnull appConfigDict) {
            fetchStrategy();
            [self setupFaceBookSDK];
        } failure:failure];
    } else {
        fetchStrategy();
        [self setupFaceBookSDK];
    }
}

/// 获取策略配置（字典版本）
/// @param token 用户token
/// @param callback 回调策略参数和字典数据
/// @param failure 失败回调
- (void)getStrategyDictWithToken:(NSString *)token
                        callback:(void(^)(BOOL isAudit, BOOL isMatchEnabled, NSDictionary *strategyDict))callback
                         failure:(void(^)(SCXErrorModel *error))failure {
    // 定义获取策略的通用处理块
    void (^fetchStrategy)(void) = ^{
        [kScAuthMar remoteStrategWithToken:token success:^(NSDictionary * _Nonnull strategyDict) {
            BOOL isEnergy = [SCDictionaryHelper boolFromDictionary:strategyDict forKey:@"isEnergy" defaultValue:NO];
            self.isCourage = isEnergy;

            // 获取flashChatConfig
            NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:strategyDict forKey:@"flashChatConfig" defaultValue:@{}];
            BOOL isSwitch = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isSwitch" defaultValue:NO];

            if (callback) {
                callback(isEnergy, isSwitch, strategyDict);
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            if (failure) {
                failure(error);
            }
        }];
    };

    // 如果没有 appConfig，先获取配置再获取策略
    if (kScAuthMar.appConfig == nil || [kScAuthMar.appConfig count] == 0) {
        [kScAuthMar remoteAppConfig:^(NSDictionary * _Nonnull appConfigDict) {
            fetchStrategy();
            [self setupFaceBookSDK];
        } failure:failure];
    } else {
        fetchStrategy();
        [self setupFaceBookSDK];
    }
}

/// 预登录处理
/// @param completion 完成回调，返回是否预登录成功
- (void)preLoginCompletion:(void(^)(NSString *token))completion failure:(void(^)(SCXErrorModel *error))failure {
    [kScAuthMar doLogin:^(NSDictionary * _Nonnull tokenDict) {
        // 获取 token 成功
        NSString *tokenString = [SCDictionaryHelper stringFromDictionary:tokenDict forKey:@"token" defaultValue:@""];
        if (!kSCIsStrEmpty(tokenString)) {
            NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:tokenDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
            NSString *userID = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            
            self.accessToken = tokenString;
            self.preTokenDict = tokenDict;
            if (completion) completion(tokenString);
        } else {
            // 获取 token 失败
            if (failure) failure([[SCXErrorModel alloc] initWitMsg:@"token is empty"]);
        }
    } failure:^(SCXErrorModel * _Nonnull error) {
        if (error.code != SCNetworkResponstCodeBlock) {
            // 获取 token 失败
            if (failure) failure(error);
        }
    }];
}

#pragma mark - 页面跳转与登录
- (void)navigateToLoginPage {
    [kScAuthMar routeToLoginVC];
    if (self.toLogin) {
        self.toLogin();
    }
}

- (void)callbackToAPageIsLogin:(NSString *)loginStr {
    if (self.auditModeHandler) {
        self.auditModeHandler(loginStr);
    }
}

- (void)navigateToMainPage {
    [kScAuthMar detectLogin:^(SCXErrorModel * _Nullable error) {
        if (error != nil) {
            // 函数内部已经处理跳转，这里可以根据情况做其他
        }
    }];
}

#pragma mark - 网络状态
- (BOOL)isNetworkAvailable {
    return [SCNetworkStatusManager shared].isNetworkReachable;
}

#pragma mark - 登录检查
/// 统一的登录检查方法
/// @param auditModeHandler 审核模式下的处理回调，返回A面是否已登录
- (void)checkLoginWithAuditModeHandler:(SCAuditModeHandler)auditModeHandler {
    kWeakSelf(self)
    self.checkTimer = self.checkTimer ?: [NSTimer scheduledTimerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        kStrongSelf
        if (strongSelf.isNetworkAvailable) {
            [timer invalidate];
            strongSelf.checkTimer = nil;
            if (auditModeHandler) {
                strongSelf.auditModeHandler = auditModeHandler;
            }
            
            [strongSelf checkUserLogin];
        }
    }];
}

- (void)checkUserLoginCompletion:(SCVoidCallback)completion {
    self.strategySuccessCallback = completion;
    [self checkUserLogin];
}

- (void)checkUserLogin {
    NSString *token = [self userToken];
    
    void (^handleStrategy)(NSString *) = ^(NSString *tokenStr) {
        [self getStrategyDictWithToken:tokenStr callback:^(BOOL isAudit, BOOL isMatchEnabled, NSDictionary *strategyDict) {
            if (isAudit) {
                // 审核模式
                if (self.auditModeHandler) {
                    // 优先使用字典数据
                    NSDictionary *loginUserDict = [kScAuthMar loginUserDict];
                    if (loginUserDict) {
                        // 将字典转换为JSON字符串
                        NSError *error;
                        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:loginUserDict options:0 error:&error];
                        if (!error && jsonData) {
                            NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                            self.auditModeHandler(jsonString);
                        } else {
                            self.auditModeHandler(nil);
                        }
                    } else if (self.preTokenDict != nil) {
                        // 使用预登录字典数据
                        NSError *error;
                        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:self.preTokenDict options:0 error:&error];
                        if (!error && jsonData) {
                            NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                            self.auditModeHandler(jsonString);
                        } else {
                            self.auditModeHandler(nil);
                        }
                    } else if (kScAuthMar.loginUserDict != nil) {
                        // 兜底使用字典数据
                        NSString *jsonString = [SCDataConverter jsonStringFromDictionary:kScAuthMar.loginUserDict];
                        self.auditModeHandler(jsonString);
                    } else if (self.preTokenDict != nil){
                        // 兜底使用预登录字典数据
                        NSString *jsonString = [SCDataConverter jsonStringFromDictionary:self.preTokenDict];
                        self.auditModeHandler(jsonString);
                    } else {
                        self.auditModeHandler(nil);
                    }
                }
                self.accessToken = @"";
//                self.preTokenModel = nil;
                self.preTokenDict = nil;
            } else {
                // 非审核模式，进入 B 面
                [SCResourceManager initAssterWithCompletion:^(BOOL success) {
                    if (self.isLogin) {
                        [self navigateToMainPage];
                    } else {
                        [kScAuthMar routeToLoginVC];
                    }
                }];
            }
            if (self.strategySuccessCallback) {
                self.strategySuccessCallback();
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            [self checkUserLogin];
        }];
    };
    
    if (self.isLogin && !kSCIsStrEmpty(token)) {
        // 优先使用字典数据获取用户ID
        // 使用便捷访问宏获取当前用户ID
        NSString *userID = kSCCurrentUserID;
        
        // 已登录，直接获取策略
        handleStrategy(token);
    } else {
        // 未登录，先预登录
        [self preLoginCompletion:^(NSString *token) {
            handleStrategy(token);
        } failure:^(SCXErrorModel *error) {
            // 预登录失败，显示重试弹窗
            if (error.code != SCNetworkResponstCodeBlock) {
                [self showRetryAlertWithCompletion:^{
                    [self checkUserLogin];
                }];
            }
        }];
    }
}

// 添加内部方法：显示重试弹窗
- (void)showRetryAlertWithCompletion:(void(^)(void))completion {
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Tips".translateString
                                                                   message:@"Failed to obtain data, please try again".translateString
                                                           preferredStyle:UIAlertControllerStyleAlert];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"Try again".translateString
                                            style:UIAlertActionStyleDefault
                                          handler:^(UIAlertAction * _Nonnull action) {
        if (completion) {
            completion();
        }
    }]];
    
    UIViewController *topVC = [self topViewController];
    [topVC presentViewController:alert animated:YES completion:nil];
}

// 添加内部方法：获取顶层控制器
- (UIViewController *)topViewController {
    UIViewController *topController = self.rootWindow.rootViewController;
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    return topController;
}

@end
