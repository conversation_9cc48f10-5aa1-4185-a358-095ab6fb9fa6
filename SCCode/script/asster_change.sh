# 指定压缩包名称和目标路径
ROOT_PATH="."
ZIP_NAME="supercall_data.zip"
DEST_PATH="supercall_data"


# 输入的明文字符串
PASSWORD="adbbdb0df8b647e82fb6f2f4236699b1"

#PASSWORD=$(echo `openssl rand -hex 16`)
echo "PASSWORD：$PASSWORD"

# 移除就得压缩包
rm -rf "$ROOT_PATH/$ZIP_NAME"
# 压缩图像为ZIP文件并添加密码
zip -rP "$PASSWORD" "$ROOT_PATH/$ZIP_NAME" "$ROOT_PATH/$DEST_PATH"


# 打印成功消息
echo "ZIP file created at $ROOT_PATH/$ZIP_NAME with password $PASSWORD"

