//
//  HomeViewController.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCHomeViewController.h"
#import <objc/runtime.h>
#import <RongIMLib/RongIMLib.h>
//Model
// #import "SCRegisterRewardModel.h" // 已移除，使用字典替代
// #import "SCStrategyModel.h" // 已移除，使用字典替代
// #import "SCAppConfigModel.h" // 已迁移到字典
#import "SCPromotionDisplayModel.h"
#import "SCActivityPromotionDisplayModel.h"
//Service
#import "SCCoinsService.h"
#import "SCIMService.h"

//View
#import "SCFloatingLayoutView.h"
//VC
#import "SCAnchorViewController.h"
#import "SCMessageViewController.h"
#import "SCPersonalViewController.h"
#import "SCFlashChatViewController.h"
#import "SCNewUserAwardPopup.h"
#import "SCStrongGuidePopoUp.h"
#import "SCActivityPromotionPopup.h"
#import "SCNewUserPromotionPopup.h"
#import "SCCoinsPopupViewController.h"

#import "SCResourceManager.h"
#import "SCAPIServiceManager.h"
#import "SCSafetyUtils.h"
#import "SCPopupManager.h"
#import "SCDiscoverViewController.h"
#import "SCDictionaryHelper.h"

@interface UITabBar (Badge)

- (void)setupBadge;

@end
@interface SCHomeViewController ()<UITabBarControllerDelegate>
//SCFloatingLayoutView
@property(nonatomic,weak) SCFloatingLayoutView * floatingLayoutView;
@property(nonatomic,strong) SCDisposeBag * disposeBag;
//是否已经显示过强引导
@property(nonatomic,assign) BOOL isHasShowStrongGuide;

@property (nonatomic, weak) SCActivityPromotionPopup *activityPopup;
@property (nonatomic, weak) SCNewUserPromotionPopup *userPromotionPopup;
@property (nonatomic, weak) SCNewUserAwardPopup *userAwardPopup;
@property (nonatomic, weak) SCMessageViewController *messageVC;

@property (nonatomic, assign) BOOL isRiskUploadRetry;

@end

@implementation SCHomeViewController

- (void)dealloc
{
    [_disposeBag dispose];
    _disposeBag = nil;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.delegate = self;
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
    self.isCanAutoShowActivityPopup = true;
    self.lastShowUserPromotionCode = nil;
    
    kScAuthMar.homeVC = self;
    _disposeBag = [[SCDisposeBag alloc] init];
    // Do any additional setup after loading the view.
    //添加文本按钮
    SCAnchorViewController * anchor = [[SCAnchorViewController alloc] init];
    anchor.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_call" selectedImageName:@"ic_tab_call_select"];
    
    SCMessageViewController * messageVC = [[SCMessageViewController alloc] init];
    messageVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_msg" selectedImageName:@"ic_tab_msg_select"];
    self.messageVC = messageVC;
    
    SCPersonalViewController* personalVC = [[SCPersonalViewController alloc] init];
    personalVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_personal" selectedImageName:@"ic_tab_personal_select"];
    
    // 初始只设置三个控制器
    [self setViewControllers:@[anchor, messageVC, personalVC]];
    
    self.tabBar.layer.shadowColor = [UIColor scBlack].CGColor;
    self.tabBar.layer.shadowOffset = CGSizeMake(0, -2);
    self.tabBar.layer.shadowOpacity = 0.1;
    self.tabBar.layer.shadowRadius = 2;
    self.tabBar.backgroundColor = [UIColor colorWithHexString:@"#290000"];
    self.tabBar.backgroundImage = [UIImage imageWithColor:[UIColor colorWithHexString:@"#290000"]];
    
    
    SCFloatingLayoutView * floatingLayoutView = [[SCFloatingLayoutView alloc] init];
    [self.view addSubview:floatingLayoutView];
    self.floatingLayoutView = floatingLayoutView;
    
    [self.floatingLayoutView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).inset(kSCTabBarFullHeight+30);
        make.width.mas_equalTo(75);
        make.trailing.equalTo(self.view).inset(17.0f);
        
    }];
    
    // 设置点击回调
    kWeakSelf(self)
    floatingLayoutView.activityAction = ^{
        [weakself showActivityPopup];
    };
    
    floatingLayoutView.promotionAction = ^{
        [weakself showUserPromotionPopup];
    };
    
    // 提前获取商店列表数据
    [SCAuthManager.instance.coinsService remoteCoinsListWithSuccess:^(NSArray<NSDictionary *> * _Nonnull coinsDicts) {
    } failure:^(SCXErrorModel * _Nonnull error) {
    }];
    
    [kSCAuthCoinsService.registerRewardObx subscribe:^(NSDictionary * _Nonnull value) {
        __strong SCHomeViewController * strongSelf = weakself;
        if(strongSelf == nil){
            return;
        }
        NSInteger coins = [SCDictionaryHelper integerFromDictionary:value forKey:@"coins" defaultValue:0];
        if(coins > 0 && ![SCNewUserAwardPopup isHaseShow]){
            [strongSelf showNewUserAwardPopup];
        }
    } error:nil disposeBag:self.disposeBag];
    
    // 检查策略字典是否存在
    if (kScAuthMar.strategyObs.value && [kScAuthMar.strategyObs.value count] > 0) {
        [weakself checkStrongGuide];
        [weakself checkIsOpenFlashChat];
    }
//    [kScAuthMar.strategyObs subscribe:^(SCStrategyModel * _Nullable value) {
//        [weakself checkStrongGuide];
//        [weakself checkIsOpenFlashChat];
//    } error:nil disposeBag:self.disposeBag];
    
    [kScAuthMar remoteStrongGuide:^(NSDictionary * _Nonnull strongGuideDict) {
        [weakself checkStrongGuide];
    } failure:nil];
    [kScAuthMar.imService.receiveMessageObs subscribe:^(RCMessage * _Nullable value) {
        [weakself refreshBadge];
    } error:nil disposeBag:self.disposeBag];
    [kScAuthMar.imService.unreadCountChangeObs afterSubscribe:^(NSNumber * _Nullable value) {
        [weakself refreshBadge];
    } error:nil disposeBag:self.disposeBag];
    
    // 新用户促销
    [kScAuthMar.coinsService.promotionObx afterSubscribe:^(SCPromotionDisplayModel * _Nonnull value) {
        NSString *promotionCode = [value code];
        if(promotionCode != nil && (self.lastShowUserPromotionCode == nil || ![promotionCode isEqualToString:self.lastShowUserPromotionCode])){
            if([value isShow]){
                [weakself showUserPromotionPopup];
                //记录已经弹框
                self.lastShowUserPromotionCode = promotionCode;
            } else {
                [weakself.userPromotionPopup dismiss];
            }
        }else{
            if(![value isShow]){
                [weakself.userPromotionPopup dismiss];
            }
        }
        
    } error:^(SCXErrorModel * _Nonnull error) {
        [weakself.userPromotionPopup dismiss];
    } disposeBag:self.disposeBag];
    
    /// 活动促销
    [kScAuthMar.coinsService.activityPromotionObx subscribe:^(SCActivityPromotionDisplayModel * _Nonnull value) {
        if(weakself.isCanAutoShowActivityPopup){
            if([value isShow]){
                weakself.isCanAutoShowActivityPopup = false;
                [weakself showActivityPopup];
            }else{
                [weakself.activityPopup dismiss];
            }
        }else{
            if(![value isShow]){
                [weakself.activityPopup dismiss];
            }
        }
        
    } error:^(SCXErrorModel * _Nonnull error) {
        [weakself.activityPopup dismiss];
    } disposeBag:self.disposeBag];
    
    //金币变化，获取新用户促销
    [kScAuthMar.availableCoinsObx afterSubscribe:^(NSNumber * _Nullable value) {
        [kScAuthMar.coinsService remotePromotionWithSuccess:nil failure:nil];
        [weakself sc_blank_empty];
    } error:nil disposeBag:self.disposeBag];
    
    [self refreshBadge];
    [self uploadRisk];
}

- (void)sc_blank_empty{}
/// 活动促销
- (void)showActivityPopup {
    SCActivityPromotionPopup *activityPopup = self.activityPopup;
    if (activityPopup == nil) {
        activityPopup = [[SCActivityPromotionPopup alloc] init];
        self.activityPopup = activityPopup;
    }
    [self.activityPopup showInViewController:self];
}

/// 新用户促销
- (void)showUserPromotionPopup {
    SCNewUserPromotionPopup *popup = self.userPromotionPopup;
    if (popup == nil) {
        popup = [[SCNewUserPromotionPopup alloc] init];
        self.userPromotionPopup = popup;
    }
    [self.userPromotionPopup showInViewController:self];
}

- (void)showNewUserAwardPopup {
    SCNewUserAwardPopup *popup = self.userAwardPopup;
    if (popup == nil) {
         popup = [[SCNewUserAwardPopup alloc] init];
        self.userAwardPopup = popup;
    }
    [self.userAwardPopup showInViewController:self];
}

//检查是否打开强化引导
- (void)checkStrongGuide{
    // 从策略字典中获取强引导开关状态
    BOOL isSwitchStrongGuide = [SCDictionaryHelper boolFromDictionary:kScAuthMar.strategyObs.value forKey:@"isSwitchStrongGuide" defaultValue:NO];
    if(kScAuthMar.strongGuideModel && isSwitchStrongGuide && !self.isHasShowStrongGuide){
        self.isHasShowStrongGuide = YES;
        [SCStrongGuidePopoUp createStrongGuideViewWithDict:kScAuthMar.strongGuideModel useOtherPaymentBlock:nil];
    }
    
}

- (void)checkIsOpenFlashChat {
    // 获取当前所有的视图控制器
    SCAnchorViewController *anchor = self.viewControllers.firstObject;
    SCMessageViewController *messageVC = nil;
    SCPersonalViewController *personalVC = nil;
    SCFlashChatViewController *flashChatVC = nil;
    SCDiscoverViewController *discoverVC = nil;
    
    // 创建需要的控制器
    if (!messageVC) {
        messageVC = [[SCMessageViewController alloc] init];
        messageVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_msg" selectedImageName:@"ic_tab_msg_select"];
        self.messageVC = messageVC;
    }
    
    if (!personalVC) {
        personalVC = [[SCPersonalViewController alloc] init];
        personalVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_personal" selectedImageName:@"ic_tab_personal_select"];
    }
    
    NSMutableArray *viewControllers = [NSMutableArray arrayWithObject:anchor];
    
    // 检查是否需要显示FlashChat - 从策略字典中获取闪聊配置
    NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.strategyObs.value forKey:@"flashChatConfig" defaultValue:@{}];
    BOOL isFlashChatSwitch = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isSwitch" defaultValue:NO];
    if (isFlashChatSwitch) {
        if (!flashChatVC) {
            flashChatVC = [[SCFlashChatViewController alloc] init];
            flashChatVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_flash_chat" selectedImageName:@"ic_tab_flash_chat_select"];
        }
        [viewControllers addObject:flashChatVC];
    }
    
    // 添加消息标签页
    [viewControllers addObject:messageVC];
    
    // 检查是否需要显示Discover - 使用专用方法获取扩展数据
    NSDictionary *appExtData = [SCDictionaryHelper appExtDataFromConfigDict:kScAuthMar.appConfig];
    BOOL bannerEnabled = [SCDictionaryHelper boolFromDictionary:appExtData forKey:@"bannerEnabled" defaultValue:NO];
    NSArray *banners = [SCDictionaryHelper arrayFromDictionary:appExtData forKey:@"banners" defaultValue:@[]];
    BOOL shouldShowDiscover = bannerEnabled && banners.count > 0;
    
    if (shouldShowDiscover) {
        if (!discoverVC) {
            discoverVC = [[SCDiscoverViewController alloc] init];
            discoverVC.tabBarItem = [self createTabBarItemWithImageName:@"ic_tab_game" selectedImageName:@"ic_tab_game_select"];
        }
        [viewControllers addObject:discoverVC];
    }
    
    // 添加个人中心标签页
    [viewControllers addObject:personalVC];
    
    // 更新视图控制器
    [self setViewControllers:viewControllers];
    
    [self setupSelectedTab];
    // 刷新badge显示
    [self refreshBadge];
}

- (void)setupSelectedTab {
    
    // 从策略字典中获取初始标签页索引
    NSInteger selectedTab = [SCDictionaryHelper integerFromDictionary:kScAuthMar.strategyObs.value forKey:@"theInitTab" defaultValue:0];
    

    if (selectedTab >= 0 && selectedTab < self.viewControllers.count) {
        self.selectedIndex = selectedTab;
    }
}

- (void)viewWillLayoutSubviews{
    [super viewWillLayoutSubviews];
    self.tabBar.backgroundColor = [UIColor scGlobalBgColor];
}

/// 风控上报
- (void)uploadRisk {
    int afterTime = 5;

    // 获取 appConfig 中的 k_interval 值
    NSDictionary *riskControlConfig = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.appConfig forKey:@"riskControlInfoConfig" defaultValue:@{}];
    NSString *interval = [SCDictionaryHelper stringFromDictionary:riskControlConfig forKey:@"k_interval" defaultValue:@""];
    if (!kSCIsStrEmpty(interval)) {
        afterTime = [interval intValue];
    }

    // 延迟执行任务
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(afterTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSString *info = [SCSafetyUtils encryptInfo];
        if (!kSCIsStrEmpty(info) && kScAuthMar.isLogin) {  // 确保 info 非空且非空字符串
            // 上传风险信息
            kWeakSelf(self);
            [SCAPIServiceManager requestUploadRiskWithInfo:info success:^{
                
                [weakself sc_blank_empty];
            } failure:^(SCXErrorModel * _Nonnull error) {
                //失败的话，重新上传一次
                if (!weakself.isRiskUploadRetry) {
                    weakself.isRiskUploadRetry = YES;
                    [weakself uploadRisk];
                }
            }];
        }
    });
}

-(void)refreshBadge{
    kWeakSelf(self);
    //刷新红点逻辑
    [[RCCoreClient sharedCoreClient] getTotalUnreadCountWith:^(int unreadCount) {
        dispatch_async(dispatch_get_main_queue(), ^{
            NSString *displayValue = unreadCount > 0 ? [NSString stringWithFormat:@"%d", unreadCount] : nil;
            if (unreadCount > 99) {
                displayValue = @"99+";
            }
            
            // 直接使用保存的引用设置badge
            weakself.messageVC.tabBarItem.badgeValue = displayValue;
            [weakself.tabBar setupBadge];
        });
    }];
}

- (UITabBarItem *)createTabBarItemWithImageName:(NSString *)imageName selectedImageName:(NSString *)selectedImageName {    
    UIImage *normalImage = [SCResourceManager loadImageWithName:imageName];
    UIImage *selectedImage = [SCResourceManager loadImageWithName:selectedImageName];
    UITabBarItem *tabBarItem = [[UITabBarItem alloc] initWithTitle:nil image:[normalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] selectedImage:[selectedImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]];
    tabBarItem.imageInsets = UIEdgeInsetsMake(8, 0, -8, 0);
    return tabBarItem;
}

#pragma mark - UITabBarControllerDelegate
- (void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController {
    self.floatingLayoutView.hidden = [viewController isKindOfClass:[SCFlashChatViewController class]] || 
                                    [viewController isKindOfClass:[SCDiscoverViewController class]];
    
    if ([viewController isKindOfClass:[SCAnchorViewController class]]) {
        [kSCAuthCoinsService checkRegisterReward];
    }
}

@end




@interface UITabBarItem (SCTabBarItemBadge)

- (NSString *)badge;

@end

@implementation UITabBar (SCTabBadge)

- (void)setupBadge {
    for (UIView *view in self.subviews) {
        if (view.tag >= 10000 && view.tag < 10004) {
            [view removeFromSuperview];
        }
    }
    
    CGFloat itemWidth = CGRectGetWidth(self.bounds) / (CGFloat)(self.items.count ?: 1);
    
    for (NSInteger index = 0; index < self.items.count; index++) {
        UITabBarItem *tabBarItem = self.items[index];
        NSString *badgeValue = tabBarItem.badge;
        
        if (badgeValue && ![badgeValue isEqualToString:@""]) {
            CGFloat x = (CGFloat)index * itemWidth + (itemWidth / 2) + 7;
            CGFloat y = 13.0; // Adjust the Y position as needed
            
            UIView *redDotView = [[UIView alloc] initWithFrame:CGRectMake(x, y, 7, 7)];
            redDotView.backgroundColor = [UIColor colorWithRed:1 green:0.313 blue:0.427 alpha:1]; // Replace with your desired color
            redDotView.layer.cornerRadius = CGRectGetWidth(redDotView.bounds) / 2;
            redDotView.tag = 10000 + index;
            [self addSubview:redDotView];
        }
    }
}

@end

@implementation UITabBarItem (SCTabBarItemBadge)

static char badgeValueKey;

- (void)setBadge:(NSString *)badge {
    objc_setAssociatedObject(self, &badgeValueKey, badge, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    [self updateBadge];
}

- (NSString *)badge {
    return objc_getAssociatedObject(self, &badgeValueKey);
}

- (void)updateBadge {
    // Implement code to update the badge view on the UITabBarItem
    // For example, you can create a custom badge view and set it on the tab bar item
}

@end
