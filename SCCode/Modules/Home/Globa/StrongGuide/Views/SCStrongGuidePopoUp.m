//
//  SCStrongGuidePopoUp.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCStrongGuidePopoUp.h"
// #import "SCStrongGuideModel.h" // 已移除，使用字典替代
#import "SCDictionaryHelper.h"
#import "SCTranslationService.h"
#import "SCFontManager.h"
#import "SCPopupManager.h"
#import "SCCoinsPopupViewController.h"

#define kSCStrongGuidePopoUpMaxHeight kSCScreenHeight - kSCSafeAreaTopHeight - kSCSafeAreaBottomHeight - 20

@interface SCStrongGuidePopoUp () <UIGestureRecognizerDelegate>

@property (nonatomic, strong) UIView *shadowView;
@property (nonatomic, strong) UIView *contentView;
// 关闭栏
@property (nonatomic, strong) UIView *closeView;
@property (nonatomic, strong) UIView *closeBar;
@property (nonatomic, strong) UILabel *titleLb;
@property (nonatomic, strong) UILabel *contentLb;
@property (nonatomic, strong) UILabel *invitatCodeLb;
@property (nonatomic, strong) UILabel *tipLb;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) NSLayoutConstraint *scrollowViewHeight;
@property (nonatomic, strong) UILabel *balanceLb;
@property (nonatomic, strong) UIButton *useOtherPaymentBtn;
@property (nonatomic, strong) UILabel *subTitleLb;

@property (nonatomic, strong) NSDictionary *guideDict; // 字典版本的数据
@property (nonatomic, assign) double height;
@property (nonatomic, copy) void(^useOtherPaymentBlock)(void);

@property (nonatomic, assign) CGFloat initialTouchY;
@property (nonatomic, assign) CGFloat dismissThreshold;

@end

@implementation SCStrongGuidePopoUp

#pragma mark - 数据获取辅助方法

// 获取内容文本
- (NSString *)getContent {
    return [SCDictionaryHelper stringFromDictionary:self.guideDict forKey:SCDictionaryKeys.shared.kSCStrongGuideContentKey defaultValue:@""];
}

// 获取包名
- (NSString *)getInPkgName {
    return [SCDictionaryHelper stringFromDictionary:self.guideDict forKey:SCDictionaryKeys.shared.kSCStrongGuideInPkgNameKey defaultValue:@""];
}

// 获取邀请码
- (NSString *)getInviteCode {
    return [SCDictionaryHelper stringFromDictionary:self.guideDict forKey:SCDictionaryKeys.shared.kSCStrongGuideInviteCodeKey defaultValue:@""];
}

// 获取原生充值重定向标志
- (NSInteger)getNativeRechargeRedirect {
    return [SCDictionaryHelper integerFromDictionary:self.guideDict forKey:SCDictionaryKeys.shared.kSCStrongGuideNativeRechargeRedirectKey defaultValue:0];
}

// 获取奖励金币数
- (NSInteger)getRewardCoins {
    return [SCDictionaryHelper integerFromDictionary:self.guideDict forKey:@"rewardCoins" defaultValue:0];
}

// 获取图标URL
- (NSString *)getInIcon {
    return [SCDictionaryHelper stringFromDictionary:self.guideDict forKey:@"inIcon" defaultValue:@""];
}

// 获取下载URL
- (NSString *)getUrl {
    return [SCDictionaryHelper stringFromDictionary:self.guideDict forKey:@"url" defaultValue:@""];
}


// 字典版本的创建方法
+ (SCStrongGuidePopoUp *)createStrongGuideViewWithDict:(NSDictionary *)guideDict useOtherPaymentBlock:(nullable void(^)(void))block {
    SCStrongGuidePopoUp *popup = [[SCStrongGuidePopoUp alloc] initWithGuideDict:guideDict];
    popup.useOtherPaymentBlock = block;
    [popup show];
    return popup;
}

// 字典版本的初始化方法
- (instancetype)initWithGuideDict:(NSDictionary *)guideDict {
    self = [super initWithFrame:kSCKeyWindow.frame];
    if (self) {
        self.guideDict = guideDict;
        [self setupUI];
    }
    return self;
}

// 统一的UI设置方法
- (void)setupUI {
    self.shadowView = [[UIView alloc] initWithFrame:kSCKeyWindow.bounds].setBackgroundColor([UIColor clearColor]).addSuperView(self);
    
    self.scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, kSCScreenWidth, 450)].addSuperView(self);
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.bounces = NO;
    
    self.contentView = [UIView new].setSize(CGSizeMake(kSCScreenWidth, 500)).addSuperView(self.scrollView);
    self.contentView.layer.cornerRadius = kSCBigCornerRadius;
    self.contentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(self.scrollView.mas_width);
        make.leading.top.bottom.trailing.equalTo(self.scrollView);
    }];
    [self createSubview];
    
    [self.contentView layoutIfNeeded];
    [self.contentView setNeedsLayout];
    CGFloat height = [self.contentView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
    self.scrollView.contentSize = CGSizeMake(kSCScreenWidth, self.height);
    if (height > kSCScreenHeight) {
        height = kSCScreenHeight;
    }
    self.height = height;
    
    self.scrollView.frame = CGRectMake(0, kSCScreenHeight, kSCScreenWidth, self.height);
}

- (void)createSubview {
    [self.contentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleDark];
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    blurView.frame = self.contentView.bounds;
    blurView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    blurView.layer.cornerRadius = kSCBigCornerRadius;
    blurView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    blurView.clipsToBounds = YES;
    [self.contentView addSubview:blurView];
    
    self.closeView = [[UIView alloc]init];
    kSCAddTapGesture(self.closeView, self, hide);
    [self.contentView addSubview:self.closeView];
    // 添加下滑手势
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    panGesture.delegate = self;
    [self.closeView addGestureRecognizer:panGesture];
    // 设置下滑关闭阈值（可以根据需要调整）
    self.dismissThreshold = 100.0f;
    [self.closeView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.leading.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(50);
        make.centerX.equalTo(self.contentView);
    }];
    
    self.closeBar = [[UIView alloc]init];
    self.closeBar.backgroundColor = [UIColor colorWithHexString:@"#BCBCBC"];
    self.closeBar.layer.cornerRadius = 1.5f;
    [self.closeView addSubview:self.closeBar];
    [self.closeBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(3.0f);
        make.width.mas_equalTo(60.0f);
        make.centerX.equalTo(self.closeView);
        make.top.equalTo(self.closeView).offset(6);
    }];
        
    self.titleLb = [UILabel new].setTextColor(UIColor.scWhite)
        .setText(@"Don’t Leave Me".translateString).addSuperView(self.contentView);
    self.titleLb.font = [SCFontManager boldFontWithSize:36];
    self.titleLb.textAlignment = NSTextAlignmentCenter;
    [self.titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.closeView.mas_bottom).offset(-25);
        make.leading.trailing.equalTo(self.contentView).inset(20);
    }];

    self.contentLb = [UILabel new].setFontRegularSize(13.0f)
        .setTextColor([UIColor scWhite])
        .setText([self getContent])
        .setNumberLines(0)
        .addSuperView(self.contentView);
    
    [self.contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(22);
        make.trailing.equalTo(self.contentView).offset(-22);
        make.top.equalTo(self.titleLb.mas_bottom).offset(5);
    }];

    NSString *content = [self getContent];
    NSString *inPkgName = [self getInPkgName];
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:content];
    NSRange range = [content rangeOfString:inPkgName];
    [attributedString addAttribute:NSForegroundColorAttributeName value:kSCColorWithHexStr(@"#FF00A4") range:range];
    self.contentLb.attributedText = attributedString;
    
    self.useOtherPaymentBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    self.useOtherPaymentBtn.layer.cornerRadius = kSCNormalCornerRadius;
    self.useOtherPaymentBtn.layer.masksToBounds = YES;
    self.useOtherPaymentBtn.titleLabel.font = kScUIFontSemibold(16.0f);
    [self.useOtherPaymentBtn setTitle:@"Use other payment".translateString forState:UIControlStateNormal];
    [self.useOtherPaymentBtn addTarget:self action:@selector(useOtherPaymentBtnAction) forControlEvents:UIControlEventTouchUpInside];
    [self.useOtherPaymentBtn sc_setThemeGradientBackground];
    [self.contentView addSubview:self.useOtherPaymentBtn];
    [self.useOtherPaymentBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentLb.mas_bottom).offset(16);
        make.centerX.equalTo(self.contentView);
        make.width.greaterThanOrEqualTo(@(kSCScreenWidth - kSCScaleWidth(72)));
        make.height.equalTo(@46);
    }];
    
    // 添加呼吸效果
    [self addBreathingEffectToButton:self.useOtherPaymentBtn];
    
    self.subTitleLb = [UILabel new].setTextColor(UIColor.scWhite).setNumberLines(0).addSuperView(self.contentView);
    self.subTitleLb.font = [SCFontManager boldFontWithSize:18.0f];
    self.subTitleLb.text = @"Or download our brother App to use";
    [self.subTitleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.useOtherPaymentBtn.mas_bottom).offset(16);
        make.leading.equalTo(self.contentView).offset(32);
        make.trailing.equalTo(self.contentView).offset(-32);
    }];

    self.balanceLb = [UILabel new].setFontRegularSize(13.0f)
        .setTextColor([UIColor scWhite])
        .setNumberLines(0)
        .addSuperView(self.contentView);
    
    [self.balanceLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(14);
        make.trailing.equalTo(self.contentView).offset(-14);
        make.top.equalTo(self.subTitleLb.mas_bottom).offset(8);
    }];

    NSString *rewards = [NSString stringWithFormat:@"%ld", [self getRewardCoins]];
    
    NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
    UIImage *coinImg = [SCResourceManager loadImageWithName:@"ic_coins_middle"];
    attachment.image = coinImg;
    attachment.bounds = CGRectMake(0, (14 - coinImg.size.height) / 2, coinImg.size.width, coinImg.size.height);
    NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:attachment];
    NSMutableAttributedString *firstPartAttributeString = [[NSMutableAttributedString alloc]initWithString:@"Your current coin balance ("];
    [firstPartAttributeString appendAttributedString:imageString];
    
    NSInteger availableCoin = kScAuthMar.availableCoinsObx.value.integerValue;
    
    NSMutableAttributedString *balanceAttributedString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@" %ld coins) stays in the current app,and at %@ you will get:\n※ %@ coin rewards\n※ First recharge discount\n※ Account information synchronization\n※ The same hot hostesses",availableCoin , inPkgName, rewards]];
    
    // 添加其他高亮
    [balanceAttributedString addAttribute:NSForegroundColorAttributeName value:kSCColorWithHexStr(@"#FF00A4") range:[balanceAttributedString.string rangeOfString:inPkgName]];
    [balanceAttributedString addAttribute:NSForegroundColorAttributeName value:kSCColorWithHexStr(@"#FFC200") range:[balanceAttributedString.string rangeOfString:[NSString stringWithFormat:@"%ld", availableCoin]]];
    NSRange rewardsRange = [balanceAttributedString.string rangeOfString:rewards options:NSBackwardsSearch];
    [balanceAttributedString addAttribute:NSForegroundColorAttributeName value:kSCColorWithHexStr(@"#FFC200") range:rewardsRange];
    
    [firstPartAttributeString appendAttributedString:balanceAttributedString];
    
    self.balanceLb.attributedText = firstPartAttributeString;
    
    // 邀请码
    self.invitatCodeLb = [UILabel new].setFontRegularSize(13.0f)
        .setTextColor([UIColor scWhite])
        .setNumberLines(0)
        .addSuperView(self.contentView);
    
    [self.invitatCodeLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(24);
        make.top.equalTo(self.balanceLb.mas_bottom).offset(5);
    }];
    
    //Fill in your invitation code: ### on the ###
    
    // 使用两个占位符的模板字符串
    NSString *templateString = @"Fill in your invitation code: ### \non the ### registration page, ";

    // 准备替换的值
    NSString *inviteCode = [self getInviteCode];
    NSString *pkgName = inPkgName;

    // 创建富文本字符串
    NSMutableAttributedString *invitatCodeAttributedString = [[NSMutableAttributedString alloc] init];

    // 创建通用的属性字典
    NSDictionary *normalAttributes = @{
        NSForegroundColorAttributeName: self.invitatCodeLb.textColor ?: UIColor.blackColor,
        NSFontAttributeName: self.invitatCodeLb.font
    };

    // 创建高亮属性字典
    NSDictionary *inviteCodeAttributes = @{
        NSForegroundColorAttributeName: kSCColorWithHexStr(@"#00FF0E"),
        NSFontAttributeName: kScUIFontSemibold(13),
        NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle),  // 添加下划线
        NSUnderlineColorAttributeName: kSCColorWithHexStr(@"#00FF0E")  // 下划线颜色与文字颜色一致
    };

    NSDictionary *pkgNameAttributes = @{
        NSForegroundColorAttributeName: kSCColorWithHexStr(@"#FF00A4"),
        NSFontAttributeName: self.invitatCodeLb.font
    };

    // 分割字符串并构建富文本
    NSArray *components = [templateString componentsSeparatedByString:@"###"];
    if (components.count == 3) { // 确保有两个占位符
        // 第一部分文本
        [invitatCodeAttributedString appendAttributedString:[[NSAttributedString alloc] initWithString:components[0] attributes:normalAttributes]];
        
        // 第一个占位符（邀请码）
        [invitatCodeAttributedString appendAttributedString:[[NSAttributedString alloc] initWithString:inviteCode attributes:inviteCodeAttributes]];
        
        // 中间部分文本
        [invitatCodeAttributedString appendAttributedString:[[NSAttributedString alloc] initWithString:components[1] attributes:normalAttributes]];
        
        // 第二个占位符（包名）
        [invitatCodeAttributedString appendAttributedString:[[NSAttributedString alloc] initWithString:pkgName attributes:pkgNameAttributes]];
        
        // 最后部分文本
        [invitatCodeAttributedString appendAttributedString:[[NSAttributedString alloc] initWithString:components[2] attributes:normalAttributes]];
    }

    // 处理RTL语言
    if (kScAuthMar.isLanguageForce) {
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.alignment = NSTextAlignmentRight;
        paragraphStyle.baseWritingDirection = NSWritingDirectionRightToLeft;
        
        [invitatCodeAttributedString addAttribute:NSParagraphStyleAttributeName
                               value:paragraphStyle
                               range:NSMakeRange(0, invitatCodeAttributedString.length)];
    }

    self.invitatCodeLb.attributedText = invitatCodeAttributedString;
    
    UIButton *copyBtn = [UIButton buttonWithTitle:@"Copy".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(10) image:nil backgroundColor:kSCColorWithHexStr(@"#FF0582") cornerRadius:10.0f].addSuperView(self.contentView);
    copyBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 5, 0, 5);
    [copyBtn addTarget:self action:@selector(copyAction) forControlEvents:UIControlEventTouchUpInside];

    [copyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).offset(-24);
        make.centerY.equalTo(self.invitatCodeLb);
        make.leading.mas_greaterThanOrEqualTo(self.invitatCodeLb.mas_trailing).offset(10);
        make.width.greaterThanOrEqualTo(@35);
        make.height.equalTo(@18);
    }];

    self.tipLb = [UILabel new].setFontRegularSize(13.0f)
        .setText(@"and all your personal information in the app will be synchronized.")
        .setTextColor(UIColor.scWhite)
        .setNumberLines(0)
        .addSuperView(self.contentView);
    
    [self.tipLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(14);
        make.trailing.equalTo(self.contentView).offset(-14 - 45);
        make.top.equalTo(self.invitatCodeLb.mas_bottom).offset(0);
    }];

    NSString *buttonTitle = [NSString stringWithFormat:@" %@ ", @"Click to translate".translateString];
    UIButton *tranBtn = [UIButton buttonWithTitle:buttonTitle titleColor:kSCColorWithHexStr(@"#FEC751") font:kScUIFontMedium(12) image:[SCResourceManager loadImageWithName:@"ic_message_translate"] backgroundColor:nil cornerRadius:0 target:self action:@selector(tranAction)]
    .addSuperView(self.contentView);
    
    [tranBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(14);
        make.top.equalTo(self.tipLb.mas_bottom).offset(14);
        make.height.equalTo(@20);
//        make.width.equalTo(@))
    }];

    // 创建下载按钮容器
    UIButton *downBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    downBtn.backgroundColor = UIColor.scBlack;
    downBtn.layer.cornerRadius = kSCNormalCornerRadius;
    [downBtn addTarget:self action:@selector(downAction) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:downBtn];

    // 创建标题label
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Download App".translateString;
    titleLabel.textColor = UIColor.scWhite;
    titleLabel.font = kScUIFontMedium(15);

    // 创建图片视图
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [iconImageView sd_setImageWithURL:[NSURL URLWithString:[self getInIcon]] placeholderImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"]];
    [iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(42, 42));
    }];

    // 创建并配置StackView
    UIStackView *stackView = [[UIStackView alloc] initWithArrangedSubviews:@[titleLabel, iconImageView]];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.spacing = 5;
    stackView.userInteractionEnabled = NO;
    [downBtn addSubview:stackView];

    // 设置StackView约束
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(downBtn);
    }];

    // 设置下载按钮约束
    [downBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(tranBtn.mas_bottom).offset(16);
        make.centerX.equalTo(self.contentView);
        make.width.greaterThanOrEqualTo(@(kSCScreenWidth - kSCScaleWidth(72)));
        make.height.equalTo(@46);
        make.bottom.equalTo(self.contentView).offset(-(MAX(kSCSafeAreaBottomHeight, 10)));
    }];

    [self.contentView layoutIfNeeded];
    [self.contentView setNeedsLayout];
    CGFloat height = [self.contentView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
    if (height > kSCScreenHeight) {
        height = kSCScreenHeight;
    }
    self.height = height;

    self.scrollView.frame = CGRectMake(0, kSCScreenHeight, kSCScreenWidth, self.height);
}

// 添加呼吸效果的方法
- (void)addBreathingEffectToButton:(UIButton *)button {
    CABasicAnimation *animation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
    animation.fromValue = @(1.0);
    animation.toValue = @(1.1);
    animation.duration = 0.8;
    animation.autoreverses = YES; // 动画结束后反向执行
    animation.repeatCount = HUGE_VALF; // 无限循环
    [button.layer addAnimation:animation forKey:@"breathingEffect"];
}

- (void)layoutSubviews{
    [super layoutSubviews];
    self.scrollView.contentSize = CGSizeMake(kSCScreenWidth, self.contentView.scHeight);
}

- (void)copyAction {
    UIPasteboard.generalPasteboard.string = [self getInviteCode];
    [self toast:@"Copy code successful".translateString];
}

- (void)tranAction {
    kWeakSelf(self);
    [kSCAuthTranslaService translateText:self.titleLb.text completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.titleLb.text = str;
            [weakself reloadView];
        }
    }];
    
    [kSCAuthTranslaService translateText:self.subTitleLb.text completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.subTitleLb.text = str;
            [weakself reloadView];
        }
    }];
    
    [kSCAuthTranslaService translateText:[self getContent] completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.contentLb.text = str;
            [weakself reloadView];
        }
    }];
    
    [kSCAuthTranslaService translateText:self.balanceLb.attributedText.string completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.balanceLb.attributedText = nil;
            weakself.balanceLb.text = str;
            [weakself reloadView];
        }
    }];
    
    [kSCAuthTranslaService translateText:self.invitatCodeLb.attributedText.string completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.invitatCodeLb.attributedText = nil;
            weakself.invitatCodeLb.text = str;
            [weakself reloadView];
        }
    }];
    
    [kSCAuthTranslaService translateText:self.tipLb.text completion:^(NSString * _Nonnull str, SCXErrorModel * _Nonnull error) {
        if (!kSCIsStrEmpty(str)) {
            weakself.tipLb.text = str;
            [weakself reloadView];
        }
    }];
}

- (void)reloadView {
    [self.contentView setNeedsLayout];
    CGFloat height = [self.contentView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
    if (height > kSCScreenHeight) {
        height = kSCScreenHeight;
    }
    self.height = height;
    self.scrollView.frame = CGRectMake(0, self.bounds.size.height - self.height, kSCScreenWidth, self.height);
}

- (void)downAction {
    NSURL *downloadURL = [NSURL URLWithString:[self getUrl]];
    if (downloadURL) {
        [UIApplication.sharedApplication openURL:downloadURL options:@{} completionHandler:nil];
    }
}

- (void)useOtherPaymentBtnAction {
    if (self.useOtherPaymentBlock) {
        self.useOtherPaymentBlock();
    }
    // 检查充值商店弹窗是否显示
    if (![[SCPopupManager shared] isShowingPopupOfClass:[SCCoinsPopupViewController class]]) {
        //转跳充值页面
        [SCCoinsPopupViewController showWithFromVC:nil entry:SCPayEntry.shared.kPayEntrySourceUserCenter];
    }
}

- (void)show {
    
    self.frame = kSCKeyWindow.bounds;
    [kSCKeyWindow addSubview:self];
    [UIView animateWithDuration:0.2 animations:^{
        self.shadowView.alpha = 1.0;
        self.scrollView.frame = CGRectMake(0, self.bounds.size.height - self.height, kSCScreenWidth, self.height);
    }];
}

- (void)hide {
    
    [UIView animateWithDuration:0.1 animations:^{
        self.shadowView.alpha = 0.0;
        self.scrollView.frame = CGRectMake(0, self.bounds.size.height, kSCScreenWidth, self.height);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

#pragma mark - Gesture Handling

- (void)handlePanGesture:(UIPanGestureRecognizer *)gesture {
    CGPoint translation = [gesture translationInView:self.contentView];
    
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan: {
            self.initialTouchY = self.scrollView.frame.origin.y;
            break;
        }
        case UIGestureRecognizerStateChanged: {
            // 只允许向下滑动
            if (translation.y < 0) {
                return;
            }
            
            CGFloat newY = self.initialTouchY + translation.y;
            // 限制最小Y值
            newY = MAX(self.bounds.size.height - self.height, newY);
            
            self.scrollView.frame = CGRectMake(0, newY, kSCScreenWidth, self.height);
            
            // 计算透明度
            CGFloat progress = translation.y / self.dismissThreshold;
            progress = MIN(1.0, progress);
            self.shadowView.alpha = 1.0 - progress;
            break;
        }
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            CGFloat velocity = [gesture velocityInView:self.contentView].y;
            CGFloat translation = [gesture translationInView:self.contentView].y;
            
            // 如果下滑距离超过阈值或下滑速度较快，则关闭弹窗
            if (translation > self.dismissThreshold || velocity > 1000) {
                [self hide];
            } else {
                // 否则恢复原位
                [UIView animateWithDuration:0.3 
                                  delay:0 
                 usingSpringWithDamping:0.8 
                  initialSpringVelocity:0.2 
                                options:UIViewAnimationOptionCurveEaseOut 
                             animations:^{
                    self.scrollView.frame = CGRectMake(0, self.bounds.size.height - self.height, kSCScreenWidth, self.height);
                    self.shadowView.alpha = 1.0;
                } completion:nil];
            }
            break;
        }
        default:
            break;
    }
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    if ([gestureRecognizer isKindOfClass:[UIPanGestureRecognizer class]]) {
        UIPanGestureRecognizer *panGesture = (UIPanGestureRecognizer *)gestureRecognizer;
        CGPoint velocity = [panGesture velocityInView:self.contentView];
        
        // 只允许垂直方向的手势
        return fabs(velocity.y) > fabs(velocity.x);
    }
    return YES;
}

@end
