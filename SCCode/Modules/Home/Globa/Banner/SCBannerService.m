//
//  SCBannerService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCBannerService.h"
#import "SCCategoryAPIManagerBanner.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCBannerService()

@end

@implementation SCBannerService

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        _bannersObs = [[SCObservable<NSArray<NSDictionary *>*> alloc] initWithValue:@[]];
    }
    return self;
}

///获取远程的Banner数据
-(void) remoteBannerWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *> *banners)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestBannerInfWithSuccess:^(NSArray<NSDictionary *> * _Nonnull bannerDicts) {
        __strong SCBannerService * strongSelf = weakself;
        if(strongSelf != nil){
            strongSelf.bannersObs.value = bannerDicts;
        }
        kSCBlockExeNotNil(success,bannerDicts);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}

- (void)sc_blank_empty{}

- (void)destroyService{
    [super destroyService];
    _bannersObs = nil;
}

@end
