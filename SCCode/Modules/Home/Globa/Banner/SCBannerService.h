//
//  SCBannerService.h
//  Supercall Banner服务
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCBaseAppService.h"
#import "SCObservable.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCBannerService : SCBaseAppService
///获取远程的Banner数据
@property(nonnull,nonatomic,strong,readonly) SCObservable<NSArray<NSDictionary *>*> *bannersObs;
-(void) remoteBannerWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *> *banners)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

@end

NS_ASSUME_NONNULL_END
