//
//  SCBannerView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCBannerView.h"
//View
#import "SCCustomPageControl.h"
#import "SCWebViewController.h"
#import "SCStrongGuidePopoUp.h"
//Dictionary Helper
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

// 原生分页Cell
@interface SCNativeBannerCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *imageView;
@end

@implementation SCNativeBannerCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupImageView];
    }
    return self;
}

- (void)setupImageView {
    self.imageView = [[UIImageView alloc] init];
    self.imageView.contentMode = UIViewContentModeScaleAspectFill;
    self.imageView.clipsToBounds = YES;
    [self.contentView addSubview:self.imageView];

    // 设置约束，让imageView填满整个cell
    self.imageView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.imageView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
        [self.imageView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor],
        [self.imageView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor],
        [self.imageView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor]
    ]];
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.imageView.image = nil;
    self.imageView.transform = CGAffineTransformIdentity;
}

@end

@interface SCBannerView()<UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UICollectionViewFlowLayout *flowLayout;
@property (nonatomic, strong) SCCustomPageControl *pageControl;
@property (nonatomic, strong) NSTimer *autoScrollTimer;
@property (nonatomic, assign) NSInteger currentIndex;
@property (nonatomic, assign) BOOL isUserInteracting;
@end

@implementation SCBannerView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if(self) {
        [self _initUI];
    }
    return self;
}

- (void)_initUI {
    // 初始化原生CollectionView
    [self setupCollectionView];
    self.currentIndex = 0;
    self.isUserInteracting = NO;

    // 保留原有的 pageControl
    self.pageControl = [[SCCustomPageControl alloc] init];
    self.pageControl.activePageColor = UIColor.scWhite;
    self.pageControl.inactivePageColor = [UIColor.scWhite colorWithAlphaComponent:0.5];;
    [self addSubview:self.pageControl];

    if (kScAuthMar.isLanguageForce) {
        self.collectionView.transform = CGAffineTransformMakeScale(-1, 1);
        self.pageControl.transform = CGAffineTransformMakeScale(-1, 1);
    }
}

- (void)setupCollectionView {
    // 创建流式布局
    self.flowLayout = [[UICollectionViewFlowLayout alloc] init];
    self.flowLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    self.flowLayout.minimumLineSpacing = 0;
    self.flowLayout.minimumInteritemSpacing = 0;

    // 创建CollectionView
    self.collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:self.flowLayout];
    self.collectionView.dataSource = self;
    self.collectionView.delegate = self;
    self.collectionView.pagingEnabled = YES;
    self.collectionView.showsHorizontalScrollIndicator = NO;
    self.collectionView.showsVerticalScrollIndicator = NO;
    self.collectionView.backgroundColor = [UIColor clearColor];

    // 注册cell
    [self.collectionView registerClass:[SCNativeBannerCell class] forCellWithReuseIdentifier:@"cell"];

    [self addSubview:self.collectionView];

    // 设置约束
    self.collectionView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.collectionView.topAnchor constraintEqualToAnchor:self.topAnchor],
        [self.collectionView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.collectionView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [self.collectionView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor]
    ]];
}

- (void)setBanners:(NSArray<NSDictionary *> *)banners {
    _banners = banners;
    self.pageControl.numberOfPages = banners.count;
    self.pageControl.currentPage = 0;
    self.currentIndex = 0;

    // 停止自动滚动
    [self stopAutoScroll];

    // 重新加载数据
    [self.collectionView reloadData];
    [self setNeedsLayout];

    // 重新开始自动滚动
    [self startAutoScrollIfNeeded];
}

- (void)layoutSubviews {
    [super layoutSubviews];

    // 更新item大小
    self.flowLayout.itemSize = self.bounds.size;

    // 如果frame发生变化，需要重新调整当前位置
    if (!CGSizeEqualToSize(self.collectionView.frame.size, self.bounds.size)) {
        self.collectionView.frame = self.bounds;
        [self scrollToCurrentIndexWithoutAnimation];
    }

    self.pageControl.frame = CGRectMake(0, self.frame.size.height - 15, self.frame.size.width, 15);
}

- (void)showIndex:(NSInteger)index {
    if (index < self.banners.count) {
        [self scrollToItemAtIndex:index animated:YES];
    }
}

- (void)scrollToItemAtIndex:(NSInteger)index animated:(BOOL)animated {
    if (index < 0 || index >= self.banners.count) {
        return;
    }

    self.currentIndex = index;
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:[self collectionViewIndexForActualIndex:index] inSection:0];
    [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:animated];
}

- (void)scrollToCurrentIndexWithoutAnimation {
    if (self.banners.count == 0) {
        return;
    }

    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:[self collectionViewIndexForActualIndex:self.currentIndex] inSection:0];
    [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:NO];
}

#pragma mark - 无限循环和自动滚动逻辑

- (NSInteger)totalItemCount {
    if (self.banners.count == 0) {
        return 0;
    }

    if (self.banners.count > 1) {
        // 无限循环时，在前后各添加一个项目
        return self.banners.count + 2;
    }

    return self.banners.count;
}

- (NSInteger)collectionViewIndexForActualIndex:(NSInteger)actualIndex {
    if (self.banners.count <= 1) {
        return actualIndex;
    }

    // 无限循环时，实际索引需要偏移1（因为前面添加了一个项目）
    return actualIndex + 1;
}

- (NSInteger)actualIndexForCollectionViewIndex:(NSInteger)collectionViewIndex {
    if (self.banners.count <= 1) {
        return collectionViewIndex;
    }

    if (collectionViewIndex == 0) {
        // 第一个项目是最后一个实际项目的副本
        return self.banners.count - 1;
    } else if (collectionViewIndex == [self totalItemCount] - 1) {
        // 最后一个项目是第一个实际项目的副本
        return 0;
    } else {
        // 中间的项目需要减去偏移量
        return collectionViewIndex - 1;
    }
}

- (void)startAutoScrollIfNeeded {
    if (self.banners.count > 1 && !self.isUserInteracting) {
        [self stopAutoScroll];
        self.autoScrollTimer = [NSTimer scheduledTimerWithTimeInterval:3.0
                                                                target:self
                                                              selector:@selector(autoScrollAction)
                                                              userInfo:nil
                                                               repeats:YES];
    }
}

- (void)stopAutoScroll {
    if (self.autoScrollTimer) {
        [self.autoScrollTimer invalidate];
        self.autoScrollTimer = nil;
    }
}

- (void)autoScrollAction {
    if (self.banners.count <= 1) {
        return;
    }

    NSInteger nextIndex = (self.currentIndex + 1) % self.banners.count;
    [self scrollToItemAtIndex:nextIndex animated:YES];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self totalItemCount];
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCNativeBannerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"cell" forIndexPath:indexPath];

    NSInteger actualIndex = [self actualIndexForCollectionViewIndex:indexPath.item];
    NSDictionary *bannerDict = self.banners[actualIndex];

    NSString *pic = [SCBannerView picFromDict:bannerDict];
    [cell.imageView sc_setImageWithURL:pic placeholderImage:[SCResourceManager loadImageWithName:@"banner_placeholder"]];
    cell.imageView.contentMode = UIViewContentModeScaleAspectFill;
    cell.imageView.clipsToBounds = YES;

    if (kScAuthMar.isLanguageForce) {
        cell.imageView.transform = CGAffineTransformMakeScale(-1, 1);
    }

    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger actualIndex = [self actualIndexForCollectionViewIndex:indexPath.item];
    NSDictionary *bannerDict = self.banners[actualIndex];
    NSInteger type = [SCBannerView typeFromDict:bannerDict];
    NSString *jumpUrl = [SCBannerView jumpUrlFromDict:bannerDict];

    if(type == 3) {
        //导量
        // 从策略字典中获取强引导开关状态
        BOOL isSwitchStrongGuide = [SCDictionaryHelper boolFromDictionary:kScAuthMar.strategyObs.value forKey:@"isSwitchStrongGuide" defaultValue:NO];
        if(kScAuthMar.strongGuideModel && isSwitchStrongGuide) {
            [SCStrongGuidePopoUp createStrongGuideViewWithDict:kScAuthMar.strongGuideModel useOtherPaymentBlock:nil];
        }
    } else {
        if(type == 1) {
            //内部浏览器
            [SCWebViewController showWithFromVC:[UIViewController currentViewController]
                                       title:@"Banner".translateString
                                         url:jumpUrl];
        } else if(type == 4) {
            //外部浏览器
            NSURL *url = [NSURL URLWithString:jumpUrl];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }
    }
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return self.bounds.size;
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    self.isUserInteracting = YES;
    [self stopAutoScroll];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    if (!decelerate) {
        self.isUserInteracting = NO;
        [self handleScrollEnd];
        [self startAutoScrollIfNeeded];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    self.isUserInteracting = NO;
    [self handleScrollEnd];
    [self startAutoScrollIfNeeded];
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
    [self handleScrollEnd];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self updateCurrentIndex];
    self.pageControl.currentPage = self.currentIndex;
}

- (void)handleScrollEnd {
    if (self.banners.count <= 1) {
        return;
    }

    // 处理无限循环的边界情况
    NSInteger currentCollectionViewIndex = [self getCurrentCollectionViewIndex];

    if (currentCollectionViewIndex == 0) {
        // 滚动到了第一个副本，跳转到最后一个实际项目
        self.currentIndex = self.banners.count - 1;
        [self scrollToCurrentIndexWithoutAnimation];
    } else if (currentCollectionViewIndex == [self totalItemCount] - 1) {
        // 滚动到了最后一个副本，跳转到第一个实际项目
        self.currentIndex = 0;
        [self scrollToCurrentIndexWithoutAnimation];
    }
}

- (NSInteger)getCurrentCollectionViewIndex {
    CGFloat pageWidth = self.collectionView.frame.size.width;
    if (pageWidth == 0) {
        return 0;
    }

    CGFloat currentOffsetX = self.collectionView.contentOffset.x;
    return (NSInteger)round(currentOffsetX / pageWidth);
}

- (void)updateCurrentIndex {
    NSInteger collectionViewIndex = [self getCurrentCollectionViewIndex];
    NSInteger newCurrentIndex = [self actualIndexForCollectionViewIndex:collectionViewIndex];

    if (newCurrentIndex != self.currentIndex) {
        self.currentIndex = newCurrentIndex;
    }
}

#pragma mark - 生命周期

- (void)dealloc {
    [self stopAutoScroll];
}

- (void)willMoveToSuperview:(UIView *)newSuperview {
    [super willMoveToSuperview:newSuperview];

    if (newSuperview == nil) {
        [self stopAutoScroll];
    }
}

- (void)didMoveToSuperview {
    [super didMoveToSuperview];

    if (self.superview != nil) {
        [self startAutoScrollIfNeeded];
    }
}

#pragma mark - Banner字典访问方法

+ (NSString *)picFromDict:(NSDictionary *)bannerDict {
    return [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerPicKey defaultValue:@""];
}

+ (NSInteger)typeFromDict:(NSDictionary *)bannerDict {
    return [SCDictionaryHelper integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerTypeKey defaultValue:0];
}

+ (NSString *)jumpUrlFromDict:(NSDictionary *)bannerDict {
    return [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerJumpUrlKey defaultValue:@""];
}

+ (NSInteger)bizTypeFromDict:(NSDictionary *)bannerDict {
    return [SCDictionaryHelper integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerBizTypeKey defaultValue:0];
}

@end


