//
//  SCBannerView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCBaseView.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCBannerView : SCBaseView

@property(nonatomic,strong) NSArray<NSDictionary *> * banners; // 字典版本Banner数据
-(void) showIndex:(NSInteger) index;

#pragma mark - Banner字典访问方法
/// 从字典获取Banner图片URL
+ (NSString *)picFromDict:(NSDictionary *)bannerDict;
/// 从字典获取Banner类型
+ (NSInteger)typeFromDict:(NSDictionary *)bannerDict;
/// 从字典获取Banner跳转URL
+ (NSString *)jumpUrlFromDict:(NSDictionary *)bannerDict;
/// 从字典获取Banner业务类型
+ (NSInteger)bizTypeFromDict:(NSDictionary *)bannerDict;

@end

NS_ASSUME_NONNULL_END
