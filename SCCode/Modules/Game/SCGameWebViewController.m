

#import "SCGameWebViewController.h"
#import "SCIMService.h"
#import "SCConversationInfoViewController.h"
#import "SCBaseNavigationController.h"
#import "SCAPIServiceManager.h"
#import "SCCategoryAPIManagerBanner.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCNavigationBar.h"
#import "SCFontManager.h"
#import "Lottie/Lottie-Swift.h"
#import "SCThrottle.h"

@interface SCGameWebViewController ()
@property(nonatomic,copy) void(^callback)(void);
@property(nonatomic,assign) BOOL isBlank;
@property(nonatomic,assign) BOOL isToOtherPage;
@property(nonatomic,assign) double webProgress;
@property(nonatomic,strong) UIView *blankView;
@property(nonatomic,copy) NSString *gameUrl;
@property(nonatomic, strong) CompatibleAnimationView *loadingAnimationView;

@end

@implementation SCGameWebViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.webView.backgroundColor = [UIColor colorWithHexString:@"#390C0C"];
    self.filterSound = YES;
    kWeakSelf(self)
    self.progressBlock = ^(CGFloat webProgress) {
        kStrongSelf
        strongSelf.webProgress = webProgress;
        if (!strongSelf.isBlank) {
            //防抖
            [SCThrottle throttleWithTag:@"WebLoading" onAfter:^{
                if( strongSelf.webProgress > 0.9) {
                    strongSelf.blankView.alpha = 1;
                    [UIView animateWithDuration:0.4 animations:^{
                        strongSelf.blankView.alpha = 0;
                    } completion:^(BOOL finished) {
                        strongSelf.blankView.hidden = YES;
                        strongSelf.blankView.alpha = 1;
                    }];
                }else {
                    strongSelf.blankView.hidden = NO;
                }
            }];
        }else{
            strongSelf.blankView.hidden = NO;
        }
    };
    
    UILabel *titleLb = [[UILabel alloc] init];
    titleLb.font = [SCFontManager boldItalicFontWithSize:20.0f];
    titleLb.text = [@"Games" translateString];
    titleLb.textColor = UIColor.scWhite;
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[[UIColor colorWithHexString:@"#FF0000"], [UIColor colorWithHexString:@"#460606"]] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *lineImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(17, 8)];
    UIImageView *lineView = [[UIImageView alloc]initWithImage:kScAuthMar.isLanguageForce ? [lineImage imageWithHorizontallyFlippedOrientation] : lineImage];
    lineView.alpha = 0.9;
    
    UIView *barView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 80, 44)];
    [barView addSubview:lineView];
    [barView addSubview:titleLb];
    [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.top.mas_offset(0);
    }];
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.width.equalTo(titleLb.mas_width);
        make.height.equalTo(@8);
        make.top.equalTo(titleLb.mas_bottom).offset(-8);
    }];
    
    [self.scNavigationBar.contentView addSubview:barView];
    [barView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
        make.height.mas_offset(44);
        make.width.mas_offset(80);
    }];
    
    self.blankView = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#390C0C"]);
    [self.scContentView addSubview:self.blankView];
    [self.blankView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
    
    // 创建 blankImgView
    UIImageView *bgImgView = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"bg_game_loading"]];
    [self.blankView addSubview:bgImgView];

    // 设置 blankImgView 的约束
    [bgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.blankView);
    }];
    
    // 创建 blankTitleLb
    UILabel *loadingLabel = [[UILabel alloc] init];
    loadingLabel.text = [[@"Loading…" translateString] copy];
    loadingLabel.font = [UIFont scSemibold:14];
    loadingLabel.textColor = [UIColor scWhite];
    [self.blankView addSubview:loadingLabel];
    // 设置 blankTitleLb 的约束
    [loadingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.leading.mas_greaterThanOrEqualTo(30);
        make.trailing.mas_lessThanOrEqualTo(-30);
        make.bottom.equalTo(self.blankView.mas_bottom).offset(-56);
    }];
    
    // 创建 loadingAnimationView
    NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/game_loading.json"];
    NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
    
    NSError *error = nil;
    NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
    if (jsonData) {
        self.loadingAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
        self.loadingAnimationView.loopAnimationCount = -1;
        [self.blankView addSubview:self.loadingAnimationView];
        [self.loadingAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(25);
            make.leading.equalTo(self.blankView).offset(50);
            make.trailing.equalTo(self.blankView).offset(-50);
            make.bottom.equalTo(loadingLabel.mas_top).offset(-3);
        }];
        [self.loadingAnimationView play];
    }
    
}
- (WKWebViewConfiguration *)defaultConfiguration{
    WKWebViewConfiguration * config = [super defaultConfiguration];
    WKUserContentController *userContentController = config.userContentController;
    [userContentController addScriptMessageHandler:self name:@"openVipService"];
    config.userContentController = userContentController;
    return config;
}

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message{
    [super userContentController:userContentController didReceiveScriptMessage:message];
    
    
    if ([message.name isEqualToString:@"openVipService"]) {
        // 打开VIP服务客服
        // 检查客服账户字典是否存在
        if(kScAuthMar.imService.userServiceAccountObx.value == nil || [kScAuthMar.imService.userServiceAccountObx.value count] == 0){
            kWeakSelf(self)
            [kScAuthMar.imService remoteUserServiceAccountWithSuccess:^(NSDictionary * _Nonnull userServiceAccountDict) {
                [weakself sc_blank_empty];
                // 使用字典版本的方法打开会话页面
                NSString *userID = [SCDictionaryHelper stringFromDictionary:userServiceAccountDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
                [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:userServiceAccountDict];
            } failure:^(SCXErrorModel * _Nonnull error) {
                [weakself sc_blank_empty];
                [kSCKeyWindow toast:@"Unable to match customer service, please try again later".translateString];
            }];
        }else{
            // 使用字典版本的方法打开会话页面
            NSString *userID = [SCDictionaryHelper stringFromDictionary:kScAuthMar.imService.userServiceAccountObx.value forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:kScAuthMar.imService.userServiceAccountObx.value];
        }
    }
}

- (void)sc_blank_empty{
    
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self getBanner];
}

- (void)viewDidDisappear:(BOOL)animated{
    [super viewDidDisappear:animated];
    [self loadBlank];
}

- (void)dealloc {
    [self.webView.configuration.userContentController removeScriptMessageHandlerForName:@"openVipService"];
}

- (void)loadRequestGameWebView{
    NSString *urlString = self.gameUrl ?: @""; // 如果 gameUrl 为 nil，则使用空字符串
    NSURL *webUrl = [NSURL URLWithString:urlString];

    if (webUrl) {
        // self.blankView.hidden = YES; // 如果需要隐藏 blankView，可以取消注释
        self.isBlank = NO; // 或者使用 [self setIsBlank:NO];
        [self.loadingAnimationView play];
        NSURLRequest *request = [NSURLRequest requestWithURL:webUrl];
        [self.webView loadRequest:request];
    }
}

- (void)loadBlank{
    self.isToOtherPage = YES; // 或者使用 [self setIsToOtherPage:YES];
    
    NSURL *webUrl = [NSURL URLWithString:@"about:blank"];
    if (webUrl) {
        self.blankView.hidden = NO; // 或者使用 [self.blankView setHidden:NO];
        [self.loadingAnimationView stop];
        self.loadingAnimationView.currentProgress = 0;
        self.isBlank = YES; // 或者使用 [self setIsBlank:YES];
        NSURLRequest *request = [NSURLRequest requestWithURL:webUrl];
        [self.webView loadRequest:request];
    }
}


- (void)getBanner{
    
    self.isToOtherPage = NO; // 或者使用 [self setIsToOtherPage:NO];
        
        if (self.gameUrl) {
            [self loadRequestGameWebView];
        } else {
            kWeakSelf(self)
            [SCAPIServiceManager requestBannerInfWithSuccess:^(NSArray<NSDictionary *> * _Nonnull bannerDicts) {
                for (NSDictionary *bannerDict in bannerDicts) {
                    NSInteger type = [SCDictionaryHelper integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerTypeKey defaultValue:0];
                    NSInteger bizType = [SCDictionaryHelper integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerBizTypeKey defaultValue:0];
                    NSString *jumpUrl = [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerJumpUrlKey defaultValue:@""];

                    if (type == 1) {
                        if (bizType == 5) {
                            if (!weakself.isToOtherPage) {
                                if (weakself.gameUrl) {
                                    // 如果 gameUrl 存在，不执行任何操作
                                } else {
                                    weakself.gameUrl = jumpUrl; // 赋值 jumpUrl
                                    [weakself loadRequestGameWebView]; // 加载请求
                                }
                            }
                        }
                    }
                }
            } failure:nil];
        }
    
    
    
}



//+(void) showWithFromVC:(UIViewController *)fromVC requestUrl:(NSString *) url callBack:(void(^)(void)) callback{
//    SCThirdPartyPayWebViewController *webVC = [[SCThirdPartyPayWebViewController alloc] init];
//    webVC.webAddress = url;
//    webVC.callback = callback;
//
//
//    SCBaseNavigationController *nv = [[SCBaseNavigationController alloc] initWithRootViewController:webVC];
//    ///注意这里知道打开使用全屏，因为不全屏，上一个App不会调用即将显示和即将关闭的逻辑
//    nv.modalPresentationStyle = UIModalPresentationFullScreen;
//    nv.modalTransitionStyle = UIModalTransitionStyleCrossDissolve;
//    [fromVC presentViewController:nv animated:YES completion:nil];
//}

@end
