//
//  SCConversationInfoViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCTranslationService.h"
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCConversationInfoViewController : UIViewController

// 原生组件属性
@property (nonatomic, strong) UICollectionView *conversationMessageCollectionView;
@property (nonatomic, strong) UIView *chatSessionInputBarControl;
@property (nonatomic, strong) NSMutableArray<SCNativeMessageModel *> *conversationDataRepository;
@property (nonatomic, strong) NSString *targetId;

// 初始化方法
- (instancetype)initWithConversationType:(RCConversationType)conversationType targetId:(NSString *)targetId;

- (SCTranslationStatus)getTranslationStatus:(long)messageId;

//+(void) showWithFromVC:(UIViewController *)fromVC tager:(NSString *) targetId tmpBaseModel:(SCUserInfoBaseModel * _Nullable) tmpBaseModel;
+(void) showWithFromVC:(UIViewController *)fromVC tager:(NSString *) targetId userDict:(NSDictionary * _Nullable) userDict;
@end

NS_ASSUME_NONNULL_END
