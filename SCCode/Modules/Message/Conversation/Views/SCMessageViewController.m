//
//  SCMessageViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/21.
//

#import "SCMessageViewController.h"
#import <JXCategoryView/JXCategoryView.h>
#import "JXCategoryDotView.h"
#import "SCGradientLineView.h"
#import "SCNavigationBar.h"
#import "SCMessageTitleItemDisplayModel.h"
#import "SCConversationListViewController.h"
#import "SCCallHistoryListViewController.h"
#import "SCFollowListViewController.h"
#import "SCIMService.h"
#import "SCFontManager.h"


@interface SCMessageViewController ()<JXCategoryViewDelegate,JXCategoryListContainerViewDelegate>

@property(nonatomic,nonnull,strong) NSArray<SCMessageTitleItemDisplayModel *> * titles;

//titleView
@property(nonatomic,nonnull,strong) JXCategoryDotView * scTitleView;



@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, strong) UIScrollView *currentListView;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;
@property (nonatomic, strong) NSMutableArray *dotStates;

@end

@implementation SCMessageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    ///隐藏返回键
    self.scNavigationBar.backButton.hidden = YES;
    
    NSArray<NSString *> * titleStrs = @[@"Messages".translateString,@"Calls".translateString,@"Followed".translateString];
    _titles = @[[[SCMessageTitleItemDisplayModel alloc] initWithTitle:titleStrs[0] viewController:[[SCConversationListViewController alloc] init]],
                [[SCMessageTitleItemDisplayModel alloc] initWithTitle:titleStrs[1] viewController:[[SCCallHistoryListViewController alloc] init]],
                [[SCMessageTitleItemDisplayModel alloc] initWithTitle:titleStrs[2] viewController:[[SCFollowListViewController alloc] init]]];
    
    ///转String
    ///
    
    NSMutableArray * _titles = @[].mutableCopy;
    _dotStates = @[@NO, @NO, @NO].mutableCopy;
    _scTitleView = [[JXCategoryDotView alloc] initWithFrame:CGRectMake(0, 0, [_titles count] * (85), 50)];
        
    self.scTitleView.titles = titleStrs;
    self.scTitleView.relativePosition = kScAuthMar.isLanguageForce ? JXCategoryDotRelativePosition_TopLeft : JXCategoryDotRelativePosition_TopRight;
    self.scTitleView.dotSize = CGSizeMake(8, 8);
    self.scTitleView.dotStates = self.dotStates;
    self.scTitleView.backgroundColor = [UIColor scGlobalBgColor];
    self.scTitleView.delegate = self;
    self.scTitleView.titleColor = [UIColor scWhite];
    self.scTitleView.titleSelectedColor = [UIColor scWhite];
    self.scTitleView.titleColorGradientEnabled = YES;
    self.scTitleView.titleFont = [SCFontManager boldItalicFontWithSize:16.0f];
    self.scTitleView.titleSelectedFont = [SCFontManager boldItalicFontWithSize:20.0f];
    self.scTitleView.cellSpacing = 23;
    self.scTitleView.averageCellSpacingEnabled = NO;
    [self.scNavigationBar.contentView addSubview:self.scTitleView];
    
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = [UIColor scLineColor];
    [self.scNavigationBar addSubview:line];
    
    //标题指示器
    SCGradientLineView *lineView = [[SCGradientLineView alloc] init];
    lineView.indicatorColor = [UIColor clearColor];
    lineView.indicatorWidthIncrement = 10;
    lineView.indicatorHeight = 8;
    lineView.verticalMargin = 6;
    self.scTitleView.indicators = @[lineView];
    
    [self.scTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.width.mas_equalTo([self.titles count] * (85));
        make.height.equalTo(self.scNavigationBar.contentView);
        make.leading.top.trailing.equalTo(self.scNavigationBar.contentView);
    }];
    
    self.listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    [self.scContentView addSubview:self.listContainerView];
    [self.listContainerView.scrollView setScrollEnabled:NO];

    self.scTitleView.listContainer = self.listContainerView;
    
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.top.equalTo(self.scContentView).offset(0);
    }];
    
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.scNavigationBar).offset(0);
        make.height.mas_equalTo(1);
        make.bottom.equalTo(self.scNavigationBar).offset(0);
    }];
    
    kWeakSelf(self);
    [kScAuthMar.imService.receiveMessageObs subscribe:^(RCMessage * _Nullable value) {
        [weakself refreshBadge];
    } error:nil disposeBag:self.disposeBag];
    [kScAuthMar.imService.unreadCountChangeObs afterSubscribe:^(NSNumber * _Nullable value) {
        [weakself refreshBadge];
    } error:nil disposeBag:self.disposeBag];
    
    [self refreshBadge];
}
-(void)refreshBadge{
    //刷新红点逻辑
    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] getTotalUnreadCountWith:^(int unreadCount) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakself.dotStates[0] = @(unreadCount > 0);
            weakself.scTitleView.dotStates = weakself.dotStates;
            [weakself.scTitleView reloadDataWithoutListContainer];
        });
    }];
}

#pragma mark - JXCategoryListContainerViewDelegate

- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return [self.titles count];
}

- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {

    id<JXCategoryListContentViewDelegate> vc = self.titles[index].viewController;
//    ((UIViewController *)vc).view.frame = listContainerView.bounds;
    
    return vc;
    
}



#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {

}

@end

