//
//  SCConversationListViewController.h
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/18.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import <JXCategoryView/JXCategoryView.h>
#import "SCNativeConversationModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCConversationListViewController : UIViewController<JXCategoryListContentViewDelegate>

// 原生TableView属性
@property (nonatomic, strong) UITableView *conversationListTableView;
// 会话数据源
@property (nonatomic, strong) NSMutableArray<SCNativeConversationModel *> *conversationListDataSource;

// 分页相关属性
@property (nonatomic, assign) BOOL isLoadingMore;
@property (nonatomic, assign) BOOL hasMoreData;
@property (nonatomic, assign) long long lastConversationTimestamp;

@end

NS_ASSUME_NONNULL_END
