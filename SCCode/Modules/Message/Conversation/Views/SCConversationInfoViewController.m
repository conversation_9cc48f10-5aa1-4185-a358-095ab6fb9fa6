//
//  SCConversationInfoViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCConversationInfoViewController.h"
#import "SCNavigationBar.h"
#import "SCUserBaseInfoView.h"
#import "SCIMService.h"
#import "SCTextMessageCell.h"
#import "SCVoiceMessageCell.h"
#import "SCVoiceMessageCell.h"
#import "SCTranslationService.h"
#import "SCAnchorInfoViewController.h"
#import "SCGiftPopupViewController.h"
#import "SCIMService.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import "SCSingleJsonMessage.h"
#import "SCGiftMessageCell.h"
#import "SCAnchorActionSheetViewController.h"
#import "SCAPIServiceManager.h"
#import "SCHyperLinkMessage.h"
#import "SCCallService.h"
#import "SCGiftSendNumModel.h"
#import "SCOnlineStatesService.h"
#import "SCInviteRechargeCoinsPopupViewController.h"
#import "SCCoinsService.h"
#import "SCRechargeCardMessageCell.h"
#import "SCFileMessageCell.h"
#import "SCRechargeCardMessageContentModel.h"
#import "SCNativeMessageModel.h"
// #import "SCAppConfigModel.h" // 已迁移到字典
#import "SCWebViewController.h"
#import "SCPermissionManager.h"
#import <RongIMLib/RongIMLib.h>
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

#import "Lottie/Lottie-Swift.h"

#import "SCThrottle.h"
#import "AGEmojiKeyboardView.h"
#import "SCImageMessageCell.h"
#import "SCMorePanelView.h"
#import "SCFullScreenPreviewMediaViewController.h"
#import "SCVoiceRecorderManager.h"
#import "SCVoiceRecordingView.h"




@interface SCConversationInfoViewController ()<SCTextMessageCellDelegate,SCVoiceMessageCellDelegate,SCRechargeCardMessageCellDelegate,UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout,UITextViewDelegate,AGEmojiKeyboardViewDelegate, AGEmojiKeyboardViewDataSource,UIScrollViewDelegate,SCMorePanelViewDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate, SCImageMessageCellDelegate, SCVoiceRecorderManagerDelegate, SCVoiceRecordingViewDelegate, UIGestureRecognizerDelegate, SCFileMessageCellDelegate>

// 原生组件属性
@property (nonatomic, strong) UICollectionViewFlowLayout *messageLayout;
@property (nonatomic, strong) UITextView *inputTextView;
@property (nonatomic, strong) UIButton *voiceButton;
@property (nonatomic, strong) UIButton *emojiButton;
@property (nonatomic, strong) UIButton *moreButton;
@property (nonatomic, strong) UIButton *sendButton;
@property (nonatomic, strong) UILabel *placeholderLabel;

// 表情键盘相关属性
@property(nonatomic,strong) UIView *emojiBoardContainer;
@property(nonatomic,strong) AGEmojiKeyboardView *emojiBoardView;
@property(nonatomic,assign) BOOL isOpenEmoji;

// 更多面板相关属性
@property (nonatomic, strong) UIView *morePanelContainer;
@property (nonatomic, strong) SCMorePanelView *morePanelView;
@property (nonatomic, assign) BOOL isOpenMorePanel;
@property (nonatomic, strong) NSArray<SCMorePanelItem *> *morePanelItems;

// 录音相关属性
@property (nonatomic, strong) UIButton *recordButton;
@property (nonatomic, strong) SCVoiceRecordingView *voiceRecordingView;
@property (nonatomic, assign) BOOL isVoiceMode; // 是否处于语音模式
@property (nonatomic, assign) BOOL isRecording; // 是否正在录音
@property (nonatomic, assign) CGPoint recordButtonInitialCenter; // 录音按钮初始中心点

//导航栏
@property(nonatomic,weak) SCNavigationBar * navigationBar;
@property(nonatomic,weak) UIButton * moreBtn;
@property (nonatomic, weak) UIButton *pinBtn;
//@property(nonatomic,weak) SCUserBaseInfoView * userBaseInfoV;
@property (nonatomic, weak) UILabel *userNameLb;
///对方用户信息字典
@property(nonatomic,strong) NSDictionary * tagerBaseInfoDict;
///临时数据字典，当详情数据没有请求下来的时候会先用缓存数据先显示
@property(nonatomic,strong) NSDictionary * tmpBaseDict;
@property(nonatomic,strong) SCDisposeBag * disposeBag;
@property(nonatomic,strong) SCOnlineStatusSubscribe * onlineStatusSub;

@property(nonatomic,strong) NSMutableDictionary<NSString*,NSNumber *> * translationStatusDic; //记录翻译的状态
@property(nonatomic,assign) BOOL isBottomRefreshed;
@property(nonatomic,assign) BOOL isLoadingMoreHistory;



/// 礼物动画
@property (nonatomic, strong) CompatibleAnimationView *giftAnimationView;
/// 拨打视频动画
@property (nonatomic, strong) CompatibleAnimationView *callAnimationView;

// 原生实现方法
- (void)createMessageCollectionView;
- (void)createInputBarControl;
- (void)setupViewLayout;
- (void)loadHistoryMessages;
- (void)loadMoreHistoryMessages;
- (void)sendTextMessage:(NSString *)text;
- (void)registerCustomCellsAndMessages;
- (void)scrollToBottomAnimated:(BOOL)animated;

// 键盘相关属性
@property (nonatomic, strong) MASConstraint *inputBarBottomConstraint;
@property (nonatomic, strong) MASConstraint *inputTextViewHeightConstraint;
@property (nonatomic, strong) MASConstraint *emojiBoardContainerBottomConstraint;
@property (nonatomic, strong) MASConstraint *morePanelContainerBottomConstraint;
@property (nonatomic, strong) MASConstraint *sendButtonTrailingConstraint;
@property (nonatomic, assign) CGFloat maxInputTextViewHeight;
@property (nonatomic, assign) CGFloat currentInputTextViewHeight;

// 手势相关属性
@property (nonatomic, strong) UITapGestureRecognizer *tapGestureToHideKeyboard;

@end

@implementation SCConversationInfoViewController

#pragma mark - 初始化方法
- (instancetype)initWithConversationType:(RCConversationType)conversationType targetId:(NSString *)targetId {
    self = [super init];
    if (self) {
        self.targetId = targetId;
        // 这里可以根据需要保存conversationType
        [self setupMorePanelItems];
    }
    return self;
}

#pragma mark - 更多面板数据初始化

- (void)setupMorePanelItems {
    NSMutableArray *items = [[NSMutableArray alloc] init];
    
    // 图片
    SCMorePanelItem *photoItem = [[SCMorePanelItem alloc] init];
    photoItem.title = @"Photo".translateString;
    photoItem.iconName = @"ic_more_photo"; // 更通用的图标名称
    photoItem.tag = 1001;
    [items addObject:photoItem];
    
    
    self.morePanelItems = [items copy];
}

#pragma mark - 原生组件创建
- (void)createMessageCollectionView {
    // 创建布局
    self.messageLayout = [[UICollectionViewFlowLayout alloc] init];
    self.messageLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
    self.messageLayout.minimumLineSpacing = 0;
    self.messageLayout.minimumInteritemSpacing = 0;

    // 创建CollectionView
    self.conversationMessageCollectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.messageLayout];
    self.conversationMessageCollectionView.delegate = self;
    self.conversationMessageCollectionView.dataSource = self;
    self.conversationMessageCollectionView.backgroundColor = [UIColor scChatBgColor];
    self.conversationMessageCollectionView.showsVerticalScrollIndicator = NO;
    self.conversationMessageCollectionView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;

    // 添加下拉刷新控件
    UIRefreshControl *refreshControl = [[UIRefreshControl alloc] init];
    [refreshControl addTarget:self action:@selector(loadMoreHistoryMessages) forControlEvents:UIControlEventValueChanged];
    self.conversationMessageCollectionView.refreshControl = refreshControl;

    // 添加到视图
    [self.view addSubview:self.conversationMessageCollectionView];

    // 注册自定义Cell
    [self registerCustomCellsAndMessages];
}

- (void)createInputBarControl {
    // 创建输入栏容器
    self.chatSessionInputBarControl = [[UIView alloc] init];
    self.chatSessionInputBarControl.backgroundColor = [UIColor colorWithHexString:@"#380D0D"];
    [self.view addSubview:self.chatSessionInputBarControl];

    // 创建麦克风按钮（左侧）
    self.voiceButton = [[UIButton alloc] init];
    [self.voiceButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_voice"] forState:UIControlStateNormal];
    self.voiceButton.tintColor = [UIColor scGray];
    [self.voiceButton addTarget:self action:@selector(onVoiceButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.chatSessionInputBarControl addSubview:self.voiceButton];

    // 创建输入框
    self.inputTextView = [[UITextView alloc] init];
    self.inputTextView.backgroundColor = [UIColor colorWithHexString:@"#4A1F1F"];
    self.inputTextView.textColor = UIColor.scWhite;
    self.inputTextView.font = kScUIFontMedium(16.0f);
    self.inputTextView.layer.cornerRadius = 21.5;
    self.inputTextView.layer.borderWidth = 1.0;
    self.inputTextView.layer.borderColor = [UIColor colorWithHexString:@"#6B2C2C"].CGColor;
    self.inputTextView.textContainerInset = UIEdgeInsetsMake(10, 12, 10, 12);
    self.inputTextView.delegate = self;
    self.inputTextView.returnKeyType = UIReturnKeyDefault;
    self.inputTextView.scrollEnabled = NO; // 初始不可滚动，通过高度变化来适应内容
    [self.chatSessionInputBarControl addSubview:self.inputTextView];

    // 计算最大高度（约3行文本）
    CGFloat lineHeight = self.inputTextView.font.lineHeight;
    self.maxInputTextViewHeight = lineHeight * 3 + self.inputTextView.textContainerInset.top + self.inputTextView.textContainerInset.bottom;

    // 设置初始高度
    self.currentInputTextViewHeight = 43;

    // 创建占位符
    self.placeholderLabel = [[UILabel alloc] init];
    self.placeholderLabel.text = @"Say something…".translateString;
    self.placeholderLabel.textColor = [UIColor scGray];
    self.placeholderLabel.font = kScUIFontMedium(14.0f);
    [self.chatSessionInputBarControl addSubview:self.placeholderLabel];

    // 创建表情按钮（右侧）
    self.emojiButton = [[UIButton alloc] init];
    [self.emojiButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_emoji"] forState:UIControlStateNormal];
    self.emojiButton.tintColor = [UIColor scGray];
    [self.emojiButton addTarget:self action:@selector(onEmojiButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.chatSessionInputBarControl addSubview:self.emojiButton];

    // 创建更多功能按钮（右侧）
    self.moreButton = [[UIButton alloc] init];
    [self.moreButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_more"] forState:UIControlStateNormal];
    self.moreButton.tintColor = [UIColor scGray];
    [self.moreButton addTarget:self action:@selector(onMoreButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.chatSessionInputBarControl addSubview:self.moreButton];

    // 创建发送按钮（右侧，初始隐藏）
    self.sendButton = [[UIButton alloc] init];
    [self.sendButton setImage:[SCResourceManager loadImageWithName:@"ic_call_chat_send"] forState:UIControlStateNormal];
    self.sendButton.layer.cornerRadius = 15.0f;
    self.sendButton.layer.masksToBounds = YES;
    [self.sendButton sc_setThemeGradientBackground];
    [self.sendButton addTarget:self action:@selector(onSendButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    self.sendButton.hidden = YES; // 初始隐藏
    self.sendButton.alpha = 0.0;
    [self.chatSessionInputBarControl addSubview:self.sendButton];

    // 创建录音按钮（替换输入框位置，初始隐藏）
    self.recordButton = [[UIButton alloc] init];
    [self.recordButton setTitle:@"Hold to talk".translateString forState:UIControlStateNormal];
    [self.recordButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    self.recordButton.titleLabel.font = kScUIFontMedium(16.0f);
    self.recordButton.backgroundColor = [UIColor colorWithHexString:@"#4A1F1F"];
    self.recordButton.layer.cornerRadius = 21.5;
    self.recordButton.hidden = YES; // 初始隐藏
    self.recordButton.alpha = 0.0;
    [self.recordButton sc_setThemeGradientBackgroundWithCornerRadius:21.5];
    [self.chatSessionInputBarControl addSubview:self.recordButton];

    // 添加录音按钮手势
    [self setupRecordButtonGestures];

    // 创建表情键盘容器
    self.emojiBoardContainer = [[UIView alloc] init];
    self.emojiBoardContainer.hidden = YES;
    self.emojiBoardContainer.alpha = 0;
    self.emojiBoardContainer.backgroundColor = [UIColor scGlobalBgColor];
    [self.view addSubview:self.emojiBoardContainer];

    // 创建表情键盘
    self.emojiBoardView = [[AGEmojiKeyboardView alloc] initWithFrame:CGRectMake(0, 0, kSCScreenWidth, 227.5f) dataSource:self];
    self.emojiBoardView.delegate = self;
    self.emojiBoardView.backgroundColor = [UIColor scGlobalBgColor];

    [self.emojiBoardContainer addSubview:self.emojiBoardView];

    // 创建更多面板容器
    self.morePanelContainer = [[UIView alloc] init];
    self.morePanelContainer.hidden = YES;
    self.morePanelContainer.alpha = 0;
    self.morePanelContainer.backgroundColor = [UIColor scGlobalBgColor];
    [self.view addSubview:self.morePanelContainer];

    // 创建更多面板视图
    self.morePanelView = [[SCMorePanelView alloc] initWithItems:self.morePanelItems];
    self.morePanelView.delegate = self;
    [self.morePanelContainer addSubview:self.morePanelView];

    // 设置约束
//    // 麦克风按钮约束
    [self.voiceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.chatSessionInputBarControl).offset(15);
        make.top.equalTo(self.chatSessionInputBarControl).offset(18);
        make.width.height.mas_equalTo(24);
    }];

    // 更多功能按钮约束
    [self.moreButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.chatSessionInputBarControl).offset(-15);
        make.top.equalTo(self.chatSessionInputBarControl).offset(18);
        make.width.height.mas_equalTo(24);
    }];

    // 表情按钮约束
    [self.emojiButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.moreButton.mas_leading).offset(-12);
        make.top.equalTo(self.chatSessionInputBarControl).offset(18);
        make.width.height.mas_equalTo(24);
    }];

    // 输入框约束
    [self.inputTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.voiceButton.mas_trailing).offset(12);
//        make.leading.equalTo(self.chatSessionInputBarControl).offset(15);
        make.trailing.equalTo(self.emojiButton.mas_leading).offset(-12);
        make.top.equalTo(self.chatSessionInputBarControl).offset(8);
        self.inputTextViewHeightConstraint = make.height.mas_equalTo(43); // 初始高度
    }];

    // 更多面板容器约束
    [self.morePanelContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        self.morePanelContainerBottomConstraint = make.bottom.equalTo(self.view);
        make.height.mas_equalTo(0); // 初始高度为0
    }];
    // 发送按钮约束（与moreButton位置相同，初始隐藏）
    [self.sendButton mas_makeConstraints:^(MASConstraintMaker *make) {
        self.sendButtonTrailingConstraint = make.trailing.equalTo(self.chatSessionInputBarControl).offset(-12);
        make.centerY.equalTo(self.emojiButton);
        make.width.height.mas_equalTo(30);
    }];

    // 录音按钮约束（与输入框位置相同，初始隐藏）
    [self.recordButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.voiceButton.mas_trailing).offset(12);
        make.trailing.equalTo(self.emojiButton.mas_leading).offset(-12);
        make.top.equalTo(self.chatSessionInputBarControl).offset(8);
        make.height.mas_equalTo(43);
    }];

    // 设置表情键盘容器约束
    [self.emojiBoardContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        self.emojiBoardContainerBottomConstraint = make.bottom.equalTo(self.view);
        make.height.mas_equalTo(0); // 初始高度为0
    }];

    // 设置表情键盘在容器内的约束
    [self.emojiBoardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.emojiBoardContainer);
        make.top.equalTo(self.emojiBoardContainer);
        make.bottom.equalTo(self.emojiBoardContainer).offset(-kSCSafeAreaBottomHeight);
        make.height.mas_equalTo(227.5f);
    }];

    // 更多面板在容器内的约束
    [self.morePanelView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.morePanelContainer);
        make.top.equalTo(self.morePanelContainer);
        make.bottom.equalTo(self.morePanelContainer).offset(-kSCSafeAreaBottomHeight);
        make.height.mas_equalTo(227.5f);
    }];

    // 占位符约束
    [self.placeholderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.inputTextView).offset(16);
        make.trailing.equalTo(self.inputTextView).offset(-16);
        make.centerY.equalTo(self.inputTextView);
    }];
}

- (void)setupViewLayout {
    // 设置整体布局约束
    [self.conversationMessageCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        make.top.equalTo(self.navigationBar.mas_bottom);
        make.bottom.equalTo(self.chatSessionInputBarControl.mas_top);
    }];

    [self.chatSessionInputBarControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        self.inputBarBottomConstraint = make.bottom.equalTo(self.view);
        make.top.equalTo(self.inputTextView).offset(-8); // 动态高度：根据输入框高度调整
        make.bottom.equalTo(self.inputTextView).offset(8 + kSCSafeAreaBottomHeight); // 底部边距 + 安全区域
    }];
}

- (void)registerCustomCellsAndMessages {
    // 注册自定义消息Cell
    [self.conversationMessageCollectionView registerClass:[SCTextMessageCell class] forCellWithReuseIdentifier:@"SCTextMessageCell"];
    [self.conversationMessageCollectionView registerClass:[SCGiftMessageCell class] forCellWithReuseIdentifier:@"SCGiftMessageCell"];
    [self.conversationMessageCollectionView registerClass:[SCVoiceMessageCell class] forCellWithReuseIdentifier:@"SCVoiceMessageCell"];
    [self.conversationMessageCollectionView registerClass:[SCRechargeCardMessageCell class] forCellWithReuseIdentifier:@"SCRechargeCardMessageCell"];
    [self.conversationMessageCollectionView registerClass:[SCImageMessageCell class] forCellWithReuseIdentifier:@"SCImageMessageCell"];
    [self.conversationMessageCollectionView registerClass:[SCFileMessageCell class] forCellWithReuseIdentifier:@"SCFileMessageCell"];

    // 注册默认Cell作为备用
    [self.conversationMessageCollectionView registerClass:[UICollectionViewCell class] forCellWithReuseIdentifier:@"DefaultCell"];
}

#pragma mark - 消息数据管理
- (void)loadHistoryMessages {
    // 初始化数据源
    if (!self.conversationDataRepository) {
        self.conversationDataRepository = [[NSMutableArray alloc] init];
    }

    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] getHistoryMessages:ConversationType_PRIVATE
                                               targetId:self.targetId
                                        oldestMessageId:-1
                                                  count:20
                                             completion:^(NSArray<RCMessage *> *messages) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakself.conversationDataRepository removeAllObjects];

            // 转换RCMessage为SCNativeMessageModel并按时间排序
            NSMutableArray *messageModels = [[NSMutableArray alloc] init];
            for (RCMessage *message in messages) {
                SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
                [messageModels addObject:model];
            }

            // 按发送时间排序，确保旧消息在前，新消息在后
            [messageModels sortUsingComparator:^NSComparisonResult(SCNativeMessageModel *obj1, SCNativeMessageModel *obj2) {
                return [@(obj1.sentTime) compare:@(obj2.sentTime)];
            }];

            // 计算消息时间显示逻辑
            [SCNativeMessageModel calculateDisplayTimeForMessages:messageModels];

            // 添加到数据源
            [weakself.conversationDataRepository addObjectsFromArray:messageModels];

            // 刷新UI
            [weakself.conversationMessageCollectionView reloadData];
            [weakself scrollToBottomAnimated:NO];
        });
    }];
}

- (void)loadMoreHistoryMessages {
    if (self.conversationDataRepository.count == 0 || self.isLoadingMoreHistory) {
        [self.conversationMessageCollectionView.refreshControl endRefreshing];
        return;
    }

    self.isLoadingMoreHistory = YES;

    // 获取最旧消息的ID
    SCNativeMessageModel *oldestMessage = self.conversationDataRepository.firstObject;
    long oldestMessageId = oldestMessage.messageId;

    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] getHistoryMessages:ConversationType_PRIVATE
                                               targetId:self.targetId
                                        oldestMessageId:oldestMessageId
                                                  count:20
                                             completion:^(NSArray<RCMessage *> *messages) {
        dispatch_async(dispatch_get_main_queue(), ^{
            weakself.isLoadingMoreHistory = NO;
            [weakself.conversationMessageCollectionView.refreshControl endRefreshing];

            if (messages.count == 0) {
                // 没有更多历史消息
                return;
            }

            // 转换RCMessage为SCNativeMessageModel并按时间排序
            NSMutableArray *messageModels = [[NSMutableArray alloc] init];
            for (RCMessage *message in messages) {
                SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
                [messageModels addObject:model];
            }

            // 按发送时间排序
            [messageModels sortUsingComparator:^NSComparisonResult(SCNativeMessageModel *obj1, SCNativeMessageModel *obj2) {
                return [@(obj1.sentTime) compare:@(obj2.sentTime)];
            }];

            // 记录当前第一个消息的位置，用于保持滚动位置
            NSInteger oldFirstMessageIndex = 0;

            // 将新的历史消息插入到数组开头
            NSIndexSet *indexSet = [NSIndexSet indexSetWithIndexesInRange:NSMakeRange(0, messageModels.count)];
            [weakself.conversationDataRepository insertObjects:messageModels atIndexes:indexSet];

            // 重新计算整个消息列表的时间显示逻辑
            [SCNativeMessageModel calculateDisplayTimeForMessages:weakself.conversationDataRepository];

            // 刷新UI
            [weakself.conversationMessageCollectionView reloadData];

            // 保持滚动位置，滚动到之前的第一个消息位置
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:messageModels.count inSection:0];
            [weakself.conversationMessageCollectionView scrollToItemAtIndexPath:indexPath
                                                                atScrollPosition:UICollectionViewScrollPositionTop
                                                                        animated:NO];
        });
    }];
}

- (void)sendTextMessage:(NSString *)text {
    if (text.length == 0) {
        return;
    }

    // 创建文本消息
    RCTextMessage *textMessage = [RCTextMessage messageWithContent:text];

    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] sendMessage:ConversationType_PRIVATE
                                        targetId:self.targetId
                                         content:textMessage
                                     pushContent:nil
                                        pushData:nil
                                        attached:^(RCMessage * _Nullable message) {
        // 在attached回调中获取message对象
        if (message) {
            dispatch_async(dispatch_get_main_queue(), ^{
                SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
                [weakself.conversationDataRepository addObject:model];

                // 重新计算消息时间显示逻辑
                [SCNativeMessageModel calculateDisplayTimeForMessages:weakself.conversationDataRepository];

                [weakself.conversationMessageCollectionView reloadData];
                [weakself scrollToBottomAnimated:YES];
            });
        }
    } success:^(long messageId) {
        // 发送成功
        
    } error:^(RCErrorCode nErrorCode, long messageId) {
        // 发送失败
        
    }];

    // 清空输入框
    self.inputTextView.text = @"";
    self.placeholderLabel.hidden = NO;
    
    // 自动收起更多面板
    if (self.isOpenMorePanel) {
        [self hideMorePanel];
    }
}

- (void)scrollToBottomAnimated:(BOOL)animated {
    if (self.conversationDataRepository.count > 0) {
        // 滚动到最后一个消息（最新消息）
        NSIndexPath *lastIndexPath = [NSIndexPath indexPathForItem:self.conversationDataRepository.count - 1 inSection:0];
        [self.conversationMessageCollectionView scrollToItemAtIndexPath:lastIndexPath
                                                       atScrollPosition:UICollectionViewScrollPositionBottom
                                                               animated:animated];
    }
}

#pragma mark - UICollectionViewDataSource
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.conversationDataRepository.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCNativeMessageModel *model = self.conversationDataRepository[indexPath.row];

    // 根据消息类型选择对应的Cell
    if ([model.content isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *jsonMessage = (SCSingleJsonMessage *)model.content;
        if ([jsonMessage.contentType isEqualToString:kSCRechargeCardMessageContentType]) {
            // 充值卡片
            SCRechargeCardMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCRechargeCardMessageCell" forIndexPath:indexPath];
            cell.scDelegate = self;
            if(model.messageDirection == MessageDirection_SEND){
                // 从字典中获取当前用户头像
                NSString *avatarUrl = kSCCurrentUserAvatarUrl;
                [cell.avatarIV sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
            }else{
                NSDictionary *userDict = [self getUserInfoDict];
                NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
                [cell.avatarIV sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
            }
            [cell setModel:model];
            return cell;
        } else {
            // 礼物消息
            SCGiftMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCGiftMessageCell" forIndexPath:indexPath];
            [cell setModel:model];
            return cell;
        }
    } else if ([model.content isKindOfClass:[RCTextMessage class]] || [model.content isKindOfClass:[SCHyperLinkMessage class]]) {
        // 文本消息
        SCTextMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCTextMessageCell" forIndexPath:indexPath];
        cell.scDelegate = self;
        if(model.messageDirection == MessageDirection_SEND){
            // 从字典中获取当前用户头像
            NSString *avatarUrl = kSCCurrentUserAvatarUrl;
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }else{
            NSDictionary *userDict = [self getUserInfoDict];
            NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }
        [cell setModel:model];
        
        if(kSCAuthTranslaService.isAutoTranslation && ![cell isMy]){
            //开启自动翻译和不是自己的消息。 会自动将为开始翻译的文本自动开始翻译
            SCTranslationStatus translateStatus = [self getTranslationStatus:model.messageId];
            if(translateStatus == SCTranslationStatusNormal){
                ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
                [self scTextMessageCellDidTapTranslation:model];
            }
        }
        
        if([model.content isKindOfClass:[SCHyperLinkMessage class]]){
            SCHyperLinkMessage * linkMessage = ((SCHyperLinkMessage *)model.content);
            if([@"recharge_link" isEqualToString:linkMessage.contentType]){
                //邀请链接
                NSDictionary * extra =  [linkMessage.extra toScJson];
                NSString * invitationId = extra[@"invitationId"];
                [kSCAuthCoinsService getInviteRechargeLinkIsValidWithInvitationId:invitationId success:nil failure:nil];
            }
        }
        
        return cell;
    } else if ([model.content isKindOfClass:[RCHQVoiceMessage class]] || [model.content isKindOfClass:[RCVoiceMessage class]]) {
        // 语音消息
        SCVoiceMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCVoiceMessageCell" forIndexPath:indexPath];
        cell.scDelegate = self;

        // 设置头像
        if(model.messageDirection == MessageDirection_SEND){
            // 从字典中获取当前用户头像
            NSString *avatarUrl = kSCCurrentUserAvatarUrl;
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }else{
            NSDictionary *userDict = [self getUserInfoDict];
            NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }

        [cell setModel:model];
        return cell;
    } else if ([model.content isKindOfClass:[RCImageMessage class]]) {
        // 图片消息
        SCImageMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCImageMessageCell" forIndexPath:indexPath];
        cell.scDelegate = self;
        
        // 设置头像
        if(model.messageDirection == MessageDirection_SEND){
            // 从字典中获取当前用户头像
            NSString *avatarUrl = kSCCurrentUserAvatarUrl;
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }else{
            NSDictionary *userDict = [self getUserInfoDict];
            NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }

        [cell setModel:model];
        return cell;
    } else if ([model.content isKindOfClass:[RCFileMessage class]]) {
        // 文件消息
        SCFileMessageCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCFileMessageCell" forIndexPath:indexPath];
        cell.scDelegate = self;

        // 设置头像
        if(model.messageDirection == MessageDirection_SEND){
            // 从字典中获取当前用户头像
            NSString *avatarUrl = kSCCurrentUserAvatarUrl;
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }else{
            NSDictionary *userDict = [self getUserInfoDict];
            NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
            [cell.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        }

        [cell setModel:model];
        return cell;
    }

    // 默认Cell
    UICollectionViewCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"DefaultCell" forIndexPath:indexPath];
    return cell;
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCNativeMessageModel *model = self.conversationDataRepository[indexPath.row];

    if ([model.content isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *jsonMessage = (SCSingleJsonMessage *)model.content;
        if ([jsonMessage.contentType isEqualToString:kSCRechargeCardMessageContentType]) {
            // 充值卡片
            return [SCRechargeCardMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:40];
        } else {
            // 礼物消息
            return [SCGiftMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:10];
        }
    } else if ([model.content isKindOfClass:[RCTextMessage class]] || [model.content isKindOfClass:[SCHyperLinkMessage class]]) {
        // 文本消息
        return [SCTextMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:40];
    } else if ([model.content isKindOfClass:[RCHQVoiceMessage class]] || [model.content isKindOfClass:[RCVoiceMessage class]]) {
        // 语音消息
        return [SCVoiceMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:20];
    } else if ([model.content isKindOfClass:[RCImageMessage class]]) {
        // 图片消息
        return [SCImageMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:40];
    } else if ([model.content isKindOfClass:[RCFileMessage class]]) {
        // 文件消息
        return [SCFileMessageCell sizeForMessageModel:model withCollectionViewWidth:collectionView.frame.size.width referenceExtraHeight:0];
    }

    // 默认尺寸
    return CGSizeMake(collectionView.frame.size.width, 60);
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    // 滑动列表时关闭表情键盘
    if (self.isOpenEmoji) {
        [self hideEmoji];
    }
}

#pragma mark - 事件处理
- (void)onVoiceButtonTapped {
    

    if (self.isVoiceMode) {
        // 当前是语音模式，切换到文本模式
        [self switchToTextMode];
    } else {
        // 当前是文本模式，切换到语音模式
        [self switchToVoiceMode];
    }
}

- (void)onEmojiButtonTapped {
    [self switchToTextMode];
    
    self.isOpenEmoji = !self.isOpenEmoji;
    
    // 收起更多面板
    if (self.isOpenMorePanel) {
        [self hideMorePanel];
    }
    
    if(self.isOpenEmoji){
        [self showEmoji];
        // 收起键盘
        [self.view endEditing:YES];
    }else{
        [self hideEmoji];
        // 打开键盘
        [self.inputTextView becomeFirstResponder];
    }
}

- (void)onMoreButtonTapped {
    [self switchToTextMode];
    
    self.isOpenMorePanel = !self.isOpenMorePanel;
    
    // 收起键盘和表情键盘
    [self.view endEditing:YES];
    if (self.isOpenEmoji) {
        [self hideEmoji];
    }
    
    if (self.isOpenMorePanel) {
        [self showMorePanel];
    } else {
        [self hideMorePanel];
    }
}

- (void)onSendButtonTapped {
    NSString *messageText = [self.inputTextView.text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    if (messageText.length > 0) {
        // 清空输入框
        self.inputTextView.text = @"";
        self.placeholderLabel.hidden = NO;

        // 重置输入框高度
        [self resetInputTextViewHeight];

        // 更新按钮状态
        [self updateButtonVisibilityAnimated:YES hasText:NO];

        // 发送消息
        [self sendTextMessage:messageText];
    }
}

- (void)updateButtonVisibilityAnimated:(BOOL)animated hasText:(BOOL)hasText {
    if (animated) {
        [UIView animateWithDuration:0.2 animations:^{
            if (hasText) {
                // 有文本时：隐藏moreButton，显示sendButton
                self.moreButton.alpha = 0.0;
                self.sendButton.alpha = 1.0;
            } else {
                // 无文本时：显示moreButton，隐藏sendButton
                self.moreButton.alpha = 1.0;
                self.sendButton.alpha = 0.0;
            }
        } completion:^(BOOL finished) {
            self.moreButton.hidden = hasText;
            self.sendButton.hidden = !hasText;
        }];
    } else {
        if (hasText) {
            self.moreButton.hidden = YES;
            self.moreButton.alpha = 0.0;
            self.sendButton.hidden = NO;
            self.sendButton.alpha = 1.0;
        } else {
            self.moreButton.hidden = NO;
            self.moreButton.alpha = 1.0;
            self.sendButton.hidden = YES;
            self.sendButton.alpha = 0.0;
        }
    }
}

#pragma mark - 表情键盘显示/隐藏

//显示Emoji
-(void)showEmoji{
    self.isOpenEmoji = YES;
    self.emojiBoardContainer.alpha = 0;
    [self.emojiBoardContainer setHidden:NO];

    // 切换表情按钮图片为键盘图片
    [self.emojiButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_keyboard"] forState:UIControlStateNormal];

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.emojiBoardContainer.alpha = 1;
        [self.emojiBoardContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(227.5f + kSCSafeAreaBottomHeight);
        }];

        // 表情键盘显示时，chatSessionInputBarControl的底部与表情键盘容器顶部对齐
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.emojiBoardContainer.mas_top).offset(kSCSafeAreaBottomHeight);
        }];

        [self.view layoutIfNeeded];
    }];
}

//隐藏Emoji键盘
-(void)hideEmoji{
    self.isOpenEmoji = NO;

    // 恢复表情按钮图片为表情图片
    [self.emojiButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_emoji"] forState:UIControlStateNormal];

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.emojiBoardContainer.alpha = 0;
        [self.emojiBoardContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];

        // 表情键盘隐藏时，恢复chatSessionInputBarControl到view底部
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.view);
        }];

        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self.emojiBoardContainer setHidden:YES];
    }];
}

#pragma mark - 更多面板显示/隐藏

//显示更多面板
-(void)showMorePanel{
    self.isOpenMorePanel = YES;
    self.morePanelContainer.alpha = 0;
    [self.morePanelContainer setHidden:NO];

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.morePanelContainer.alpha = 1;
        [self.morePanelContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(227.5f + kSCSafeAreaBottomHeight);
        }];

        // 更多面板显示时，chatSessionInputBarControl的底部与更多面板容器顶部对齐
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.morePanelContainer.mas_top).offset(kSCSafeAreaBottomHeight);
        }];

        [self.view layoutIfNeeded];
    }];
}

//隐藏更多面板
-(void)hideMorePanel{
    self.isOpenMorePanel = NO;

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.morePanelContainer.alpha = 0;
        [self.morePanelContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];

        // 更多面板隐藏时，恢复chatSessionInputBarControl到view底部
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.view);
        }];

        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self.morePanelContainer setHidden:YES];
    }];
}

#pragma mark - 输入栏处理
- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    // 更新占位符显示状态
    dispatch_async(dispatch_get_main_queue(), ^{
        self.placeholderLabel.hidden = (textView.text.length > 0 || text.length > 0);
    });

    return YES;
}

- (void)textViewDidChange:(UITextView *)textView {
    // 更新占位符显示状态
    self.placeholderLabel.hidden = (textView.text.length > 0);

    // 动态调整输入框高度
    [self adjustInputTextViewHeight];

    // 根据文本内容切换按钮显示状态
    BOOL hasText = textView.text.length > 0;
    [self updateButtonVisibilityAnimated:YES hasText:hasText];
}

- (void)adjustInputTextViewHeight {
    // 计算文本所需的高度
    CGSize textSize = [self.inputTextView sizeThatFits:CGSizeMake(self.inputTextView.frame.size.width, CGFLOAT_MAX)];
    CGFloat newHeight = textSize.height;

    // 限制最小和最大高度
    CGFloat minHeight = 43.0; // 最小高度
    newHeight = MAX(minHeight, MIN(newHeight, self.maxInputTextViewHeight));

    // 如果高度发生变化，更新约束
    if (fabs(newHeight - self.currentInputTextViewHeight) > 1.0) {
        self.currentInputTextViewHeight = newHeight;

        [self.inputTextViewHeightConstraint uninstall];
        [self.inputTextView mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputTextViewHeightConstraint = make.height.mas_equalTo(newHeight);
        }];

        // 启用或禁用滚动
        self.inputTextView.scrollEnabled = (newHeight >= self.maxInputTextViewHeight);

        // 动画更新布局
        [UIView animateWithDuration:0.2 animations:^{
            [self.view layoutIfNeeded];
        }];
    }
}

- (void)resetInputTextViewHeight {
    // 重置为最小高度
    CGFloat minHeight = 43.0;
    self.currentInputTextViewHeight = minHeight;

    [self.inputTextViewHeightConstraint uninstall];
    [self.inputTextView mas_updateConstraints:^(MASConstraintMaker *make) {
        self.inputTextViewHeightConstraint = make.height.mas_equalTo(minHeight);
    }];

    // 禁用滚动
    self.inputTextView.scrollEnabled = NO;

    // 动画更新布局
    [UIView animateWithDuration:0.2 animations:^{
        [self.view layoutIfNeeded];
    }];
}

#pragma mark - HandleOnMessageReceived
- (void)handleOnReceived:(RCMessage *)message {
    // 只处理当前会话的消息
    if (![message.targetId isEqualToString:self.targetId]) {
        return;
    }
    
    NSString *objectName = message.objectName;
    NSString *content = @"";
    if ([objectName isEqualToString:@"RC:TxtMsg"]) {
        content = @"Text";
    } else if ([objectName isEqualToString:@"RC:ImgMsg"]) {
        content = @"image";
    } else if ([objectName isEqualToString:@"RC:HQVCMsg"]) {
        content = @"voice";
    } else if([objectName isEqualToString:@"RC:FileMsg"]) {
        content = @"file";
    } else if([objectName isEqualToString:@"LC:HyperLinkMsg"]){
        content = @"Link";
    } else if ([objectName isEqualToString:@"LC:SingleJsonMsg"]) {
        if ([message.content isKindOfClass:[SCSingleJsonMessage class]]) {
            SCSingleJsonMessage *messageContent = (SCSingleJsonMessage *)message.content;
            if ([messageContent.contentType isEqualToString:@"tpp"]) {
                content = @"Recharge Card";
            }
        }
    }
    if (kSCIsStrEmpty(content)) {
        return;
    }
    
    dispatch_async(dispatch_get_main_queue(), ^{
        // 转换为SCNativeMessageModel并添加到数据源
        SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
        [self.conversationDataRepository addObject:model];

        // 重新计算消息时间显示逻辑
        [SCNativeMessageModel calculateDisplayTimeForMessages:self.conversationDataRepository];

        // 刷新UI并滚动到底部
        [self.conversationMessageCollectionView reloadData];
        [self scrollToBottomAnimated:YES];
    });
    
    [self clearMessagesUnread];
}

- (void)clearMessagesUnread {
    // 清除当前会话的未读消息数
    [[RCCoreClient sharedCoreClient] clearMessagesUnreadStatus:ConversationType_PRIVATE
                                                      targetId:self.targetId
                                                    completion:^(BOOL ret) {
    }];
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent; // 返回白色状态栏样式
}

- (NSMutableDictionary<NSString *,NSNumber *> *)translationStatusDic {
    if (!_translationStatusDic) {
        _translationStatusDic = [NSMutableDictionary new];
    }
    return _translationStatusDic;
}



- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self requestUserInfo];
    [self.navigationController setNavigationBarHidden:YES animated:animated];
    if(_onlineStatusSub){
        [_onlineStatusSub start];
        //进入页面立刻刷新一次
        [kSCAuthOnlineStatesService refresh];
    }
    [self setInputViewColor];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [self setInputViewColor];
    [self clearMessagesUnread];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [self updateSourceUserInfo];
    if(_onlineStatusSub){
        [_onlineStatusSub pause];
    }
    //刷新未读消息数量通知
    kScAuthMar.imService.unreadCountChangeObs.value = @(0);
}

#pragma mark - 点击空白处隐藏键盘

- (void)setupTapGestureToHideKeyboard {
    // 创建点击手势识别器
    self.tapGestureToHideKeyboard = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapToHideKeyboard:)];
    self.tapGestureToHideKeyboard.delegate = self;
    self.tapGestureToHideKeyboard.cancelsTouchesInView = NO; // 不取消其他控件的触摸事件

    // 添加到主视图
    [self.view addGestureRecognizer:self.tapGestureToHideKeyboard];
}

- (void)handleTapToHideKeyboard:(UITapGestureRecognizer *)gesture {
    // 隐藏系统键盘
    [self.view endEditing:YES];

    // 隐藏表情键盘
    if (self.isOpenEmoji) {
        [self hideEmoji];
    }

    // 隐藏更多面板
    if (self.isOpenMorePanel) {
        [self hideMorePanel];
    }
}

#pragma mark - 键盘处理

- (void)setupKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    // 如果表情键盘正在显示，先隐藏它
    if(self.isOpenEmoji){
        [self hideEmoji];
    }
    
    // 如果更多面板正在显示，先隐藏它
    if(self.isOpenMorePanel){
        [self hideMorePanel];
    }

    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];

    // 计算键盘高度（考虑安全区域）
    CGFloat keyboardHeight = keyboardFrame.size.height;
    if (@available(iOS 11.0, *)) {
        keyboardHeight -= self.view.safeAreaInsets.bottom;
    }

    [UIView animateWithDuration:duration delay:0 options:(UIViewAnimationOptions)curve animations:^{
        // 系统键盘显示时，chatSessionInputBarControl直接与键盘底部对齐
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.view).offset(-keyboardHeight);
        }];

        [self.view layoutIfNeeded];
    } completion:nil];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    if (self.isOpenEmoji || self.isOpenMorePanel) {
        return;
    }
    
    NSDictionary *userInfo = notification.userInfo;
    NSTimeInterval duration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve curve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];

    [UIView animateWithDuration:duration delay:0 options:(UIViewAnimationOptions)curve animations:^{
        // 系统键盘隐藏时，恢复chatSessionInputBarControl到view底部
        [self.inputBarBottomConstraint uninstall];
        [self.chatSessionInputBarControl mas_updateConstraints:^(MASConstraintMaker *make) {
            self.inputBarBottomConstraint = make.bottom.equalTo(self.view);
        }];

        [self.view layoutIfNeeded];
    } completion:nil];
}

- (void)dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [_disposeBag dispose];
    _disposeBag = nil;
}

- (void)setInputViewColor{
    // 设置输入框颜色 - 现在使用原生inputTextView
    self.inputTextView.backgroundColor = [UIColor colorWithHexString:@"#1D0202"];
    self.inputTextView.textColor = UIColor.scWhite;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _disposeBag = [[SCDisposeBag alloc]init];

    // 设置基础UI
    self.view.backgroundColor = [UIColor scChatBgColor];

    // 创建原生组件
    [self createMessageCollectionView];
    [self createInputBarControl];
    // 初始化UI
    [self _initUI];
    [self setupViewLayout];

    // 加载历史消息
    [self loadHistoryMessages];
    
    //收到新消息
    kWeakSelf(self);
    [kScAuthMar.imService.receiveMessageObs afterSubscribe:^(RCMessage * _Nullable value) {
        // 收到新消息时重新加载所有会话数据
        [weakself handleOnReceived:value];
    } error:nil disposeBag:self.disposeBag];

    _onlineStatusSub = [[SCOnlineStatusSubscribe alloc] init];
    [_onlineStatusSub.changeObs subscribe:^(NSArray<NSString *> * _Nullable value) {
        [weakself onChangeOnlineStatus];
    } error:nil disposeBag:self.disposeBag];
    [kSCAuthOnlineStatesService add:self.onlineStatusSub dispose:self.disposeBag];
    //监听在线状态
    [self.onlineStatusSub addWithUserID:self.targetId];

    // 注册键盘通知
    [self setupKeyboardNotifications];

    // 设置点击空白处隐藏键盘的手势
    [self setupTapGestureToHideKeyboard];

    // 设置录音管理器代理
    [SCVoiceRecorderManager sharedManager].delegate = self;
}
-(void) onChangeOnlineStatus{
    NSDictionary *onlineStatusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:self.targetId];
    SCAnchorStatus status = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:onlineStatusDict forKey:@"status" defaultValue:AnchorStatusOffline];
//    self.userBaseInfoV.onLineView.dotView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:status];
    
    self.callAnimationView.hidden = (status != AnchorStatusOnline);
}

// 页面返回时，更新来源数据
- (void)updateSourceUserInfo {
    if (self.tmpBaseDict && self.tagerBaseInfoDict) {
        // 更新临时字典中的关注和拉黑状态
        NSMutableDictionary *mutableTmpDict = [self.tmpBaseDict mutableCopy];
        mutableTmpDict[@"isFriend"] = @([SCDictionaryHelper boolFromDictionary:self.tagerBaseInfoDict forKey:@"isFriend" defaultValue:NO]);
        mutableTmpDict[@"isBlock"] = @([SCDictionaryHelper boolFromDictionary:self.tagerBaseInfoDict forKey:@"isBlock" defaultValue:NO]);
        self.tmpBaseDict = [mutableTmpDict copy];
    }
}

-(void) requestUserInfo{
    kWeakSelf(self);
    // 使用字典版本的API调用
    [SCAPIServiceManager requestUserInfoWithUserId:self.targetId cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(NSDictionary * _Nonnull userDict) {
        // 直接使用字典数据
        weakself.tagerBaseInfoDict = userDict;
        // 移除RCIM userInfoDataSource设置，因为不再使用RongIMKit
        [weakself updateUI];

    } failure:nil];
}

-(void) updateUI{
    NSDictionary *userDict = [self getUserInfoDict];
    if(userDict == nil){
        return;
    }

//    [self.userBaseInfoV.avatarImageView sc_setImageWithURL:userModel.avatarUrl  placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
//    self.userBaseInfoV.nameLabel.text = userModel.nickname;
    self.userNameLb.text = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    kWeakSelf(self)
    [SCThrottle throttleWithTag:@"conversationInfo" onAfter:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakself.conversationMessageCollectionView reloadData];
        });
    }];

}

// 删除重复的inputTextView方法，保留前面实现的版本

// 字典版本的用户信息获取
-(NSDictionary *) getUserInfoDict{
    // 优先使用详细信息字典
    if (self.tagerBaseInfoDict) {
        return self.tagerBaseInfoDict;
    }

    // 其次使用临时数据字典
    if (self.tmpBaseDict) {
        return self.tmpBaseDict;
    }

    return nil;
}

- (SCTranslationStatus)getTranslationStatus:(long)messageId {
    NSNumber *status = [self.translationStatusDic objectForKey:@(messageId).stringValue.md5String];
    if (status) {
        return status.integerValue;
    }
    return SCTranslationStatusNormal;
}

-(void) _initUI{
    //导航栏初始化
    SCNavigationBar * navigationBar = [[SCNavigationBar alloc] init];
    navigationBar.backgroundColor = [UIColor scChatBgColor];
    [navigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    [navigationBar.backButton addTarget:self action:@selector(onBack) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:navigationBar];
    self.navigationBar = navigationBar;
    [self.navigationBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.view).offset(0);
        make.height.equalTo(@(kSCNavBarFullHeight));
    }];
    
//    SCUserBaseInfoView * userBaseInfoV = [[SCUserBaseInfoView alloc] init];
    UILabel *userNameLabel = [[UILabel alloc]init];
    userNameLabel.font = kScUIFontSemibold(18.0f);
    userNameLabel.textColor = UIColor.scWhite;
    
    [self.navigationBar addSubview:userNameLabel];
    self.userNameLb = userNameLabel;
    
    [userNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(44);
        make.leading.equalTo(self.navigationBar.backButton.mas_trailing).offset(0);
        make.centerY.equalTo(self.navigationBar.backButton);
        make.trailing.equalTo(self.navigationBar).offset(-132);
    }];

    if([kScAuthMar.imService isUserServiceAccountWithId:self.targetId] || [kScAuthMar.imService isSystemWithId:self.targetId] ){
        ///客服消息。 需要显示输入框，但是无法举报和拉黑的操作
//        self.userBaseInfoV.onLineView.hidden = YES;
    }else{
        //其他用户
        UIStackView *btnStackView = [[UIStackView alloc]init];
        btnStackView.spacing = 5;
        [navigationBar addSubview:btnStackView];
        [btnStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.navigationBar).offset(0);
            make.centerY.equalTo(self.navigationBar.backButton);
        }];
        
        UIButton *pinBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [pinBtn addTarget:self action:@selector(onPin) forControlEvents:UIControlEventTouchUpInside];
        [pinBtn setImage:[SCResourceManager loadImageWithName:@"ic_pin"] forState:UIControlStateNormal];
        [pinBtn setImage:[SCResourceManager loadImageWithName:@"ic_unpined"] forState:UIControlStateSelected];
        [btnStackView addArrangedSubview:pinBtn];
        self.pinBtn = pinBtn;
        
        UIButton * moreBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_white_more"] target:self action:@selector(onMore)];
        [btnStackView addArrangedSubview:moreBtn];
        self.moreBtn = moreBtn;
        kWeakSelf(self)
        [[RCCoreClient sharedCoreClient] getConversation:ConversationType_PRIVATE targetId:self.targetId completion:^(RCConversation * _Nullable conversation) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakself.pinBtn setSelected:conversation.isTop];
            });
        }];
        
        [self.moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(44);
        }];
        
        [self.pinBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.height.mas_equalTo(44);
        }];
        
        //点击事件
//        userBaseInfoV.avatarImageView.userInteractionEnabled = YES;
//        kSCAddTapGesture(userBaseInfoV.avatarImageView, self, onTapAvatar);
        
        UIStackView *sideStackView = [[UIStackView alloc]init];
        sideStackView.axis = UILayoutConstraintAxisVertical;
        sideStackView.spacing = 20.0f;
        [self.view addSubview:sideStackView];
        [sideStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.view).offset(-15);
            make.bottom.equalTo(self.chatSessionInputBarControl.mas_top).inset(20.0f);
        }];
        
        {
            // 创建礼物动画视图
            NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/gift_animation.json"];
            NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
            
            NSError *error = nil;
            NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
            if (jsonData) {
                self.giftAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
                self.giftAnimationView.loopAnimationCount = -1;
                kSCAddTapGesture(self.giftAnimationView, self, onGift)
                [sideStackView addArrangedSubview:self.giftAnimationView];
                [self.giftAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(CGSizeMake(62, 62));
                }];
                [self.giftAnimationView play];
            }
        }
        {
            // 创建拨打动画视图
            NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/message_call_animation.json"];
            NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
            
            NSError *error = nil;
            NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
            if (jsonData) {
                self.callAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
                self.callAnimationView.loopAnimationCount = -1;
                kSCAddTapGesture(self.callAnimationView, self, onCall)
                [sideStackView addArrangedSubview:self.callAnimationView];
                [self.callAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.size.mas_equalTo(CGSizeMake(62, 62));
                }];
                [self.callAnimationView play];
            }
        }
        
    }
    
    // 移除setDisplayUserNameInCell调用，因为不再继承RCConversationViewController

    // 设置基础UI样式
    self.view.backgroundColor = [UIColor scChatBgColor];
    self.conversationMessageCollectionView.backgroundColor = [UIColor scChatBgColor];
    self.chatSessionInputBarControl.backgroundColor = [UIColor colorWithHexString:@"#380D0D"];

    // 移除对不存在属性的引用，这些功能将在后续阶段重新实现
    // 如：pluginBoardView, recordButton, emojiButton等

    // 注册充值卡Cell（如果需要）
    [self.conversationMessageCollectionView registerClass:[SCRechargeCardMessageCell class] forCellWithReuseIdentifier:kRechargeCardCellIdentifier];

    // 移除enableNewComingMessageIcon设置，因为不再继承父类
    
}

// 删除重复的registerCustomCellsAndMessages方法
static NSString * const kRechargeCardCellIdentifier = @"kRechargeCardCellIdentifier";
- (void)viewWillLayoutSubviews{
    [super viewWillLayoutSubviews];

    // 移除对不存在属性的引用
    // 这些UI配置将在后续阶段重新实现
    [self setInputViewColor];
}

- (void)didTapCellPortrait:(NSString *)userId{
    if(![userId isEqualToString:self.targetId]){
        return;
    }
    if([kScAuthMar.imService isSystemWithId:userId] || [kScAuthMar.imService isUserServiceAccountWithId:userId] ){
        ///系统消息
        return;
    }
    [SCAnchorInfoViewController openWith:userId from:self];
}

#pragma mark - Cell头像点击回调

- (void)scTextMessageCellDidTapAvatar:(SCNativeMessageModel *)model {
    NSString *userId = nil;
    if (model.messageDirection == MessageDirection_SEND) {
        NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
        userId = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    } else {
        userId = self.targetId;
    }
    [self didTapCellPortrait:userId];
}

- (void)scVoiceMessageCellDidTapAvatar:(SCNativeMessageModel *)model {
    NSString *userId = nil;
    if (model.messageDirection == MessageDirection_SEND) {
        NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
        userId = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    } else {
        userId = self.targetId;
    }
    [self didTapCellPortrait:userId];
}

- (void)scRechargeCardMessageCellDidTapAvatar:(SCNativeMessageModel *)model {
    NSString *userId = nil;
    if (model.messageDirection == MessageDirection_SEND) {
        NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
        userId = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    } else {
        userId = self.targetId;
    }
    [self didTapCellPortrait:userId];
}

//- (void)pluginBoardView:(RCPluginBoardView *)pluginBoardView clickedItemWithTag:(NSInteger)tag {
//    // 暂时移除插件板功能，将在后续阶段重新实现
//    // 这个方法将在实现完整的输入栏功能时重新添加
//}

#pragma mark - SCRechargeCardMessageCellDelegate

- (void)sCRechargeCardMessageCell:(SCRechargeCardMessageCell *)cell didTapRechargeCard:(SCRechargeCardMessageContentModel *)model{
    // 从配置字典中获取第三方支付打开类型
    NSInteger tppOpenType = [SCDictionaryHelper integerFromDictionary:kScAuthMar.appConfig forKey:@"tppOpenType" defaultValue:0];
    if(tppOpenType == 0){
        //0 内部
        [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:model.tppOutsideTitle url:model.tppUrl];
    }else{
        //1 外部
        NSURL *url = [NSURL URLWithString:model.tppUrl];
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
    }
}


#pragma mark - more

-(void)onMore{
    kWeakSelf(self)
    NSDictionary *userDict = [self getUserInfoDict];
    NSMutableDictionary *mutableUserDict = [userDict mutableCopy];
    [SCAnchorActionSheetViewController showMoreActionSheetWithFromVC:self userDict:mutableUserDict actionBlock:^(NSInteger index) {
        weakself.tagerBaseInfoDict = [mutableUserDict copy];
        weakself.tmpBaseDict = [mutableUserDict copy];
        [weakself sc_blank_empty];
    }];
}
- (void)sc_blank_empty{}
-(void)onCall{
    NSDictionary *userDict = [self getUserInfoDict];
    if(userDict != nil){
        // 创建包含状态信息的用户字典
        NSDictionary *onlineStatusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:self.targetId];
        SCAnchorStatus status = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:onlineStatusDict forKey:@"status" defaultValue:AnchorStatusOffline];
        NSMutableDictionary *mutableUserDict = [userDict mutableCopy];
        mutableUserDict[@"status"] = [SCDictionaryHelper stringFromAnchorStatus:status];

        [kSCAuthCallService startCallingWithUserDict:[mutableUserDict copy] callSource:SCCallSourceConversation];
    }
}

- (void)onPin {
    kWeakSelf(self)
    [[RCCoreClient sharedCoreClient] getConversation:ConversationType_PRIVATE targetId:self.targetId completion:^(RCConversation * _Nullable conversation) {
        [[RCCoreClient sharedCoreClient] setConversationToTop:ConversationType_PRIVATE targetId:weakself.targetId isTop: !conversation.isTop completion:^(BOOL ret) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakself conversationToTopResult:conversation];
            });
        }];
    }];
}
- (void)conversationToTopResult:(RCConversation *)conversation{
    [self.pinBtn setSelected: !conversation.isTop];
    [self.view hiddenLoading];
    [self.view toast: conversation.isTop ? @"Cancel pinned successfully".translateString : @"Pinned successfully".translateString];
}
-(void) onTapAvatar{
    [self didTapCellPortrait:self.targetId];
}
-(void)onBack{
    [self.navigationController popViewControllerAnimated:YES];
}
-(void)onGift{
    [self.view endEditing:YES];
    kWeakSelf(self);
    SCGiftPopupViewController * vc = [SCGiftPopupViewController showWithFromVC:self userId:self.targetId channelName:nil isMulti:NO isFromVideoCall:NO];
    [vc.giftCompleteObs subscribe:^(SCGiftSendNumModel * _Nonnull gift) {
        if(gift.giftModel != nil){
            [weakself giftCompleteObsResult:gift];
        }
    } error:nil disposeBag:self.disposeBag];
}

- (void)giftCompleteObsResult:(SCGiftSendNumModel *)gift{
    kWeakSelf(self)

    // 创建礼物消息
    SCSingleJsonMessage *giftMessage = [SCSingleJsonMessage messageCreateWithContent:@"You have sent" contentType:@"gift"];

    // 从字典中获取当前用户昵称
    NSString *fromUserName = kSCCurrentUserNickname ?: @"";

    NSDictionary *param = @{
        @"giftCode": [SCDictionaryHelper stringFromDictionary:gift.giftModel forKey:@"code" defaultValue:@""],
        @"fromUserName": fromUserName,
        @"toUserName": [SCDictionaryHelper stringFromDictionary:[self getUserInfoDict] forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""]
    };
    //转为JSON字符串
    NSString *giftStr = [SCDataConverter jsonStringFromDictionary:param];
    if (giftStr) {
        giftMessage.extra = @"";
        giftMessage.content = giftStr;
    }

    // 直接使用RCCoreClient发送消息，类似sendTextMessage的实现
    [[RCCoreClient sharedCoreClient] sendMessage:ConversationType_PRIVATE
                                        targetId:self.targetId
                                         content:giftMessage
                                     pushContent:nil
                                        pushData:nil
                                        attached:^(RCMessage * _Nullable message) {
        // 在attached回调中获取message对象并添加到会话列表
        if (message) {
            dispatch_async(dispatch_get_main_queue(), ^{
                SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
                [weakself.conversationDataRepository addObject:model];

                // 重新计算消息时间显示逻辑
                [SCNativeMessageModel calculateDisplayTimeForMessages:weakself.conversationDataRepository];

                [weakself.conversationMessageCollectionView reloadData];
                [weakself scrollToBottomAnimated:YES];
            });
        }
    } success:^(long messageId) {
        // 发送成功
        
    } error:^(RCErrorCode nErrorCode, long messageId) {
        // 发送失败
        
        [weakself sc_blank_empty];
    }];
}

///更新列表，并滑动到最底部
-(void) updateMessageListUIWithModel:(SCNativeMessageModel * _Nullable)model{
    
    kWeakSelf(self);
    [SCThrottle throttleWithTag:@"conversationInfo" onAfter:^{
        dispatch_async(dispatch_get_main_queue(), ^{
            model.cellSize = CGSizeZero;
            [weakself.conversationMessageCollectionView reloadData];
            if (self.isBottomRefreshed) {
                return;
            }
            self.isBottomRefreshed = YES;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                SCNativeMessageModel *model2 = [weakself.conversationDataRepository lastObject];
                if (model2.messageId == model.messageId ) {
                    [weakself scrollToBottomAnimated:YES];
                    self.isBottomRefreshed = NO;
                    
                }
            });
            
        });
    }];
    
}

#pragma mark - SCTextMessageCellDelegate
- (void)scTextMessageCellDidTapTranslation:(SCNativeMessageModel *)model{
    NSString *messageKey = @(model.messageId).stringValue.md5String;
    [self.translationStatusDic setObject:@(SCTranslationStatusTranslating) forKey:messageKey];

    NSString *text = @"";
    if ([model.content isKindOfClass:[SCHyperLinkMessage class]]) {
        SCHyperLinkMessage * linkMessage = ((SCHyperLinkMessage *)model.content);
        text = linkMessage.content ?: @"";
    }else {
        text =((RCTextMessage *)model.content).content;
    }
    
    
    kWeakSelf(self);
    __block BOOL isSuccess = false;
    [kSCAuthTranslaService translateText:text completion:^(NSString * _Nonnull t, SCXErrorModel * _Nonnull error) {
        if (!error) {
            isSuccess = true;
            [weakself.translationStatusDic setObject:@(SCTranslationStatusSuccess) forKey:messageKey];
        } else {
            [weakself.translationStatusDic setObject:@(SCTranslationStatusFail) forKey:messageKey];
        }
        
        [weakself updateMessageListUIWithModel:model];
    }];
    
    dispatch_async(dispatch_get_main_queue(), ^{
        if(!isSuccess){
            ///刷新页面
            [weakself updateMessageListUIWithModel:model];
        }
    });
}

- (void)scTextMessageCellDidTapInvitationId:(NSString *)invitationId{
    //点击邀请充值id
    NSInteger status = [[kSCAuthCoinsService.inviteRechargeLinkCache objectForKey:invitationId] integerValue];
    if(status == 0){
        //没有缓存记录
        kWeakSelf(self);
        [weakself.view showLoading];
        [kSCAuthCoinsService remoteInviteRechargeLinkIsValidWithInvitationId:invitationId success:^(BOOL isValid) {
            [weakself.view hiddenLoading];
            if(isValid){
                [weakself.view endEditing:YES];
                [SCInviteRechargeCoinsPopupViewController showFromVC:weakself inviteId:invitationId];
            }else{
                //失效
                [weakself.view toast:@"Link has expired".translateString];
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself.view hiddenLoading];
            //网络请求失败请稍后再试 英文
            [weakself.view toast:@"Network request failed, please try again later"];
            
        }];
    }else{
        if(status == 2){
            //失效
            [self.view toast:@"Link has expired".translateString];
        }else{
            [self.view endEditing:YES];
            //直接转跳
            [SCInviteRechargeCoinsPopupViewController showFromVC:self inviteId:invitationId];
        }
        
    }
//    [SCInviteRechargeCoinsPopupViewController sho]
}

- (SCTranslationStatus)scTextMessageCell:(SCTextMessageCell *)cell getTranslationStatusWithMessageId:(long)messageId {
    return [self getTranslationStatus:messageId];
}

#pragma mark - RCIMUserInfoDataSource
- (void)getUserInfoWithUserId:(NSString *)userId completion:(void (^)(RCUserInfo *userInfo))completion{
//    if([kScAuthMar.imService isSystemWithId:userId] || [kScAuthMar.imService isUserServiceAccountWithId:userId] ){
//        ///系统消息
//        completion(nil);
//        return;
//    }
    NSDictionary *userDict;
    if([userId isEqualToString:self.targetId]){
        userDict = [self getUserInfoDict];
    }else{
        // 从字典中获取当前用户信息
        userDict = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    }

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    RCUserInfo * userInfo = [[RCUserInfo alloc] initWithUserId:userId name:nickname portrait:avatarUrl];
    completion(userInfo);
}

#pragma mark - SCRoute

//+(void) showWithFromVC:(UIViewController *)fromVC tager:(NSString *) targetId tmpBaseModel:(SCUserInfoBaseModel *) tmpBaseModel{
//    // 已废弃：请使用showWithFromVC:tager:userDict:方法
//    [self showWithFromVC:fromVC tager:targetId userDict:nil];
//}

+(void) showWithFromVC:(UIViewController *)fromVC tager:(NSString *) targetId userDict:(NSDictionary * _Nullable) userDict{
    SCConversationInfoViewController * vc = [[SCConversationInfoViewController alloc] initWithConversationType:ConversationType_PRIVATE targetId:targetId];
    vc.tmpBaseDict = userDict;
    [fromVC.navigationController pushViewController:vc animated:YES];
}

#pragma mark - AGEmojiKeyboardViewDelegate

- (void)emojiKeyBoardView:(AGEmojiKeyboardView *)emojiKeyBoardView didUseEmoji:(NSString *)emoji {
    // 将表情添加到输入框
    self.inputTextView.text = [self.inputTextView.text stringByAppendingString:emoji];

    // 隐藏占位符
    self.placeholderLabel.hidden = self.inputTextView.text.length > 0;

    // 触发文本变化处理
    [self textViewDidChange:self.inputTextView];
}

- (void)emojiKeyBoardViewDidPressBackSpace:(AGEmojiKeyboardView *)emojiKeyBoardView {
    // 删除最后一个字符（可能是emoji）
    if(self.inputTextView.text.length > 0){
        self.inputTextView.text = [self.inputTextView.text removedLastString];
    }

    // 更新占位符显示状态
    self.placeholderLabel.hidden = self.inputTextView.text.length > 0;

    // 触发文本变化处理
    [self textViewDidChange:self.inputTextView];
}

#pragma mark - AGEmojiKeyboardViewDataSource

- (UIImage *)emojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView imageForSelectedCategory:(AGEmojiKeyboardViewCategoryImage)category {
    NSString *imageName = @"";
    switch (category) {
        case AGEmojiKeyboardViewCategoryImageCar:
            imageName = @"car_s";
            break;
        case AGEmojiKeyboardViewCategoryImageBell:
            imageName = @"bell_s";
            break;;
        case  AGEmojiKeyboardViewCategoryImageFace:
            imageName = @"face_s";
            break;
        case AGEmojiKeyboardViewCategoryImageFlower:
            imageName = @"flower_s";
            break;
        case AGEmojiKeyboardViewCategoryImageRecent:
            imageName = @"recent_s";
            break;
        case AGEmojiKeyboardViewCategoryImageCharacters:
            imageName = @"characters_s";
        default:
            break;
    }
    UIImage *img = [SCResourceManager loadImageWithName:imageName];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (UIImage *)emojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView imageForNonSelectedCategory:(AGEmojiKeyboardViewCategoryImage)category {
    NSString *imageName = @"";
    switch (category) {
        case AGEmojiKeyboardViewCategoryImageCar:
            imageName = @"car_n";
            break;
        case AGEmojiKeyboardViewCategoryImageBell:
            imageName = @"bell_n";
            break;;
        case  AGEmojiKeyboardViewCategoryImageFace:
            imageName = @"face_n";
            break;
        case AGEmojiKeyboardViewCategoryImageFlower:
            imageName = @"flower_n";
            break;
        case AGEmojiKeyboardViewCategoryImageRecent:
            imageName = @"recent_n";
            break;
        case AGEmojiKeyboardViewCategoryImageCharacters:
            imageName = @"characters_n";
        default:
            break;
    }
    UIImage *img = [SCResourceManager loadImageWithName:imageName];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (UIImage *)backSpaceButtonImageForEmojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView {
    UIImage *img = [SCResourceManager loadImageWithName:@"backspace_n"];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (AGEmojiKeyboardViewCategoryImage)defaultCategoryForEmojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView {
    return  AGEmojiKeyboardViewCategoryImageFace;
}

#pragma mark - SCMorePanelViewDelegate

- (void)morePanelView:(UIView *)morePanelView didSelectItemAtIndex:(NSInteger)index withItem:(SCMorePanelItem *)item {
    
    
    switch (item.tag) {
        case 1001: // Photo
            [self presentImagePicker];
            break;
        default:
            break;
    }
    
    // 选择功能后自动收起面板
    if (self.isOpenMorePanel) {
        [self hideMorePanel];
    }
}

#pragma mark - Image Picker

- (void)presentImagePicker {
    UIImagePickerController *picker = [[UIImagePickerController alloc] init];
    picker.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
    picker.delegate = self;
    picker.allowsEditing = NO;
    [self presentViewController:picker animated:YES completion:nil];
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey, id> *)info {
    [picker dismissViewControllerAnimated:YES completion:nil];
    
    UIImage *selectedImage = info[UIImagePickerControllerEditedImage] ?: info[UIImagePickerControllerOriginalImage];
    
    if (selectedImage) {
        RCImageMessage *mediaMessageContent = [RCImageMessage messageWithImage:selectedImage];

        RCMessage *message = [[RCMessage alloc] initWithType:ConversationType_PRIVATE
                                                    targetId:self.targetId
                                                   direction:MessageDirection_SEND
                                                     content:mediaMessageContent];
        
        // 创建消息模型并设置为发送中状态
        SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
        model.sentStatus = SCMessageSentStatusSending;
        
        model.messageUId = [NSUUID UUID].UUIDString;
        
        // 添加到界面并刷新
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.conversationDataRepository addObject:model];
            [self.conversationMessageCollectionView reloadData];
            [self scrollToBottomAnimated:YES];
        });
        
        kWeakSelf(self);
        [[RCCoreClient sharedCoreClient] sendMediaMessage:message pushContent:nil pushData:nil progress:^(int progress, RCMessage * _Nonnull progressMessage) {
            // 发送进度回调 - 暂时不处理
        } successBlock:^(RCMessage * _Nonnull successMessage) {
            
            
            dispatch_async(dispatch_get_main_queue(), ^{
                // 在消息列表中查找并更新消息状态
                SCNativeMessageModel *storedModel = [weakself findMessageWithUID:model.messageUId];
                if (storedModel) {
                    storedModel.sentStatus = SCMessageSentStatusSent;
                    storedModel.messageId = successMessage.messageId;
                    [storedModel updateFromMessage:successMessage];
                    
                    // 刷新界面
                    [weakself.conversationMessageCollectionView reloadData];
                }
                
                // 发送成功后自动收起更多面板
                if (weakself.isOpenMorePanel) {
                    [weakself hideMorePanel];
                }
            });
        } errorBlock:^(RCErrorCode nErrorCode, RCMessage * _Nonnull errorMessage) {
            
            
            dispatch_async(dispatch_get_main_queue(), ^{
                // 在消息列表中查找并更新消息状态
                SCNativeMessageModel *storedModel = [weakself findMessageWithUID:model.messageUId];
                if (storedModel) {
                    storedModel.sentStatus = SCMessageSentStatusFailed;
                    
                    // 刷新界面显示重试按钮
                    [weakself.conversationMessageCollectionView reloadData];
                }
            });
        } cancel:nil];
    }
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [picker dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - Helper Methods

- (SCNativeMessageModel *)findMessageWithUID:(NSString *)messageUId {
    for (SCNativeMessageModel *message in self.conversationDataRepository) {
        if (message.messageUId == messageUId) {
            return message;
        }
    }
    return nil;
}

#pragma mark - SCImageMessageCellDelegate

- (void)scImageMessageCellDidTapImage:(SCNativeMessageModel *)model {
    if ([model.content isKindOfClass:[RCImageMessage class]]) {
        RCImageMessage *imageMessage = (RCImageMessage *)model.content;
        //使用  预览图片。   [SCFullScreenPreviewMediaViewController showWithFromVC:self media:self.viewModel.userInfo.photoMediasDefaulyAvatar userInfo:self.viewModel.userInfo type:SCFullScreenPreviewMediaTypePhoto index:index];
//        NSMutableArray<SCMediaListModel *> *mediaList = [NSMutableArray array];
//        SCMediaListModel *mediaModel = [[SCMediaListModel alloc] init];
//        mediaModel.mediaUrl = imageMessage.imageUrl;
//        mediaModel.mediaType = ;
//        [mediaList addObject:mediaModel];
        // 转换为字典格式
        NSMutableArray<NSDictionary *> *mediaDicts = [NSMutableArray array];
        NSDictionary *mediaDict = @{
            @"mediaUrl": imageMessage.imageUrl ?: @"",
            @"mediaType": @"photo"
        };
        [mediaDicts addObject:mediaDict];
        [SCFullScreenPreviewMediaViewController showWithFromVC:self media:mediaDicts userDict:[self getUserInfoDict] type:SCFullScreenPreviewMediaTypePhoto index:0 isShowCall:false];
    
    }
}

- (void)scImageMessageCellDidTapRetry:(SCNativeMessageModel *)model {
    if (![model.content isKindOfClass:[RCImageMessage class]]) {
        return;
    }
    
    RCImageMessage *imageMessage = (RCImageMessage *)model.content;
    
    // 重新设置为发送中状态
    model.sentStatus = SCMessageSentStatusSending;
    
    // 刷新界面显示发送状态
    [self.conversationMessageCollectionView reloadData];
    
    // 重新创建RCMessage并发送
    RCMessage *retryMessage = [[RCMessage alloc] initWithType:ConversationType_PRIVATE
                                                     targetId:self.targetId
                                                    direction:MessageDirection_SEND
                                                      content:imageMessage];
    
    // 使用新的UID作为标识符，但更新原有消息的UID
    long originalMessageUID = model.messageId;
    model.messageId = retryMessage.messageId;
    
    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] sendMediaMessage:retryMessage pushContent:nil pushData:nil progress:^(int progress, RCMessage * _Nonnull progressMessage) {
        // 发送进度回调
    } successBlock:^(RCMessage * _Nonnull successMessage) {
        
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // 在消息列表中查找并更新消息状态
            SCNativeMessageModel *storedModel = [weakself findMessageWithUID:retryMessage.messageUId];
            if (storedModel) {
                storedModel.sentStatus = SCMessageSentStatusSent;
                storedModel.messageId = successMessage.messageId;
                [storedModel updateFromMessage:successMessage];
                
                // 刷新界面
                [weakself.conversationMessageCollectionView reloadData];
            }
        });
    } errorBlock:^(RCErrorCode nErrorCode, RCMessage * _Nonnull errorMessage) {
        
        
        dispatch_async(dispatch_get_main_queue(), ^{
            // 在消息列表中查找并更新消息状态
            SCNativeMessageModel *storedModel = [weakself findMessageWithUID:retryMessage.messageUId];
            if (storedModel) {
                storedModel.sentStatus = SCMessageSentStatusFailed;
                
                // 刷新界面显示重试按钮
                [weakself.conversationMessageCollectionView reloadData];
            }
        });
    } cancel:nil];
}


#pragma mark - SCFileMessageCellDelegate

- (void)scFileMessageCellDidTapFile:(SCNativeMessageModel *)model {
    if ([model.content isKindOfClass:[RCFileMessage class]]) {
        RCFileMessage *fileMessage = (RCFileMessage *)model.content;
        

        // TODO: 实现文件下载和打开逻辑
        // 这里可以添加文件下载、预览或分享功能
        if (fileMessage.fileUrl && fileMessage.fileUrl.length > 0) {
            // 如果有文件URL，可以尝试下载或在浏览器中打开
            NSURL *url = [NSURL URLWithString:fileMessage.fileUrl];
            if (url && [[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }
    }
}                

#pragma mark - 录音相关方法

- (void)switchToVoiceMode {
    if (self.isVoiceMode) {
        return;
    }

    self.isVoiceMode = YES;

    // 隐藏输入框，显示录音按钮
    [UIView animateWithDuration:0.3 animations:^{
        self.inputTextView.alpha = 0.0;
        self.placeholderLabel.alpha = 0.0;
        self.recordButton.alpha = 1.0;
    } completion:^(BOOL finished) {
        self.inputTextView.hidden = YES;
        self.placeholderLabel.hidden = YES;
        self.recordButton.hidden = NO;
    }];

    // 更新语音按钮图标
    [self.voiceButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_keyboard"] forState:UIControlStateNormal];

    // 收起键盘和面板
    [self.view endEditing:YES];
    if (self.isOpenEmoji) {
        [self hideEmoji];
    }
    if (self.isOpenMorePanel) {
        [self hideMorePanel];
    }
}

- (void)switchToTextMode {
    if (!self.isVoiceMode) {
        return;
    }

    self.isVoiceMode = NO;

    // 显示输入框，隐藏录音按钮
    [UIView animateWithDuration:0.3 animations:^{
        self.recordButton.alpha = 0.0;
        self.inputTextView.alpha = 1.0;
        self.placeholderLabel.alpha = (self.inputTextView.text.length == 0) ? 1.0 : 0.0;
    } completion:^(BOOL finished) {
        self.recordButton.hidden = YES;
        self.inputTextView.hidden = NO;
        self.placeholderLabel.hidden = (self.inputTextView.text.length > 0);
    }];

    // 恢复语音按钮图标
    [self.voiceButton setImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_voice"] forState:UIControlStateNormal];
}

- (void)setupRecordButtonGestures {
    // 长按手势
    UILongPressGestureRecognizer *longPressGesture = [[UILongPressGestureRecognizer alloc] initWithTarget:self action:@selector(handleRecordButtonLongPress:)];
    longPressGesture.minimumPressDuration = 0.1; // 最小按压时间
    [self.recordButton addGestureRecognizer:longPressGesture];

    // 拖拽手势
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handleRecordButtonPan:)];
    panGesture.delegate = self; // 设置代理以处理手势冲突
    [self.recordButton addGestureRecognizer:panGesture];
}

- (void)handleRecordButtonLongPress:(UILongPressGestureRecognizer *)gesture {
    switch (gesture.state) {
        case UIGestureRecognizerStateBegan:
            [self startRecording];
            break;
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled:
            [self stopRecording];
            break;
        default:
            break;
    }
}

- (void)handleRecordButtonPan:(UIPanGestureRecognizer *)gesture {
    if (!self.isRecording) {
        return;
    }

    // 获取手指在整个视图中的位置
    CGPoint locationInView = [gesture locationInView:self.view];

    // 如果录音界面存在，检测手指是否在containerView区域内
    if (self.voiceRecordingView && !self.voiceRecordingView.hidden) {
        // 将位置转换到录音界面坐标系
        CGPoint locationInRecordingView = [self.view convertPoint:locationInView toView:self.voiceRecordingView];

        // 获取containerView在录音界面中的frame
        CGRect containerFrame = self.voiceRecordingView.containerView.frame;

        // 检测手指是否在containerView区域内
        BOOL isInContainerView = CGRectContainsPoint(containerFrame, locationInRecordingView);

        if (isInContainerView) {
            // 手指在containerView内，切换到取消模式
            if (self.voiceRecordingView.currentState != SCVoiceRecordingViewStateCancelHint) {
                [self.voiceRecordingView switchToCancelHint];
            }
        } else {
            // 手指在containerView外，恢复录音模式
            if (self.voiceRecordingView.currentState != SCVoiceRecordingViewStateRecording) {
                [self.voiceRecordingView switchToRecording];
            }
        }

        if (gesture.state == UIGestureRecognizerStateEnded) {
            if (isInContainerView) {
                // 在containerView内松开，取消录音
                [self cancelRecording];
            } else {
                // 在containerView外松开，发送录音
                [self stopRecording];
            }
        }
    }
}


- (void)scFileMessageCellDidTapRetry:(SCNativeMessageModel *)model {
    if (![model.content isKindOfClass:[RCFileMessage class]]) {
        return;
    }

    

    // TODO: 实现文件消息重试发送逻辑
    // 这里需要根据实际的文件发送逻辑来实现重试功能

    // 暂时更新状态为发送中
    model.sentStatus = SCMessageSentStatusSending;
    [self.conversationMessageCollectionView reloadData];
}

- (void)scFileMessageCellDidTapAvatar:(SCNativeMessageModel *)model {
    [self didTapCellPortrait:model.senderUserId];
}

- (void)handleVoiceRecordingPan:(UIPanGestureRecognizer *)gesture {
    // 这个方法现在不再使用，保留以防需要
    [self handleRecordButtonPan:gesture];
}

- (void)startRecording {
    // 检查录音权限
    [[SCVoiceRecorderManager sharedManager] checkRecordPermission];
}

- (void)stopRecording {
    if (!self.isRecording) {
        return;
    }

    BOOL success = [[SCVoiceRecorderManager sharedManager] stopRecording];
    if (!success) {
        
    }
}

- (void)cancelRecording {
    if (!self.isRecording) {
        return;
    }

    [[SCVoiceRecorderManager sharedManager] cancelRecording];
    [self hideVoiceRecordingView];
    self.isRecording = NO;
}

- (void)showVoiceRecordingView {
    if (!self.voiceRecordingView) {
        self.voiceRecordingView = [[SCVoiceRecordingView alloc] init];
        self.voiceRecordingView.delegate = self;
    }

    [self.voiceRecordingView showInView:self.view];
}

- (void)hideVoiceRecordingView {
    if (self.voiceRecordingView) {
        [self.voiceRecordingView hide];
    }
}

#pragma mark - SCVoiceRecorderManagerDelegate

- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
            didChangeState:(SCVoiceRecordState)state
                  duration:(NSTimeInterval)duration
                     error:(nullable NSError *)error {

    switch (state) {
        case SCVoiceRecordStateRecording:
            
            self.isRecording = YES;
            [self showVoiceRecordingView];
            break;

        case SCVoiceRecordStateCompleted:
            
            self.isRecording = NO;
            [self hideVoiceRecordingView];
            [self sendVoiceMessage];
            break;

        case SCVoiceRecordStateError:
            
            self.isRecording = NO;
            [self hideVoiceRecordingView];
            [self showRecordingError:error];
            break;

        case SCVoiceRecordStateIdle:
            self.isRecording = NO;
            [self hideVoiceRecordingView];
            break;

        default:
            break;
    }


}

- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
            didUpdateVolume:(float)volume {
    // 更新波纹动画
    if (self.voiceRecordingView) {
        [self.voiceRecordingView updateVolume:volume];
    }
}

- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
         permissionGranted:(BOOL)granted
           shouldShowAlert:(BOOL)shouldShowAlert {

    if (granted) {
        // 权限已授权，开始录音
        BOOL success = [[SCVoiceRecorderManager sharedManager] startRecording];
        if (!success) {
            
        }
    } else if (shouldShowAlert) {
        // 显示权限提示
        [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                       fromViewController:self
                                              cancelBlock:nil];
    }
}

#pragma mark - SCVoiceRecordingViewDelegate

- (void)voiceRecordingView:(SCVoiceRecordingView *)recordingView
           didChangeState:(SCVoiceRecordingViewState)state {
    // 录音界面状态改变处理
    
}

- (void)voiceRecordingView:(SCVoiceRecordingView *)recordingView
            didPanGesture:(UIPanGestureRecognizer *)gesture {
    // 处理录音界面的拖拽手势
    [self handleVoiceRecordingPan:gesture];
}

#pragma mark - 语音消息发送

- (void)sendVoiceMessage {
    NSString *voiceFilePath = [SCVoiceRecorderManager sharedManager].recordingFilePath;
    if (!voiceFilePath || ![[NSFileManager defaultManager] fileExistsAtPath:voiceFilePath]) {
        
        return;
    }

    // 获取录音时长
    NSTimeInterval duration = [SCVoiceRecorderManager sharedManager].currentDuration;

    // 创建语音消息
    RCHQVoiceMessage *voiceMessage = [RCHQVoiceMessage messageWithPath:voiceFilePath duration:(long)duration];
    
    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] sendMediaMessage:ConversationType_PRIVATE
                                             targetId:self.targetId
                                              content:voiceMessage
                                          pushContent:nil
                                             pushData:nil
                                             attached:^(RCMessage * _Nullable message) {
        // 在attached回调中获取message对象
        if (message) {
            dispatch_async(dispatch_get_main_queue(), ^{
                SCNativeMessageModel *model = [SCNativeMessageModel modelWithMessage:message];
                [weakself.conversationDataRepository addObject:model];

                // 重新计算消息时间显示逻辑
                [SCNativeMessageModel calculateDisplayTimeForMessages:weakself.conversationDataRepository];

                [weakself.conversationMessageCollectionView reloadData];
                [weakself scrollToBottomAnimated:YES];
            });
        }
    } progress:^(int progress, long messageId) {
        // 发送进度回调（语音消息通常很快，可以不处理）
        
    } success:^(long messageId) {
        // 发送成功
        
    } error:^(RCErrorCode errorCode, long messageId) {
        // 发送失败
        
    } cancel:^(long messageId) {
        // 用户取消发送
        
    }];
}

- (void)showRecordingError:(NSError *)error {
    
    // 显示录音错误提示
    NSString *message = error.localizedDescription ?: @"Recording failed, please try again".translateString;

    UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Recording Failed".translateString
                                                                   message:message
                                                            preferredStyle:UIAlertControllerStyleAlert];

    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"OK".translateString
                                                       style:UIAlertActionStyleDefault
                                                     handler:nil];
    [alert addAction:okAction];

    [self presentViewController:alert animated:YES completion:nil];
}

#pragma mark - UIGestureRecognizerDelegate

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    // Allow long press and pan gestures to be recognized simultaneously
    if ([gestureRecognizer isKindOfClass:[UIPanGestureRecognizer class]] ||
        [gestureRecognizer isKindOfClass:[UILongPressGestureRecognizer class]]) {
        return YES;
    }
    return NO;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    // 如果不是点击隐藏键盘的手势，使用默认行为
    if (gestureRecognizer != self.tapGestureToHideKeyboard) {
        return YES;
    }

    // 获取触摸点的视图
    UIView *touchedView = touch.view;

    // 如果触摸的是输入相关的控件，不触发隐藏键盘
    if ([touchedView isKindOfClass:[UITextView class]] ||
        [touchedView isKindOfClass:[UIButton class]] ||
        [touchedView isDescendantOfView:self.chatSessionInputBarControl] ||
        [touchedView isDescendantOfView:self.emojiBoardContainer] ||
        [touchedView isDescendantOfView:self.morePanelContainer]) {
        return NO;
    }

    // 如果触摸的是录音相关的视图，不触发隐藏键盘
    if (self.voiceRecordingView && [touchedView isDescendantOfView:self.voiceRecordingView]) {
        return NO;
    }

    return YES;
}

@end
