//
//  SCConversationListViewController.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCConversationListViewController.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCConversationListCell.h"
// #import "SCUserInfoBaseModel.h" // 已移除，使用字典替代
#import "SCConversationDisplayModel.h"
#import "SCAPIServiceManager.h"
#import "SCIMService.h"
#import "SCConversationInfoViewController.h"
#import "SCOnlineStatesService.h"
#import "SCAnchorInfoViewController.h"
#import "SCRobotCustomerServiceViewController.h"
#import "UIScrollView+EmptyDataSet.h"
#import "SCMessagePopupManager.h"
#import "SCThrottle.h"
#import "SCIMUIConfig.h"
#import "SCNativeConversationModel.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCConversationListViewController ()<DZNEmptyDataSetSource, DZNEmptyDataSetDelegate, UITableViewDataSource, UITableViewDelegate>

@property(nonatomic,strong) NSMutableDictionary<NSString *,NSDictionary *> *userInfoDictDis;
@property(nonatomic,strong) SCDisposeBag *disposeBag;
@property(nonatomic,strong) SCOnlineStatusSubscribe * onlineStatusSub;

// 原生实现方法
- (void)createConversationListTableView;
- (void)setupConversationListTableViewConstraints;
- (void)loadConversationListData;
- (void)refreshConversationListData;
- (void)refreshConversationTableViewIfNeeded;

- (long long)getConversationTimestamp:(RCConversation *)conversation;
- (void)loadAllConversationsInQueue;
- (void)addConversationsWithDeduplication:(NSArray<SCNativeConversationModel *> *)newModels;

@end

@implementation SCConversationListViewController

#pragma mark - 原生TableView创建和配置
- (void)createConversationListTableView {
    // 创建TableView
    self.conversationListTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.conversationListTableView.delegate = self;
    self.conversationListTableView.dataSource = self;
    self.conversationListTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.conversationListTableView.rowHeight = 86.0f;
    self.conversationListTableView.backgroundColor = [UIColor scGlobalBgColor];
    self.conversationListTableView.emptyDataSetSource = self;
    self.conversationListTableView.emptyDataSetDelegate = self;

    // 注册Cell
    [self.conversationListTableView registerClass:[SCConversationListCell class] forCellReuseIdentifier:cellIdentify];

    // 添加到视图
    [self.view addSubview:self.conversationListTableView];
}

- (void)setupConversationListTableViewConstraints {
    [self.conversationListTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

#pragma mark - 数据加载和刷新
- (void)loadConversationListData {
    [self loadAllConversationsInQueue];
}

- (void)refreshConversationListData {
    [self loadConversationListData];
}

- (void)refreshConversationTableViewIfNeeded {
    // 保持原有的刷新逻辑
    [self commonReload];
}

#pragma mark - 分页相关方法
- (long long)getConversationTimestamp:(RCConversation *)conversation {
    // 优先使用receivedTime，如果为0则使用sentTime
    return conversation.receivedTime > 0 ? conversation.receivedTime : conversation.sentTime;
}

- (void)loadAllConversationsInQueue {
    // 初始化数据源
    if (!self.conversationListDataSource) {
        self.conversationListDataSource = [[NSMutableArray alloc] init];
    }
    if (!self.userInfoDictDis) {
        self.userInfoDictDis = [[NSMutableDictionary alloc] init];
    }

    // 清空现有数据
    [self.conversationListDataSource removeAllObjects];

    // 重置分页状态
    self.lastConversationTimestamp = 0;
    self.hasMoreData = YES;
    self.isLoadingMore = NO;

    // 开始队列加载
    [self loadNextBatchInQueue];
}

- (void)loadNextBatchInQueue {
    // 如果正在加载，直接返回
    if (self.isLoadingMore) {
        return;
    }

    // 如果没有更多数据，完成加载
    if (!self.hasMoreData) {
        // 处理数据并刷新UI
        [self willReloadTableData:self.conversationListDataSource];
        [self.conversationListTableView reloadData];
        return;
    }

    // 设置加载状态
    self.isLoadingMore = YES;

    kWeakSelf(self);
    [[RCCoreClient sharedCoreClient] getConversationList:@[@(ConversationType_PRIVATE)]
                                                   count:20  // 增加每次加载数量以提高效率
                                               startTime:self.lastConversationTimestamp
                                              completion:^(NSArray<RCConversation *> *conversations) {
        dispatch_async(dispatch_get_main_queue(), ^{
            // 重置加载状态
            weakself.isLoadingMore = NO;

            // 转换RCConversation为SCNativeConversationModel
            NSMutableArray *newModels = [[NSMutableArray alloc] init];
            for (RCConversation *conversation in conversations) {
                SCNativeConversationModel *model = [SCNativeConversationModel modelWithConversation:conversation];
                [newModels addObject:model];
            }

            // 添加新数据到数据源，去重处理
            [weakself addConversationsWithDeduplication:newModels];

            // 更新最后一条会话的时间戳
            if (conversations.count > 0) {
                RCConversation *lastConversation = [conversations lastObject];
                weakself.lastConversationTimestamp = [weakself getConversationTimestamp:lastConversation];
            }

            // 判断是否还有更多数据
            weakself.hasMoreData = (conversations.count >= 20);

            // 继续加载下一批数据
            [weakself loadNextBatchInQueue];
        });
    }];
}

- (void)addConversationsWithDeduplication:(NSArray<SCNativeConversationModel *> *)newModels {
    // 创建现有会话的targetId集合用于去重
    NSMutableSet<NSString *> *existingTargetIds = [[NSMutableSet alloc] init];
    for (SCNativeConversationModel *existingModel in self.conversationListDataSource) {
        if (existingModel.targetId) {
            [existingTargetIds addObject:existingModel.targetId];
        }
    }

    // 只添加不重复的会话
    for (SCNativeConversationModel *newModel in newModels) {
        if (newModel.targetId && ![existingTargetIds containsObject:newModel.targetId]) {
            [self.conversationListDataSource addObject:newModel];
            [existingTargetIds addObject:newModel.targetId];
        }
    }
}

#pragma mark - JXCategoryListContentViewDelegate
- (UIView *)listView{
    return self.view;
}
- (void)listWillAppear{
    if(self.onlineStatusSub){
        [self onChangeOnlineStatusWithIds:nil];
        [self.onlineStatusSub start];
        //进入页面立刻刷新一次
        [kSCAuthOnlineStatesService refresh];
    }
}

- (void)listWillDisappear{
    if(self.onlineStatusSub){
        //标记暂停
        [self.onlineStatusSub pause];
    }
}

- (void)dealloc{
    [_disposeBag dispose];
    _disposeBag = nil;
}
#pragma mark - 消息

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    //关闭消息弹窗
    SCMessagePopupManager.shared.enableMessagePopup = NO;
    // 刷新会话列表数据
    [self refreshConversationListData];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    // 开启消息弹窗
    SCMessagePopupManager.shared.enableMessagePopup = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor scGlobalBgColor];

    _disposeBag = [[SCDisposeBag alloc] init];
    _userInfoDictDis = [[NSMutableDictionary alloc] init];

    // 初始化分页状态
    _hasMoreData = YES;
    _isLoadingMore = NO;
    _lastConversationTimestamp = 0;

    // 创建原生TableView
    [self createConversationListTableView];
    [self setupConversationListTableViewConstraints];

    // Do any additional setup after loading the view.

//    kScAuthMar.strategy.topOfficialUserIDS
    kWeakSelf(self);
    [kScAuthMar.imService.topOfficialUsersObs subscribe:^(NSArray<NSDictionary *> * _Nonnull userDictArray) {
        for (NSDictionary *userDict in userDictArray) {
            NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            if (userID.length > 0) {
                // 存储到字典版本的用户信息字典中
                weakself.userInfoDictDis[userID] = userDict;
            }
        }
    } error:nil disposeBag:self.disposeBag];

    [kScAuthMar.imService.userServiceAccountObx subscribe:^(NSDictionary * _Nonnull userServiceAccountDict) {
        NSString *userID = [SCDictionaryHelper stringFromDictionary:userServiceAccountDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        if (userID.length > 0) {
            // 存储到字典版本的用户信息字典中
            weakself.userInfoDictDis[userID] = userServiceAccountDict;
        }
    } error:nil disposeBag:self.disposeBag];

    //开启在线状态监听的初始化
    _onlineStatusSub = [[SCOnlineStatusSubscribe alloc] init];
    //默认关闭
    [_onlineStatusSub pause];
    [_onlineStatusSub.changeObs subscribe:^(NSArray<NSString *> * _Nullable value) {
        [weakself onChangeOnlineStatusWithIds:value];
    } error:nil disposeBag:self.disposeBag];
    [kSCAuthOnlineStatesService add:self.onlineStatusSub dispose:self.disposeBag];
    //收到新消息
    [kScAuthMar.imService.receiveMessageObs afterSubscribe:^(RCMessage * _Nullable value) {
        // 收到新消息时重新加载所有会话数据，确保会话顺序正确
        [weakself loadAllConversationsInQueue];
    } error:nil disposeBag:self.disposeBag];

    // 移除KVO监听，改为直接管理数据源
    // [self addObserver:self forKeyPath:@"conversationListDataSource" options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld context:nil];

    [self setupHeaderView];

    // 加载初始数据
    [self loadConversationListData];
}

- (void)setupHeaderView {
    UIView *headerView = [[UIView alloc] init];
    headerView.frame = CGRectMake(0, 0, self.view.frame.size.width, 86);
    
    SCGradientColors *colors = [SCGradientColors themeColorWithOrientation:SCGradientColorsOrientationHorizontal];
    UIImage *gradientImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(headerView.scWidth, 86)];
    
    UIImageView *bgImageView = [[UIImageView alloc]initWithImage:gradientImage];
    bgImageView.alpha = 0.15;
    [headerView addSubview:bgImageView];
    
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.contentMode = UIViewContentModeScaleAspectFill;
    iconImageView.layer.masksToBounds = YES;
    iconImageView.layer.cornerRadius = (kSCConversationAvatarWH / 2.0);
    iconImageView.image = [SCResourceManager loadImageWithName:@"avatar_robot_customer"];
    [headerView addSubview:iconImageView];
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"Customer Service".translateString;
    titleLabel.textColor = UIColor.scWhite;
    titleLabel.font = kScUIFontSemibold(14);
    [headerView addSubview:titleLabel];
    
    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(headerView);
    }];
    
    [iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(headerView).offset(24);
        make.centerY.equalTo(headerView);
        make.size.mas_equalTo(CGSizeMake(kSCConversationAvatarWH, kSCConversationAvatarWH));
    }];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(iconImageView.mas_trailing).offset(7);
        make.centerY.equalTo(headerView);
    }];
    
    kSCAddTapGesture(headerView, self, onClickCustomerService);
    
    self.conversationListTableView.tableHeaderView = headerView;
}

-(void) onChangeOnlineStatusWithIds:(NSArray<NSString *> *) ids{
    for (UITableViewCell *cell in self.conversationListTableView.visibleCells) {
        if ([cell isKindOfClass:[SCConversationListCell class]]) {
            SCConversationListCell *conversationCell = (SCConversationListCell *)cell;
            NSIndexPath *indexPath = [self.conversationListTableView indexPathForCell:cell];
            if (indexPath.row < self.conversationListDataSource.count) {
                SCNativeConversationModel *model = [self.conversationListDataSource objectAtIndex:indexPath.row];
                
                NSDictionary *statusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:model.targetId];
                
                if ([kScAuthMar.imService isSystemWithId:model.targetId] ||
                    [kScAuthMar.imService isUserServiceAccountWithId:model.targetId]) {
                    conversationCell.onlineStatusIV.hidden = YES;
                } else {
                    if (statusDict) {
                        SCAnchorStatus newStatus = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:statusDict forKey:@"status" defaultValue:AnchorStatusOffline];
                        if (newStatus != AnchorStatusUnknown) {
                            conversationCell.onlineStatusIV.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:newStatus];
                            conversationCell.onlineStatusIV.hidden = NO;
                        } else {
                            conversationCell.onlineStatusIV.hidden = YES;
                        }
                    } else {
                        conversationCell.onlineStatusIV.hidden = YES;
                    }
                }
            }
        }
    }
}

- (void)commonReload{
    // 移除节流延迟，立即刷新UI以提升用户体验
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.conversationListTableView reloadData];
    });
}

// 优化的用户信息刷新方法，只刷新特定用户相关的cell
- (void)reloadUserInfoForUserId:(NSString *)userId {
    if (!userId || userId.length == 0) return;

    dispatch_async(dispatch_get_main_queue(), ^{
        // 数据源安全检查
        if (!self.conversationListDataSource || self.conversationListDataSource.count == 0) {
            return;
        }

        // 查找对应的indexPath并只刷新该cell
        NSInteger targetIndex = -1;
        for (NSInteger i = 0; i < self.conversationListDataSource.count; i++) {
            SCNativeConversationModel *model = self.conversationListDataSource[i];
            if (model.targetId && [model.targetId isEqualToString:userId]) {
                targetIndex = i;
                break;
            }
        }

        if (targetIndex >= 0 && targetIndex < self.conversationListDataSource.count) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:targetIndex inSection:0];

            // 检查tableView是否存在且有效
            if (!self.conversationListTableView || !self.conversationListTableView.superview) {
                return;
            }

            // 检查cell是否在可见范围内，如果是则只刷新该cell，否则不需要刷新
            NSArray *visibleIndexPaths = [self.conversationListTableView indexPathsForVisibleRows];
            if ([visibleIndexPaths containsObject:indexPath]) {
                [self.conversationListTableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
            }
        }
    });
}

static NSString * cellIdentify = @"SCConversationListCell_key";

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.conversationListDataSource.count;
}

- (NSMutableArray<SCNativeConversationModel *> *)willReloadTableData:(NSMutableArray<SCNativeConversationModel *> *)dataSource{
    
    if([self.conversationListDataSource count] == 0){
        //首次
        NSMutableSet<NSString *> * notAddUser = [NSMutableSet new];
        // 从策略字典中获取官方用户ID列表
        NSArray *topOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCTopOfficialUserIdsKey defaultValue:@[]];
        NSArray *broadcasterFollowOfficialUserIds = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:SCDictionaryKeys.shared.kSCBroadcasterFollowOfficialUserIdsKey defaultValue:@[]];
        [notAddUser addObjectsFromArray:topOfficialUserIds];
        [notAddUser addObjectsFromArray:broadcasterFollowOfficialUserIds];

        
        for (SCNativeConversationModel * item in dataSource) {
            if(item.targetId == nil){
                //跳过没有用户id的数据
                continue;
            }
            
            // 检查是否为官方用户
            if ([topOfficialUserIds containsObject:item.targetId] || [broadcasterFollowOfficialUserIds containsObject:item.targetId]) {
                if (!item.isTop) {
                    kWeakSelf(self);
                    [[RCCoreClient sharedCoreClient] setConversationToTop:item.conversationType targetId:item.targetId isTop:YES completion:^(BOOL ret) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            if (ret) {
                                item.isTop = YES;
                                [weakself refreshConversationListTable];
                            }
                        });
                    }];
                }
            } else {
                //在线状态
                [self.onlineStatusSub addWithUserID:item.targetId];
            }
            // 检查字典版本的用户信息是否存在
            if(self.userInfoDictDis[item.targetId] == nil){
                //发起请求
                kWeakSelf(self);
                [SCAPIServiceManager requestUserBaseWithUserId:item.targetId success:^(NSDictionary * _Nonnull userDict) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        // 存储到字典版本的用户信息字典中
                        weakself.userInfoDictDis[item.targetId] = userDict;
                        // 使用优化的刷新方法，只刷新特定用户的cell
                        [weakself reloadUserInfoForUserId:item.targetId];
                    });
                } failure:nil];
            }
            if([notAddUser containsObject:item.targetId]){
                [notAddUser removeObject:item.targetId];
            }
        }
    }else{
        //遍历加载用户数据
        for (SCNativeConversationModel * item in dataSource) {
            if(item.targetId == nil){
                //跳过没有用户id的数据
                continue;
            }
            [self.onlineStatusSub addWithUserID:item.targetId];
            // 检查字典版本的用户信息是否存在
            if(self.userInfoDictDis[item.targetId] == nil){
                //发起请求
                kWeakSelf(self);
                [SCAPIServiceManager requestUserBaseWithUserId:item.targetId success:^(NSDictionary * _Nonnull userDict) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        // 存储到字典版本的用户信息字典中
                        weakself.userInfoDictDis[item.targetId] = userDict;
                        // 使用优化的刷新方法，只刷新特定用户的cell
                        [weakself reloadUserInfoForUserId:item.targetId];
                    });
                } failure:nil];
            }
        }
    }
    
    
    
    
    
    return dataSource;
}

- (void)refreshConversationListTable {
    [SCThrottle throttleWithTag:kSCThrottleReloadConversationListKey duration:0.3 onAfter:^{
        [SCThrottle cancelWithTag:kSCThrottleReloadConversationListKey];
        [self commonReload];
    }];
}

#pragma mark - UITableViewDelegate
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCConversationListCell * cell = [tableView dequeueReusableCellWithIdentifier:cellIdentify];

    if (indexPath.row < self.conversationListDataSource.count) {
        return [self configureCell:cell withModelAtIndexPath:indexPath];
    }

    return cell;
}

- (UITableViewCell *)configureCell:(SCConversationListCell *)cell withModelAtIndexPath:(NSIndexPath *)indexPath {
    SCNativeConversationModel * model = self.conversationListDataSource[indexPath.row];

    SCConversationDisplayModel * displayModel = [SCConversationDisplayModel new];
    displayModel.conversation = model;

    // 使用字典版本的用户信息
    NSDictionary *userDict = [self.userInfoDictDis objectForKey:model.targetId];
    displayModel.userInfoDict = userDict;

    [cell setCustomModel:displayModel];

    NSDictionary *statusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:model.targetId];
    if([kScAuthMar.imService isSystemWithId:model.targetId] || [kScAuthMar.imService isUserServiceAccountWithId:model.targetId]){
        //系统消息和客服消息不显示
        cell.onlineStatusIV.hidden = YES;
    } else {
        if (statusDict) {
            SCAnchorStatus newStatus = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:statusDict forKey:@"status" defaultValue:AnchorStatusOffline];
            if (newStatus != AnchorStatusUnknown) {
                cell.onlineStatusIV.setBackgroundColor([SCDictionaryHelper dotColorForAnchorStatus:newStatus]);
                cell.onlineStatusIV.hidden = NO;
            } else {
                cell.onlineStatusIV.hidden = YES;
            }
        } else {
            cell.onlineStatusIV.hidden = YES;
        }              
    }

    kWeakSelf(self)
    cell.tapAvatarBlock = ^(SCConversationDisplayModel * _Nonnull customModel) {
        // 使用便捷访问方法获取用户ID
        NSString *userId = [customModel getUserID];
        if (userId.length == 0) {
            return;
        }
        if([kScAuthMar.imService isSystemWithId:userId] || [kScAuthMar.imService isUserServiceAccountWithId:userId] ){
            return;
        }
        [SCAnchorInfoViewController openWith:userId from:weakself];
    };

    [self.onlineStatusSub addWithUserID:model.targetId];
    return cell;
}



- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 86.0f;
}

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCNativeConversationModel * model = self.conversationListDataSource[indexPath.row];
    if([kScAuthMar.imService isSystemWithId:model.targetId] || [kScAuthMar.imService isUserServiceAccountWithId:model.targetId]){
        //系统消息和客服消息不可操作
        UISwipeActionsConfiguration * config = [UISwipeActionsConfiguration configurationWithActions:@[]];
        return config;
    }
    kWeakSelf(self);
    UIContextualAction * action = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"" handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        
        [[RCCoreClient sharedCoreClient] removeConversation:model.conversationType targetId:model.targetId completion:^(BOOL ret) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (model.unreadMessageCount > 0) {
                    kScAuthMar.imService.unreadCountChangeObs.value = @(0);
                }
                [weakself.conversationListDataSource removeObjectAtIndex:indexPath.row];
                [weakself commonReload];
            });
        }];
        
        completionHandler(YES);
    }];
    action.image = [SCResourceManager loadImageWithName:@"ic_delete_conversation"];
    action.backgroundColor = kSCColorWithHexStr(@"#FE3E69");
    //置顶
    UIContextualAction * topAction = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleNormal title:model.isTop ? @"Unpin".translateString : @"Pin".translateString handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        
    
        SCNativeConversationModel * model = weakself.conversationListDataSource[indexPath.row];
        [[RCCoreClient sharedCoreClient] setConversationToTop:model.conversationType targetId:model.targetId isTop: !model.isTop completion:^(BOOL ret) {
            dispatch_async(dispatch_get_main_queue(), ^{
                model.isTop = !model.isTop;
                [weakself refreshConversationListData];
                [weakself.view toast: model.isTop ? @"Pinned successfully".translateString : @"Cancel pinned successfully".translateString];
            });
        }];
        completionHandler(YES);
    }];
    topAction.image = [SCResourceManager loadImageWithName:model.isTop ? @"ic_unpined" : @"ic_pin"];
    topAction.backgroundColor = [UIColor scBlack];
    UISwipeActionsConfiguration * config = [UISwipeActionsConfiguration configurationWithActions:@[action,topAction]];
    config.performsFirstActionWithFullSwipe = false;
    return config;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if (indexPath.row < self.conversationListDataSource.count) {
        SCNativeConversationModel * model = self.conversationListDataSource[indexPath.row];
        // 使用字典版本的用户信息
        NSDictionary *userDict = [self.userInfoDictDis objectForKey:model.targetId];
        [SCConversationInfoViewController showWithFromVC:self tager:model.targetId userDict:userDict];
    }
}

#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [SCResourceManager loadImageWithName:@"ic_list_empty"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"No Data".translateString;
    NSDictionary *attributes = @{NSFontAttributeName: kScUIFontMedium(24),
                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B7010D"]};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView
{
    return YES;
}


#pragma mark -Action
- (void)onClickCustomerService {
    SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

@end


