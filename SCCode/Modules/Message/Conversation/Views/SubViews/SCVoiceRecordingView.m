//
//  SCVoiceRecordingView.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/17.
//  语音录制界面组件 - 显示录音提示框、波纹动画、录音时长等
//

#import "SCVoiceRecordingView.h"
#import "SCResourceManager.h"
#import "Masonry.h"

@interface SCVoiceRecordingView ()

// 主容器视图
@property (nonatomic, strong, readwrite) UIView *containerView;

// 提示文本标签
@property (nonatomic, strong, readwrite) UILabel *hintLabel;

// 波纹动画视图
@property (nonatomic, strong, readwrite) UIView *waveAnimationView;

// 波纹条数组（用于上下跳动效果）
@property (nonatomic, strong) NSMutableArray<UIView *> *waveBars;

// 当前界面状态
@property (nonatomic, assign, readwrite) SCVoiceRecordingViewState currentState;

// 动画定时器
@property (nonatomic, strong, nullable) NSTimer *animationTimer;

@end

@implementation SCVoiceRecordingView

#pragma mark - 初始化

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupConstraints];
        [self setupWaveAnimation];
        [self setupGestures];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.6];
    self.alpha = 0.0;
    self.hidden = YES;

    // 主容器视图
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = [UIColor colorWithHexString:@"#1A1A1A"];
    self.containerView.layer.cornerRadius = 28.0;
    self.containerView.layer.masksToBounds = YES;
    [self addSubview:self.containerView];

    // 波纹动画视图
    self.waveAnimationView = [[UIView alloc] init];
    self.waveAnimationView.backgroundColor = [UIColor clearColor];
    [self.containerView addSubview:self.waveAnimationView];

    // 提示文本标签
    self.hintLabel = [[UILabel alloc] init];
    self.hintLabel.text = @"Release to send".translateString;
    self.hintLabel.textColor = [UIColor whiteColor];
    self.hintLabel.font = [UIFont systemFontOfSize:16.0 weight:UIFontWeightMedium];
    self.hintLabel.textAlignment = NSTextAlignmentCenter;
    [self.containerView addSubview:self.hintLabel];

    // 初始状态
    self.currentState = SCVoiceRecordingViewStateRecording;
}

- (void)setupConstraints {
    // 主容器约束
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.mas_equalTo(178);
        make.height.mas_equalTo(153);
    }];

    // 波纹动画视图约束
    [self.waveAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.containerView);
        make.top.equalTo(self.containerView).offset(30);
        make.width.mas_equalTo(100);
        make.height.mas_equalTo(70);
    }];

    // 提示文本标签约束
    [self.hintLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.containerView);
        make.top.equalTo(self.waveAnimationView.mas_bottom).offset(10);
        make.leading.trailing.equalTo(self.containerView).inset(12);
        make.bottom.equalTo(self.containerView).offset(-20);
    }];
}

- (void)setupWaveAnimation {
    self.waveBars = [[NSMutableArray alloc] init];

    // 创建7个波纹条（类似音频波形）
    CGFloat barWidth = 3.0;
    CGFloat barSpacing = 4.0;
    CGFloat totalWidth = 7 * barWidth + 6 * barSpacing;
    CGFloat startX = (100 - totalWidth) / 2.0; // 居中显示

    for (int i = 0; i < 7; i++) {
        UIView *waveBar = [[UIView alloc] init];
        waveBar.backgroundColor = [UIColor whiteColor];
        waveBar.layer.cornerRadius = barWidth / 2.0;

        // 设置初始高度（中间的条较高，两边的条较低）
        CGFloat baseHeight = 8.0;
        CGFloat maxHeight = 60.0;
        CGFloat heightMultiplier = 1.0 - abs(i - 3) * 0.15; // 中间为1.0，两边递减
        CGFloat initialHeight = baseHeight + (maxHeight - baseHeight) * heightMultiplier * 0.5;

        waveBar.frame = CGRectMake(startX + i * (barWidth + barSpacing),
                                  (70 - initialHeight) / 2.0,
                                  barWidth,
                                  initialHeight);

        [self.waveAnimationView addSubview:waveBar];
        [self.waveBars addObject:waveBar];
    }
}

- (void)setupGestures {
    // 添加拖拽手势用于检测上滑取消
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    [self addGestureRecognizer:panGesture];
}

- (void)handlePanGesture:(UIPanGestureRecognizer *)gesture {
    // 将手势事件传递给代理处理
    if ([self.delegate respondsToSelector:@selector(voiceRecordingView:didPanGesture:)]) {
        [self.delegate voiceRecordingView:self didPanGesture:gesture];
    }
}

#pragma mark - 公共方法

- (void)showInView:(UIView *)parentView {
    if (self.superview) {
        [self removeFromSuperview];
    }
    
    [parentView addSubview:self];
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(parentView);
    }];
    
    self.hidden = NO;
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1.0;
    } completion:^(BOOL finished) {
        [self startWaveAnimation];
    }];
}

- (void)hide {
    [self stopWaveAnimation];

    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0.0;
    } completion:^(BOOL finished) {
        self.hidden = YES;
        [self removeFromSuperview];
        // 隐藏后重置到初始状态，为下次显示做准备
        [self resetToInitialState];
    }];
}



- (void)updateVolume:(float)volume {
    // 根据音量调整波纹动画的强度
    [self updateWaveAnimationWithVolume:volume];
}

- (void)switchToCancelHint {
    if (self.currentState == SCVoiceRecordingViewStateCancelHint) {
        return;
    }

    self.currentState = SCVoiceRecordingViewStateCancelHint;

    [UIView animateWithDuration:0.2 animations:^{
        self.containerView.backgroundColor = [UIColor colorWithHexString:@"#FE3E69"];
        self.hintLabel.text = @"Release to cancel".translateString;
        self.hintLabel.textColor = [UIColor whiteColor];
    }];

    if ([self.delegate respondsToSelector:@selector(voiceRecordingView:didChangeState:)]) {
        [self.delegate voiceRecordingView:self didChangeState:self.currentState];
    }
}

- (void)switchToRecording {
    if (self.currentState == SCVoiceRecordingViewStateRecording) {
        return;
    }

    self.currentState = SCVoiceRecordingViewStateRecording;

    [UIView animateWithDuration:0.2 animations:^{
        self.containerView.backgroundColor = [UIColor colorWithHexString:@"#1A1A1A"];
        self.hintLabel.text = @"Release to send".translateString;
        self.hintLabel.textColor = [UIColor whiteColor];
    }];

    if ([self.delegate respondsToSelector:@selector(voiceRecordingView:didChangeState:)]) {
        [self.delegate voiceRecordingView:self didChangeState:self.currentState];
    }
}

- (void)resetToInitialState {
    // 重置状态
    self.currentState = SCVoiceRecordingViewStateRecording;

    // 重置容器样式
    self.containerView.backgroundColor = [UIColor colorWithHexString:@"#1A1A1A"];

    // 重置提示文本
    self.hintLabel.text = @"Release to send".translateString;
    self.hintLabel.textColor = [UIColor whiteColor];

    // 重置波纹条到初始状态
    for (int i = 0; i < self.waveBars.count; i++) {
        UIView *waveBar = self.waveBars[i];
        [waveBar.layer removeAllAnimations];

        // 重置到初始高度
        CGFloat baseHeight = 8.0;
        CGFloat maxHeight = 60.0;
        CGFloat heightMultiplier = 1.0 - abs(i - 3) * 0.15;
        CGFloat initialHeight = baseHeight + (maxHeight - baseHeight) * heightMultiplier * 0.5;

        CGRect frame = waveBar.frame;
        frame.size.height = initialHeight;
        frame.origin.y = (70 - initialHeight) / 2.0;
        waveBar.frame = frame;

        // 重置透明度
        waveBar.alpha = 1.0;
    }
}

#pragma mark - 波纹动画

- (void)startWaveAnimation {
    [self stopWaveAnimation];
    
    self.animationTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
                                                           target:self
                                                         selector:@selector(updateWaveAnimation)
                                                         userInfo:nil
                                                          repeats:YES];
}

- (void)stopWaveAnimation {
    if (self.animationTimer) {
        [self.animationTimer invalidate];
        self.animationTimer = nil;
    }

    // 停止所有动画并重置到初始状态
    for (int i = 0; i < self.waveBars.count; i++) {
        UIView *waveBar = self.waveBars[i];
        [waveBar.layer removeAllAnimations];

        // 重置到初始高度
        CGFloat baseHeight = 8.0;
        CGFloat maxHeight = 60.0;
        CGFloat heightMultiplier = 1.0 - abs(i - 3) * 0.15;
        CGFloat initialHeight = baseHeight + (maxHeight - baseHeight) * heightMultiplier * 0.5;

        CGRect frame = waveBar.frame;
        frame.size.height = initialHeight;
        frame.origin.y = (70 - initialHeight) / 2.0;
        waveBar.frame = frame;
    }
}

- (void)updateWaveAnimation {
    // 默认音量为0.3，如果没有音量更新
    [self updateWaveAnimationWithVolume:0.3];
}

- (void)updateWaveAnimationWithVolume:(float)volume {
    // 确保音量在合理范围内
    volume = MAX(0.2, MIN(1.0, volume));

    for (int i = 0; i < self.waveBars.count; i++) {
        UIView *waveBar = self.waveBars[i];

        // 计算每个条的高度变化
        CGFloat baseHeight = 8.0;
        CGFloat maxHeight = 60.0;

        // 中间的条较高，两边的条较低
        CGFloat heightMultiplier = 1.0 - abs(i - 3) * 0.15;

        // 添加随机因子让每个条的动画不完全同步
        CGFloat randomFactor = 0.7 + (arc4random_uniform(60) / 100.0); // 0.7 - 1.3

        // 根据音量和位置计算目标高度
        CGFloat targetHeight = baseHeight + (maxHeight - baseHeight) * heightMultiplier * volume * randomFactor;

        // 创建高度变化动画
        [UIView animateWithDuration:0.12
                              delay:i * 0.015 // 每个条稍微延迟，创造波浪效果
                            options:UIViewAnimationOptionCurveEaseInOut | UIViewAnimationOptionAllowUserInteraction
                         animations:^{
            CGRect frame = waveBar.frame;
            frame.size.height = targetHeight;
            frame.origin.y = (70 - targetHeight) / 2.0; // 保持垂直居中
            waveBar.frame = frame;

            // 根据音量调整透明度
            waveBar.alpha = 0.7 + volume * 0.3;
        } completion:nil];
    }
}

#pragma mark - 内存管理

- (void)dealloc {
    [self stopWaveAnimation];
}

@end
