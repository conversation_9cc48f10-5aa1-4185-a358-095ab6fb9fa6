//
//  SCUserBaseInfoView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/19.
//

#import "SCUserBaseInfoView.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
// #import "SCUserInfoModel.h" // 已迁移到字典

@implementation SCUserBaseInfoView

- (void)initUI{
    [super initUI];
    
    UIImageView * avatarImageView = [[UIImageView alloc] init].setCornerRadius(38/2.0);
    [self addSubview:avatarImageView];
    _avatarImageView = avatarImageView;
    
    UILabel * nameLabel = [UILabel labelWithText:@"" textColor:UIColor.scBlack font:kScUIFontMedium(15)];
    nameLabel.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
    [self addSubview:nameLabel];
    _nameLabel = nameLabel;
    
    SCOnlineStatusView * onLineView = [[SCOnlineStatusView alloc] initWithType:SCOnlineStatusViewTypeSmall];
    [self addSubview:onLineView];
    _onLineView = onLineView;
        
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(0);
        make.centerY.equalTo(self);
        make.width.height.mas_equalTo(38);
    }];
    
    [self.nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarImageView.mas_trailing).offset(10);
        make.trailing.equalTo(self).offset(-10);
        make.centerY.equalTo(self).offset(-10);
        make.height.mas_equalTo(21);
    }];
    
    [self.onLineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarImageView.mas_trailing).offset(2);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(0);
        make.height.mas_equalTo(18);
    }];
}

- (void)configureWithUserDict:(NSDictionary *)userDict {
    // 设置头像
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    [self.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    // 设置昵称
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    self.nameLabel.text = nickname;

    // 设置在线状态
    NSString *statusStr = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:statusStr];
    self.onLineView.dotView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:status];
}

@end
