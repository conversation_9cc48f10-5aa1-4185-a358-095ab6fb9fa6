//
//  SCVoiceRecordingView.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/17.
//  语音录制界面组件 - 显示录音提示框、波纹动画、录音时长等
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

// 录音界面状态枚举
typedef NS_ENUM(NSInteger, SCVoiceRecordingViewState) {
    SCVoiceRecordingViewStateRecording,     // 录音中
    SCVoiceRecordingViewStateCancelHint     // 上滑取消提示
};

@class SCVoiceRecordingView;

// 录音界面代理协议
@protocol SCVoiceRecordingViewDelegate <NSObject>

@optional

/**
 * 录音界面状态改变回调
 * @param recordingView 录音界面实例
 * @param state 新的界面状态
 */
- (void)voiceRecordingView:(SCVoiceRecordingView *)recordingView
           didChangeState:(SCVoiceRecordingViewState)state;

/**
 * 拖拽手势回调（用于上滑取消）
 * @param recordingView 录音界面实例
 * @param gesture 拖拽手势识别器
 */
- (void)voiceRecordingView:(SCVoiceRecordingView *)recordingView
            didPanGesture:(UIPanGestureRecognizer *)gesture;

@end

@interface SCVoiceRecordingView : UIView

// 代理
@property (nonatomic, weak) id<SCVoiceRecordingViewDelegate> delegate;

// 当前界面状态
@property (nonatomic, assign, readonly) SCVoiceRecordingViewState currentState;

// 主容器视图
@property (nonatomic, strong, readonly) UIView *containerView;

// 提示文本标签
@property (nonatomic, strong, readonly) UILabel *hintLabel;

// 波纹动画视图
@property (nonatomic, strong, readonly) UIView *waveAnimationView;

/**
 * 显示录音界面
 * @param parentView 父视图
 */
- (void)showInView:(UIView *)parentView;

/**
 * 隐藏录音界面
 */
- (void)hide;



/**
 * 更新音量（用于波纹动画）
 * @param volume 音量级别 (0.0 - 1.0)
 */
- (void)updateVolume:(float)volume;

/**
 * 切换到取消提示状态
 */
- (void)switchToCancelHint;

/**
 * 切换回录音状态
 */
- (void)switchToRecording;

/**
 * 重置界面样式到初始状态
 */
- (void)resetToInitialState;

@end

NS_ASSUME_NONNULL_END
