//
//  SCMorePanelView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/7/14.
//

#import "SCMorePanelView.h"
@implementation SCMorePanelItem
@end


#pragma mark - 更多面板视图

@interface SCMorePanelView () <UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>
@end

@implementation SCMorePanelView

- (instancetype)initWithItems:(NSArray<SCMorePanelItem *> *)items {
    self = [super init];
    if (self) {
        self.items = items ?: @[];
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.backgroundColor = [UIColor scGlobalBgColor];
    
    // 创建布局
    self.flowLayout = [[UICollectionViewFlowLayout alloc] init];
    self.flowLayout.scrollDirection = UICollectionViewScrollDirectionVertical;
    
    // 计算Cell尺寸：每行4个，2行
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat itemSpacing = 20.0f;
    CGFloat sideMargin = 30.0f;
    CGFloat itemWidth = (screenWidth - sideMargin * 2 - itemSpacing * 3) / 4.0f;
    CGFloat itemHeight = 80.0f;
    
    self.flowLayout.itemSize = CGSizeMake(itemWidth, itemHeight);
    self.flowLayout.minimumInteritemSpacing = itemSpacing;
    self.flowLayout.minimumLineSpacing = 20.0f;
    self.flowLayout.sectionInset = UIEdgeInsetsMake(20, sideMargin, 20, sideMargin);
    
    // 创建CollectionView
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:self.flowLayout];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    self.collectionView.backgroundColor = [UIColor clearColor];
    self.collectionView.showsVerticalScrollIndicator = NO;
    self.collectionView.showsHorizontalScrollIndicator = NO;
    [self addSubview:self.collectionView];
    
    // 注册Cell
    [self.collectionView registerClass:[SCMorePanelItemCell class] forCellWithReuseIdentifier:@"SCMorePanelItemCell"];
    
    // 设置约束
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
}

- (void)reloadData {
    [self.collectionView reloadData];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return 1;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.items.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCMorePanelItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCMorePanelItemCell" forIndexPath:indexPath];
    if (indexPath.row < self.items.count) {
        SCMorePanelItem *item = self.items[indexPath.row];
        [cell configureWithItem:item];
    }
    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    if (indexPath.row < self.items.count) {
        SCMorePanelItem *item = self.items[indexPath.row];
        if ([self.delegate respondsToSelector:@selector(morePanelView:didSelectItemAtIndex:withItem:)]) {
            [self.delegate morePanelView:self didSelectItemAtIndex:indexPath.row withItem:item];
        }
    }
}

@end


@implementation SCMorePanelItemCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 圆形背景
    self.backgroundCircle = [[UIView alloc] init];
    self.backgroundCircle.backgroundColor = [UIColor colorWithHexString:@"#4A1F1F"];
    self.backgroundCircle.layer.cornerRadius = 25.0f;
    [self.contentView addSubview:self.backgroundCircle];
    
    // 图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.iconImageView.tintColor = [UIColor scWhite];
    [self.backgroundCircle addSubview:self.iconImageView];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = kScUIFontMedium(12.0f);
    self.titleLabel.textColor = [UIColor scWhite];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.numberOfLines = 1;
    [self.contentView addSubview:self.titleLabel];
    
    // 设置约束
    [self.backgroundCircle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.top.equalTo(self.contentView);
        make.width.height.mas_equalTo(50.0f);
    }];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.backgroundCircle);
        make.width.height.mas_equalTo(24.0f);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backgroundCircle.mas_bottom).offset(8);
        make.leading.trailing.equalTo(self.contentView);
        make.bottom.lessThanOrEqualTo(self.contentView);
    }];
}

- (void)configureWithItem:(SCMorePanelItem *)item {
    self.titleLabel.text = item.title;
    UIImage *icon = [SCResourceManager loadImageWithName:item.iconName];
    
    // 如果图标不存在，使用默认图标
    if (!icon) {
        if (item.tag == 1001) {
            // 图片的默认图标
            icon = [[UIImage systemImageNamed:@"photo"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        } else if (item.tag == 1002) {
            // 文件的默认图标
            icon = [[UIImage systemImageNamed:@"doc"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        } else {
            // 通用默认图标
            icon = [[UIImage systemImageNamed:@"plus"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
        }
    }
    
    self.iconImageView.image = icon;
}

@end
