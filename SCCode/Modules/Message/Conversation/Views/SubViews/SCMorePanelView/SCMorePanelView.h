//
//  SCMorePanelView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2025/7/14.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN
#pragma mark - 更多面板数据模型
@interface SCMorePanelItem : NSObject
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *iconName;
@property (nonatomic, assign) NSInteger tag;
@end

#pragma mark - 更多面板Cell

@interface SCMorePanelItemCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *backgroundCircle;

- (void)configureWithItem:(SCMorePanelItem *)item;
@end


#pragma mark - 更多面板代理协议

@protocol SCMorePanelViewDelegate <NSObject>
- (void)morePanelView:(UIView *)morePanelView didSelectItemAtIndex:(NSInteger)index withItem:(SCMorePanelItem *)item;
@end


@interface SCMorePanelView : UIView
@property (nonatomic, weak) id<SCMorePanelViewDelegate> delegate;
@property (nonatomic, strong) NSArray<SCMorePanelItem *> *items;
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UICollectionViewFlowLayout *flowLayout;

- (instancetype)initWithItems:(NSArray<SCMorePanelItem *> *)items;
- (void)reloadData;
@end

NS_ASSUME_NONNULL_END
