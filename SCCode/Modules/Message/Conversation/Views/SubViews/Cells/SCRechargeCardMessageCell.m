//
//  SCRechargeCardMessageCell.m
//  Supercall
//
//  Created by guanweihong on 2024/2/7.
//

#import "SCRechargeCardMessageCell.h"
#import "SCIMUIConfig.h"
#import "SCSingleJsonMessage.h"
#import "SCDictionaryHelper.h"
#import "SCDataConverter.h"
#import "SCRechargeCardMessageContentModel.h"


#define SCRechargeCardMessageCellContentMargin kSCBigCornerRadius/2.0f
//标题间距
#define SCRechargeCardMessageCellTitleTopMargin 3.5f
//内容顶部间距
#define SCRechargeCardMessageCellContentLeftMargin 3.5f
#define SCRechargeCardMessageCellContentRightMargin 3.5f
//图片间距
#define SCRechargeCardMessageCellImageTopMargin 3.5f
#define SCRechargeCardMessageCellImageBottomMargin 10.0f
//图片宽高比
#define SCRechargeCardMessageCellImageWidthHeight 214.0f/124.0f
//线条
#define SCRechargeCardMessageCellLineHeight 1.0f/[UIScreen mainScreen].scale
//底部高度
#define SCRechargeCardMessageCellBottomHeight 36.5f
//头像左边距
#define SCRechargeCardMessageCellAvatarLeftMargin 15.0f
//头像上边距
#define SCRechargeCardMessageCellAvatarTopMargin 10.0f
// bubble上边距
#define SCRechargeCardMessageCellBubbleTopMargin 10.0f

@interface SCRechargeCardMessageCell ()
///消息泡泡
@property(nonatomic,weak) UIView * bubbleV;
///内容布局
@property(nonatomic,weak) UIView * contentV;
///标题
@property(nonatomic,weak) UILabel * titleL;
///图片
@property(nonatomic,weak) UIImageView * imageV;
///分割线
@property(nonatomic,weak) UIView * lineV;
//点击描述语
@property(nonatomic,weak) UILabel * clickL;
//小箭头
@property(nonatomic,weak) UIImageView * arrowV;
@end
@implementation SCRechargeCardMessageCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}
///初始化内容视图
-(void)_initUI{
    self.avatarIV = [UIImageView new].addSuperView(self.contentView);
    self.avatarIV.layer.cornerRadius = kSCMessageAvatarWH / 2.0f;
    self.avatarIV.layer.masksToBounds = YES;
    self.avatarIV.userInteractionEnabled = YES;

    // 添加头像点击手势
    UITapGestureRecognizer *avatarTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onAvatarTapped)];
    [self.avatarIV addGestureRecognizer:avatarTap];
    
    self.bubbleV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#32312F")).setCornerRadius(18.0f).addSuperView(self.contentView);
//    self.bubbleV.layer.maskedCorners = kScAuthMar.isLanguageForce ? kCALayerMinXMaxYCorner | kCALayerMinXMinYCorner | kCALayerMaxXMaxYCorner : kCALayerMinXMaxYCorner | kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner;
    self.bubbleV.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner | kCALayerMinXMinYCorner;

    
    self.contentV = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#32312F"]).setCornerRadius(kSCBigCornerRadius).addSuperView(self.bubbleV);
    
    self.titleL = [UILabel new].setTextColor(UIColor.scWhite).setFontRegularSize(15).setNumberLines(0).addSuperView(self.contentV);
    
    self.imageV = [UIImageView new].setContentMode(UIViewContentModeScaleAspectFill).addSuperView(self.contentV);
    self.imageV.clipsToBounds = YES;
    
    self.lineV = [UIView new].setBackgroundColor([UIColor scGray]).addSuperView(self.contentV);
    
    self.clickL = [UILabel new].setTextColor(kSCColorWithHexStr(@"#EFF04E")).setText(@"Click to check details".translateString).setFontRegularSize(12).addSuperView(self.contentV);
    
    self.arrowV = [UIImageView new].setImage([SCResourceManager loadImageWithName:@"ic_arr_right_gray" isAutoForce:YES]).setContentMode(UIViewContentModeCenter).addSuperView(self.contentV);
    
    self.contentV.userInteractionEnabled = YES;
    kSCAddTapGesture(self.contentV, self, onTapContentV);

    // 添加时间标签
    UILabel *timeLb =  [[UILabel alloc] init];
    timeLb.font = [UIFont systemFontOfSize:12];
    timeLb.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    timeLb.textAlignment = NSTextAlignmentCenter;
    timeLb.hidden = YES;
    self.timeLabel = timeLb;
    [self.contentView addSubview:self.timeLabel];
}

- (void)layoutSubviews{
    [super layoutSubviews];

    // 设置时间标签布局
    CGFloat timeTopOffset = 0;
    if (self.model.isDisplayMessageTime && !self.timeLabel.hidden) {
        self.timeLabel.frame = CGRectMake(0, 0, self.contentView.scWidth, 30);
        timeTopOffset = 30;
    }

    CGFloat bubbleWidth = self.frame.size.width - (kSCMessageAvatarWH + SCRechargeCardMessageCellAvatarLeftMargin + kSCMessageContentLeftMargin)*2;
    CGFloat contenViewtWidth = bubbleWidth - SCRechargeCardMessageCellContentMargin*2;
    CGFloat contentWidth = contenViewtWidth - SCRechargeCardMessageCellContentLeftMargin - SCRechargeCardMessageCellContentRightMargin;
    CGFloat imageHeight = contentWidth / (SCRechargeCardMessageCellImageWidthHeight);
    CGSize titleSize = [UILabel labelSizeWithText:self.titleL.text font:self.titleL.font maxSize:CGSizeMake(contentWidth, 200)];

    // 内容布局（相对位置不变）
    self.titleL.frame = CGRectMake(SCRechargeCardMessageCellContentLeftMargin, SCRechargeCardMessageCellTitleTopMargin, contentWidth, titleSize.height);
    self.imageV.frame = CGRectMake(SCRechargeCardMessageCellContentLeftMargin, self.titleL.scBottom + SCRechargeCardMessageCellImageTopMargin, contentWidth, imageHeight);
    self.lineV.frame = CGRectMake(SCRechargeCardMessageCellContentLeftMargin, self.imageV.scBottom + SCRechargeCardMessageCellImageBottomMargin, contentWidth, SCRechargeCardMessageCellLineHeight);
    self.clickL.frame = CGRectMake(SCRechargeCardMessageCellContentLeftMargin, self.lineV.scBottom, contentWidth-10, SCRechargeCardMessageCellBottomHeight);
    self.arrowV.setSize(CGSizeMake(10, 10)).setCenterY(self.clickL.scCenterY);
    self.contentV.frame = CGRectMake(SCRechargeCardMessageCellContentMargin, SCRechargeCardMessageCellContentMargin, contenViewtWidth, self.clickL.scBottom);

    if (kScAuthMar.isLanguageForce) {
        // RTL语言： 头像在右侧，气泡在左侧
        self.arrowV.scLeft = self.imageV.scLeft;
        self.avatarIV.frame = CGRectMake(self.contentView.scWidth - SCRechargeCardMessageCellAvatarLeftMargin - kSCMessageAvatarWH,
                                        SCRechargeCardMessageCellAvatarTopMargin + timeTopOffset,
                                        kSCMessageAvatarWH,
                                        kSCMessageAvatarWH);
        self.bubbleV.frame = CGRectMake(self.contentView.scWidth - SCRechargeCardMessageCellAvatarLeftMargin - kSCMessageAvatarWH - SCRechargeCardMessageCellAvatarLeftMargin - bubbleWidth,
                                       SCRechargeCardMessageCellBubbleTopMargin + timeTopOffset,
                                       bubbleWidth,
                                       self.contentV.scBottom + SCRechargeCardMessageCellContentMargin);
    } else {
        // 头像在左侧，气泡在右侧
        self.arrowV.scRight = self.imageV.scRight;
        self.avatarIV.frame = CGRectMake(SCRechargeCardMessageCellAvatarLeftMargin,
                                        SCRechargeCardMessageCellAvatarTopMargin + timeTopOffset,
                                        kSCMessageAvatarWH,
                                        kSCMessageAvatarWH);
        self.bubbleV.frame = CGRectMake(kSCMessageAvatarWH + SCRechargeCardMessageCellAvatarLeftMargin + kSCMessageContentLeftMargin,
                                       SCRechargeCardMessageCellBubbleTopMargin + timeTopOffset,
                                       bubbleWidth,
                                       self.contentV.scBottom + SCRechargeCardMessageCellContentMargin);
    }
}


+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight{
    if(model == nil){
        return CGSizeZero;
    }
    
    
    
    CGFloat bubbleWidth = collectionViewWidth - (kSCMessageAvatarWH + SCRechargeCardMessageCellAvatarLeftMargin + kSCMessageContentLeftMargin)*2;
    CGFloat contenViewtWidth = bubbleWidth - SCRechargeCardMessageCellContentMargin*2;
    CGFloat contentWidth = contenViewtWidth - SCRechargeCardMessageCellContentLeftMargin - SCRechargeCardMessageCellContentRightMargin;
    CGFloat imageHeight = contentWidth / (SCRechargeCardMessageCellImageWidthHeight);
    CGSize titleSize = CGSizeZero;
    if ([model.content isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *textContent = (SCSingleJsonMessage *)model.content;
        if([textContent.contentType isEqualToString:kSCRechargeCardMessageContentType]){

            // 使用字典处理而不是YYModel
            NSDictionary *contentDict = nil;
            if ([textContent.content isKindOfClass:[NSDictionary class]]) {
                contentDict = (NSDictionary *)textContent.content;
            } else if ([textContent.content isKindOfClass:[NSString class]]) {
                contentDict = [SCDataConverter dictionaryFromJSONString:(NSString *)textContent.content];
            }

            NSString *tppTitle = [SCDictionaryHelper stringFromDictionary:contentDict forKey:@"tppTitle" defaultValue:@""];

            titleSize = [UILabel labelSizeWithText:tppTitle font:kScUIFontMedium(15) maxSize:CGSizeMake(contentWidth, 200)];
            
        }
    }
    
    CGFloat height = SCRechargeCardMessageCellContentMargin + SCRechargeCardMessageCellTitleTopMargin + titleSize.height + SCRechargeCardMessageCellImageTopMargin + imageHeight + SCRechargeCardMessageCellImageBottomMargin + SCRechargeCardMessageCellLineHeight + SCRechargeCardMessageCellBottomHeight + SCRechargeCardMessageCellContentMargin;

    // 如果需要显示时间，增加时间显示的高度
    if (model.isDisplayMessageTime) {
        height += 40;
    }

    return CGSizeMake(collectionViewWidth, height + extraHeight);
}

- (void)setModel:(SCNativeMessageModel *)model{
    _model = model;
    if ([model.content isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *textContent = (SCSingleJsonMessage *)model.content;
        if([textContent.contentType isEqualToString:kSCRechargeCardMessageContentType]){
            // 使用字典处理而不是YYModel
            NSDictionary *contentDict = nil;
            if ([textContent.content isKindOfClass:[NSDictionary class]]) {
                contentDict = (NSDictionary *)textContent.content;
            } else if ([textContent.content isKindOfClass:[NSString class]]) {
                contentDict = [SCDataConverter dictionaryFromJSONString:(NSString *)textContent.content];
            }

            NSString *tppTitle = [SCDictionaryHelper stringFromDictionary:contentDict forKey:@"tppTitle" defaultValue:@""];
            NSString *tppImageUrl = [SCDictionaryHelper stringFromDictionary:contentDict forKey:@"tppImageUrl" defaultValue:@""];

            self.titleL.setText(tppTitle);
            [self.imageV sc_setImageWithURL:tppImageUrl];
        }
    }

    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];
    } else {
        self.timeLabel.hidden = YES;
    }
}

-(void) onTapContentV{
    if(self.scDelegate && [self.scDelegate respondsToSelector:@selector(sCRechargeCardMessageCell:didTapRechargeCard:)]){
        if ([self.model.content isKindOfClass:[SCSingleJsonMessage class]]) {
            SCSingleJsonMessage *textContent = (SCSingleJsonMessage *)self.model.content;
            if([textContent.contentType isEqualToString:kSCRechargeCardMessageContentType]){
                // 使用字典处理而不是YYModel，创建临时Model对象保持兼容性
                NSDictionary *contentDict = nil;
                if ([textContent.content isKindOfClass:[NSDictionary class]]) {
                    contentDict = (NSDictionary *)textContent.content;
                } else if ([textContent.content isKindOfClass:[NSString class]]) {
                    contentDict = [SCDataConverter dictionaryFromJSONString:(NSString *)textContent.content];
                }

                SCRechargeCardMessageContentModel *model = [SCModelCompatibility modelFromDictionary:contentDict modelClass:[SCRechargeCardMessageContentModel class]];
                [self.scDelegate sCRechargeCardMessageCell:self didTapRechargeCard:model];
            }
        }
        
    }
}

#pragma mark - 头像点击事件

- (void)onAvatarTapped {
    if (self.scDelegate && [self.scDelegate respondsToSelector:@selector(scRechargeCardMessageCellDidTapAvatar:)]) {
        [self.scDelegate scRechargeCardMessageCellDidTapAvatar:self.model];
    }
}

@end
