//
//  SCImageMessageCell.h
//  Supercall
//
//  Created by AI Assistant on 2024/12/28.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN

@class SCImageMessageCell;
@protocol SCImageMessageCellDelegate <NSObject>

@optional
/// 点击图片消息
- (void)scImageMessageCellDidTapImage:(SCNativeMessageModel *)model;

/// 点击重试发送
- (void)scImageMessageCellDidTapRetry:(SCNativeMessageModel *)model;

@end

@interface SCImageMessageCell : UICollectionViewCell

@property (nonatomic, strong) SCNativeMessageModel *model;
@property (nonatomic, weak) UIImageView *avatarImageView;
@property (nonatomic, weak) UILabel *timeLabel;
@property (nonatomic, weak) id<SCImageMessageCellDelegate> scDelegate;

/// 计算Cell尺寸
+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

@end

NS_ASSUME_NONNULL_END 