//
//  SCFileMessageCell.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/18.
//

#import "SCFileMessageCell.h"
#import "SCIMUIConfig.h"
#import "SCConstant.h"
#import <Masonry/Masonry.h>
#import "SCCategoryNSDateCode.h"
#import "SCResourceManager.h"

// 文件消息的尺寸常量
static CGFloat const kSCFileMessageWidth = 240.0f;
static CGFloat const kSCFileMessageHeight = 70.0f;
static CGFloat const kSCFileIconSize = 40.0f;
static CGFloat const kSCFileContentPadding = 12.0f;

@interface SCFileMessageCell()

/// 消息内容容器
@property (nonatomic, weak) UIView *messageContentView;
/// 气泡背景
@property (nonatomic, weak) UIImageView *bubbleBackgroundView;
/// 文件图标
@property (nonatomic, weak) UIImageView *fileIconView;
/// 文件名标签
@property (nonatomic, weak) UILabel *fileNameLabel;
/// 文件大小标签
@property (nonatomic, weak) UILabel *fileSizeLabel;
/// 加载指示器
@property (nonatomic, weak) UIActivityIndicatorView *loadingIndicator;
/// 重试按钮
@property (nonatomic, weak) UIButton *retryButton;

@end

@implementation SCFileMessageCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 添加头像
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = kSCMessageAvatarWH / 2.0f;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    avatarImageView.userInteractionEnabled = YES;
    [self.contentView addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;

    // 添加头像点击手势
    UITapGestureRecognizer *avatarTapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleAvatarTap)];
    [self.avatarImageView addGestureRecognizer:avatarTapGesture];

    // 创建消息内容容器
    UIView *messageContentView = [[UIView alloc] init];
    [self.contentView addSubview:messageContentView];
    self.messageContentView = messageContentView;

    // 创建气泡背景
    UIImageView *bubbleBackgroundView = [[UIImageView alloc] init];
    bubbleBackgroundView.userInteractionEnabled = YES;
    bubbleBackgroundView.layer.cornerRadius = kSCBigCornerRadius;
    bubbleBackgroundView.layer.masksToBounds = YES;
    [self.messageContentView addSubview:bubbleBackgroundView];
    self.bubbleBackgroundView = bubbleBackgroundView;

    // 添加气泡点击手势
    UITapGestureRecognizer *bubbleTapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleFileTap)];
    [self.bubbleBackgroundView addGestureRecognizer:bubbleTapGesture];

    // 创建文件图标
    UIImageView *fileIconView = [[UIImageView alloc] init];
    fileIconView.contentMode = UIViewContentModeScaleAspectFit;
    fileIconView.image = [SCResourceManager loadImageWithName:@"ic_file_document"];
    if (!fileIconView.image) {
        // 如果没有找到文件图标，使用系统默认图标
        fileIconView.image = [UIImage systemImageNamed:@"doc.fill"];
        fileIconView.tintColor = [UIColor scWhite];
    }
    [self.bubbleBackgroundView addSubview:fileIconView];
    self.fileIconView = fileIconView;

    // 创建文件名标签
    UILabel *fileNameLabel = [[UILabel alloc] init];
    fileNameLabel.font = kScUIFontMedium(14.0f);
    fileNameLabel.textColor = [UIColor scWhite];
    fileNameLabel.numberOfLines = 2;
    fileNameLabel.lineBreakMode = NSLineBreakByTruncatingTail;
    [self.bubbleBackgroundView addSubview:fileNameLabel];
    self.fileNameLabel = fileNameLabel;

    // 创建文件大小标签
    UILabel *fileSizeLabel = [[UILabel alloc] init];
    fileSizeLabel.font = kScUIFontRegular(12.0f);
    fileSizeLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.7];
    fileSizeLabel.numberOfLines = 1;
    [self.bubbleBackgroundView addSubview:fileSizeLabel];
    self.fileSizeLabel = fileSizeLabel;

    // 创建加载指示器
    UIActivityIndicatorView *loadingIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
    loadingIndicator.hidesWhenStopped = YES;
    [self.bubbleBackgroundView addSubview:loadingIndicator];
    self.loadingIndicator = loadingIndicator;

    // 创建重试按钮
    UIButton *retryButton = [[UIButton alloc] init];
    [retryButton setImage:[SCResourceManager loadImageWithName:@"ic_retry"] forState:UIControlStateNormal];
    if (!retryButton.currentImage) {
        [retryButton setImage:[UIImage systemImageNamed:@"arrow.clockwise"] forState:UIControlStateNormal];
        retryButton.tintColor = [UIColor scWhite];
    }
    [retryButton addTarget:self action:@selector(handleRetryTap) forControlEvents:UIControlEventTouchUpInside];
    retryButton.hidden = YES;
    [self.bubbleBackgroundView addSubview:retryButton];
    self.retryButton = retryButton;

    // 创建时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.font = kScUIFontRegular(11.0f);
    timeLabel.textColor = [UIColor scGray];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.hidden = YES;
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;
}

- (void)setupConstraintsForMessageDirection:(RCMessageDirection)direction {
    // 清除之前的约束
    [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：头像在右边
            make.trailing.equalTo(self.contentView).offset(-kSCMessageAvatarLeftMargin);
        } else {
            // 接收消息：头像在左边
            make.leading.equalTo(self.contentView).offset(kSCMessageAvatarLeftMargin);
        }
        make.top.equalTo(self.contentView).offset(10);
        make.size.mas_equalTo(CGSizeMake(kSCMessageAvatarWH, kSCMessageAvatarWH));
    }];

    [self.messageContentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：内容在头像左边
            make.trailing.equalTo(self.avatarImageView.mas_leading).offset(-kSCMessageContentLeftMargin);
        } else {
            // 接收消息：内容在头像右边
            make.leading.equalTo(self.avatarImageView.mas_trailing).offset(kSCMessageContentLeftMargin);
        }
        make.top.equalTo(self.contentView).offset(10);
        make.bottom.equalTo(self.contentView).offset(-10);
        make.width.mas_equalTo(kSCFileMessageWidth);
    }];

    [self.bubbleBackgroundView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.messageContentView);
    }];

    // 文件图标约束
    [self.fileIconView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.bubbleBackgroundView).offset(kSCFileContentPadding);
        make.centerY.equalTo(self.bubbleBackgroundView);
        make.size.mas_equalTo(CGSizeMake(kSCFileIconSize, kSCFileIconSize));
    }];

    // 文件名标签约束
    [self.fileNameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.fileIconView.mas_trailing).offset(kSCFileContentPadding);
        make.trailing.equalTo(self.bubbleBackgroundView).offset(-kSCFileContentPadding);
        make.top.equalTo(self.bubbleBackgroundView).offset(kSCFileContentPadding);
    }];

    // 文件大小标签约束
    [self.fileSizeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.fileNameLabel);
        make.trailing.equalTo(self.fileNameLabel);
        make.top.equalTo(self.fileNameLabel.mas_bottom).offset(4);
    }];

    // 加载指示器约束
    [self.loadingIndicator mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.bubbleBackgroundView).offset(-kSCFileContentPadding);
        make.centerY.equalTo(self.bubbleBackgroundView);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];

    // 重试按钮约束
    [self.retryButton mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.bubbleBackgroundView).offset(-kSCFileContentPadding);
        make.centerY.equalTo(self.bubbleBackgroundView);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];

    // 时间标签约束
    [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.bottom.equalTo(self.messageContentView.mas_top).offset(-5);
        make.height.mas_equalTo(kSCMessageTimeHeight);
    }];
}

- (void)updateUIForSendStatus:(SCMessageSentStatus)status {
    switch (status) {
        case SCMessageSentStatusSending:
            self.loadingIndicator.hidden = NO;
            self.retryButton.hidden = YES;
            [self.loadingIndicator startAnimating];
            break;
        case SCMessageSentStatusSent:
            self.loadingIndicator.hidden = YES;
            self.retryButton.hidden = YES;
            [self.loadingIndicator stopAnimating];
            break;
        case SCMessageSentStatusFailed:
            self.loadingIndicator.hidden = YES;
            self.retryButton.hidden = NO;
            [self.loadingIndicator stopAnimating];
            break;
        default:
            self.loadingIndicator.hidden = YES;
            self.retryButton.hidden = YES;
            [self.loadingIndicator stopAnimating];
            break;
    }
}

- (NSString *)formatFileSize:(long long)fileSize {
    if (fileSize < 1024) {
        return [NSString stringWithFormat:@"%lld B", fileSize];
    } else if (fileSize < 1024 * 1024) {
        return [NSString stringWithFormat:@"%.1f KB", fileSize / 1024.0];
    } else if (fileSize < 1024 * 1024 * 1024) {
        return [NSString stringWithFormat:@"%.1f MB", fileSize / (1024.0 * 1024.0)];
    } else {
        return [NSString stringWithFormat:@"%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0)];
    }
}

#pragma mark - Event Handlers

- (void)handleFileTap {
    if ([self.scDelegate respondsToSelector:@selector(scFileMessageCellDidTapFile:)]) {
        [self.scDelegate scFileMessageCellDidTapFile:self.model];
    }
}

- (void)handleRetryTap {
    if ([self.scDelegate respondsToSelector:@selector(scFileMessageCellDidTapRetry:)]) {
        [self.scDelegate scFileMessageCellDidTapRetry:self.model];
    }
}

- (void)handleAvatarTap {
    if ([self.scDelegate respondsToSelector:@selector(scFileMessageCellDidTapAvatar:)]) {
        [self.scDelegate scFileMessageCellDidTapAvatar:self.model];
    }
}

#pragma mark - Setters

- (void)setModel:(SCNativeMessageModel *)model {
    _model = model;

    if ([model.content isKindOfClass:[RCFileMessage class]]) {
        RCFileMessage *fileMessage = (RCFileMessage *)model.content;

        // 根据发送状态显示不同UI
        [self updateUIForSendStatus:model.sentStatus];

        // 设置文件名
        self.fileNameLabel.text = fileMessage.name ?: @"未知文件";

        // 设置文件大小
        self.fileSizeLabel.text = [self formatFileSize:fileMessage.size];

        // 根据消息方向设置约束和样式
        [self setupConstraintsForMessageDirection:model.messageDirection];

        // 设置气泡背景颜色
        UIColor *bubbleColor;
        if (model.messageDirection == MessageDirection_SEND) {
            bubbleColor = [UIColor colorWithHexString:@"#A82F4B"];
        } else {
            bubbleColor = [UIColor colorWithHexString:@"#32312F"];
        }
        self.bubbleBackgroundView.backgroundColor = bubbleColor;
    }

    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];
    } else {
        self.timeLabel.hidden = YES;
    }
}

#pragma mark - Static Methods

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight {
    if (![model.content isKindOfClass:[RCFileMessage class]]) {
        return CGSizeMake(collectionViewWidth, 60);
    }

    RCFileMessage *fileMessage = (RCFileMessage *)model.content;

    // 计算文件名需要的高度（最多两行）
    CGFloat maxFileNameWidth = kSCFileMessageWidth - kSCFileIconSize - kSCFileContentPadding * 3; // 减去图标宽度和边距
    NSString *fileName = fileMessage.name ?: @"File";

    CGRect textRect = [fileName boundingRectWithSize:CGSizeMake(maxFileNameWidth, CGFLOAT_MAX)
                                             options:NSStringDrawingUsesLineFragmentOrigin
                                          attributes:@{NSFontAttributeName: kScUIFontMedium(14.0f)}
                                             context:nil];

    // 限制最多两行，每行大约20像素高度
    CGFloat fileNameHeight = MIN(ceil(textRect.size.height), 40.0f);

    // 计算基础高度：文件名高度 + 文件大小高度 + 内边距
    CGFloat contentHeight = fileNameHeight + 16 + kSCFileContentPadding * 2; // 16是文件大小标签的高度
    CGFloat cellHeight = MAX(contentHeight, kSCFileMessageHeight) + 20; // 确保不小于最小高度，加上上下边距

    // 如果需要显示时间，增加时间区域高度
    if (model.isDisplayMessageTime) {
        cellHeight += kSCMessageTimeHeight + 5; // 时间高度 + 间距
    }

    // 添加额外高度
    cellHeight += extraHeight;

    return CGSizeMake(collectionViewWidth, cellHeight);
}

@end
