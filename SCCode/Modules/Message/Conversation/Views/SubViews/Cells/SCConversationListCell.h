//
//  SCConversationListCell.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import <UIKit/UIKit.h>
@class SCConversationDisplayModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCConversationListCell : UITableViewCell
///在线状态圆点
@property (nonatomic, strong) UIView *onlineStatusIV;
@property (nonatomic, strong) SCConversationDisplayModel *customModel;
@property (nonatomic, strong) NSDictionary *conversationDict;

@property (nonatomic, copy) void (^tapAvatarBlock)(SCConversationDisplayModel *customModel);
@property (nonatomic, copy) void (^tapAvatarDictBlock)(NSDictionary *conversationDict);

- (void)configureWithDict:(NSDictionary *)conversationDict;

@end

NS_ASSUME_NONNULL_END
