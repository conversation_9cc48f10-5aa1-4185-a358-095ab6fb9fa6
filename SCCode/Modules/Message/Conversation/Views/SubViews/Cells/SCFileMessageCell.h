//
//  SCFileMessageCell.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/18.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN

@class SCFileMessageCell;
@protocol SCFileMessageCellDelegate <NSObject>

@optional
/// 点击文件消息
- (void)scFileMessageCellDidTapFile:(SCNativeMessageModel *)model;

/// 点击重试发送
- (void)scFileMessageCellDidTapRetry:(SCNativeMessageModel *)model;

/// 点击头像
- (void)scFileMessageCellDidTapAvatar:(SCNativeMessageModel *)model;

@end

@interface SCFileMessageCell : UICollectionViewCell

@property (nonatomic, strong) SCNativeMessageModel *model;
@property (nonatomic, weak) UIImageView *avatarImageView;
@property (nonatomic, weak) UILabel *timeLabel;
@property (nonatomic, weak) id<SCFileMessageCellDelegate> scDelegate;

/// 计算Cell尺寸
+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

@end

NS_ASSUME_NONNULL_END
