//
//  SCRechargeCardMessageCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/7.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN
@class SCRechargeCardMessageCell,SCRechargeCardMessageContentModel;
@protocol SCRechargeCardMessageCellDelegate <NSObject>

/// 点击充值卡
-(void) sCRechargeCardMessageCell:(SCRechargeCardMessageCell *)cell didTapRechargeCard:(SCRechargeCardMessageContentModel *)model;

@optional
///点击头像
-(void) scRechargeCardMessageCellDidTapAvatar:(SCNativeMessageModel *)model;

@end


@interface SCRechargeCardMessageCell : UICollectionViewCell
//头像，自定义样式，头像在 RTL 环境下不能自动调整，需要手动管理头像
@property(nonatomic,weak) UIImageView *avatarIV;
@property(nonatomic,weak) UILabel *timeLabel;
@property(nonatomic,weak) id<SCRechargeCardMessageCellDelegate> scDelegate;
@property (nonatomic, strong) SCNativeMessageModel *model;

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

@end

NS_ASSUME_NONNULL_END
