//
//  SCImageMessageCell.m
//  Supercall
//
//  Created by AI Assistant on 2024/12/28.
//

#import "SCImageMessageCell.h"
#import "SCIMUIConfig.h"
#import "SCConstant.h"
#import <Masonry/Masonry.h>
#import <SDWebImage/SDWebImage.h>
#import "SCCategoryNSDateCode.h"

// 图片消息的最小和最大尺寸
//static CGFloat const kSCImageMessageMinWidth = 120.0f;
//static CGFloat const kSCImageMessageMinHeight = 120.0f;
//static CGFloat const kSCImageMessageMaxWidth = 200.0f;
static CGFloat const kSCImageMessageWidth = 187.5f;
static CGFloat const kSCImageMessageHeight = 250.0f;

@interface SCImageMessageCell()

/// 消息内容容器
@property (nonatomic, weak) UIView *messageContentView;
/// 图片视图
@property (nonatomic, weak) UIImageView *imageView;
/// 气泡背景
@property (nonatomic, weak) UIImageView *bubbleBackgroundView;
/// 加载指示器
@property (nonatomic, weak) UIActivityIndicatorView *loadingIndicator;
/// 下载进度标签
@property (nonatomic, weak) UILabel *progressLabel;
/// 重试按钮
@property (nonatomic, weak) UIButton *retryButton;

@end

@implementation SCImageMessageCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    // 添加头像
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = kSCMessageAvatarWH / 2.0f;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;

    // 创建消息内容容器
    UIView *messageContentView = [[UIView alloc] init];
    [self.contentView addSubview:messageContentView];
    self.messageContentView = messageContentView;

    // 创建气泡背景
    UIImageView *bubbleBackgroundView = [[UIImageView alloc] init];
    bubbleBackgroundView.userInteractionEnabled = YES;
    bubbleBackgroundView.backgroundColor = [UIColor clearColor];
    [self.messageContentView addSubview:bubbleBackgroundView];
    self.bubbleBackgroundView = bubbleBackgroundView;

    // 创建图片视图
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.contentMode = UIViewContentModeScaleAspectFill;
    imageView.layer.cornerRadius = kSCBigCornerRadius;
    imageView.layer.masksToBounds = YES;
    imageView.userInteractionEnabled = YES;
    imageView.backgroundColor = [UIColor colorWithHexString:@"#2A2A2A"];
    [self.bubbleBackgroundView addSubview:imageView];
    self.imageView = imageView;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleImageTap)];
    [self.imageView addGestureRecognizer:tapGesture];

    // 创建加载指示器
    UIActivityIndicatorView *loadingIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhite];
    loadingIndicator.hidesWhenStopped = YES;
    [self.imageView addSubview:loadingIndicator];
    self.loadingIndicator = loadingIndicator;

    // 创建进度标签
    UILabel *progressLabel = [[UILabel alloc] init];
    progressLabel.textColor = [UIColor whiteColor];
    progressLabel.font = kScUIFontMedium(12.0f);
    progressLabel.textAlignment = NSTextAlignmentCenter;
    progressLabel.hidden = YES;
    [self.imageView addSubview:progressLabel];
    self.progressLabel = progressLabel;

    // 创建重试按钮
    UIButton *retryButton = [[UIButton alloc] init];
    retryButton.backgroundColor = [UIColor colorWithRed:1.0 green:0.3 blue:0.3 alpha:0.8];
    retryButton.layer.cornerRadius = 18.0f;
    retryButton.titleLabel.font = kScUIFontMedium(12.0f);
    [retryButton setTitle:@"Retry".translateString forState:UIControlStateNormal];
    [retryButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [retryButton addTarget:self action:@selector(handleRetryTap) forControlEvents:UIControlEventTouchUpInside];
    retryButton.hidden = YES;
    [self.imageView addSubview:retryButton];
    self.retryButton = retryButton;

    // 添加时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.font = [UIFont systemFontOfSize:12];
    timeLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.hidden = YES;
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;

    [self setupConstraints];
}

- (void)setupConstraints {
    // 加载指示器约束
    [self.loadingIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.imageView);
    }];

    // 进度标签约束
    [self.progressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.imageView);
        make.top.equalTo(self.loadingIndicator.mas_bottom).offset(8);
    }];

    // 重试按钮约束
    [self.retryButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.imageView);
        make.width.mas_equalTo(80);
        make.height.mas_equalTo(40);
    }];
    
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(kSCMessageAvatarLeftMargin);
        make.top.equalTo(self.contentView).offset(10);
        make.width.height.mas_equalTo(kSCMessageAvatarWH);
    }];

    // 图片视图约束
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.bubbleBackgroundView).insets(UIEdgeInsetsMake(0, 0, 0, 0));
        make.width.mas_equalTo(kSCImageMessageWidth);
        make.height.mas_equalTo(kSCImageMessageHeight);
    }];

    // 气泡背景约束
    [self.bubbleBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.messageContentView);
    }];

    // 时间标签约束
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(30);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
   
}

- (void)setupConstraintsForMessageDirection:(RCMessageDirection)direction {
    // 根据消息方向调整布局
    BOOL shouldShowOnRight = (direction == MessageDirection_SEND);

    CGFloat avatarMargin = kSCMessageAvatarLeftMargin;
    CGFloat messageMargin = kSCMessageContentLeftMargin;

    if (shouldShowOnRight) {
        // 我的消息显示在右侧
        [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.contentView).offset(-avatarMargin);
            // 如果需要显示时间，头像位置下移
            if (self.model.isDisplayMessageTime) {
                make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
            } else {
                make.top.equalTo(self.contentView).offset(10);
            }
            make.width.height.mas_equalTo(kSCMessageAvatarWH);
        }];
        
        [self.messageContentView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.avatarImageView.mas_leading).offset(-messageMargin);
            // 如果需要显示时间，消息内容位置下移
            if (self.model.isDisplayMessageTime) {
                make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
            } else {
                make.top.equalTo(self.contentView).offset(10);
            }
            make.bottom.lessThanOrEqualTo(self.contentView);
        }];
    } else {
        // 对方消息显示在左侧
        [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.contentView).offset(avatarMargin);
            // 如果需要显示时间，头像位置下移
            if (self.model.isDisplayMessageTime) {
                make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
            } else {
                make.top.equalTo(self.contentView).offset(10);
            }
            make.width.height.mas_equalTo(kSCMessageAvatarWH);
        }];
        
        [self.messageContentView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.avatarImageView.mas_trailing).offset(messageMargin);
            // 如果需要显示时间，消息内容位置下移
            if (self.model.isDisplayMessageTime) {
                make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
            } else {
                make.top.equalTo(self.contentView).offset(10);
            }
            make.bottom.lessThanOrEqualTo(self.contentView);
        }];
    }
}

- (void)setModel:(SCNativeMessageModel *)model {
    _model = model;
    
    if ([model.content isKindOfClass:[RCImageMessage class]]) {
        RCImageMessage *imageMessage = (RCImageMessage *)model.content;
        
        // 根据发送状态显示不同UI
        [self updateUIForSendStatus:model.sentStatus];
        
        UIImage *image = imageMessage.originalImage;
        
        // 设置占位图
        self.imageView.image =  image ?:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"];
        
        // 只在发送成功或接收消息时加载图片
        if (imageMessage.imageUrl != nil && imageMessage.imageUrl.length > 0 & image == nil) {
            [self loadImageWithURL:imageMessage.imageUrl];
        }
    }

    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];
    } else {
        self.timeLabel.hidden = YES;
    }
    
    [self setupConstraintsForMessageDirection:self.model.messageDirection];
}

- (void)handleImageTap {
    if ([self.scDelegate respondsToSelector:@selector(scImageMessageCellDidTapImage:)]) {
        [self.scDelegate scImageMessageCellDidTapImage:self.model];
    }
}

- (void)handleRetryTap {
    if ([self.scDelegate respondsToSelector:@selector(scImageMessageCellDidTapRetry:)]) {
        [self.scDelegate scImageMessageCellDidTapRetry:self.model];
    }
}

- (void)updateUIForSendStatus:(SCMessageSentStatus)status {
    switch (status) {
        case SCMessageSentStatusSending:
            // 发送中：显示加载指示器
            [self.loadingIndicator startAnimating];
            self.progressLabel.hidden = NO;
            self.progressLabel.text = @"Sending…".translateString;
            self.retryButton.hidden = YES;
            break;
            
        case SCMessageSentStatusSent:
            // 发送成功：隐藏所有状态指示器
            [self.loadingIndicator stopAnimating];
            self.progressLabel.hidden = YES;
            self.retryButton.hidden = YES;
            break;
            
        case SCMessageSentStatusFailed:
            // 发送失败：显示重试按钮
            [self.loadingIndicator stopAnimating];
            self.progressLabel.hidden = YES;
            self.retryButton.hidden = NO;
            break;
            
        default:
            // 未知状态：隐藏所有指示器
            [self.loadingIndicator stopAnimating];
            self.progressLabel.hidden = YES;
            self.retryButton.hidden = YES;
            break;
    }
}

- (void)loadImageWithURL:(NSString *)imageUrl {
    kWeakSelf(self);
    [self.imageView sd_setImageWithURL:[NSURL URLWithString:imageUrl]
                      placeholderImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"]
                               options:SDWebImageRetryFailed
                              progress:^(NSInteger receivedSize, NSInteger expectedSize, NSURL * _Nullable targetURL) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (expectedSize > 0) {
                CGFloat progress = (CGFloat)receivedSize / (CGFloat)expectedSize;
                weakself.progressLabel.text = [NSString stringWithFormat:@"%.0f%%", progress * 100];
            }
        });
    } completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error) {
                // 加载失败，显示错误图标
                weakself.imageView.image = [SCResourceManager loadImageWithName:@"ic_image_load_failed"];
            }
        });
    }];
}



+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight {
    if (![model.content isKindOfClass:[RCImageMessage class]]) {
        return CGSizeMake(collectionViewWidth, 60);
    }
    
    RCImageMessage *imageMessage = (RCImageMessage *)model.content;
    
    // 获取图片原始尺寸
    CGSize originalSize = CGSizeMake(kSCImageMessageWidth,kSCImageMessageHeight);
    
//    // 如果有原始尺寸信息，使用它
//    if (imageMessage.originalImage) {
//        originalSize = imageMessage.originalImage.size;
//    }
    
    

    
    // 加上头像和边距
    CGFloat totalWidth = collectionViewWidth;
    CGFloat totalHeight = MAX(originalSize.height + extraHeight, kSCMessageAvatarWH + 10 * 2);

    // 如果需要显示时间，增加时间显示的高度
    if (model.isDisplayMessageTime) {
        totalHeight += 40;
    }

    return CGSizeMake(totalWidth, totalHeight);
}

@end 
