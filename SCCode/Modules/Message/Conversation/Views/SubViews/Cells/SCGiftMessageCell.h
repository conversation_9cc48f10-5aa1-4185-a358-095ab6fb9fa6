//
//  SCGiftMessageCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCGiftMessageCell : UICollectionViewCell

@property (nonatomic, strong) SCNativeMessageModel *model;
@property (nonatomic, strong) NSDictionary *messageDict;
@property (nonatomic, weak) UILabel *timeLabel;

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;
+ (CGSize)sizeForMessageDict:(NSDictionary *)messageDict withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

// 字典支持方法
- (void)configureWithMessageDict:(NSDictionary *)messageDict;

@end

NS_ASSUME_NONNULL_END
