//
//  SCConversationListCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCConversationListCell.h"
#import <RongIMLib/RongIMLib.h>
#import "SCConversationDisplayModel.h"
#import "SCSingleJsonMessage.h"
#import "SCHyperLinkMessage.h"
#import "SCIMUIConfig.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCConversationListCell ()

@property (nonatomic, strong) UIImageView *avatarImgView;
@property (nonatomic, strong) UILabel *nameLb;
//@property (nonatomic, strong) UIImageView *systemIconImgView;
@property (nonatomic, strong) UILabel *contentLb;
@property (nonatomic, strong) UILabel *redLb;
@property (nonatomic, strong) UILabel *timeLb;
@property (nonatomic, strong) UIImageView *gradientBgImgView;
@property (nonatomic, strong) UIView *lineV;

- (void)createUI;

@end

@implementation SCConversationListCell


- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self createUI];
    }
    return self;
}

- (void)setCustomModel:(SCConversationDisplayModel *)model {
    if (!model) return;
    _customModel = model;
    if (model && [model hasUserInfo]) {
        // 使用便捷访问方法，自动处理字典和Model的兼容性
        [self.avatarImgView sc_setImageWithURL:[model getUserAvatarUrl] placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        self.nameLb.text = [model getUserNickname];
    }
//    self.systemIconImgView.hidden = !model.isSystem;
    
    self.redLb.text = model.unReadDisplayStr;
    self.redLb.hidden = !model.isShowUnRead;
    ///格式化
    self.timeLb.text = [[[NSDate alloc] initWithTimeIntervalSince1970:model.conversation.sentTime/1000.0] formattedTimeAgo];
    CGSize timeSize = [self.timeLb sizeThatFits:CGSizeMake(150, 20)];
    [self.timeLb mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(timeSize.width);
    }];
    
    self.gradientBgImgView.hidden = !model.conversation.isTop;
    self.lineV.hidden = model.conversation.isTop;
    
    if (!model.conversation.lastestMessage) {
        self.contentLb.text = @"";
        return;
    }
    RCMessageContent *messageContent = model.conversation.lastestMessage;
    
    if ([messageContent isKindOfClass:[RCTextMessage class]]) {
        RCTextMessage *textContent = (RCTextMessage *)messageContent;
        // 处理文本消息类型
        self.contentLb.text = textContent.content;
    }else if ([messageContent isKindOfClass:[SCHyperLinkMessage class]]){
        // 处理文本消息类型
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"Link".translateString];
    } else if ([messageContent isKindOfClass:[RCImageMessage class]]) {
        // 处理图片消息类型
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"image".translateString];
    } else if ([messageContent isKindOfClass:[RCVoiceMessage class]]) {
        // 处理语音消息类型
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"voice".translateString];
    } else if ([messageContent isKindOfClass:[RCHQVoiceMessage class]]) {
        // 处理语音消息类型
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"voice".translateString];
    } else if ([messageContent isKindOfClass:[RCFileMessage class]]) {
        // 处理位置消息类型
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"file".translateString];
    } else if ([messageContent isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *giftContent = (SCSingleJsonMessage *)messageContent;
        // 处理位置消息类型
        if ([giftContent.contentType isEqualToString:@"tpp"]) {
            self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"Recharge Card".translateString];
        } else if ([giftContent.contentType isEqualToString:@"gift"]) {
            self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"gift".translateString];
        } else {
            self.contentLb.text = @"";
        }
    } else {
        // 处理其他消息类型或未知类型,处理自定义会话：礼物显示"[file]";
        self.contentLb.text = [NSString stringWithFormat:@"[%@]", @"unknown".translateString];
    }
}
- (void)createUI {
    self.contentView.backgroundColor = [UIColor scGlobalBgColor];
    
    UIImage *gradientImage = [UIImage imageGradientWithSCGradientColors:[SCGradientColors themeColorWithOrientation:SCGradientColorsOrientationHorizontal] size:CGSizeMake(self.scWidth, 86.0f)];
    self.gradientBgImgView = [[UIImageView alloc]initWithImage:gradientImage];
    self.gradientBgImgView.alpha = 0.15;
    self.gradientBgImgView.hidden = YES;
    
    self.avatarImgView = [[UIImageView alloc] init];
    self.avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    self.avatarImgView.layer.masksToBounds = YES;
    self.avatarImgView.layer.cornerRadius = (kSCConversationAvatarWH / 2.0);
    
    UIView *contentContainView = [[UIView alloc] init];
    
    self.nameLb = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontSemibold(14)];
    self.nameLb.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
    self.nameLb.numberOfLines = 1;
    
//    self.systemIconImgView = [UIImageView new].setImageName(@"ic_system_avatar");
    
    self.contentLb = [UILabel labelWithText:@"" textColor:[UIColor scGray] font:kScUIFontMedium(12)];
    self.contentLb.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
    self.contentLb.numberOfLines = 1;
    
    self.redLb = [UILabel labelWithText:@"99+" textColor:[UIColor whiteColor] font:kScUIFontMedium(13) alignment:NSTextAlignmentCenter];
    self.redLb.setBackgroundColor(UIColor.scRed)
        .setCornerRadius(19.0 / 2.0);
    self.redLb.adjustsFontSizeToFitWidth = YES;
    self.redLb.layer.masksToBounds = YES;
    
    self.timeLb = [UILabel labelWithText:@"" textColor:[UIColor scGray] font:kScUIFontRegular(12) alignment: kScAuthMar.isLanguageForce ? NSTextAlignmentLeft : NSTextAlignmentRight];
    
    UIView *lineView = [[UIView alloc]init];
    lineView.backgroundColor = [UIColor.scWhite colorWithAlphaComponent:0.1];
    self.lineV = lineView;
    
    [self.contentView addSubview:self.gradientBgImgView];
    [self.contentView addSubview:self.avatarImgView];
    [self.contentView addSubview:self.redLb];
    [self.contentView addSubview:self.timeLb];
    [self.contentView addSubview:self.lineV];
    
    [self.gradientBgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(26.0);
        make.centerY.equalTo(self.contentView);
        make.width.height.equalTo(@(kSCConversationAvatarWH));
    }];
    
    [self.contentView addSubview:contentContainView];
    [contentContainView addSubview:self.nameLb];
//    [contentContainView addSubview:self.systemIconImgView];
    [contentContainView addSubview:self.contentLb];
    
    [contentContainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarImgView.mas_trailing).offset(7.0);
        make.centerY.equalTo(self.contentView);
        make.trailing.lessThanOrEqualTo(self.timeLb.mas_leading).offset(-20.0);
    }];
    
    [self.nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(contentContainView);
        make.top.equalTo(contentContainView);
        make.trailing.equalTo(self.timeLb.mas_leading).offset(-5);
    }];
    
//    [self.systemIconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.leading.equalTo(self.nameLb.mas_trailing).offset(5.0);
//        make.centerY.equalTo(self.nameLb.mas_centerY);
//    }];
    
    [self.contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nameLb.mas_bottom).offset(5.0);
        make.leading.equalTo(contentContainView);
        make.trailing.lessThanOrEqualTo(contentContainView);
        make.bottom.equalTo(contentContainView);
    }];
    
    [self.timeLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.nameLb);
        make.trailing.equalTo(self.contentView).offset(-16.0);
        make.width.greaterThanOrEqualTo(@(65.0));
    }];
    
    [self.redLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentLb);
        make.trailing.equalTo(self.contentView).offset(-16.0);
        make.width.height.equalTo(@(19.0));
    }];
    
    _onlineStatusIV = [UIView new].setCornerRadius(6.0f).addSuperView(self.contentView);
    [self.onlineStatusIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(12.0f, 12.0f));
        make.bottom.equalTo(self.avatarImgView);
        make.trailing.equalTo(self.avatarImgView).inset(4.0f);
    }];
    
    [self.lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView).offset(-1.0f);
        make.leading.equalTo(self.contentView).offset(74.0f);
        make.trailing.equalTo(self.contentView).offset(-15.0f);
        make.height.mas_equalTo(1.0f);
    }];
    
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapAvatarAction)];
    self.avatarImgView.userInteractionEnabled = YES;
    [self.avatarImgView addGestureRecognizer:tap];
}

- (void)tapAvatarAction {
    if (self.tapAvatarDictBlock && self.conversationDict) {
        self.tapAvatarDictBlock(self.conversationDict);
    } else if (self.tapAvatarBlock && self.customModel) {
        self.tapAvatarBlock(self.customModel);
    }
}

- (void)configureWithDict:(NSDictionary *)conversationDict {
    self.conversationDict = conversationDict;

    // 获取用户信息字典
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:conversationDict forKey:@"userInfo" defaultValue:@{}];

    // 设置头像和昵称
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    [self.avatarImgView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    self.nameLb.text = nickname;

//    // 设置系统图标
//    BOOL isSystem = [SCDictionaryHelper boolFromDictionary:conversationDict forKey:@"isSystem" defaultValue:NO];
//    self.systemIconImgView.hidden = !isSystem;

    // 设置未读消息
    NSInteger unreadCount = [SCDictionaryHelper integerFromDictionary:conversationDict forKey:@"unreadMessageCount" defaultValue:0];
    NSString *unReadDisplayStr = unreadCount > 99 ? @"99+" : [NSString stringWithFormat:@"%ld", (long)unreadCount];
    self.redLb.text = unReadDisplayStr;
    self.redLb.hidden = !(unreadCount > 0);

    // 设置时间
    NSTimeInterval sentTime = [SCDictionaryHelper doubleFromDictionary:conversationDict forKey:@"sentTime" defaultValue:0];
    self.timeLb.text = [[[NSDate alloc] initWithTimeIntervalSince1970:sentTime/1000.0] formattedTimeAgo];
    CGSize timeSize = [self.timeLb sizeThatFits:CGSizeMake(150, 20)];
    [self.timeLb mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(timeSize.width);
    }];

    // 设置置顶状态
    BOOL isTop = [SCDictionaryHelper boolFromDictionary:conversationDict forKey:@"isTop" defaultValue:NO];
    self.gradientBgImgView.hidden = !isTop;
    self.lineV.hidden = isTop;

    // 设置最后一条消息内容（这里简化处理，实际可能需要更复杂的逻辑）
    NSString *lastMessageContent = [SCDictionaryHelper stringFromDictionary:conversationDict forKey:@"lastMessageContent" defaultValue:@""];
    self.contentLb.text = lastMessageContent;
}

@end
