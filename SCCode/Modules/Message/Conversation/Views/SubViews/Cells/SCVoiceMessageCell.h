//
//  SCVoiceMessageCell.h
//  Supercall
//
//  Created by sumengliu on 2024/12/25.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"
#import "SCVoicePlayerManager.h"

NS_ASSUME_NONNULL_BEGIN

// 语音气泡宽度相关常量
static const CGFloat kMaxVoiceDuration = 60.0f;  // 最大语音时长60秒
static const CGFloat kAudioBubbleMinWidth = 70.0f;  // 最小气泡宽度
static const CGFloat kAudioBubbleMaxWidth = 180.0f; // 最大气泡宽度

@class SCVoiceMessageCell;
@protocol SCVoiceMessageCellDelegate <NSObject>

@optional
///点击头像
-(void) scVoiceMessageCellDidTapAvatar:(SCNativeMessageModel *)model;

@end

@interface SCVoiceMessageCell : UICollectionViewCell

@property (nonatomic, strong) SCNativeMessageModel *model;
@property (nonatomic, weak) UIImageView *avatarImageView;
@property (nonatomic, weak) UIImageView *playVoiceView;
@property (nonatomic, weak) UILabel *voiceDurationLabel;
@property (nonatomic, weak) UIImageView *bubbleBackgroundView;
@property (nonatomic, weak) UILabel *timeLabel;
@property (nonatomic, weak) UIView *unplayedDotView; // 未播放红点视图
@property (nonatomic, weak) id<SCVoiceMessageCellDelegate> scDelegate;

// 动画相关属性
@property (nonatomic, assign) NSInteger animationIndex;
@property (nullable, nonatomic, strong) NSTimer *animationTimer;

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

// 根据语音时长计算气泡宽度
+ (CGFloat)getBubbleWidthForModel:(SCNativeMessageModel *)model;

@end

NS_ASSUME_NONNULL_END
