//
//  SCVoiceMessageCell.m
//  Supercall
//
//  Created by sumeng<PERSON><PERSON> on 2024/12/25.
//
//  架构说明：
//  本Cell使用通知机制监听语音播放状态，解决了Cell复用导致的messageId不一致问题。
//  - 在initWithFrame中注册通知监听
//  - 在通知处理方法中根据messageId过滤相关消息
//  - 在dealloc中自动注销通知监听
//  - 保留原有代理方法实现，确保向后兼容
//

#import "SCVoiceMessageCell.h"
#import "SCResourceManager.h"
#import "SCIMUIConfig.h"
#import "SCVoicePlayerManager.h"
#import <RongIMLib/RongIMLib.h>
#import "SCCategoryNSDateCode.h"

@implementation SCVoiceMessageCell

#pragma mark - Private Methods

- (CGFloat)getBubbleWidth {
    CGFloat duration = 0;

    // 获取语音时长
    if ([self.model.content isKindOfClass:[RCVoiceMessage class]]) {
        RCVoiceMessage *voiceMessage = (RCVoiceMessage *)self.model.content;
        duration = voiceMessage.duration;
    } else if ([self.model.content isKindOfClass:[RCHQVoiceMessage class]]) {
        RCHQVoiceMessage *hqVoiceMessage = (RCHQVoiceMessage *)self.model.content;
        duration = hqVoiceMessage.duration;
    }

    // 限制最大时长
    if (duration > kMaxVoiceDuration) {
        duration = kMaxVoiceDuration;
    }

    // 根据时长计算宽度：线性插值
    CGFloat ratio = duration / kMaxVoiceDuration;
    CGFloat width = kAudioBubbleMinWidth + (kAudioBubbleMaxWidth - kAudioBubbleMinWidth) * ratio;

    // 确保宽度在合理范围内
    width = MAX(kAudioBubbleMinWidth, MIN(kAudioBubbleMaxWidth, width));

    return width;
}

#pragma mark - Lifecycle

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.animationIndex = 0;
        [self _initUI];
        [self setupNotificationObservers];
    }
    return self;
}

- (void)setupNotificationObservers {
    // 注册语音播放状态变化通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleVoicePlayerStateChange:)
                                                 name:SCVoicePlayerStateDidChangeNotification
                                               object:nil];

    // 注册语音下载进度更新通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleVoicePlayerProgressUpdate:)
                                                 name:SCVoicePlayerDownloadProgressDidUpdateNotification
                                               object:nil];
}

- (void)prepareForReuse {
    [super prepareForReuse];
    // 清理动画状态
    [self disableCurrentAnimationTimer];
    // 注意：不再需要设置代理，因为使用通知机制
}

- (void)dealloc {
    // 注销通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)_initUI {
    // 创建头像
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = kSCMessageAvatarWH / 2.0;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    avatarImageView.userInteractionEnabled = YES;
    [self.contentView addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;

    // 添加头像点击手势
    UITapGestureRecognizer *avatarTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onAvatarTapped)];
    [avatarImageView addGestureRecognizer:avatarTap];

    // 创建气泡背景
    UIImageView *bubbleBackgroundView = [[UIImageView alloc] init];
    bubbleBackgroundView.userInteractionEnabled = YES;
    [self.contentView addSubview:bubbleBackgroundView];
    self.bubbleBackgroundView = bubbleBackgroundView;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onTapVoiceBubble)];
    [bubbleBackgroundView addGestureRecognizer:tapGesture];

    // 创建语音播放图标
    UIImageView *playVoiceView = [[UIImageView alloc] init];
    [self.contentView addSubview:playVoiceView];
    self.playVoiceView = playVoiceView;

    // 创建语音时长标签
    UILabel *voiceDurationLabel = [[UILabel alloc] init];
    voiceDurationLabel.textColor = UIColor.scWhite;
    voiceDurationLabel.font = [UIFont systemFontOfSize:14];
    [self.contentView addSubview:voiceDurationLabel];
    self.voiceDurationLabel = voiceDurationLabel;

    // 添加时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.font = [UIFont systemFontOfSize:12];
    timeLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.hidden = YES;
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;

    // 创建未播放红点视图
    UIView *unplayedDotView = [[UIView alloc] init];
    unplayedDotView.backgroundColor = [UIColor redColor];
    unplayedDotView.layer.cornerRadius = 4.0; // 半径为4，直径为8
    unplayedDotView.layer.masksToBounds = YES;
    unplayedDotView.hidden = YES; // 初始隐藏
    [self.contentView addSubview:unplayedDotView];
    self.unplayedDotView = unplayedDotView;

    // 设置约束
    [self setupConstraints];
}

- (void)setupConstraints {
    // 头像约束 - 初始设置，会在setupConstraintsForMessageDirection中重新设置
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(kSCMessageAvatarLeftMargin);
        make.top.equalTo(self.contentView).offset(10);
        make.width.height.mas_equalTo(kSCMessageAvatarWH);
    }];

    // 基础约束设置，具体布局在setDataModel中根据消息方向调整
    [self.bubbleBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(120);
        make.height.mas_equalTo(40);
        make.centerY.equalTo(self.contentView);
    }];

}

- (void)setupConstraintsForMessageDirection:(RCMessageDirection)direction {
    // 根据消息方向调整头像位置
    [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：头像在右侧
            make.trailing.equalTo(self.contentView).offset(-kSCMessageAvatarLeftMargin);
        } else {
            // 接收消息：头像在左侧
            make.leading.equalTo(self.contentView).offset(kSCMessageAvatarLeftMargin);
        }
        // 如果需要显示时间，头像位置下移
        if (self.model.isDisplayMessageTime) {
            make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(10);
        }
        make.width.height.mas_equalTo(kSCMessageAvatarWH);
    }];

    // 根据消息方向调整气泡位置
    CGFloat bubbleWidth = [self getBubbleWidth];
    [self.bubbleBackgroundView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：气泡在头像左侧
            make.trailing.equalTo(self.avatarImageView.mas_leading).offset(-kSCMessageContentLeftMargin);
        } else {
            // 接收消息：气泡在头像右侧
            make.leading.equalTo(self.avatarImageView.mas_trailing).offset(kSCMessageContentLeftMargin);
        }
        make.width.mas_equalTo(bubbleWidth);
        make.height.mas_equalTo(40);
        // 如果需要显示时间，气泡位置下移
        if (self.model.isDisplayMessageTime) {
            make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(10);
        }
    }];

    // 根据消息方向调整播放图标和时长标签位置
    [self.playVoiceView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：播放图标在右侧
            make.trailing.equalTo(self.bubbleBackgroundView).offset(-10);
        } else {
            // 接收消息：播放图标在左侧
            make.leading.equalTo(self.bubbleBackgroundView).offset(10);
        }
        make.centerY.equalTo(self.bubbleBackgroundView);
        make.width.height.mas_equalTo(20);
    }];

    // 设置红点视图约束（只在接收消息时显示）
    [self.unplayedDotView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_RECEIVE) {
            // 接收消息：红点在播放图标左侧
            make.leading.equalTo(self.bubbleBackgroundView.mas_trailing).offset(5);
            make.centerY.equalTo(self.bubbleBackgroundView);
        } else {
            // 发送消息：红点在播放图标右侧（但通常不显示）
            make.leading.equalTo(self.playVoiceView.mas_trailing).offset(5);
            make.centerY.equalTo(self.playVoiceView);
        }
        make.width.height.mas_equalTo(8);
    }];

    [self.voiceDurationLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：时长在左侧
            make.trailing.equalTo(self.playVoiceView.mas_leading).offset(-10);
        } else {
            // 接收消息：时长在右侧
            make.leading.equalTo(self.playVoiceView.mas_trailing).offset(10);
        }
        make.centerY.equalTo(self.bubbleBackgroundView);
    }];

    // 时间标签约束
    [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(30);
    }];
}

- (void)setModel:(SCNativeMessageModel *)model {
    _model = model;

    // 设置语音时长（需要在setupConstraintsForMessageDirection之前设置，因为约束计算需要用到时长）
    if ([model.content isKindOfClass:[RCVoiceMessage class]]) {
        RCVoiceMessage *voiceMessage = (RCVoiceMessage *)model.content;
        self.voiceDurationLabel.text = [NSString stringWithFormat:@"%ld\"", (long)voiceMessage.duration];
    } else if ([model.content isKindOfClass:[RCHQVoiceMessage class]]) {
        RCHQVoiceMessage *hqVoiceMessage = (RCHQVoiceMessage *)model.content;
        self.voiceDurationLabel.text = [NSString stringWithFormat:@"%ld\"", (long)hqVoiceMessage.duration];
    } else {
        self.voiceDurationLabel.text = @"0\"";
    }

    // 根据消息方向和语音时长设置约束
    [self setupConstraintsForMessageDirection:model.messageDirection];

    // 头像将在Controller中设置，这里不需要设置

    // 设置语音播放图标
    NSString *voiceIconName = (model.messageDirection == MessageDirection_SEND) ? @"to_voice_3" : @"from_voice_3";
    self.playVoiceView.image = [[SCResourceManager loadImageWithName:voiceIconName isAutoForce:YES] imageWithTintColor:UIColor.scWhite];

    // 设置样式
    self.voiceDurationLabel.textColor = UIColor.scWhite;
    UIColor *bubbleColor = [UIColor colorWithHexString:@"#32312F"];
    if(model.messageDirection == MessageDirection_SEND){
        bubbleColor = [UIColor colorWithHexString:@"#A82F4B"];
    }

    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];
    } else {
        self.timeLabel.hidden = YES;
    }

    // 使用动态计算的气泡宽度创建背景图片
    CGFloat bubbleWidth = [self getBubbleWidth];
    UIImage *bubbleImage = [[UIImage imageWithColor:bubbleColor imageSize:CGSizeMake(bubbleWidth, 40) cornerRadius:kSCBigCornerRadius] resizableImageWithCapInsets:UIEdgeInsetsMake(kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius) resizingMode:UIImageResizingModeStretch];
    self.bubbleBackgroundView.image = bubbleImage;

    // 设置红点显示状态（只有接收的语音消息才显示红点）
    [self updateUnplayedDotVisibility];
}

- (void)scheduleAnimationOperation {
    // 递增动画索引
    self.animationIndex++;

    // 计算图像索引 (1, 2, 3, 0 循环)
    NSInteger imageIndex = self.animationIndex % 4;
    if (imageIndex == 0) imageIndex = 3; // 确保索引在1-3之间

    // 根据消息方向和动画索引选择图标
    NSString *playingIndicatorIndex;
    if (self.model.messageDirection == MessageDirection_SEND) {
        playingIndicatorIndex = [NSString stringWithFormat:@"to_voice_%ld", (long)imageIndex];
    } else {
        playingIndicatorIndex = [NSString stringWithFormat:@"from_voice_%ld", (long)imageIndex];
    }

    UIImage *image = [SCResourceManager loadImageWithName:playingIndicatorIndex isAutoForce:YES];

    self.playVoiceView.image = [image imageWithTintColor:UIColor.scWhite];
}

- (void)disableCurrentAnimationTimer {
    // 停止并清理定时器
    if (self.animationTimer && [self.animationTimer isValid]) {
        [self.animationTimer invalidate];
        self.animationTimer = nil;
    }

    // 停止动画，重置动画索引
    self.animationIndex = 0;

    // 恢复到静态图标
    NSString *playingIndicatorIndex;
    if (self.model.messageDirection == MessageDirection_SEND) {
        playingIndicatorIndex = @"to_voice_3";
    } else {
        playingIndicatorIndex = @"from_voice_3";
    }

    UIImage *image = [SCResourceManager loadImageWithName:playingIndicatorIndex isAutoForce:YES];

    if (kScAuthMar.isLanguageForce) {
        image = [image imageFlippedForRightToLeftLayoutDirection];
    }

    self.playVoiceView.image = [image imageWithTintColor:UIColor.scWhite];
}

- (void)startVoiceAnimation {
    // 先停止之前的动画
    [self disableCurrentAnimationTimer];

    // 重置动画索引
    self.animationIndex = 0;

    // 启动定时器，每0.3秒执行一次动画
    self.animationTimer = [NSTimer scheduledTimerWithTimeInterval:0.3
                                                           target:self
                                                         selector:@selector(scheduleAnimationOperation)
                                                         userInfo:nil
                                                          repeats:YES];
}

- (void)stopVoiceAnimation {
    [self disableCurrentAnimationTimer];
}

- (void)updateUnplayedDotVisibility {
    // 只有接收的语音消息才可能显示红点
    if (self.model.messageDirection != MessageDirection_RECEIVE) {
        self.unplayedDotView.hidden = YES;
        return;
    }

    // 使用融云API检查是否已听过
    kWeakSelf(self);
    [[SCVoicePlayerManager sharedManager] checkMessageListenedStatus:self.model.messageId completion:^(BOOL isListened) {
        // 确保在主线程更新UI
        dispatch_async(dispatch_get_main_queue(), ^{
            // 检查cell是否还对应同一个消息（防止cell复用导致的问题）
            if (weakself.model.messageId == self.model.messageId) {
                weakself.unplayedDotView.hidden = isListened;
            }
        });
    }];
}

- (void)onTapVoiceBubble {
    // 使用语音播放管理器播放语音
    if ([self.model.content isKindOfClass:[RCVoiceMessage class]] || [self.model.content isKindOfClass:[RCHQVoiceMessage class]]) {

        [[SCVoicePlayerManager sharedManager] playVoiceMessage:self.model];
    }
}

#pragma mark - 通知处理方法

- (void)handleVoicePlayerStateChange:(NSNotification *)notification {
    long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];

    // 只处理当前Cell对应的消息
    if (self.model.messageId != messageId) {
        return;
    }

    SCVoicePlayState state = [notification.userInfo[SCVoicePlayerNotificationStateKey] integerValue];
    NSError *error = notification.userInfo[SCVoicePlayerNotificationErrorKey];

    // 调用原有的处理逻辑
    [self voicePlayerManager:[SCVoicePlayerManager sharedManager] didChangePlayState:state forMessageId:messageId error:error];
}

- (void)handleVoicePlayerProgressUpdate:(NSNotification *)notification {
    long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];

    // 只处理当前Cell对应的消息
    if (self.model.messageId != messageId) {
        return;
    }

    float progress = [notification.userInfo[SCVoicePlayerNotificationProgressKey] floatValue];

    // 调用原有的处理逻辑
    [self voicePlayerManager:[SCVoicePlayerManager sharedManager] didUpdateDownloadProgress:progress forMessageId:messageId];
}

#pragma mark - SCVoicePlayerManagerDelegate

- (void)voicePlayerManager:(id)manager didChangePlayState:(SCVoicePlayState)state forMessageId:(long)messageId error:(nullable NSError *)error {
    // 只处理当前Cell对应的消息
    if (self.model.messageId != messageId) {
        return;
    }

    switch (state) {
        case SCVoicePlayStateDownloading:
            
            // 可以在这里显示下载进度指示器
            break;

        case SCVoicePlayStatePlaying:

            [self startVoiceAnimation];
            // 开始播放时立即隐藏红点
            [self updateUnplayedDotVisibility];
            break;

        case SCVoicePlayStatePaused:
            
            [self stopVoiceAnimation];
            break;

        case SCVoicePlayStateCompleted:

            [self stopVoiceAnimation];
            break;

        case SCVoicePlayStateError:
            
            [self stopVoiceAnimation];
            // 可以在这里显示错误提示
            break;

        case SCVoicePlayStateIdle:
        default:
            [self stopVoiceAnimation];
            break;
    }
}

- (void)voicePlayerManager:(id)manager didUpdateDownloadProgress:(float)progress forMessageId:(long)messageId {
    // 只处理当前Cell对应的消息
    if (self.model.messageId != messageId) {
        return;
    }

    
    // 可以在这里更新下载进度UI
}

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight {
    // 语音消息的高度：头像高度 + 上下边距(10 + 10)
    CGFloat cellHeight = kSCMessageAvatarWH + 20; // 头像高度 + 上下边距

    // 如果需要显示时间，增加时间显示的高度
    if (model.isDisplayMessageTime) {
        cellHeight += 40;
    }

    return CGSizeMake(collectionViewWidth, extraHeight + cellHeight);
}

+ (CGFloat)getBubbleWidthForModel:(SCNativeMessageModel *)model {
    CGFloat duration = 0;

    // 获取语音时长
    if ([model.content isKindOfClass:[RCVoiceMessage class]]) {
        RCVoiceMessage *voiceMessage = (RCVoiceMessage *)model.content;
        duration = voiceMessage.duration;
    } else if ([model.content isKindOfClass:[RCHQVoiceMessage class]]) {
        RCHQVoiceMessage *hqVoiceMessage = (RCHQVoiceMessage *)model.content;
        duration = hqVoiceMessage.duration;
    }

    // 限制最大时长
    if (duration > kMaxVoiceDuration) {
        duration = kMaxVoiceDuration;
    }

    // 根据时长计算宽度：线性插值
    CGFloat ratio = duration / kMaxVoiceDuration;
    CGFloat width = kAudioBubbleMinWidth + (kAudioBubbleMaxWidth - kAudioBubbleMinWidth) * ratio;

    // 确保宽度在合理范围内
    width = MAX(kAudioBubbleMinWidth, MIN(kAudioBubbleMaxWidth, width));

    return width;
}

#pragma mark - 头像点击事件

- (void)onAvatarTapped {
    if (self.scDelegate && [self.scDelegate respondsToSelector:@selector(scVoiceMessageCellDidTapAvatar:)]) {
        [self.scDelegate scVoiceMessageCellDidTapAvatar:self.model];
    }
}

@end
