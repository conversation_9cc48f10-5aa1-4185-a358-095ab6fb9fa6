//
//  SCTextMessageCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/19.
//

#import <UIKit/UIKit.h>
#import <RongIMLib/RongIMLib.h>
#import "SCTranslationService.h"
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN
@class SCTextMessageCell;
@protocol SCTextMessageCellDelegate <NSObject>

@optional
-(void) scTextMessageCellDidTapText:(SCNativeMessageModel *)model;

-(void) scTextMessageCellDidTapTranslation:(SCNativeMessageModel *)model;
///点击邀请充值链接
-(void) scTextMessageCellDidTapInvitationId:(NSString *)invitationId;

///点击头像
-(void) scTextMessageCellDidTapAvatar:(SCNativeMessageModel *)model;

- (SCTranslationStatus)scTextMessageCell:(SCTextMessageCell *)cell getTranslationStatusWithMessageId:(long)messageId;


@end

@interface SCTextMessageCell : UICollectionViewCell

@property (nonatomic, strong) SCNativeMessageModel *model;
@property (nonatomic, weak) UIImageView *avatarImageView;
@property (nonatomic, weak) UILabel *timeLabel;
@property(nonatomic,weak) id<SCTextMessageCellDelegate> scDelegate;

-(BOOL) isMy;
-(NSString *) contentStr;

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight;

@end

NS_ASSUME_NONNULL_END
