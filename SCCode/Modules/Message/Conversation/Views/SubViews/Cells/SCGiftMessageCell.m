//
//  SCGiftMessageCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCGiftMessageCell.h"
#import "SCSingleJsonMessage.h"
// 移除YYModel依赖，已改为使用字典处理
#import <SDWebImage/SDWebImage.h>
#import "SCGiftService.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import "SCCategoryNSDateCode.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
@interface SCGiftMessageCell()

//文案1
@property(nonatomic,weak) UILabel *titleL;

@end

@implementation SCGiftMessageCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}
///初始化内容视图
-(void)_initUI{
    
    //文案1
    UILabel *titleL = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontMedium(12) alignment:NSTextAlignmentLeft];

    [self.contentView addSubview:titleL];
    self.titleL = titleL;
    
    [self.titleL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.centerY.equalTo(self.contentView);
    }];

    // 添加时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.font = [UIFont systemFontOfSize:12];
    timeLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.hidden = YES;
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;

    // 时间标签约束
    [self.timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(6);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(22);
    }];

}
- (void)setModel:(SCNativeMessageModel *)model{
    _model = model;
    
    if ([model.content isKindOfClass:[SCSingleJsonMessage class]]) {
        SCSingleJsonMessage *textContent = (SCSingleJsonMessage *)model.content;
        NSData *jsonData = [textContent.content dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error;
        NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:&error];

        NSString *giftCode = dic[@"giftCode"] ?: @"";
        NSString *fromUserName = [dic[@"fromUserName"] replaceMoreThan10];
        NSString *toUserName = [dic[@"toUserName"] replaceMoreThan10];
        NSString *text = @"";
        // 从字典中获取当前用户ID
        NSString *currentUserID = kSCCurrentUserID;
        if ([model.senderUserId isEqualToString:currentUserID]) {
            text = [@"You have sent ### a".translateString stringByReplacingOccurrencesOfString:@"###" withString:toUserName ?: @""];
        } else {
            text = [@"You received ### a".translateString stringByReplacingOccurrencesOfString:@"###" withString:fromUserName ?: @""];
        }
        
        // 使用字典版本的礼物图标获取
        NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:giftCode];
        NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);
        NSURL *imageUrl = [NSURL URLWithString:iconThumbPath];
        kWeakSelf(self)
        [[SDWebImageManager sharedManager] loadImageWithURL:imageUrl options:0 progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            
            
            if (error) {
                
            } else {
                // 在主线程更新UI
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakself giftAttributeWithImage:image text:text];
                });
            }
        }];
        
        [self giftAttributeWithImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"] text:text];

    }

    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];

        // 调整titleL的约束以为时间标签留出空间，使用更小的间距
        [self.titleL mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.contentView);
            make.top.equalTo(self.timeLabel.mas_bottom).offset(2);
//            make.bottom.equalTo(self.contentView).offset(-10);
        }];
    } else {
        self.timeLabel.hidden = YES;

        // 恢复titleL的原始约束
        [self.titleL mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.contentView);
            make.centerY.equalTo(self.contentView);
        }];
    }
}

-(void) giftAttributeWithImage:(UIImage *)image text:(NSString *)text{
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
    CGSize imageSize = CGSizeMake(17, 17);
    textAttachment.bounds = CGRectMake(0, 0, imageSize.width, imageSize.height);
    textAttachment.image = image;
    NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:textAttachment];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@ ", text]]];
    [attributedString appendAttributedString:imageString];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:@" !"]];
    
    // 处理RTL语言
    if (kScAuthMar.isLanguageForce) {
        // 为整个文本设置段落方向
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.alignment = NSTextAlignmentRight;
        paragraphStyle.baseWritingDirection = NSWritingDirectionRightToLeft;
        
        [attributedString addAttribute:NSParagraphStyleAttributeName
                                 value:paragraphStyle
                                 range:NSMakeRange(0, attributedString.length)];
    }
    
    self.titleL.attributedText = attributedString;
}

- (void)configureWithMessageDict:(NSDictionary *)messageDict {
    self.messageDict = messageDict;

    // 处理时间显示
    BOOL isDisplayMessageTime = [SCDictionaryHelper boolFromDictionary:messageDict forKey:@"isDisplayMessageTime" defaultValue:NO];
    if (isDisplayMessageTime) {
        NSTimeInterval sentTime = [SCDictionaryHelper doubleFromDictionary:messageDict forKey:@"sentTime" defaultValue:0];
        NSDate *date = [NSDate dateWithTimeIntervalSince1970:sentTime/1000.0];
        self.timeLabel.text = [date formattedTimeAgo];
        self.timeLabel.hidden = NO;
    } else {
        self.timeLabel.hidden = YES;
    }

    // 处理礼物内容
    NSDictionary *content = [SCDictionaryHelper dictionaryFromDictionary:messageDict forKey:@"content" defaultValue:@{}];
    NSString *giftCode = [SCDictionaryHelper stringFromDictionary:content forKey:@"giftCode" defaultValue:@""];
    NSInteger giftNum = [SCDictionaryHelper integerFromDictionary:content forKey:@"giftNum" defaultValue:1];

    NSString *text = [NSString stringWithFormat:@"Gift x%ld".translateString, giftNum];

    // 使用字典版本的礼物图标获取
    NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:giftCode];
    NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);
    NSURL *imageUrl = [NSURL URLWithString:iconThumbPath];
    kWeakSelf(self)
    [[SDWebImageManager sharedManager] loadImageWithURL:imageUrl options:0 progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
        if (error) {
            
        } else {
            // 在主线程更新UI
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakself giftAttributeWithImage:image text:text];
            });
        }
    }];

    [self giftAttributeWithImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"] text:text];
}

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight{
    CGFloat height = 30.0;

    // 如果需要显示时间，增加时间显示的高度
    if (model.isDisplayMessageTime) {
        height += 32;
    }

    return CGSizeMake(collectionViewWidth, extraHeight + height);
}

+ (CGSize)sizeForMessageDict:(NSDictionary *)messageDict withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight{
    CGFloat height = 30.0;

    // 如果需要显示时间，增加时间显示的高度
    BOOL isDisplayMessageTime = [SCDictionaryHelper boolFromDictionary:messageDict forKey:@"isDisplayMessageTime" defaultValue:NO];
    if (isDisplayMessageTime) {
        height += 32;
    }

    return CGSizeMake(collectionViewWidth, extraHeight + height);
}

@end
