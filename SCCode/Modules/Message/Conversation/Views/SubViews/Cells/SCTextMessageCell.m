//
//  SCTextMessageCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/19.
//





#import "SCTextMessageCell.h"
// 移除YYModel依赖
#import "SCTranslationService.h"
#import "SCIMUIConfig.h"
#import "SCHyperLinkMessage.h"
#import <Masonry/Masonry.h>
#import "SCConversationInfoViewController.h"
#import "SCCategoryNSDateCode.h"

// 最小宽度，防止加上圆角后变形
static CGFloat kBubbleBasicMinWidth = 20.0f;

@interface SCTextMessageCell()

//消息内容容器
@property(nonatomic,weak) UIView * messageContentView;
@property(nonatomic,weak) UILabel * contentL;
@property(nonatomic,strong) UIImage * bubbleLeftImg;
@property(nonatomic,strong) UIImage * bubbleRightImg;
@property(nonatomic,weak) UIImageView * bubbleV;
///翻译的分割线
@property(nonatomic,weak) UIView * dividingLineV;
///翻译的内容
@property(nonatomic,weak) UILabel * translateL;
///翻译按钮
@property(nonatomic,weak) UIButton * translateBtn;
///翻译加载框
@property(nonatomic,weak) UIActivityIndicatorView * translateLoadingV;
///翻译失败的提示
@property(nonatomic,weak) UILabel * translateErrorL;
///翻译状态
@property(nonatomic,assign) SCTranslationStatus translateStatus;

@end

@implementation SCTextMessageCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}
///初始化内容视图
-(void)_initUI{
    // 添加头像
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = kSCMessageAvatarWH / 2.0f;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.userInteractionEnabled = YES;
    [self.contentView addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;

    // 添加头像点击手势
    UITapGestureRecognizer *avatarTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onAvatarTapped)];
    [avatarImageView addGestureRecognizer:avatarTap];

    // 创建消息内容容器
    UIView *messageContentView = [[UIView alloc] init];
    [self.contentView addSubview:messageContentView];
    self.messageContentView = messageContentView;

    UIImageView * bubbleV = [[UIImageView alloc] init];
    bubbleV.contentMode = UIViewContentModeScaleToFill;
    [self.messageContentView addSubview:bubbleV];
    self.bubbleV = bubbleV;
    
    UILabel * contentL = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kSCMessageTextFont alignment:NSTextAlignmentLeft];
    kSCAddTapGesture(contentL, self, labelTapped:);
    contentL.userInteractionEnabled = YES;
    [self.messageContentView addSubview:contentL];
    self.contentL = contentL;
    
    UIView * dividingLineV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#6F6F6F"));
    dividingLineV.hidden = YES;
    [self.messageContentView addSubview:dividingLineV];
    self.dividingLineV = dividingLineV;
    
    
    UILabel * translateL = [UILabel labelWithText:@"" textColor:[UIColor colorWithHexString:@"#FFF549"] font:kSCTranslateTextFont alignment:kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft];
    translateL.hidden = YES;
    [self.messageContentView addSubview:translateL];
    self.translateL = translateL;
    
    UIButton * translateBtn = [UIButton buttonWithTitle:@"Click to translate".translateString titleColor:kSCColorWithHexStr(@"#FFC200") font:kScUIFontMedium(12) image:[SCResourceManager loadImageWithName:@"ic_message_translate"] backgroundImage:nil target:self action:@selector(onTranslate)];
    [translateBtn setTitleEdgeInsets: kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(0, 0, 0, 4) : UIEdgeInsetsMake(0, 4, 0, 0)];
    [self.messageContentView addSubview:translateBtn];
    self.translateBtn = translateBtn;
    
    
    UIActivityIndicatorView * translateLoadingV = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium];
    translateLoadingV.color = [UIColor colorWithHexString:@"#FFF549"];
    translateLoadingV.hidesWhenStopped = YES;
    translateLoadingV.hidden = YES;
    [self.messageContentView addSubview:translateLoadingV];
    self.translateLoadingV = translateLoadingV;
    
    UILabel * translateErrorL = [UILabel labelWithText:@"Translation failed.".translateString textColor:kSCColorWithHexStr(@"#FFF549") font:kScUIFontRegular(13) alignment:kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft];
    translateErrorL.hidden = YES;
    [self.messageContentView addSubview:translateErrorL];
    self.translateErrorL = translateErrorL;
    kSCAddTapGesture(self.translateErrorL, self, onTranslate);

    // 添加时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.font = [UIFont systemFontOfSize:12];
    timeLabel.textColor = [UIColor colorWithRed:0.6 green:0.6 blue:0.6 alpha:1.0];
    timeLabel.textAlignment = NSTextAlignmentCenter;
    timeLabel.hidden = YES;
    [self.contentView addSubview:timeLabel];
    self.timeLabel = timeLabel;

    // 初始约束设置将在setModel中根据消息方向动态调整
}

- (void)setupConstraintsForMessageDirection:(RCMessageDirection)direction {
    // 清除之前的约束
    [self.avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：头像在右侧
            make.trailing.equalTo(self.contentView).offset(-kSCMessageAvatarLeftMargin);
        } else {
            // 接收消息：头像在左侧
            make.leading.equalTo(self.contentView).offset(kSCMessageAvatarLeftMargin);
        }
        // 如果需要显示时间，头像位置下移
        if (self.model.isDisplayMessageTime) {
            make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(10);
        }
        make.width.height.mas_equalTo(kSCMessageAvatarWH);
    }];

    // 消息内容容器约束 - 使用固定宽度，在layoutSubviews中动态调整
    [self.messageContentView mas_remakeConstraints:^(MASConstraintMaker *make) {
        if (direction == MessageDirection_SEND) {
            // 发送消息：内容容器紧贴头像左侧
            make.trailing.equalTo(self.avatarImageView.mas_leading).offset(-kSCMessageContentLeftMargin);
            make.width.mas_equalTo(200); // 临时宽度，会在layoutSubviews中更新
        } else {
            // 接收消息：内容容器在头像右侧
            make.leading.equalTo(self.avatarImageView.mas_trailing).offset(kSCMessageContentLeftMargin);
            make.width.mas_equalTo(200); // 临时宽度，会在layoutSubviews中更新
        }
        // 如果需要显示时间，消息内容位置下移
        if (self.model.isDisplayMessageTime) {
            make.top.equalTo(self.timeLabel.mas_bottom).offset(5);
        } else {
            make.top.equalTo(self.contentView).offset(10);
        }
        make.bottom.equalTo(self.contentView);
    }];

    // 时间标签约束
    [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(30);
    }];
}

- (void)layoutSubviews{
    [super layoutSubviews];
    CGFloat contentWidth = self.frame.size.width - (kSCMessageAvatarWH + kSCMessageAvatarLeftMargin + kSCMessageContentLeftMargin)*2;
    CGSize contentSize = [UILabel labelSizeWithText:self.contentStr font:kSCMessageTextFont maxSize:CGSizeMake(contentWidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
    CGFloat bubbleY = 0;
    CGFloat bubbleX = 0;
    CGFloat bubbleWidth = contentSize.width + kSCMessageTextLeftRightMargin*2;
    
    CGFloat maxWidth = contentSize.width;
    
    CGFloat bubbleHeight = contentSize.height + kSCMessageTextTopMargin + kSCMessageTextBottomMargin;
    if([self isMy]){
        // 我的消息：气泡从右侧开始，左对齐到messageContentView
        bubbleX = 0; // 在messageContentView内从左开始
    }else{

        switch (self.translateStatus) {
            case SCTranslationStatusTranslating:
            {
                CGFloat mWidth = MAX(kBubbleBasicMinWidth, maxWidth);
                CGFloat bWidth = mWidth + kSCMessageTextLeftRightMargin*2;
                
                self.dividingLineV.frame = CGRectMake(kSCMessageTextLeftRightMargin, bubbleY + contentSize.height + kSCMessageTextTopMargin + kSCMessageTextBottomMargin, mWidth, kSCDividingLineHeight);
                //翻译中
                self.translateLoadingV.scCenterX = bWidth/2.0;
                self.translateLoadingV.scTop = self.dividingLineV.scBottom + kSCMessageTextTopMargin;
                
                bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight + kSCMessageTextTopMargin*2;
            }
                break;
            case SCTranslationStatusFail:
            {
                CGSize errorLabelSize = [UILabel labelSizeWithText:@"Translation failed.".translateString font:kScUIFontRegular(13) maxSize:CGSizeMake(contentWidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
                
                if(errorLabelSize.width > maxWidth){
                    maxWidth = errorLabelSize.width;
                }
                                
                CGFloat mWidth = MAX(kBubbleBasicMinWidth, maxWidth);
                CGFloat bWidth = mWidth + kSCMessageTextLeftRightMargin*2;
                
                self.dividingLineV.frame = CGRectMake(kSCMessageTextLeftRightMargin, bubbleY + contentSize.height + kSCMessageTextTopMargin + kSCMessageTextBottomMargin, mWidth, kSCDividingLineHeight);
                //翻译失败
                self.translateErrorL.frame = CGRectMake(bubbleX + (bWidth - maxWidth) / 2, self.dividingLineV.scBottom + kSCMessageTextTopMargin, maxWidth, errorLabelSize.height);
                
                bubbleHeight += kSCDividingLineHeight + errorLabelSize.height + kSCMessageTextTopMargin*2;

            }
                
                break;
            case SCTranslationStatusSuccess:
            {
                CGSize contentSize2 = [UILabel labelSizeWithText:[kSCAuthTranslaService getCacheWithText:self.contentStr] font:kSCTranslateTextFont maxSize:CGSizeMake(contentWidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
                if(contentSize2.width > maxWidth){
                    maxWidth = contentSize2.width;
                }
                
                CGFloat mWidth = MAX(kBubbleBasicMinWidth, maxWidth);
                CGFloat bWidth = mWidth + kSCMessageTextLeftRightMargin*2;
                
                self.dividingLineV.frame = CGRectMake(kSCMessageTextLeftRightMargin, bubbleY + contentSize.height + kSCMessageTextTopMargin + kSCMessageTextBottomMargin, mWidth, kSCDividingLineHeight);
                //翻译成功
                self.translateL.frame = CGRectMake(bubbleX + (bWidth - maxWidth) / 2, self.dividingLineV.scBottom + kSCMessageTextTopMargin, maxWidth, contentSize2.height);
                bubbleHeight +=  kSCDividingLineHeight +  kSCMessageTextTopMargin*2 + contentSize2.height;
                
            }
                break;
            default:
                
                break;
        }
        
    }
    
    bubbleWidth = MAX(kBubbleBasicMinWidth, maxWidth) + kSCMessageTextLeftRightMargin*2;
    
    self.contentL.frame = CGRectMake(bubbleX + (bubbleWidth - maxWidth) / 2, bubbleY + kSCMessageTextTopMargin, maxWidth, contentSize.height);
    
    self.bubbleV.frame = CGRectMake(bubbleX, bubbleY, bubbleWidth, bubbleHeight);
    
    if(!self.translateBtn.hidden){
        // 计算翻译按钮文本的实际宽度
        NSString *translateText = self.translateBtn.titleLabel.text;
        UIFont *translateFont = self.translateBtn.titleLabel.font;
        CGSize textSize = [translateText boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16.0f)
                                                    options:NSStringDrawingUsesLineFragmentOrigin
                                                 attributes:@{NSFontAttributeName: translateFont}
                                                    context:nil].size;
        
        // 添加左右边距和图标宽度
        CGFloat iconWidth = kSCTranslateBtnHeight;
        CGFloat padding = 4.0f;    // 文本与图标的间距
        CGFloat btnWidth = textSize.width + iconWidth + padding;
        
        CGFloat maxWidth = MAX(self.bubbleV.scWidth, btnWidth);
        
        // 设置按钮frame
        self.translateBtn.frame = CGRectMake(kScAuthMar.isLanguageForce ? (maxWidth - btnWidth) : 0, self.bubbleV.scBottom, btnWidth, kSCTranslateBtnHeight);
        // 更新contentSize
        [self.messageContentView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(maxWidth);
        }];
        self.messageContentView.scSize = CGSizeMake(maxWidth, self.bubbleV.scHeight + kSCTranslateBtnHeight);
        //气泡的位置考虑语言方向
        self.bubbleV.scLeft = kScAuthMar.isLanguageForce ? (maxWidth - self.bubbleV.scWidth) : 0;
        self.contentL.scLeft = self.bubbleV.scLeft + (self.bubbleV.scWidth - self.contentL.scWidth) / 2;
    } else {
        if ([self isMy]) {
            // 我的消息：动态更新messageContentView的宽度约束
            [self.messageContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(bubbleWidth);
            }];
            self.messageContentView.scSize = CGSizeMake(bubbleWidth, bubbleHeight);
            // 气泡在messageContentView内从左开始（bubbleX已经设置为0）
        } else {
            // 对方消息：动态更新messageContentView的宽度约束
            [self.messageContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(bubbleWidth);
            }];
            self.messageContentView.scSize = self.bubbleV.frame.size;
        }
    }
}
-(BOOL) isMy{
    return self.model.messageDirection == MessageDirection_SEND;
}
-(NSString *) contentStr{
    if([self.model.content isKindOfClass:[SCHyperLinkMessage class]]){
        return ((SCHyperLinkMessage *)self.model.content).content;
    }
    return ((RCTextMessage *)self.model.content).content;
}

- (void)setModel:(SCNativeMessageModel *)model{
    _model = model;
    if(model == nil){
        return;
    }

    // 根据消息方向设置约束
    [self setupConstraintsForMessageDirection:model.messageDirection];
    
    // 使用代理方法获取翻译状态
    if ([self.scDelegate respondsToSelector:@selector(scTextMessageCell:getTranslationStatusWithMessageId:)]) {
        self.translateStatus = [self.scDelegate scTextMessageCell:self getTranslationStatusWithMessageId:model.messageId];
    } else {
        self.translateStatus = SCTranslationStatusNormal;
    }
    
    if(kSCAuthTranslaService.isAutoTranslation && self.translateStatus == SCTranslationStatusNormal){
        ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
        self.translateStatus = SCTranslationStatusTranslating;
    }
    
    self.contentL.textColor = UIColor.scWhite;
    self.contentL.textAlignment = NSTextAlignmentLeft;
    self.bubbleV.setBackgroundColor(self.isMy ? kSCColorWithHexStr(@"#A82F4B") : kSCColorWithHexStr(@"#32312F")).setCornerRadius(18.0f);
    
    self.bubbleV.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMinYCorner | kCALayerMaxXMaxYCorner | kCALayerMinXMinYCorner;
    
    if([self.model.content isKindOfClass:[SCHyperLinkMessage class]]){
        SCHyperLinkMessage * linkMessage = ((SCHyperLinkMessage *)self.model.content);
        NSString * linkeStr = linkMessage.content;
        NSString * linkeUrl = nil;
        if([@"recharge_link" isEqualToString:linkMessage.contentType]){
            //邀请链接
            NSDictionary * extra =  [linkMessage.extra toScJson];
            NSString * invitationId = extra[@"invitationId"];
            linkeUrl = [NSString stringWithFormat:@"SC://recharge_link?invitationId=%@",invitationId];
        }
        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:linkeStr];

        // 设置其他属性，如字体和颜色
        NSDictionary *otherAttributes = @{NSLinkAttributeName: [NSURL URLWithString:linkeUrl],
                                          NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#007AFF"] };
        [attributedString setAttributes:otherAttributes range:NSMakeRange(0, linkeStr.length)];
        
        // 处理RTL语言
        if (kScAuthMar.isLanguageForce) {
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            paragraphStyle.alignment = NSTextAlignmentRight;
            
            [attributedString addAttribute:NSParagraphStyleAttributeName
                                   value:paragraphStyle
                                   range:NSMakeRange(0, attributedString.length)];
        }

        self.contentL.attributedText = attributedString;
        
    }else{
        NSAttributedString *attributedText = [self generateAttributedTextWithLinks:self.contentStr];
        // 处理RTL语言
        if (kScAuthMar.isLanguageForce && !self.isMy) {
            self.contentL.textAlignment = NSTextAlignmentRight;
        }
        self.contentL.attributedText = attributedText;
    }
    
    // 设置时间显示
    if (model.isDisplayMessageTime) {
        self.timeLabel.hidden = NO;
        NSDate *messageDate = [NSDate dateWithTimeIntervalSince1970:model.sentTime / 1000.0];
        self.timeLabel.text = [messageDate formattedMessageTime];
    } else {
        self.timeLabel.hidden = YES;
    }

    //翻译相关
    [self updateTranslateUI];
    [self setNeedsDisplay];
}

-(void) updateTranslateUI{
    self.translateBtn.hidden = YES;
    [self.translateLoadingV stopAnimating];
    self.translateLoadingV.hidden = YES;
    self.translateErrorL.hidden = YES;
    self.translateL.hidden = YES;
    self.dividingLineV.hidden = YES;
    //不翻译自己的
    if(self.isMy){
        return;
    }


    
    switch (self.translateStatus) {
        case SCTranslationStatusTranslating:
            //翻译中
            self.translateLoadingV.hidden = NO;
            [self.translateLoadingV startAnimating];
            self.dividingLineV.hidden = NO;
            break;
        case SCTranslationStatusFail:
            //翻译失败
            self.translateErrorL.hidden = NO;
            self.dividingLineV.hidden = NO;
            break;
        case SCTranslationStatusSuccess:
            //翻译成功
            self.translateL.hidden = NO;
            self.dividingLineV.hidden = NO;
            self.translateL.text = [kSCAuthTranslaService getCacheWithText:self.contentStr];
            break;
        default:
            self.translateBtn.hidden = NO;
            break;
    }
}

+ (CGSize)sizeForMessageModel:(SCNativeMessageModel *)model withCollectionViewWidth:(CGFloat)collectionViewWidth referenceExtraHeight:(CGFloat)extraHeight{
    if(model == nil){
        return CGSizeZero;
    }
    
    NSString * contentStr = @"";
    
    if ([model.content isKindOfClass:[SCHyperLinkMessage class]]) {
            SCHyperLinkMessage *textContent = (SCHyperLinkMessage *)model.content;
            // 处理文本消息类型
            contentStr = textContent.content;
        }else {
            RCTextMessage *textContent = (RCTextMessage *)model.content;
            // 处理文本消息类型
            contentStr = textContent.content;
        }
    
    CGFloat contentwidth = collectionViewWidth - (kSCMessageAvatarWH + kSCMessageAvatarLeftMargin + kSCMessageContentLeftMargin)*2;
    CGSize contentSize = [UILabel labelSizeWithText:contentStr font:kSCMessageTextFont maxSize:CGSizeMake(contentwidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
    
    CGFloat height = contentSize.height  + kSCMessageTextTopMargin + kSCMessageTextBottomMargin;
    
    if(model.messageDirection != MessageDirection_SEND){
        
        SCTranslationStatus translateStatus = SCTranslationStatusNormal;
        UIViewController *viewController = [UIViewController currentViewController];
        if ([viewController isKindOfClass:[SCConversationInfoViewController class]]) {
            SCConversationInfoViewController *conVc = (SCConversationInfoViewController *)viewController;
            translateStatus = [conVc getTranslationStatus:model.messageId];
        }
        
        if(kSCAuthTranslaService.isAutoTranslation && translateStatus == SCTranslationStatusNormal){
            ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
            translateStatus = SCTranslationStatusTranslating;
        }
        switch (translateStatus) {
            case SCTranslationStatusTranslating:
                height += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight + kSCMessageTextTopMargin*2;
                break;
            case SCTranslationStatusFail:
            {
                height += kSCDividingLineHeight + kSCMessageTextTopMargin*2;
                CGSize errorLabelSize = [UILabel labelSizeWithText:@"Translation failed.".translateString font:kScUIFontRegular(13) maxSize:CGSizeMake(contentwidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
                if(errorLabelSize.width > contentwidth){
                    contentwidth = errorLabelSize.width;
                }
                height += errorLabelSize.height;
            }
                break;
            case SCTranslationStatusSuccess:
            {
                height += kSCDividingLineHeight +  kSCMessageTextTopMargin*2;
                CGSize contentSize2 = [UILabel labelSizeWithText:[kSCAuthTranslaService getCacheWithText:contentStr] font:kSCTranslateTextFont maxSize:CGSizeMake(contentwidth - kSCMessageTextLeftRightMargin*2, MAXFLOAT)];
                if(contentSize2.width > contentwidth){
                    contentwidth = contentSize2.width;
                }
                height += contentSize2.height;
            }
                break;
            default:
                height += kSCTranslateBtnHeight;
                break;
        }
    }

    if (model.isDisplayMessageTime) {
        height = height + 40;
    }
    return CGSizeMake(collectionViewWidth, height + extraHeight);
}

#pragma mark - action
-(void)onTranslate{
    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(scTextMessageCellDidTapTranslation:)]){
        [self.scDelegate scTextMessageCellDidTapTranslation:self.model];
    }
}
- (void)labelTapped:(UITapGestureRecognizer *)gesture {
    UILabel *label = (UILabel *)gesture.view;
    if (![label isKindOfClass:[UILabel class]]) {
        return;
    }
    
    CGPoint location = [gesture locationInView:label];
    
    // 获取被点击的链接
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    [textStorage addLayoutManager:layoutManager];
    
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    [layoutManager addTextContainer:textContainer];
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:location inTextContainer:textContainer fractionOfDistanceBetweenInsertionPoints:nil];
    
    if (characterIndex < label.attributedText.length) {
        NSDictionary *attributes = [label.attributedText attributesAtIndex:characterIndex effectiveRange:NULL];
        
        NSURL *url = attributes[NSLinkAttributeName];
        
        if ([url isKindOfClass:[NSURL class]]) {
            //SC://recharge_link?invitationId=xxxx
            if([url.scheme isEqualToString:@"SC"]){
                //内部协议
                if([url.host isEqualToString:@"recharge_link"]){
                    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
                    
                    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
                    for (NSURLQueryItem *item in components.queryItems) {
                            [parameters setValue:item.value forKey:item.name];
                        }
                    //充值链接
                    NSString *invitationId = [parameters objectForKey:@"invitationId"];
                    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(scTextMessageCellDidTapInvitationId:)]){
                        [self.scDelegate scTextMessageCellDidTapInvitationId:invitationId];
                    }
                }
            }else{
                //其他业务
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
            
        } else if ([url isKindOfClass:[NSString class]]) {
            NSURL *toUrl = [NSURL URLWithString:(NSString *)url];
            [[UIApplication sharedApplication] openURL:toUrl options:@{} completionHandler:nil];
        }
    }
}


// 生成包含超链接的 attributedText
- (NSAttributedString *)generateAttributedTextWithLinks:(NSString *)originalText {
    // 创建可变的 attributedString
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:originalText];
    
    // 设置普通文本的样式
    [attributedString addAttribute:NSFontAttributeName value:kSCMessageTextFont range:NSMakeRange(0, originalText.length)];
    [attributedString addAttribute:NSForegroundColorAttributeName value:UIColor.scWhite range:NSMakeRange(0, originalText.length)];
    
    // 使用正则表达式查找文本中的链接
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"https?://\\S+" options:NSRegularExpressionCaseInsensitive error:&error];
    
    if (error) {
        
        return attributedString;
    }
    
    // 遍历匹配结果，设置链接样式
    [regex enumerateMatchesInString:originalText options:0 range:NSMakeRange(0, originalText.length) usingBlock:^(NSTextCheckingResult *result, NSMatchingFlags flags, BOOL *stop) {
        NSRange linkRange = result.range;
        NSString *linkText = [originalText substringWithRange:linkRange];
        
        // 设置链接文本的样式
        [attributedString addAttribute:NSLinkAttributeName value:linkText range:linkRange];
        [attributedString addAttribute:NSForegroundColorAttributeName value:[UIColor colorWithHexString:@"#007AFF"] range:linkRange];
        [attributedString addAttribute:NSUnderlineStyleAttributeName value:@(NSUnderlineStyleSingle) range:linkRange];
    }];
    
    return attributedString;
}

#pragma mark - 头像点击事件

- (void)onAvatarTapped {
    if (self.scDelegate && [self.scDelegate respondsToSelector:@selector(scTextMessageCellDidTapAvatar:)]) {
        [self.scDelegate scTextMessageCellDidTapAvatar:self.model];
    }
}

@end

