//
//  SCVoicePlayerManager.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  语音播放管理器 - 统一管理语音消息的下载、缓存和播放
//

#import "SCVoicePlayerManager.h"
#import "SCAVAudioSessionUtils.h"

// 通知名称常量实现
NSString * const SCVoicePlayerStateDidChangeNotification = @"SCVoicePlayerStateDidChangeNotification";
NSString * const SCVoicePlayerDownloadProgressDidUpdateNotification = @"SCVoicePlayerDownloadProgressDidUpdateNotification";

// 通知UserInfo的key实现
NSString * const SCVoicePlayerNotificationMessageIdKey = @"SCVoicePlayerNotificationMessageIdKey";
NSString * const SCVoicePlayerNotificationStateKey = @"SCVoicePlayerNotificationStateKey";
NSString * const SCVoicePlayerNotificationProgressKey = @"SCVoicePlayerNotificationProgressKey";
NSString * const SCVoicePlayerNotificationErrorKey = @"SCVoicePlayerNotificationErrorKey";



@interface SCVoicePlayerManager () <AVAudioPlayerDelegate>

// AVAudioPlayer实例
@property (nonatomic, strong, nullable) AVAudioPlayer *audioPlayer;

// 当前播放的消息模型
@property (nonatomic, strong, nullable) SCNativeMessageModel *currentPlayingModel;

// 当前播放的消息ID
@property (nonatomic, assign, readwrite) long currentPlayingMessageId;

// 当前播放状态
@property (nonatomic, assign, readwrite) SCVoicePlayState currentPlayState;

// 语音文件缓存目录路径
@property (nonatomic, copy) NSString *voiceCachePath;

// 下载任务字典 (messageId -> 下载状态)
@property (nonatomic, strong) NSMutableDictionary<NSNumber *, NSNumber *> *downloadingTasks;



@end

@implementation SCVoicePlayerManager

#pragma mark - 单例模式

+ (instancetype)sharedManager {
    static SCVoicePlayerManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SCVoicePlayerManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupVoiceCachePath];
        [self setupInitialState];
        [self setupNotifications];
    }
    return self;
}

#pragma mark - 初始化设置

- (void)setupVoiceCachePath {
    // 创建语音缓存目录
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachesDirectory = [paths firstObject];
    self.voiceCachePath = [cachesDirectory stringByAppendingPathComponent:@"SCVoiceCache"];
    
    // 确保目录存在
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:self.voiceCachePath]) {
        NSError *error;
        [fileManager createDirectoryAtPath:self.voiceCachePath 
                withIntermediateDirectories:YES 
                                 attributes:nil 
                                      error:&error];
        if (error) {
            
        }
    }
}

- (void)setupInitialState {
    self.currentPlayingMessageId = 0;
    self.currentPlayState = SCVoicePlayStateIdle;
    self.downloadingTasks = [[NSMutableDictionary alloc] init];
}

- (void)setupNotifications {
    // 监听应用进入后台通知，停止播放
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationDidEnterBackground)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];
    
    // 监听音频中断通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(audioSessionInterruption:)
                                                 name:AVAudioSessionInterruptionNotification
                                               object:nil];
}

#pragma mark - 公共接口

- (void)playVoiceMessage:(SCNativeMessageModel *)model {
    if (!model || (!([model.content isKindOfClass:[RCVoiceMessage class]] || [model.content isKindOfClass:[RCHQVoiceMessage class]]))) {
        
        return;
    }
    
    // 如果正在播放相同的消息，则停止播放
    if (self.currentPlayingMessageId == model.messageId && self.currentPlayState == SCVoicePlayStatePlaying) {
        [self stopCurrentPlaying];
        return;
    }
    
    // 停止当前播放
    [self stopCurrentPlaying];
    
    // 设置新的播放消息
    self.currentPlayingModel = model;
    self.currentPlayingMessageId = model.messageId;
    
    // 检查文件是否已缓存
    NSString *cachedFilePath = [self cachedFilePathForMessage:model];
    if ([[NSFileManager defaultManager] fileExistsAtPath:cachedFilePath]) {
        // 文件已缓存，直接播放
        [self playLocalVoiceFile:cachedFilePath];
    } else {
        // 文件未缓存，需要下载
        [self downloadAndPlayVoiceMessage:model];
    }
}

- (void)stopCurrentPlaying {
    if (self.audioPlayer) {
        [self.audioPlayer stop];
        self.audioPlayer = nil;
    }
    
    [self updatePlayState:SCVoicePlayStateIdle forMessageId:self.currentPlayingMessageId error:nil];
    
    self.currentPlayingModel = nil;
    self.currentPlayingMessageId = 0;
}

- (void)pauseCurrentPlaying {
    if (self.audioPlayer && self.audioPlayer.isPlaying) {
        [self.audioPlayer pause];
        [self updatePlayState:SCVoicePlayStatePaused forMessageId:self.currentPlayingMessageId error:nil];
    }
}

- (void)resumeCurrentPlaying {
    if (self.audioPlayer && !self.audioPlayer.isPlaying && self.currentPlayState == SCVoicePlayStatePaused) {
        [self.audioPlayer play];
        [self updatePlayState:SCVoicePlayStatePlaying forMessageId:self.currentPlayingMessageId error:nil];
    }
}

- (BOOL)isPlayingMessageId:(long)messageId {
    return self.currentPlayingMessageId == messageId && self.currentPlayState == SCVoicePlayStatePlaying;
}

- (SCVoicePlayState)playStateForMessageId:(long)messageId {
    if (self.currentPlayingMessageId == messageId) {
        return self.currentPlayState;
    }
    return SCVoicePlayStateIdle;
}

- (void)clearVoiceCache {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    [fileManager removeItemAtPath:self.voiceCachePath error:&error];
    if (error) {

    } else {
        [self setupVoiceCachePath]; // 重新创建缓存目录
    }
}

#pragma mark - 融云消息状态管理

- (void)checkMessageListenedStatus:(long)messageId completion:(void (^)(BOOL isListened))completion {
    if (messageId <= 0) {
        if (completion) {
            completion(NO);
        }
        return;
    }
    
    [[RCCoreClient sharedCoreClient] getMessage:messageId completion:^(RCMessage * _Nullable message) {
        BOOL isListened = message ? message.receivedStatusInfo.isListened : NO;
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(isListened);
            });
        }
    }];
}

- (void)markMessageAsListened:(long)messageId completion:(nullable void (^)(BOOL success))completion {
    if (messageId <= 0) {
        if (completion) {
            completion(NO);
        }
        return;
    }

    // 创建接收状态信息对象并标记为已听
    RCReceivedStatusInfo *statusInfo = [[RCReceivedStatusInfo alloc] initWithReceivedStatus:0];
    [statusInfo markAsListened];

    // 使用融云API设置消息接收状态
    [[RCCoreClient sharedCoreClient] setMessageReceivedStatus:messageId
                                           receivedStatusInfo:statusInfo
                                                   completion:^(BOOL ret) {
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(ret);
            });
        }
    }];
}

#pragma mark - 私有方法

#pragma mark - 通知发送辅助方法

- (void)postStateChangeNotificationWithState:(SCVoicePlayState)state messageId:(long)messageId error:(NSError *)error {
    NSMutableDictionary *userInfo = [NSMutableDictionary dictionary];
    userInfo[SCVoicePlayerNotificationMessageIdKey] = @(messageId);
    userInfo[SCVoicePlayerNotificationStateKey] = @(state);

    if (error) {
        userInfo[SCVoicePlayerNotificationErrorKey] = error;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:SCVoicePlayerStateDidChangeNotification
                                                            object:self
                                                          userInfo:[userInfo copy]];
    });
}

- (void)postProgressUpdateNotificationWithProgress:(float)progress messageId:(long)messageId {
    NSDictionary *userInfo = @{
        SCVoicePlayerNotificationMessageIdKey: @(messageId),
        SCVoicePlayerNotificationProgressKey: @(progress)
    };

    dispatch_async(dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:SCVoicePlayerDownloadProgressDidUpdateNotification
                                                            object:self
                                                          userInfo:userInfo];
    });
}

- (NSString *)cachedFilePathForMessage:(SCNativeMessageModel *)model {
    NSString *fileName = [NSString stringWithFormat:@"voice_%ld.wav", model.messageId];
    return [self.voiceCachePath stringByAppendingPathComponent:fileName];
}

- (void)downloadAndPlayVoiceMessage:(SCNativeMessageModel *)model {
    // 标记为下载中状态
    [self updatePlayState:SCVoicePlayStateDownloading forMessageId:model.messageId error:nil];
    self.downloadingTasks[@(model.messageId)] = @(YES);

    // 创建RCMessage对象用于下载
    RCMessage *rcMessage = [[RCMessage alloc] init];
    rcMessage.messageId = model.messageId;
    rcMessage.content = model.content;
    rcMessage.messageDirection = model.messageDirection;
    rcMessage.targetId = model.targetId;
    rcMessage.conversationType = model.conversationType;

    __weak typeof(self) weakSelf = self;

    // 使用RongCloud下载媒体消息
    [[RCCoreClient sharedCoreClient] downloadMediaMessage:rcMessage
                                            progressBlock:^(int progress) {
        // 下载进度回调
        [weakSelf voicePlayerManager:progress messageId:model.messageId];
        
    } successBlock:^(NSString * _Nullable mediaPath) {
        // 下载成功
        [weakSelf.downloadingTasks removeObjectForKey:@(model.messageId)];

        if (mediaPath && weakSelf.currentPlayingMessageId == model.messageId) {
            // 将下载的文件复制到缓存目录
            NSString *cachedPath = [weakSelf cachedFilePathForMessage:model];
            NSError *copyError;
            [[NSFileManager defaultManager] copyItemAtPath:mediaPath toPath:cachedPath error:&copyError];

            if (copyError) {
                
                [weakSelf updatePlayState:SCVoicePlayStateError forMessageId:model.messageId error:copyError];
            } else {
                // 开始播放
                [weakSelf playLocalVoiceFile:cachedPath];
            }
        }
    } errorBlock:^(RCErrorCode errorCode) {
        // 下载失败
        [weakSelf.downloadingTasks removeObjectForKey:@(model.messageId)];

        NSError *error = [NSError errorWithDomain:@"SCVoicePlayerManager"
                                             code:errorCode
                                         userInfo:@{NSLocalizedDescriptionKey: @"语音消息下载失败"}];
        [weakSelf updatePlayState:SCVoicePlayStateError forMessageId:model.messageId error:error];
    } cancelBlock:^{
        // 下载取消
        [weakSelf.downloadingTasks removeObjectForKey:@(model.messageId)];
        [weakSelf updatePlayState:SCVoicePlayStateIdle forMessageId:model.messageId error:nil];
    }];
}

- (void)voicePlayerManager:(int)progress messageId:(long) messageId{
    float progressFloat = progress / 100.0f;

    // 发送通知
    [self postProgressUpdateNotificationWithProgress:progressFloat messageId:messageId];

    // 保持原有代理调用（向后兼容）
    __weak typeof(self) weakSelf = self;
    dispatch_async(dispatch_get_main_queue(), ^{
        if ([weakSelf.delegate respondsToSelector:@selector(voicePlayerManager:didUpdateDownloadProgress:forMessageId:)]) {
            [weakSelf.delegate voicePlayerManager:weakSelf didUpdateDownloadProgress:progressFloat forMessageId:messageId];
        }
    });
}

- (void)playLocalVoiceFile:(NSString *)filePath {
    // 配置音频会话
    NSError *sessionError;
    [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayback error:&sessionError];
    if (sessionError) {
        
        [self updatePlayState:SCVoicePlayStateError forMessageId:self.currentPlayingMessageId error:sessionError];
        return;
    }

    // 创建AVAudioPlayer
    NSURL *fileURL = [NSURL fileURLWithPath:filePath];
    NSError *playerError;
    self.audioPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:fileURL error:&playerError];

    if (playerError || !self.audioPlayer) {
        
        [self updatePlayState:SCVoicePlayStateError forMessageId:self.currentPlayingMessageId error:playerError];
        return;
    }

    // 设置代理和播放参数
    self.audioPlayer.delegate = self;
    self.audioPlayer.volume = 1.0;

    // 开始播放
    BOOL success = [self.audioPlayer play];
    if (success) {
        // 播放开始时立即标记为已听（用户点击播放后红点应立即消失）
        [self markMessageAsListened:self.currentPlayingMessageId completion:nil];
        [self updatePlayState:SCVoicePlayStatePlaying forMessageId:self.currentPlayingMessageId error:nil];
    } else {
        NSError *playError = [NSError errorWithDomain:@"SCVoicePlayerManager"
                                                 code:-1
                                             userInfo:@{NSLocalizedDescriptionKey: @"音频播放启动失败"}];
        [self updatePlayState:SCVoicePlayStateError forMessageId:self.currentPlayingMessageId error:playError];
    }
}

#pragma mark - AVAudioPlayerDelegate

- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    long messageId = self.currentPlayingMessageId;

    if (flag) {
        // 播放成功完成（已在播放开始时标记为已播放）
        [self updatePlayState:SCVoicePlayStateCompleted forMessageId:messageId error:nil];
    } else {
        NSError *error = [NSError errorWithDomain:@"SCVoicePlayerManager"
                                             code:-1
                                         userInfo:@{NSLocalizedDescriptionKey: @"音频播放异常结束"}];
        [self updatePlayState:SCVoicePlayStateError forMessageId:messageId error:error];
    }

    // 清理播放器
    self.audioPlayer = nil;
    self.currentPlayingModel = nil;
    self.currentPlayingMessageId = 0;
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError *)error {
    
    [self updatePlayState:SCVoicePlayStateError forMessageId:self.currentPlayingMessageId error:error];

    // 清理播放器
    self.audioPlayer = nil;
    self.currentPlayingModel = nil;
    self.currentPlayingMessageId = 0;
}

- (void)updatePlayState:(SCVoicePlayState)state forMessageId:(long)messageId error:(nullable NSError *)error {
    self.currentPlayState = state;

    // 发送通知
    [self postStateChangeNotificationWithState:state messageId:messageId error:error];

    // 保持原有代理调用（向后兼容）
    if ([self.delegate respondsToSelector:@selector(voicePlayerManager:didChangePlayState:forMessageId:error:)]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.delegate voicePlayerManager:self didChangePlayState:state forMessageId:messageId error:error];
        });
    }
}

#pragma mark - 通知处理

- (void)applicationDidEnterBackground {
    [self stopCurrentPlaying];
}

- (void)audioSessionInterruption:(NSNotification *)notification {
    NSNumber *interruptionType = notification.userInfo[AVAudioSessionInterruptionTypeKey];
    if (interruptionType.integerValue == AVAudioSessionInterruptionTypeBegan) {
        [self pauseCurrentPlaying];
    }
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
