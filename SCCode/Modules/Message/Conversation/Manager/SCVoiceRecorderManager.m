//
//  SCVoiceRecorderManager.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/17.
//  语音录制管理器 - 统一管理语音录制功能
//

#import "SCVoiceRecorderManager.h"
#import "SCAVAudioSessionUtils.h"
#import "SCPermissionManager.h"
#import "SCVoicePlayerManager.h"

@interface SCVoiceRecorderManager () <AVAudioRecorderDelegate>

// AVAudioRecorder实例
@property (nonatomic, strong, nullable) AVAudioRecorder *audioRecorder;

// 录音状态
@property (nonatomic, assign, readwrite) SCVoiceRecordState currentState;

// 录音时长
@property (nonatomic, assign, readwrite) NSTimeInterval currentDuration;

// 录音文件路径
@property (nonatomic, copy, readwrite, nullable) NSString *recordingFilePath;

// 录音缓存目录路径
@property (nonatomic, copy) NSString *recordingCachePath;

// 录音定时器（用于更新时长和音量）
@property (nonatomic, strong, nullable) NSTimer *recordingTimer;

// 录音开始时间
@property (nonatomic, strong, nullable) NSDate *recordingStartTime;

@end

@implementation SCVoiceRecorderManager

#pragma mark - 单例

+ (instancetype)sharedManager {
    static SCVoiceRecorderManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc] init];
    });
    return instance;
}

#pragma mark - 初始化

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupDefaultValues];
        [self setupRecordingCachePath];
    }
    return self;
}

- (void)setupDefaultValues {
    _currentState = SCVoiceRecordStateIdle;
    _currentDuration = 0.0;
    _maxRecordDuration = 60.0; // 默认最大60秒
    _minRecordDuration = 1.0;  // 默认最小1秒
}

- (void)setupRecordingCachePath {
    // 创建录音缓存目录
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachesDirectory = [paths firstObject];
    self.recordingCachePath = [cachesDirectory stringByAppendingPathComponent:@"VoiceRecordings"];
    
    // 确保目录存在
    NSFileManager *fileManager = [NSFileManager defaultManager];
    if (![fileManager fileExistsAtPath:self.recordingCachePath]) {
        NSError *error;
        [fileManager createDirectoryAtPath:self.recordingCachePath
               withIntermediateDirectories:YES
                                attributes:nil
                                     error:&error];
        if (error) {
            
        }
    }
}

#pragma mark - 权限检查

- (void)checkRecordPermission {
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeMicrophone
                                       completion:^(BOOL granted, BOOL shouldShowAlert) {
        if ([self.delegate respondsToSelector:@selector(voiceRecorderManager:permissionGranted:shouldShowAlert:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.delegate voiceRecorderManager:self
                                  permissionGranted:granted
                                    shouldShowAlert:shouldShowAlert];
            });
        }
    }];
}

#pragma mark - 录音控制

- (BOOL)startRecording {
    // 检查当前状态
    if (self.currentState == SCVoiceRecordStateRecording) {
        
        return NO;
    }

    // 停止当前正在播放的语音消息（录音和播放互斥）
    [[SCVoicePlayerManager sharedManager] stopCurrentPlaying];
    

    // 配置音频会话
    NSError *sessionError;
    [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayAndRecord
                                                    withMode:AVAudioSessionModeDefault
                                                       error:&sessionError];
    if (sessionError) {
        
        [self updateState:SCVoiceRecordStateError duration:0.0 error:sessionError];
        return NO;
    }
    
    // 生成录音文件路径
    NSString *fileName = [NSString stringWithFormat:@"voice_record_%ld.wav", (long)[[NSDate date] timeIntervalSince1970]];
    self.recordingFilePath = [self.recordingCachePath stringByAppendingPathComponent:fileName];
    
    // 配置录音参数（符合RongCloud要求）
    // 必须是单声道的 wav 格式音频数据，采样率8000Hz，采样位数16位
    NSDictionary *recordSettings = @{
        AVFormatIDKey: @(kAudioFormatLinearPCM),
        AVSampleRateKey: @(8000.0f),                    // RongCloud要求8000Hz
        AVNumberOfChannelsKey: @(1),                    // 单声道
        AVLinearPCMBitDepthKey: @(16),                  // 16位采样精度
        AVLinearPCMIsNonInterleaved: @(NO),             // RongCloud要求参数
        AVLinearPCMIsFloatKey: @(NO),                   // 非浮点格式
        AVLinearPCMIsBigEndianKey: @(NO)                // 小端格式
    };
    
    // 创建录音器
    NSURL *recordingURL = [NSURL fileURLWithPath:self.recordingFilePath];
    NSError *recorderError;
    self.audioRecorder = [[AVAudioRecorder alloc] initWithURL:recordingURL
                                                      settings:recordSettings
                                                         error:&recorderError];
    
    if (recorderError || !self.audioRecorder) {
        
        [self updateState:SCVoiceRecordStateError duration:0.0 error:recorderError];
        return NO;
    }
    
    // 设置代理和参数
    self.audioRecorder.delegate = self;
    self.audioRecorder.meteringEnabled = YES; // 启用音量监测
    
    // 开始录音
    BOOL success = [self.audioRecorder record];
    if (success) {
        self.recordingStartTime = [NSDate date];
        [self updateState:SCVoiceRecordStateRecording duration:0.0 error:nil];
        [self startRecordingTimer];
        
        return YES;
    } else {
        NSError *startError = [NSError errorWithDomain:@"SCVoiceRecorderManager"
                                                  code:-1
                                              userInfo:@{NSLocalizedDescriptionKey: @"Failed to start recording".translateString}];
        [self updateState:SCVoiceRecordStateError duration:0.0 error:startError];
        
        return NO;
    }
}

- (BOOL)stopRecording {
    if (self.currentState != SCVoiceRecordStateRecording) {
        
        return NO;
    }
    
    // 停止录音
    [self.audioRecorder stop];
    [self stopRecordingTimer];
    
    // 检查录音时长是否满足最小要求
    if (self.currentDuration < self.minRecordDuration) {
        NSString *content = [@"Recording must be at least ### seconds".translateString stringByReplacingOccurrencesOfString:@"###" withString:[NSString stringWithFormat:@"%.0f", self.minRecordDuration]];
        NSError *durationError = [NSError errorWithDomain:@"SCVoiceRecorderManager"
                                                     code:-2
                                                 userInfo:@{NSLocalizedDescriptionKey: content}];
        [self updateState:SCVoiceRecordStateError duration:self.currentDuration error:durationError];
        [self cleanupCurrentRecording];
        return NO;
    }
    
    [self updateState:SCVoiceRecordStateCompleted duration:self.currentDuration error:nil];
    
    return YES;
}

- (void)cancelRecording {
    if (self.currentState == SCVoiceRecordStateRecording) {
        [self.audioRecorder stop];
        [self stopRecordingTimer];
    }
    
    [self cleanupCurrentRecording];
    [self updateState:SCVoiceRecordStateIdle duration:0.0 error:nil];
    
}

- (BOOL)pauseRecording {
    if (self.currentState != SCVoiceRecordStateRecording) {
        return NO;
    }
    
    [self.audioRecorder pause];
    [self stopRecordingTimer];
    [self updateState:SCVoiceRecordStatePaused duration:self.currentDuration error:nil];
    return YES;
}

- (BOOL)resumeRecording {
    if (self.currentState != SCVoiceRecordStatePaused) {
        return NO;
    }
    
    BOOL success = [self.audioRecorder record];
    if (success) {
        [self startRecordingTimer];
        [self updateState:SCVoiceRecordStateRecording duration:self.currentDuration error:nil];
    }
    return success;
}

#pragma mark - 辅助方法

- (void)startRecordingTimer {
    [self stopRecordingTimer];
    
    self.recordingTimer = [NSTimer scheduledTimerWithTimeInterval:0.1
                                                           target:self
                                                         selector:@selector(updateRecordingProgress)
                                                         userInfo:nil
                                                          repeats:YES];
}

- (void)stopRecordingTimer {
    if (self.recordingTimer) {
        [self.recordingTimer invalidate];
        self.recordingTimer = nil;
    }
}

- (void)updateRecordingProgress {
    if (self.audioRecorder && self.recordingStartTime) {
        // 更新录音时长
        self.currentDuration = [[NSDate date] timeIntervalSinceDate:self.recordingStartTime];
        
        // 检查是否超过最大录音时长
        if (self.currentDuration >= self.maxRecordDuration) {
            [self stopRecording];
            return;
        }
        
        // 更新音量（用于波纹动画）
        [self.audioRecorder updateMeters];
        float averagePower = [self.audioRecorder averagePowerForChannel:0];
        float volume = pow(10, (0.05 * averagePower)); // 转换为0-1范围
        volume = MAX(0.0, MIN(1.0, volume)); // 确保在0-1范围内
        
        // 通知代理
        if ([self.delegate respondsToSelector:@selector(voiceRecorderManager:didUpdateVolume:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.delegate voiceRecorderManager:self didUpdateVolume:volume];
            });
        }
    }
}

- (void)cleanupCurrentRecording {
    // 删除录音文件
    if (self.recordingFilePath) {
        NSFileManager *fileManager = [NSFileManager defaultManager];
        if ([fileManager fileExistsAtPath:self.recordingFilePath]) {
            NSError *error;
            [fileManager removeItemAtPath:self.recordingFilePath error:&error];
            if (error) {
                
            }
        }
        self.recordingFilePath = nil;
    }
    
    // 清理录音器
    self.audioRecorder = nil;
    self.recordingStartTime = nil;
    self.currentDuration = 0.0;
}

- (void)updateState:(SCVoiceRecordState)state duration:(NSTimeInterval)duration error:(nullable NSError *)error {
    self.currentState = state;
    
    if ([self.delegate respondsToSelector:@selector(voiceRecorderManager:didChangeState:duration:error:)]) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.delegate voiceRecorderManager:self
                               didChangeState:state
                                     duration:duration
                                        error:error];
        });
    }
}

#pragma mark - 公共方法

- (long long)getRecordingFileSize {
    if (!self.recordingFilePath) {
        return 0;
    }
    
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSDictionary *attributes = [fileManager attributesOfItemAtPath:self.recordingFilePath error:nil];
    return [attributes fileSize];
}

- (void)clearRecordingCache {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error;
    [fileManager removeItemAtPath:self.recordingCachePath error:&error];
    if (error) {
        
    } else {
        [self setupRecordingCachePath]; // 重新创建缓存目录
    }
}

#pragma mark - AVAudioRecorderDelegate

- (void)audioRecorderDidFinishRecording:(AVAudioRecorder *)recorder successfully:(BOOL)flag {
    if (flag) {
        
    } else {
        
        NSError *error = [NSError errorWithDomain:@"SCVoiceRecorderManager"
                                             code:-3
                                         userInfo:@{NSLocalizedDescriptionKey: @"An error occurred during recording".translateString}];
        [self updateState:SCVoiceRecordStateError duration:self.currentDuration error:error];
    }
}

- (void)audioRecorderEncodeErrorDidOccur:(AVAudioRecorder *)recorder error:(NSError *)error {
    
    [self updateState:SCVoiceRecordStateError duration:self.currentDuration error:error];
    [self cleanupCurrentRecording];
}

@end
