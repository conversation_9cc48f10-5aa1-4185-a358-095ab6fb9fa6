//
//  SCVoiceRecorderManager.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/17.
//  语音录制管理器 - 统一管理语音录制功能
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>

NS_ASSUME_NONNULL_BEGIN

// 录音状态枚举
typedef NS_ENUM(NSInteger, SCVoiceRecordState) {
    SCVoiceRecordStateIdle,         // 空闲状态
    SCVoiceRecordStateRecording,    // 录音中
    SCVoiceRecordStatePaused,       // 暂停
    SCVoiceRecordStateCompleted,    // 录音完成
    SCVoiceRecordStateError         // 录音错误
};

@class SCVoiceRecorderManager;

// 录音管理器代理协议
@protocol SCVoiceRecorderManagerDelegate <NSObject>

@optional

/**
 * 录音状态改变回调
 * @param manager 录音管理器实例
 * @param state 新的录音状态
 * @param duration 当前录音时长（秒）
 * @param error 错误信息（如果有）
 */
- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
            didChangeState:(SCVoiceRecordState)state
                  duration:(NSTimeInterval)duration
                     error:(nullable NSError *)error;

/**
 * 录音音量变化回调（用于波纹动画）
 * @param manager 录音管理器实例
 * @param volume 音量级别 (0.0 - 1.0)
 */
- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
            didUpdateVolume:(float)volume;

/**
 * 录音权限检查结果回调
 * @param manager 录音管理器实例
 * @param granted 是否已授权
 * @param shouldShowAlert 是否需要显示权限提示
 */
- (void)voiceRecorderManager:(SCVoiceRecorderManager *)manager
         permissionGranted:(BOOL)granted
           shouldShowAlert:(BOOL)shouldShowAlert;

@end

@interface SCVoiceRecorderManager : NSObject

// 单例实例
+ (instancetype)sharedManager;

// 代理
@property (nonatomic, weak) id<SCVoiceRecorderManagerDelegate> delegate;

// 当前录音状态
@property (nonatomic, assign, readonly) SCVoiceRecordState currentState;

// 当前录音时长
@property (nonatomic, assign, readonly) NSTimeInterval currentDuration;

// 录音文件路径
@property (nonatomic, copy, readonly, nullable) NSString *recordingFilePath;

// 最大录音时长（秒），默认60秒
@property (nonatomic, assign) NSTimeInterval maxRecordDuration;

// 最小录音时长（秒），默认1秒
@property (nonatomic, assign) NSTimeInterval minRecordDuration;

/**
 * 检查录音权限
 */
- (void)checkRecordPermission;

/**
 * 开始录音
 * 注意：开始录音时会自动停止当前正在播放的语音消息，确保录音和播放操作互斥
 * @return 是否成功开始录音
 */
- (BOOL)startRecording;

/**
 * 停止录音
 * @return 是否成功停止录音
 */
- (BOOL)stopRecording;

/**
 * 取消录音（删除录音文件）
 */
- (void)cancelRecording;

/**
 * 暂停录音
 * @return 是否成功暂停录音
 */
- (BOOL)pauseRecording;

/**
 * 恢复录音
 * @return 是否成功恢复录音
 */
- (BOOL)resumeRecording;

/**
 * 获取录音文件大小
 * @return 文件大小（字节）
 */
- (long long)getRecordingFileSize;

/**
 * 清理录音缓存
 */
- (void)clearRecordingCache;

@end

NS_ASSUME_NONNULL_END
