//
//  SCVoicePlayerManager.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  语音播放管理器 - 统一管理语音消息的下载、缓存和播放
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import <RongIMLib/RongIMLib.h>
#import "SCNativeMessageModel.h"

NS_ASSUME_NONNULL_BEGIN

/**
 * 语音播放器通知机制
 *
 * 为了解决Cell复用导致的messageId不一致问题，语音播放器采用通知机制替代单一代理模式。
 * 所有需要监听语音播放状态的组件都可以注册相应的通知监听器。
 *
 * 使用方式：
 * 1. 注册通知监听：
 *    [[NSNotificationCenter defaultCenter] addObserver:self
 *                                             selector:@selector(handleStateChange:)
 *                                                 name:SCVoicePlayerStateDidChangeNotification
 *                                               object:nil];
 *
 * 2. 在通知处理方法中根据messageId判断是否需要响应：
 *    - (void)handleStateChange:(NSNotification *)notification {
 *        long messageId = [notification.userInfo[SCVoicePlayerNotificationMessageIdKey] longValue];
 *        if (self.model.messageId != messageId) return;
 *        // 处理状态变化...
 *    }
 *
 * 3. 在dealloc中注销通知监听：
 *    [[NSNotificationCenter defaultCenter] removeObserver:self];
 */

// 通知名称常量
extern NSString * const SCVoicePlayerStateDidChangeNotification;        // 播放状态变化通知
extern NSString * const SCVoicePlayerDownloadProgressDidUpdateNotification;  // 下载进度更新通知

// 通知UserInfo的key
extern NSString * const SCVoicePlayerNotificationMessageIdKey;   // 消息ID (NSNumber *)
extern NSString * const SCVoicePlayerNotificationStateKey;       // 播放状态 (NSNumber *, SCVoicePlayState)
extern NSString * const SCVoicePlayerNotificationProgressKey;    // 下载进度 (NSNumber *, float 0.0-1.0)
extern NSString * const SCVoicePlayerNotificationErrorKey;       // 错误信息 (NSError *, 可选)

// 语音播放状态枚举
typedef NS_ENUM(NSInteger, SCVoicePlayState) {
    SCVoicePlayStateIdle,       // 空闲状态
    SCVoicePlayStateDownloading, // 下载中
    SCVoicePlayStatePlaying,    // 播放中
    SCVoicePlayStatePaused,     // 暂停
    SCVoicePlayStateCompleted,  // 播放完成
    SCVoicePlayStateError       // 播放错误
};

@protocol SCVoicePlayerManagerDelegate <NSObject>

@optional
/**
 * 语音播放状态变化回调
 * @param messageId 消息ID
 * @param state 播放状态
 * @param error 错误信息（仅在状态为Error时有值）
 */
- (void)voicePlayerManager:(id)manager didChangePlayState:(SCVoicePlayState)state forMessageId:(long)messageId error:(nullable NSError *)error;

/**
 * 语音下载进度回调
 * @param messageId 消息ID
 * @param progress 下载进度 (0.0 - 1.0)
 */
- (void)voicePlayerManager:(id)manager didUpdateDownloadProgress:(float)progress forMessageId:(long)messageId;

@end

@interface SCVoicePlayerManager : NSObject

// 单例实例
+ (instancetype)sharedManager;

// 代理
@property (nonatomic, weak) id<SCVoicePlayerManagerDelegate> delegate;

// 当前播放的消息ID
@property (nonatomic, assign, readonly) long currentPlayingMessageId;

// 当前播放状态
@property (nonatomic, assign, readonly) SCVoicePlayState currentPlayState;

/**
 * 播放语音消息
 * @param model 消息模型
 */
- (void)playVoiceMessage:(SCNativeMessageModel *)model;

/**
 * 停止当前播放
 */
- (void)stopCurrentPlaying;

/**
 * 暂停当前播放
 */
- (void)pauseCurrentPlaying;

/**
 * 恢复播放
 */
- (void)resumeCurrentPlaying;

/**
 * 检查指定消息是否正在播放
 * @param messageId 消息ID
 * @return 是否正在播放
 */
- (BOOL)isPlayingMessageId:(long)messageId;

/**
 * 获取指定消息的播放状态
 * @param messageId 消息ID
 * @return 播放状态
 */
- (SCVoicePlayState)playStateForMessageId:(long)messageId;

/**
 * 清理缓存的语音文件
 */
- (void)clearVoiceCache;

@end

NS_ASSUME_NONNULL_END
