//
//  SCConversationDisplayModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import <Foundation/Foundation.h>
// #import "SCUserInfoBaseModel.h" // 已移除，使用字典替代
#import "SCNativeConversationModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCConversationDisplayModel : NSObject

// 字典版本的用户信息
@property(nonatomic,strong,nullable) NSDictionary * userInfoDict;
@property(nonatomic,strong) SCNativeConversationModel * conversation;
@property(nonatomic,assign,readonly) BOOL isSystem;

//未读消息显示
@property(nonatomic,copy,readonly) NSString * unReadDisplayStr;
//实现显示未读消息红点
@property(nonatomic,assign,readonly) BOOL isShowUnRead;

#pragma mark - 便捷访问方法
// 这些方法从userInfoDict获取数据
- (NSString *)getUserID;
- (NSString *)getUserNickname;
- (NSString *)getUserAvatarUrl;
- (NSInteger)getUserAge;
- (NSInteger)getUserGender;
- (BOOL)hasUserInfo;

@end

NS_ASSUME_NONNULL_END
