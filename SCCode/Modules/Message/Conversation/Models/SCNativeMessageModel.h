//
//  SCNativeMessageModel.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  原生消息模型，替代RCMessageModel
//

#import <Foundation/Foundation.h>
#import <RongIMLib/RongIMLib.h>

NS_ASSUME_NONNULL_BEGIN

// 消息发送状态枚举
typedef NS_ENUM(NSInteger, SCMessageSentStatus) {
    SCMessageSentStatusUnknown = 0,    // 未知状态
    SCMessageSentStatusSending = 1,    // 发送中
    SCMessageSentStatusSent = 2,       // 已发送
    SCMessageSentStatusFailed = 3      // 发送失败
};

@interface SCNativeMessageModel : NSObject

// 消息基本属性
@property (nonatomic, assign) long messageId;
@property (nonatomic, assign) RCMessageDirection messageDirection;
@property (nonatomic, assign) long long sentTime;
@property (nonatomic, assign) long long receivedTime;
@property (nonatomic, copy) NSString *targetId;
@property (nonatomic, copy, nullable) NSString *senderUserId;
@property (nonatomic, assign) RCConversationType conversationType;
@property (nonatomic, strong) RCMessageContent *content;
@property (nonatomic, copy) NSString *messageUId;
@property (nonatomic, copy, nullable) NSString *extra;
@property (nonatomic, assign) BOOL isDisplayMessageTime;

// 发送状态相关属性
@property (nonatomic, assign) SCMessageSentStatus sentStatus;

// UI相关属性
@property (nonatomic, assign) CGSize cellSize;

// 便利构造方法
+ (instancetype)modelWithMessage:(RCMessage *)message;

// 转换方法
- (void)updateFromMessage:(RCMessage *)message;

// 时间显示计算方法
+ (void)calculateDisplayTimeForMessages:(NSMutableArray<SCNativeMessageModel *> *)messages;

@end

NS_ASSUME_NONNULL_END
