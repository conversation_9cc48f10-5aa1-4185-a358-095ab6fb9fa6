//
//  SCNativeMessageModel.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  原生消息模型，替代RCMessageModel
//

#import "SCNativeMessageModel.h"

@implementation SCNativeMessageModel

+ (instancetype)modelWithMessage:(RCMessage *)message {
    SCNativeMessageModel *model = [[SCNativeMessageModel alloc] init];
    [model updateFromMessage:message];
    return model;
}

- (void)updateFromMessage:(RCMessage *)message {
    if (!message) {
        return;
    }

    self.messageId = message.messageId;
    self.messageDirection = message.messageDirection;
    self.sentTime = message.sentTime;
    self.receivedTime = message.receivedTime;
    self.targetId = message.targetId;
    self.senderUserId = message.senderUserId;
    self.conversationType = message.conversationType;
    self.content = message.content;
    self.messageUId = message.messageUId;
    self.extra = message.extra;

    // isDisplayMessageTime 默认为NO，需要在UI层根据时间间隔计算
    self.isDisplayMessageTime = NO;
    
    // 根据消息方向设置发送状态
    if (message.messageDirection == MessageDirection_SEND) {
        // 发送的消息，根据messageId判断状态
        if (message.messageId > 0) {
            self.sentStatus = SCMessageSentStatusSent; // 已发送
        } else {
            self.sentStatus = SCMessageSentStatusSending; // 发送中
        }
    } else {
        self.sentStatus = SCMessageSentStatusSent; // 接收的消息都是已发送状态
    }
}

+ (void)calculateDisplayTimeForMessages:(NSMutableArray<SCNativeMessageModel *> *)messages {
    if (messages.count == 0) {
        return;
    }

    // 时间间隔阈值：5分钟（300秒）
    const NSTimeInterval kTimeIntervalThreshold = 300.0;

    // 第一条消息总是显示时间
    SCNativeMessageModel *firstMessage = messages.firstObject;
    firstMessage.isDisplayMessageTime = YES;

    // 从第二条消息开始计算时间间隔
    for (NSInteger i = 1; i < messages.count; i++) {
        SCNativeMessageModel *currentMessage = messages[i];
        SCNativeMessageModel *previousMessage = messages[i - 1];

        // 计算当前消息与前一条消息的时间间隔（秒）
        NSTimeInterval timeInterval = (currentMessage.sentTime - previousMessage.sentTime) / 1000.0;

        // 如果时间间隔大于阈值，则显示时间
        currentMessage.isDisplayMessageTime = (timeInterval >= kTimeIntervalThreshold);
    }
}

@end
