//
//  SCNativeConversationModel.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  原生会话模型，替代RCConversationModel
//

#import <Foundation/Foundation.h>
#import <RongIMLib/RongIMLib.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCNativeConversationModel : NSObject

// 会话基本属性
@property (nonatomic, assign) RCConversationType conversationType;
@property (nonatomic, copy) NSString *targetId;
@property (nonatomic, copy, nullable) NSString *conversationTitle;
@property (nonatomic, assign) int unreadMessageCount;
@property (nonatomic, assign) BOOL isTop;
@property (nonatomic, assign) long long receivedTime;
@property (nonatomic, assign) long long sentTime;
@property (nonatomic, strong, nullable) RCMessageContent *lastestMessage;
@property (nonatomic, assign) long lastestMessageId;

// 便利构造方法
+ (instancetype)modelWithConversation:(RCConversation *)conversation;

// 转换方法
- (void)updateFromConversation:(RCConversation *)conversation;

@end

NS_ASSUME_NONNULL_END
