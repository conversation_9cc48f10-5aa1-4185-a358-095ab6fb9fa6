//
//  SCNativeConversationModel.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/11.
//  原生会话模型，替代RCConversationModel
//

#import "SCNativeConversationModel.h"

@implementation SCNativeConversationModel

+ (instancetype)modelWithConversation:(RCConversation *)conversation {
    SCNativeConversationModel *model = [[SCNativeConversationModel alloc] init];
    [model updateFromConversation:conversation];
    return model;
}

- (void)updateFromConversation:(RCConversation *)conversation {
    if (!conversation) {
        return;
    }
    
    self.conversationType = conversation.conversationType;
    self.targetId = conversation.targetId;
    self.conversationTitle = conversation.conversationTitle;
    self.unreadMessageCount = conversation.unreadMessageCount;
    self.isTop = conversation.isTop;
    self.receivedTime = conversation.receivedTime;
    self.sentTime = conversation.sentTime;
    self.lastestMessage = conversation.latestMessage;
    self.lastestMessageId = conversation.latestMessageId;
}

@end
