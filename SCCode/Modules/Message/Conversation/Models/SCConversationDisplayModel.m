//
//  SCConversationDisplayModel.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCConversationDisplayModel.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCIMService.h"

@implementation SCConversationDisplayModel

- (NSString *)unReadDisplayStr{
    return self.conversation.unreadMessageCount > 99 ? @"99+" : [NSString stringWithFormat:@"%ld", (long)self.conversation.unreadMessageCount];;
}

- (BOOL)isShowUnRead{
    return self.conversation.unreadMessageCount > 0;
}

- (BOOL)isSystem {
    NSString *userID = [self getUserID];
    if (userID.length == 0) {
        return NO;
    }
    return [kScAuthMar.imService isSystemWithId:userID];
}

#pragma mark - 便捷访问方法

// 获取用户ID
- (NSString *)getUserID {
    if (self.userInfoDict) {
        return [SCDictionaryHelper stringFromDictionary:self.userInfoDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    }
    return @"";
}

// 获取用户昵称
- (NSString *)getUserNickname {
    if (self.userInfoDict) {
        return [SCDictionaryHelper stringFromDictionary:self.userInfoDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    }
    return @"";
}

// 获取用户头像URL
- (NSString *)getUserAvatarUrl {
    if (self.userInfoDict) {
        return [SCDictionaryHelper stringFromDictionary:self.userInfoDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    }
    return @"";
}

// 获取用户年龄
- (NSInteger)getUserAge {
    if (self.userInfoDict) {
        return [SCDictionaryHelper integerFromDictionary:self.userInfoDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    }
    return 0;
}

// 获取用户性别
- (NSInteger)getUserGender {
    if (self.userInfoDict) {
        return [SCDictionaryHelper integerFromDictionary:self.userInfoDict forKey:SCDictionaryKeys.shared.kSCUserGenderKey defaultValue:0];
    }
    return 0;
}

// 检查是否有用户信息
- (BOOL)hasUserInfo {
    return (self.userInfoDict != nil && [self.userInfoDict count] > 0);
}

@end
