//
//  SCIMUIConfig.h
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/1/24.
//

#ifndef SCIMUIConfig_h
#define SCIMUIConfig_h

///会话列表界面中显示的头像大小
#define kSCConversationAvatarWH 48.0f
///会话页面中显示的头像大小
#define kSCMessageAvatarWH 38.0f
///头像左边间距
#define kSCMessageAvatarLeftMargin 10.0f
///时间区域高度
#define kSCMessageTimeHeight 20.0f
///昵称的高度
#define kSCMessageNickNameHeight 20.0f
///内容和头像的间距
#define kSCMessageContentLeftMargin 10.0f
///内容区域最大宽度  【最大宽度为 减去 两倍的头像区域】
#define kSCMessageContentMaxWidth (kSCScreenWidth - (kSCMessageAvatarWH + kSCMessageAvatarLeftMargin + kSCMessageContentLeftMargin)*2)
///Text文本顶部间距
#define kSCMessageTextTopMargin 10.0f
///Text文本底部间距
#define kSCMessageTextBottomMargin 10.0f
///Text文本左右间距
#define kSCMessageTextLeftRightMargin 10.0f
///内容字体大小
#define kSCMessageTextFont kScUIFontMedium(13)
///翻译文本字体大小
#define kSCTranslateTextFont kScUIFontMedium(13)
///翻译加载中和错误的高度
#define kSCTranslateLoadingErrorHeight 37.0f
///分割线高度
#define kSCDividingLineHeight 1.0f/[UIScreen mainScreen].scale
///翻译按钮高度
#define kSCTranslateBtnHeight 16.0f
///点赞按钮最小宽度
#define kSCMessageLikeBtnMinWidth 104.0f
///点赞按钮高度
#define kSCMessageLikeBtnHeight 45.0f

#endif /* SCIMUIConfig_h */
