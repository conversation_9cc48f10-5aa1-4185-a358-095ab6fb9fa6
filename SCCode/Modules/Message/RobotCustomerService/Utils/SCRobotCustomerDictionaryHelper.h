//
//  SCRobotCustomerDictionaryHelper.h
//  Supercall
//
//  Created by AI Assistant on 2025/1/28.
//  机器人客服字典处理工具类 - Model迁移到字典的辅助工具
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - 字典键名常量定义

// SCRobotCustomerQuestionSetModel 相关键名

@interface SCRobotCustomerDictionaryHelper : NSObject

#pragma mark - 字典创建和初始化方法

/**
 * 创建问题集合字典
 * @param content 内容
 * @param faqInfoList FAQ信息数组（字典数组）
 * @return 问题集合字典
 */
+ (NSDictionary *)createQuestionSetDictWithContent:(NSString *)content 
                                       faqInfoList:(NSArray<NSDictionary *> *)faqInfoList;

/**
 * 创建FAQ信息字典
 * @param code 问题代码
 * @param question 问题内容
 * @param type 问题类型
 * @return FAQ信息字典
 */
+ (NSDictionary *)createFAQInfoDictWithCode:(NSInteger)code 
                                   question:(NSString *)question 
                                       type:(NSInteger)type;

/**
 * 创建消息回答字典
 * @param content 回答内容
 * @param eventHandleList 事件处理列表（字典数组）
 * @return 消息回答字典
 */
+ (NSDictionary *)createMessageAnswerDictWithContent:(NSString *)content 
                                     eventHandleList:(NSArray<NSDictionary *> *)eventHandleList;

/**
 * 创建回答事件处理字典
 * @param handleType 处理类型
 * @param matchStr 匹配字符串
 * @param toUrl 跳转URL
 * @return 回答事件处理字典
 */
+ (NSDictionary *)createAnswerEventHandleDictWithHandleType:(NSInteger)handleType 
                                                   matchStr:(NSString *)matchStr 
                                                      toUrl:(NSString *)toUrl;

/**
 * 创建我的问题字典
 * @param code 问题代码
 * @param question 问题内容
 * @return 我的问题字典
 */
+ (NSDictionary *)createMyQuestionDictWithCode:(NSInteger)code 
                                      question:(NSString *)question;

#pragma mark - 安全的字典访问方法

/**
 * 安全获取字符串值
 * @param dict 源字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 字符串值
 */
+ (NSString *)safeStringFromDict:(NSDictionary *)dict 
                          forKey:(NSString *)key 
                    defaultValue:(NSString *)defaultValue;

/**
 * 安全获取整数值
 * @param dict 源字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 整数值
 */
+ (NSInteger)safeIntegerFromDict:(NSDictionary *)dict 
                          forKey:(NSString *)key 
                    defaultValue:(NSInteger)defaultValue;

/**
 * 安全获取布尔值
 * @param dict 源字典
 * @param key 键名
 * @param defaultValue 默认值
 * @return 布尔值
 */
+ (BOOL)safeBoolFromDict:(NSDictionary *)dict 
                  forKey:(NSString *)key 
            defaultValue:(BOOL)defaultValue;

/**
 * 安全获取数组值
 * @param dict 源字典
 * @param key 键名
 * @return 数组值，失败时返回空数组
 */
+ (NSArray *)safeArrayFromDict:(NSDictionary *)dict 
                        forKey:(NSString *)key;

/**
 * 安全获取字典值
 * @param dict 源字典
 * @param key 键名
 * @return 字典值，失败时返回空字典
 */
+ (NSDictionary *)safeDictFromDict:(NSDictionary *)dict 
                            forKey:(NSString *)key;

#pragma mark - 特殊功能方法

/**
 * 从问题字符串中提取问题标题（去除前面的符号）
 * 对应原SCRobotCustomerFAQInfoModel的questionTitle属性
 * @param question 原始问题字符串
 * @return 处理后的问题标题
 */
+ (NSString *)questionTitleFromQuestion:(NSString *)question;

/**
 * 为字典生成UUID标识符
 * @param dict 目标字典
 * @param key UUID键名
 * @return 包含UUID的新字典
 */
+ (NSDictionary *)addUUIDToDict:(NSDictionary *)dict 
                         forKey:(NSString *)key;

/**
 * 深度复制字典（对应NSCopying协议）
 * @param dict 源字典
 * @return 深度复制的字典
 */
+ (NSDictionary *)deepCopyDict:(NSDictionary *)dict;

#pragma mark - 数据验证方法

/**
 * 验证问题集合字典的有效性
 * @param dict 问题集合字典
 * @return 是否有效
 */
+ (BOOL)isValidQuestionSetDict:(NSDictionary *)dict;

/**
 * 验证FAQ信息字典的有效性
 * @param dict FAQ信息字典
 * @return 是否有效
 */
+ (BOOL)isValidFAQInfoDict:(NSDictionary *)dict;

/**
 * 验证消息回答字典的有效性
 * @param dict 消息回答字典
 * @return 是否有效
 */
+ (BOOL)isValidMessageAnswerDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
