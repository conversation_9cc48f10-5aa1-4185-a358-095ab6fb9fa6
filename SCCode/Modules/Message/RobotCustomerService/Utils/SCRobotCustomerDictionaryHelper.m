//
//  SCRobotCustomerDictionaryHelper.m
//  Supercall
//
//  Created by AI Assistant on 2025/1/28.
//  机器人客服字典处理工具类 - Model迁移到字典的辅助工具
//

#import "SCRobotCustomerDictionaryHelper.h"

#pragma mark - 字典键名常量定义



@implementation SCRobotCustomerDictionaryHelper

#pragma mark - 字典创建和初始化方法

+ (NSDictionary *)createQuestionSetDictWithContent:(NSString *)content 
                                       faqInfoList:(NSArray<NSDictionary *> *)faqInfoList {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    // 生成UUID作为questionId
    dict[SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey] = [[NSUUID UUID] UUIDString];
    dict[SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey] = content ?: @"";
    dict[SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey] = faqInfoList ?: @[];
    
    return [dict copy];
}

+ (NSDictionary *)createFAQInfoDictWithCode:(NSInteger)code 
                                   question:(NSString *)question 
                                       type:(NSInteger)type {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    // 生成UUID作为faqId
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey] = [[NSUUID UUID] UUIDString];
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey] = @(code);
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoQuestionKey] = question ?: @"";
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoTypeKey] = @(type);
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoImageUrlKey] = @"";
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoMessageAnswerKey] = @{};
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoIsViewExampleKey] = @(NO);
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoHandleTypeKey] = @(0);
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoToUrlKey] = @"";
    dict[SCDictionaryKeys.shared.kSCRobotFAQInfoIsLikeKey] = @(0);
    
    return [dict copy];
}

+ (NSDictionary *)createMessageAnswerDictWithContent:(NSString *)content 
                                     eventHandleList:(NSArray<NSDictionary *> *)eventHandleList {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[SCDictionaryKeys.shared.kSCRobotMessageAnswerContentKey] = content ?: @"";
    dict[SCDictionaryKeys.shared.kSCRobotMessageAnswerEventHandleListKey] = eventHandleList ?: @[];
    
    return [dict copy];
}

+ (NSDictionary *)createAnswerEventHandleDictWithHandleType:(NSInteger)handleType 
                                                   matchStr:(NSString *)matchStr 
                                                      toUrl:(NSString *)toUrl {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[SCDictionaryKeys.shared.kSCRobotAnswerEventHandleTypeKey] = @(handleType);
    dict[SCDictionaryKeys.shared.kSCRobotAnswerEventMatchStrKey] = matchStr ?: @"";
    dict[SCDictionaryKeys.shared.kSCRobotAnswerEventToUrlKey] = toUrl ?: @"";
    
    return [dict copy];
}

+ (NSDictionary *)createMyQuestionDictWithCode:(NSInteger)code 
                                      question:(NSString *)question {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    
    dict[SCDictionaryKeys.shared.kSCMyQuestionCodeKey] = @(code);
    dict[SCDictionaryKeys.shared.kSCMyQuestionQuestionKey] = question ?: @"";
    
    return [dict copy];
}

#pragma mark - 安全的字典访问方法

+ (NSString *)safeStringFromDict:(NSDictionary *)dict 
                          forKey:(NSString *)key 
                    defaultValue:(NSString *)defaultValue {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return defaultValue;
    }
    
    id value = dict[key];
    if ([value isKindOfClass:[NSString class]]) {
        return value;
    } else if ([value isKindOfClass:[NSNumber class]]) {
        return [value stringValue];
    }
    
    return defaultValue;
}

+ (NSInteger)safeIntegerFromDict:(NSDictionary *)dict 
                          forKey:(NSString *)key 
                    defaultValue:(NSInteger)defaultValue {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return defaultValue;
    }
    
    id value = dict[key];
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value integerValue];
    } else if ([value isKindOfClass:[NSString class]]) {
        return [value integerValue];
    }
    
    return defaultValue;
}

+ (BOOL)safeBoolFromDict:(NSDictionary *)dict 
                  forKey:(NSString *)key 
            defaultValue:(BOOL)defaultValue {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return defaultValue;
    }
    
    id value = dict[key];
    if ([value isKindOfClass:[NSNumber class]]) {
        return [value boolValue];
    } else if ([value isKindOfClass:[NSString class]]) {
        return [value boolValue];
    }
    
    return defaultValue;
}

+ (NSArray *)safeArrayFromDict:(NSDictionary *)dict 
                        forKey:(NSString *)key {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return @[];
    }
    
    id value = dict[key];
    if ([value isKindOfClass:[NSArray class]]) {
        return value;
    }
    
    return @[];
}

+ (NSDictionary *)safeDictFromDict:(NSDictionary *)dict 
                            forKey:(NSString *)key {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return @{};
    }
    
    id value = dict[key];
    if ([value isKindOfClass:[NSDictionary class]]) {
        return value;
    }
    
    return @{};
}

#pragma mark - 特殊功能方法

+ (NSString *)questionTitleFromQuestion:(NSString *)question {
    if (!question || ![question isKindOfClass:[NSString class]] || question.length == 0) {
        return @"";
    }
    
    // 创建正则表达式模式 - 对应原SCRobotCustomerFAQInfoModel的questionTitle逻辑
    NSString *pattern = @"^[^a-zA-Z0-9\\s]*\\s*(.*)$";
    
    // 创建正则表达式对象
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:pattern 
                                                                           options:0 
                                                                             error:&error];
    if (error) {
        
        return question;
    }
    
    // 匹配输入字符串
    NSTextCheckingResult *match = [regex firstMatchInString:question 
                                                    options:0 
                                                      range:NSMakeRange(0, question.length)];
    
    if (match && match.numberOfRanges > 1) {
        // 获取匹配到的子字符串的范围
        NSRange resultRange = [match rangeAtIndex:1];
        
        if (resultRange.location != NSNotFound && resultRange.length > 0) {
            // 截取字符串
            NSString *resultString = [question substringWithRange:resultRange];
            
            return resultString;
        }
    }
    
    // 如果字符串中只包含空格和符号，则不进行截取，保持原样输出
    
    return question;
}

+ (NSDictionary *)addUUIDToDict:(NSDictionary *)dict 
                         forKey:(NSString *)key {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]] || !key) {
        return dict ?: @{};
    }
    
    NSMutableDictionary *mutableDict = [dict mutableCopy];
    mutableDict[key] = [[NSUUID UUID] UUIDString];
    
    return [mutableDict copy];
}

+ (NSDictionary *)deepCopyDict:(NSDictionary *)dict {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]]) {
        return @{};
    }
    
    // 使用NSKeyedArchiver进行深度复制
    NSError *error = nil;
    NSData *data = [NSKeyedArchiver archivedDataWithRootObject:dict 
                                         requiringSecureCoding:NO 
                                                         error:&error];
    if (error || !data) {
        
        return dict;
    }
    
    NSDictionary *copiedDict = [NSKeyedUnarchiver unarchivedObjectOfClass:[NSDictionary class] 
                                                                 fromData:data 
                                                                    error:&error];
    if (error) {
        
        return dict;
    }
    
    return copiedDict ?: dict;
}

#pragma mark - 数据验证方法

+ (BOOL)isValidQuestionSetDict:(NSDictionary *)dict {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]]) {
        return NO;
    }

    // 检查必需的字段
    NSString *questionId = [self safeStringFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey defaultValue:@""];
    NSString *content = [self safeStringFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey defaultValue:@""];
    NSArray *faqInfoList = [self safeArrayFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey];

    // questionId和content不能为空，faqInfoList必须是数组
    if (questionId.length == 0 || content.length == 0) {
        return NO;
    }

    // 验证faqInfoList中的每个元素
    for (id item in faqInfoList) {
        if (![item isKindOfClass:[NSDictionary class]] || ![self isValidFAQInfoDict:item]) {
            return NO;
        }
    }

    return YES;
}

+ (BOOL)isValidFAQInfoDict:(NSDictionary *)dict {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]]) {
        return NO;
    }

    // 检查必需的字段
    NSString *faqId = [self safeStringFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey defaultValue:@""];
    NSString *question = [self safeStringFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoQuestionKey defaultValue:@""];
    NSInteger code = [self safeIntegerFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey defaultValue:-1];
    NSInteger type = [self safeIntegerFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoTypeKey defaultValue:-1];

    // faqId和question不能为空，code和type必须有效
    if (faqId.length == 0 || question.length == 0 || code < 0 || type < 0) {
        return NO;
    }

    // 检查messageAnswer字段（如果存在）
    NSDictionary *messageAnswer = [self safeDictFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoMessageAnswerKey];
    if (messageAnswer.count > 0 && ![self isValidMessageAnswerDict:messageAnswer]) {
        return NO;
    }

    return YES;
}

+ (BOOL)isValidMessageAnswerDict:(NSDictionary *)dict {
    if (!dict || ![dict isKindOfClass:[NSDictionary class]]) {
        return NO;
    }

    // 检查必需的字段
    NSString *content = [self safeStringFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotMessageAnswerContentKey defaultValue:@""];
    NSArray *eventHandleList = [self safeArrayFromDict:dict forKey:SCDictionaryKeys.shared.kSCRobotMessageAnswerEventHandleListKey];

    // content不能为空
    if (content.length == 0) {
        return NO;
    }

    // 验证eventHandleList中的每个元素
    for (id item in eventHandleList) {
        if (![item isKindOfClass:[NSDictionary class]]) {
            return NO;
        }

        NSDictionary *eventDict = (NSDictionary *)item;
        NSInteger handleType = [self safeIntegerFromDict:eventDict forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventHandleTypeKey defaultValue:-1];
        NSString *matchStr = [self safeStringFromDict:eventDict forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventMatchStrKey defaultValue:@""];
        NSString *toUrl = [self safeStringFromDict:eventDict forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventToUrlKey defaultValue:@""];

        // handleType必须有效，matchStr不能为空
        if (handleType < 0 || matchStr.length == 0) {
            return NO;
        }
    }

    return YES;
}

@end
