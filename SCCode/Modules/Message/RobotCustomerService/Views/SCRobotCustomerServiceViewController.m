//
//  SCRobotCustomerServiceViewController.m
//  Supercall
//
//  Created by guan<PERSON>hong on 2024/2/7.
//

#import "SCRobotCustomerServiceViewController.h"
#import "SCNavigationBar.h"
#import "SCRobotCustomerQuestionSetCell.h"
#import "SCAPIServiceManager.h"
#import "SCIMService.h"
#import "SCConversationInfoViewController.h"
#import "SCRobotCustomerQuestionCell.h"
#import "SCRobotCustomerAnswerCell.h"
#import "SCWebViewController.h"
#import "SCRobotCustomerDictionaryHelper.h"

@interface SCRobotCustomerServiceViewController ()<SCTextMessageCellDelegate,SCRobotCustomerAnswerCellDelegate>

//列表
@property(nonatomic,weak) UITableView * tableView;
@property(nonatomic,strong) NSMutableArray * messages;
@property(nonatomic,strong) NSDictionary * questionSetDict; // 字典版本

@property(nonatomic,strong) NSMutableDictionary<NSString*,NSNumber *> * translationStatusDic; //记录翻译的状态

@end

@interface SCRobotCustomerServiceViewController (SCRobotCustomerTableView)<UITableViewDelegate,UITableViewDataSource>

@end

@implementation SCRobotCustomerServiceViewController

- (NSMutableDictionary<NSString *,NSNumber *> *)translationStatusDic {
    if (!_translationStatusDic) {
        _translationStatusDic = [NSMutableDictionary new];
    }
    return _translationStatusDic;
}

- (void)initData{
    [super initData];
    self.messages = [NSMutableArray array];
}

- (void)initUI{
    [super initUI];
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.scNavigationBar.backgroundColor = [UIColor scChatBgColor];
    
    self.title = @"Customer Service".translateString;
    //人工客服按钮
    UIButton * customerBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_robot_customer"] target:self action:@selector(onClickCustomerService)]
    .addSuperView(self.scNavigationBar);
    [customerBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.trailing.bottom.equalTo(self.scNavigationBar.contentView);
        make.width.equalTo(customerBtn.mas_height);
    }];
    
    _tableView = [UITableView tableViewWithFrame:self.scContentView.bounds style:UITableViewStylePlain delegate:self dataSource:self].addSuperView(self.scContentView);
    _tableView.backgroundColor = [UIColor scChatBgColor];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    _tableView.contentInset = UIEdgeInsetsMake(10.0f, 0, 0, 0);
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
    
    kWeakSelf(self);
    [SCAPIServiceManager requestRobotCustomerQuestionSetWithSuccess:^(NSDictionary * _Nonnull questionSetDict) {
        // 使用字典版本处理数据
        weakself.questionSetDict = questionSetDict;
        [weakself.messages removeAllObjects];
        [weakself.messages addObject:questionSetDict];
        [weakself.tableView reloadData];
    } failure:nil];
}

- (SCTranslationStatus)translationStatusWithKey:(NSString *)key {
    NSNumber *status = [self.translationStatusDic objectForKey:key];
    if (status) {
        return status.integerValue;
    }
    return SCTranslationStatusNormal;
}

- (void)sc_blank_empty{
    
}
///点击客服按钮
-(void)onClickCustomerService{
    //客服
    kWeakSelf(self)
    // 检查客服账户字典是否存在
    if(kScAuthMar.imService.userServiceAccountObx.value == nil || [kScAuthMar.imService.userServiceAccountObx.value count] == 0){
        [kScAuthMar.imService remoteUserServiceAccountWithSuccess:^(NSDictionary * _Nonnull userServiceAccountDict) {
            [weakself sc_blank_empty];
            // 使用字典版本的方法打开会话页面
            NSString *userID = [SCDictionaryHelper stringFromDictionary:userServiceAccountDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:userServiceAccountDict];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            [kSCKeyWindow toast:@"Unable to match customer service, please try again later".translateString];
        }];
    }else{
        // 使用字典版本的方法打开会话页面
        NSString *userID = [SCDictionaryHelper stringFromDictionary:kScAuthMar.imService.userServiceAccountObx.value forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:kScAuthMar.imService.userServiceAccountObx.value];
    }
}

- (void)translateWithKey:(NSString *)key translateText:(NSString *)translateText {
    [self.translationStatusDic setObject:@(SCTranslationStatusTranslating) forKey:key];
    
    kWeakSelf(self);
    __block BOOL isSuccess = false;
    [kSCAuthTranslaService translateText:translateText completion:^(NSString * _Nonnull t, SCXErrorModel * _Nonnull error) {
        if (!error) {
            isSuccess = true;
            [weakself.translationStatusDic setObject:@(SCTranslationStatusSuccess) forKey:key];
        } else {
            [weakself.translationStatusDic setObject:@(SCTranslationStatusFail) forKey:key];
        }
        ///刷新页面
        [weakself.tableView reloadData];
    }];
    dispatch_async(dispatch_get_main_queue(), ^{
        if(!isSuccess){
            ///刷新页面
            [weakself.tableView reloadData];
        }
    });
}

#pragma mark - SCTextMessageCellDelegate
- (void)robotCustomerQuestionSetCell:(SCRobotCustomerQuestionSetCell *)cell scTextMessageCellDidTapTranslation:(NSString *)translationStr{
    NSString *questionId = [SCRobotCustomerDictionaryHelper safeStringFromDict:cell.modelDict
                                                                        forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey
                                                                  defaultValue:@""];
    NSString *key = questionId.md5String;
    [self translateWithKey:key translateText:translationStr];
}
- (void)robotCustomerQuestionSetCell:(SCRobotCustomerQuestionSetCell *)cell tapQuestion:(NSInteger)questionCode{

    // 使用字典版本处理数据
    NSArray *faqInfoList = [SCRobotCustomerDictionaryHelper safeArrayFromDict:self.questionSetDict
                                                                       forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey];

    for (NSDictionary *itemDict in faqInfoList) {
        NSInteger code = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:itemDict
                                                                       forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey
                                                                 defaultValue:-1];
        if(code == questionCode){
            NSInteger type = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:itemDict
                                                                           forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoTypeKey
                                                                     defaultValue:-1];
            if(type == 1){
                //消息
                NSString *question = [SCRobotCustomerDictionaryHelper safeStringFromDict:itemDict
                                                                                  forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoQuestionKey
                                                                            defaultValue:@""];
                NSString *questionTitle = [SCRobotCustomerDictionaryHelper questionTitleFromQuestion:question];

                // 创建我的问题字典
                NSDictionary *myQuestionDict = [SCRobotCustomerDictionaryHelper createMyQuestionDictWithCode:questionCode
                                                                                                    question:questionTitle];

                // 创建FAQ信息字典的副本并生成新的faqId
                NSMutableDictionary *faqDict = [itemDict mutableCopy];
                [faqDict removeObjectForKey:SCDictionaryKeys.shared.kSCMyQuestionCodeKey];
                [faqDict removeObjectForKey:SCDictionaryKeys.shared.kSCMyQuestionQuestionKey];
                faqDict[SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey] = [[NSUUID UUID] UUIDString];
                faqDict[SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey] = [[NSUUID UUID] UUIDString];
                [self.messages addObject:myQuestionDict];
                [self.messages addObject:[faqDict copy]];
                [self.tableView reloadData];
                ///延迟0.1秒
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self _scrollToBottom];
                });
            }else if(type == 2){
                ///直接处理
                NSInteger handleType = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:itemDict
                                                                                     forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoHandleTypeKey
                                                                               defaultValue:0];
                if(handleType == 1){
                    ///客服
                    [self onClickCustomerService];
                }else if(handleType == 2){
                    //转跳网页
                    NSString *toUrl = [SCRobotCustomerDictionaryHelper safeStringFromDict:itemDict
                                                                                   forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoToUrlKey
                                                                             defaultValue:@""];
                    [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:@"" url:toUrl];
                }else if(handleType == 3){
                    //3： 跳转第三方充值页
#warning 先转跳网页，后续和其他开发同步
                    NSString *toUrl = [SCRobotCustomerDictionaryHelper safeStringFromDict:itemDict
                                                                                   forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoToUrlKey
                                                                             defaultValue:@""];
                    [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:@"" url:toUrl];
                }else{
                    ///无法提供帮助，请稍后再试 （英文）
                    [self.view toast:@"Unable to provide help, please try again later".translateString];
                }

            }else{
                ///无法提供帮助，请稍后再试 （英文）
                [self.view toast:@"Unable to provide help, please try again later".translateString];
            }

            return;
        }
    }

}
#pragma mark - SCRobotCustomerAnswerCellDelegate
- (void)robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell tapTranslation:(NSString *)translationStr{
    NSString *faqId = [SCRobotCustomerDictionaryHelper safeStringFromDict:cell.modelDict
                                                                   forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey
                                                             defaultValue:@""];
    NSString *key = faqId.md5String;
    [self translateWithKey:key translateText:translationStr];
}

- (void)robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell viewExample:(NSString *)url{
    [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:@"" url:url];
}

- (void)robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell tapEventHandleType:(NSInteger)handleType toUrl:(NSString *)toUrl{
    if(handleType == 1){
        ///客服
        [self onClickCustomerService];
    }else if(handleType == 2){
        //转跳网页
        [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:@"" url:toUrl];
    }else if(handleType == 3){
        //3： 跳转第三方充值页
        [SCWebViewController showWithFromVC:[UIViewController currentViewController] title:@"" url:toUrl];
    }
}

- (SCTranslationStatus)getTranslationStatusWithKey:(NSString *)key {
    return [self translationStatusWithKey:key];
}

//滑动到最底部
-(void) _scrollToBottom{
    NSInteger lastSectionIndex = self.tableView.numberOfSections - 1;
    if (lastSectionIndex >= 0) {
        NSInteger lastRowIndex = [self.tableView numberOfRowsInSection:lastSectionIndex] - 1;
        if (lastRowIndex >= 0) {
            NSIndexPath *lastIndexPath = [NSIndexPath indexPathForRow:lastRowIndex inSection:lastSectionIndex];
            [self.tableView scrollToRowAtIndexPath:lastIndexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
        }
    }
}


#pragma mark - tableView

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    NSDictionary *dict = self.messages[indexPath.row];

    // 检查是否是问题集合字典
    if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey]){
        SCRobotCustomerQuestionSetCell *cell = [SCRobotCustomerQuestionSetCell initWithFormTableView:tableView];
        cell.modelDict = dict;
        cell.scDelegate = self;
        return cell;
    }
    // 检查是否是我的问题字典
    else if([dict objectForKey:SCDictionaryKeys.shared.kSCMyQuestionCodeKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCMyQuestionQuestionKey]){
        SCRobotCustomerQuestionCell *cell = [SCRobotCustomerQuestionCell initWithFormTableView:tableView];
        cell.modelDict = dict;
        return cell;
    }
    // 检查是否是FAQ信息字典
    else if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey]){
        SCRobotCustomerAnswerCell *cell = [SCRobotCustomerAnswerCell initWithFormTableView:tableView];
        cell.modelDict = dict;
        cell.scDelegate = self;

        kWeakSelf(self)
        cell.feedbackDictBlock = ^(NSDictionary *modelDict, NSInteger likeStatus) {
            // 更新数据源中的字典
            NSInteger index = [weakself.messages indexOfObject:modelDict];
            if (index != NSNotFound) {
                NSMutableDictionary *mutableDict = [modelDict mutableCopy];
                mutableDict[SCDictionaryKeys.shared.kSCRobotFAQInfoIsLikeKey] = @(likeStatus);
                weakself.messages[index] = [mutableDict copy];

                // 显示提示
                NSString *message = (likeStatus == 1) ?
                    @"Helpful Successfull".translateString :
                    @"Unhelpful Successfull".translateString;
                [kSCKeyWindow toast:message];
            }
        };

        return cell;
    }
    
    SCRobotCustomerQuestionCell *cell = [SCRobotCustomerQuestionCell initWithFormTableView:tableView];
    
    
    
    return cell;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger row = indexPath.row;
    NSDictionary *dict = self.messages[row];
    if(kSCAuthTranslaService.isAutoTranslation){
        //开启自动翻译和不是自己的消息。 会自动将为开始翻译的文本自动开始翻译

        // 检查是否是问题集合字典
        if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey]){
            NSString *questionId = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                               forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey
                                                                         defaultValue:@""];
            SCTranslationStatus translateStatus = [self getTranslationStatusWithKey:questionId.md5String];
            if(translateStatus == SCTranslationStatusNormal){
                ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
                NSString *content = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                                 forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey
                                                                           defaultValue:@""];
                NSMutableAttributedString *questionAtt = [SCRobotCustomerQuestionSetCell questionAttributedTextWithDict:dict];
                NSString *translateText = [NSString stringWithFormat:@"%@\n%@", content, questionAtt.string];
                [self translateWithKey:questionId.md5String translateText:translateText];
            }
        }
        // 检查是否是FAQ信息字典
        else if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey]){
            NSString *faqId = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                           forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey
                                                                     defaultValue:@""];
            SCTranslationStatus translateStatus = [self getTranslationStatusWithKey:faqId.md5String];
            if(translateStatus == SCTranslationStatusNormal){
                ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
                NSMutableAttributedString *questionAtt = [SCRobotCustomerAnswerCell questionAttributedTextWithDict:dict];
                [self translateWithKey:faqId.md5String translateText:questionAtt.string];
            }
        }
    }
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.messages count];
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSDictionary *dict = self.messages[indexPath.row];

    // 检查是否是问题集合字典
    if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey]){
        return [SCRobotCustomerQuestionSetCell cellHeightWithDict:dict];
    }
    // 检查是否是我的问题字典
    else if([dict objectForKey:SCDictionaryKeys.shared.kSCMyQuestionCodeKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCMyQuestionQuestionKey]){
        return [SCRobotCustomerQuestionCell cellHeightWithDict:dict];
    }
    // 检查是否是FAQ信息字典
    else if([dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey] && [dict objectForKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey]){
        return [SCRobotCustomerAnswerCell cellHeightWithDict:dict];
    }
    return 0;
}

@end
