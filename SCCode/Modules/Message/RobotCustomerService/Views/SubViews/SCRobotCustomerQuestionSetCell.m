//
//  SCRobotCustomerProblemSetCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/2/7.
//

#import "SCRobotCustomerQuestionSetCell.h"
#import "SCIMUIConfig.h"
#import "SCRobotCustomerServiceViewController.h"
#import "SCRobotCustomerDictionaryHelper.h"

@interface SCRobotCustomerQuestionSetCell()

//头像
@property(nonatomic,weak) UIImageView *avatarIV;
//泡泡
@property(nonatomic,weak) UIView *bubbleV;
//文本内容
@property(nonatomic,weak) UILabel *contentL;
//问题
@property(nonatomic,weak) UILabel *questionLabel;
//翻译分割线
@property(nonatomic,weak) UIView *translateLineV;
//翻译文本
@property(nonatomic,weak) UILabel *translateLabel;
///翻译加载框
@property(nonatomic,weak) UIActivityIndicatorView * translateLoadingV;
///翻译失败的提示
@property(nonatomic,weak) UILabel * translateErrorL;
///翻译状态
@property(nonatomic,assign) SCTranslationStatus translateStatus;
///翻译按钮
@property(nonatomic,weak) UIButton * translateBtn;

@end


@implementation SCRobotCustomerQuestionSetCell

-(void)initUI{
    [super initUI];
    self.backgroundColor = [UIColor scChatBgColor];    
    self.contentView.backgroundColor = [UIColor scChatBgColor];
    
    _avatarIV = [UIImageView new].setImageName(@"avatar_robot_customer").addSuperView(self.contentView);
    _bubbleV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#32312F")).setCornerRadius(18.0f).addSuperView(self.contentView);
    
    _contentL = UILabel.new.setTextColor([kSCColorWithHexStr(@"#FFFFFF") colorWithAlphaComponent:0.85f]).setFont(kScUIFontRegular(14.0f)).setNumberLines(0).addSuperView(_bubbleV);
    
    _questionLabel = UILabel.new.setTextColor(kSCColorWithHexStr(@"#FF6B00")).setFont(kScUIFontRegular(14.0f)).setNumberLines(0).setText(@"Question".translateString).addSuperView(_bubbleV);
    kSCAddTapGesture(self.questionLabel, self, onTapQuestionLabel:);
    
    _translateLineV = UIView.new.setBackgroundColor([kSCColorWithHexStr(@"#FFFFFF") colorWithAlphaComponent:0.27]).addSuperView(_bubbleV);
    
    _translateLabel = UILabel.new.setTextColor([kSCColorWithHexStr(@"#FFFFFF") colorWithAlphaComponent:0.85f]).setFont(kScUIFontRegular(14.0f)).setNumberLines(0).setText(@"Translate".translateString).addSuperView(_bubbleV);
    
    
    UIActivityIndicatorView * translateLoadingV = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium].addSuperView(self.bubbleV);
    translateLoadingV.color = [UIColor colorWithHexString:@"#FFF549"];
    translateLoadingV.hidesWhenStopped = YES;
    translateLoadingV.hidden = YES;
    self.translateLoadingV = translateLoadingV;
    
    _translateErrorL = UILabel.new.setTextColor(kSCColorWithHexStr(@"#FFFFFF")).setFont(kScUIFontRegular(14.0f)).setText(@"Translation failed.".translateString).addSuperView(_bubbleV);
    kSCAddTapGesture(_translateErrorL, self, onTranslate);
    
    UIButton * translateBtn = [UIButton buttonWithTitle:@"Click to translate".translateString titleColor:kSCColorWithHexStr(@"#FFC200") font:kScUIFontRegular(12) image:[SCResourceManager loadImageWithName:@"ic_message_translate"] backgroundImage:nil target:self action:@selector(onTranslate)];
    [translateBtn setTitleEdgeInsets: kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(0, 0, 0, 4) : UIEdgeInsetsMake(0, 4, 0, 0)];
    [self.contentView addSubview:translateBtn];
    self.translateBtn = translateBtn;
    
}


// 字典版本的setter方法
- (void)setModelDict:(NSDictionary *)modelDict{
    _modelDict = modelDict;

    // 使用字典助手类安全访问数据
    NSString *content = [SCRobotCustomerDictionaryHelper safeStringFromDict:modelDict
                                                                     forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey
                                                               defaultValue:@""];
    self.contentL.text = content;

    // 设置问题文本
    self.questionLabel.attributedText = [SCRobotCustomerQuestionSetCell questionAttributedTextWithDict:modelDict];

    // 需要翻译的文案
    NSString *translateText = [NSString stringWithFormat:@"%@\n%@", self.contentL.text, self.questionLabel.attributedText.string];

    // 获取questionId用于翻译状态管理
    NSString *questionId = [SCRobotCustomerDictionaryHelper safeStringFromDict:modelDict
                                                                        forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey
                                                                  defaultValue:@""];
    NSString *key = questionId.md5String;
    if ([self.scDelegate respondsToSelector:@selector(getTranslationStatusWithKey:)]) {
        self.translateStatus = [self.scDelegate getTranslationStatusWithKey:key];
    } else {
        self.translateStatus = SCTranslationStatusNormal;
    }

    if(kSCAuthTranslaService.isAutoTranslation && self.translateStatus == SCTranslationStatusNormal){
        // 如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
        self.translateStatus = SCTranslationStatusTranslating;
    }

    [self updateTranslateUI:translateText];
}
-(void) updateTranslateUI:(NSString *)translateText{
    self.translateBtn.hidden = YES;
    [self.translateLoadingV stopAnimating];
    self.translateLoadingV.hidden = YES;
    self.translateErrorL.hidden = YES;
    self.translateLabel.hidden = YES;
    self.translateLineV.hidden = YES;


    
    switch (self.translateStatus) {
        case SCTranslationStatusTranslating:
            //翻译中
            self.translateLoadingV.hidden = NO;
            [self.translateLoadingV startAnimating];
            self.translateLineV.hidden = NO;
            break;
        case SCTranslationStatusFail:
            //翻译失败
            self.translateErrorL.hidden = NO;
            self.translateLineV.hidden = NO;
            break;
        case SCTranslationStatusSuccess:
            //翻译成功
            self.translateLabel.hidden = NO;
            self.translateLineV.hidden = NO;
            self.translateLabel.text = [kSCAuthTranslaService getCacheWithText:translateText];
            break;
        default:
            self.translateBtn.hidden = NO;
            break;
    }
}



- (void)layoutSubviews{
    [super layoutSubviews];
    
    self.avatarIV.frame = CGRectMake(kSCMessageAvatarLeftMargin, 0, kSCMessageAvatarWH, kSCMessageAvatarWH);
    
    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    //文本高度
    CGSize contentSize = [UILabel labelSizeWithText:self.contentL.text font:self.contentL.font maxSize:CGSizeMake(contentW, 1000)];
    
    CGSize questionSize = [self.questionLabel.attributedText boundingRectWithSize:CGSizeMake(contentW, 1000) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;
    
    CGSize translateSize = [UILabel labelSizeWithText:self.translateLabel.text font:self.translateLabel.font maxSize:CGSizeMake(contentW, 1000)];
    CGSize translateFailSize = [UILabel labelSizeWithText:self.translateErrorL.text font:self.translateErrorL.font maxSize:CGSizeMake(contentW, 1000)];
    
    CGFloat bubbleY = 0;
    CGFloat bubbleX = kSCMessageAvatarLeftMargin + kSCMessageAvatarWH + kSCMessageContentLeftMargin;
    CGFloat contentX = kSCMessageTextTopMargin;
    CGFloat contentY = kSCMessageTextLeftRightMargin;
    
    CGFloat bubbleWidth = MAX(contentSize.width,questionSize.width);
    
    ///更新内容布局
    self.contentL.frame = CGRectMake(contentX, contentY, bubbleWidth, contentSize.height);
    ///更新问题布局
    self.questionLabel.frame = CGRectMake(contentX, self.contentL.scBottom + kSCMessageTextBottomMargin, bubbleWidth, questionSize.height);
    
    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height  + kSCMessageTextBottomMargin + questionSize.height + kSCMessageTextBottomMargin;
    
    switch (self.translateStatus) {
        case SCTranslationStatusTranslating:
            //翻译中
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译中
            self.translateLoadingV.scCenterX = bubbleWidth/2.0;
            self.translateLoadingV.scTop = self.translateLineV.scBottom + kSCMessageTextTopMargin;
            
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight ;
            break;
        case SCTranslationStatusFail:
            bubbleWidth = MAX(bubbleWidth,translateFailSize.width);
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译失败
            self.translateErrorL.frame = CGRectMake(contentX, self.translateLineV.scBottom, bubbleWidth, kSCTranslateLoadingErrorHeight);
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight ;
            break;
        case SCTranslationStatusSuccess:
        {
            bubbleWidth = MAX(bubbleWidth,translateSize.width);
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译成功
            self.translateLabel.frame = CGRectMake(contentX, self.translateLineV.scBottom + kSCMessageTextTopMargin, bubbleWidth, translateSize.height);
            bubbleHeight +=  kSCDividingLineHeight +  kSCMessageTextTopMargin*2 + translateSize.height;
            
        }
            break;
        default:
            
            break;
    }
    
    self.bubbleV.frame = CGRectMake(bubbleX, bubbleY, bubbleWidth + kSCMessageTextLeftRightMargin*2, bubbleHeight);
    if(!self.translateBtn.hidden){
        // 计算翻译按钮文本的实际宽度
        NSString *translateText = self.translateBtn.titleLabel.text;
        UIFont *translateFont = self.translateBtn.titleLabel.font;
        CGSize textSize = [translateText boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16.0f)
                                                    options:NSStringDrawingUsesLineFragmentOrigin
                                                 attributes:@{NSFontAttributeName: translateFont}
                                                    context:nil].size;
        
        // 添加左右边距和图标宽度
        CGFloat iconWidth = 16.0f;
        CGFloat padding = 4.0f;    // 文本与图标的间距
        CGFloat btnWidth = textSize.width + iconWidth + padding;
        
        self.translateBtn.frame = CGRectMake(bubbleX, self.bubbleV.scBottom + 5, btnWidth, 16.0f);
    }
    
    if (kScAuthMar.isLanguageForce) {
        self.avatarIV.scRight = self.contentView.scWidth - kSCMessageAvatarLeftMargin;
        self.bubbleV.scRight = self.contentView.scWidth - bubbleX;
        self.contentL.scRight = self.bubbleV.scWidth - contentX;
        self.questionLabel.scRight = self.bubbleV.scWidth - contentX;
        self.translateBtn.scRight = self.bubbleV.scWidth + bubbleX;
    }
}



#pragma mark - action
-(void)onTranslate{
    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(robotCustomerQuestionSetCell:scTextMessageCellDidTapTranslation:)]){
        NSMutableAttributedString *questionAtt = [SCRobotCustomerQuestionSetCell questionAttributedTextWithDict:self.modelDict];
        NSString *content = [SCRobotCustomerDictionaryHelper safeStringFromDict:self.modelDict
                                                                         forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey
                                                                   defaultValue:@""];
        NSString *translateText = [NSString stringWithFormat:@"%@\n%@", content, questionAtt.string];
        [self.scDelegate robotCustomerQuestionSetCell:self scTextMessageCellDidTapTranslation:translateText];
    }
}

-(void)onTapQuestionLabel:(UITapGestureRecognizer *)gesture{
    UILabel *label = (UILabel *)gesture.view;
    if (![label isKindOfClass:[UILabel class]]) {
        return;
    }
    
    CGPoint location = [gesture locationInView:label];
    
    // 获取被点击的链接
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    [textStorage addLayoutManager:layoutManager];
    
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    [layoutManager addTextContainer:textContainer];
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:location inTextContainer:textContainer fractionOfDistanceBetweenInsertionPoints:nil];
    
    if (characterIndex < label.attributedText.length) {
        NSDictionary *attributes = [label.attributedText attributesAtIndex:characterIndex effectiveRange:NULL];
        
        NSURL *url = attributes[NSLinkAttributeName];
        if ([url isKindOfClass:[NSURL class]]) {
            //SC://robot.question?questionCode=%ld
            if([url.scheme isEqualToString:@"SC"]){
                //内部协议
                if([url.host isEqualToString:@"robot.question"]){
                    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
                    
                    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
                    for (NSURLQueryItem *item in components.queryItems) {
                            [parameters setValue:item.value forKey:item.name];
                        }
                    //充值链接
                    NSInteger questionCode = [[parameters objectForKey:@"questionCode"] integerValue];
                    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(robotCustomerQuestionSetCell:tapQuestion:)]){
                        [self.scDelegate robotCustomerQuestionSetCell:self tapQuestion:questionCode];
                    }
                }
            }else{
                //其他业务
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
            
        }
    }
}

#pragma mark - 字典版本的静态方法

+ (NSMutableAttributedString *)questionAttributedTextWithDict:(NSDictionary *)dict {
    NSArray *faqInfoList = [SCRobotCustomerDictionaryHelper safeArrayFromDict:dict
                                                                       forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetFaqInfoListKey];

    NSMutableString *fullQuestionStr = [NSMutableString string];
    [faqInfoList enumerateObjectsUsingBlock:^(NSDictionary *faqDict, NSUInteger idx, BOOL *stop) {
        NSString *question = [SCRobotCustomerDictionaryHelper safeStringFromDict:faqDict
                                                                           forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoQuestionKey
                                                                     defaultValue:@""];
        [fullQuestionStr appendFormat: (idx == faqInfoList.count - 1) ? @"%@" : @"%@\n", question];
    }];

    NSMutableAttributedString *question = [[NSMutableAttributedString alloc] initWithString:fullQuestionStr];
    for (NSDictionary *faqDict in faqInfoList) {
        NSInteger code = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:faqDict
                                                                       forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoCodeKey
                                                                 defaultValue:0];
        NSString *questionText = [SCRobotCustomerDictionaryHelper safeStringFromDict:faqDict
                                                                              forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoQuestionKey
                                                                        defaultValue:@""];

        NSURL *url = [NSURL URLWithString:[NSString stringWithFormat:@"SC://robot.question?questionCode=%ld", code]];
        NSRange range = [fullQuestionStr rangeOfString:questionText];
        CGFloat lineHeight = 32.0;
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.lineSpacing = lineHeight - kScUIFontRegular(14.0f).lineHeight;

        // Add RTL support
        if (kScAuthMar.isLanguageForce) {
            paragraphStyle.alignment = NSTextAlignmentRight; // Align text to the right for RTL
        } else {
            paragraphStyle.alignment = NSTextAlignmentLeft; // Align text to the left for LTR
        }

        [question addAttributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#FF6B00"),
                                 NSLinkAttributeName:url,
                                 NSParagraphStyleAttributeName:paragraphStyle}
                          range:range];
    }
    [question addAttribute:NSFontAttributeName value:kScUIFontRegular(14.0f) range:NSMakeRange(0, fullQuestionStr.length)];
    return question;
}

+ (CGFloat)cellHeightWithDict:(NSDictionary *)dict {
    // 使用字典数据计算Cell高度
    NSString *content = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                     forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetContentKey
                                                               defaultValue:@""];

    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    CGSize contentSize = [UILabel labelSizeWithText:content
                                               font:kScUIFontRegular(14.0f)
                                            maxSize:CGSizeMake(contentW, 1000)];

    NSMutableAttributedString *questionAttr = [self questionAttributedTextWithDict:dict];
    CGSize questionSize = [questionAttr boundingRectWithSize:CGSizeMake(contentW, 1000)
                                                     options:NSStringDrawingUsesLineFragmentOrigin
                                                     context:nil].size;

    CGSize translateFailSize = [UILabel labelSizeWithText:@"Translation failed.".translateString
                                                     font:kScUIFontRegular(14.0f)
                                                  maxSize:CGSizeMake(contentW, 1000)];

    CGFloat bubbleWidth = MAX(contentSize.width, questionSize.width);
    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height + kSCMessageTextBottomMargin + questionSize.height + kSCMessageTextBottomMargin;

    // 获取翻译状态
    NSString *translateText = [NSString stringWithFormat:@"%@\n%@", content, questionAttr.string];
    SCTranslationStatus translateStatus = SCTranslationStatusNormal;

    // 获取questionId用于翻译状态查询
    NSString *questionId = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                        forKey:SCDictionaryKeys.shared.kSCRobotQuestionSetQuestionIdKey
                                                                  defaultValue:@""];

    UIViewController *viewController = [UIViewController currentViewController];
    if ([viewController isKindOfClass:[SCRobotCustomerServiceViewController class]]) {
        SCRobotCustomerServiceViewController *vc = (SCRobotCustomerServiceViewController *)viewController;
        translateStatus = [vc translationStatusWithKey:questionId.md5String];
    }

    CGSize translateSize = [UILabel labelSizeWithText:translateText
                                                 font:kScUIFontRegular(14.0f)
                                              maxSize:CGSizeMake(contentW, 1000)];

    // 根据翻译状态调整高度
    switch (translateStatus) {
        case SCTranslationStatusTranslating:
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight;
            break;
        case SCTranslationStatusFail:
            bubbleWidth = MAX(bubbleWidth, translateFailSize.width);
            bubbleHeight += kSCDividingLineHeight + translateFailSize.height;
            break;
        case SCTranslationStatusSuccess:
        {
            bubbleWidth = MAX(bubbleWidth, translateSize.width);
            bubbleHeight += kSCDividingLineHeight + kSCMessageTextTopMargin*2 + translateSize.height;
        }
            break;
        default:
            bubbleHeight += 11.0f + 5;
            break;
    }

    // 底部间距
    bubbleHeight += 20;
    return bubbleHeight;
}

@end
