//
//  SCRobotCustomerAnswerCell.h
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/2/7.
//

#import "SCBaseTableViewCell.h"
#import "SCTranslationService.h"

@class SCRobotCustomerAnswerCell;
NS_ASSUME_NONNULL_BEGIN

@protocol SCRobotCustomerAnswerCellDelegate <NSObject>
///点击翻译
-(void) robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell tapTranslation:(NSString *)translationStr;

///点击VIEW EXAMPLE
-(void) robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell viewExample:(NSString *)url;

///点击富文本
-(void) robotCustomerAnswerCell:(SCRobotCustomerAnswerCell *)cell tapEventHandleType:(NSInteger)handleType toUrl:(NSString *)toUrl;

- (SCTranslationStatus)getTranslationStatusWithKey:(NSString *)key;

@end

@interface SCRobotCustomerAnswerCell : SCBaseTableViewCell

@property(nonatomic,strong) NSDictionary * modelDict;
@property(nonatomic,weak) id<SCRobotCustomerAnswerCellDelegate> scDelegate;

// 字典版本方法
+(CGFloat) cellHeightWithDict:(NSDictionary *)dict;
+ (NSMutableAttributedString *) questionAttributedTextWithDict:(NSDictionary *)dict;

typedef void (^SCAnswerFeedbackDictBlock)(NSDictionary *modelDict, NSInteger likeStatus);
@property (nonatomic, copy) SCAnswerFeedbackDictBlock feedbackDictBlock;

@end

NS_ASSUME_NONNULL_END
