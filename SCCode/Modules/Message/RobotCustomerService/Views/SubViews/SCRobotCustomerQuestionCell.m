//
//  SCRobotCustomerQuestionCell.m
//  Supercall
//  用户问题 Cell
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/7.
//

#import "SCRobotCustomerQuestionCell.h"
#import "SCIMUIConfig.h"
#import "SCRobotCustomerDictionaryHelper.h"

@interface SCRobotCustomerQuestionCell ()
//头像
@property(nonatomic,weak) UIImageView *avatarIV;
//泡泡
@property(nonatomic,weak) UIView *bubbleV;
//文本内容
@property(nonatomic,weak) UILabel *contentL;

@end

@implementation SCRobotCustomerQuestionCell

- (void)initUI{
    [super initUI];
    self.backgroundColor = [UIColor scChatBgColor];    
    self.contentView.backgroundColor = [UIColor scChatBgColor];
    
    // 使用便捷访问宏获取当前用户头像
    _avatarIV = [UIImageView new].addSuperView(self.contentView);
    _avatarIV.setCornerRadius(kSCMessageAvatarWH/2);
    [_avatarIV sc_setImageWithURL:kSCCurrentUserAvatarUrl ?: @""];
    
    _bubbleV = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#A82F4B"]).setCornerRadius(18.0f).addSuperView(self.contentView);
    
    _contentL = UILabel.new.setTextColor([UIColor scWhite]).setFont(kScUIFontRegular(15)).setNumberLines(0).addSuperView(_bubbleV);
    
}



// 字典版本的setter方法
- (void)setModelDict:(NSDictionary *)modelDict{
    _modelDict = modelDict;

    NSString *question = [SCRobotCustomerDictionaryHelper safeStringFromDict:modelDict
                                                                      forKey:SCDictionaryKeys.shared.kSCMyQuestionQuestionKey
                                                                defaultValue:@""];
    self.contentL.text = question;
}


- (void)layoutSubviews{
    [super layoutSubviews];
    
    self.avatarIV.frame = CGRectMake(kSCMessageAvatarLeftMargin, 0, kSCMessageAvatarWH, kSCMessageAvatarWH);
    self.avatarIV.scRight = self.contentView.scWidth;
    
    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    //文本高度
    CGSize contentSize = [UILabel labelSizeWithText:self.contentL.text font:self.contentL.font maxSize:CGSizeMake(contentW, 1000)];
    
    
    CGFloat bubbleY = 0;
    CGFloat bubbleX = kSCMessageAvatarLeftMargin + kSCMessageAvatarWH + kSCMessageContentLeftMargin;
    CGFloat contentX = kSCMessageTextTopMargin;
    CGFloat contentY = kSCMessageTextLeftRightMargin;
    
    CGFloat bubbleWidth = contentSize.width;
    
    
    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height  + kSCMessageTextBottomMargin ;
    ///更新内容布局
    self.contentL.frame = CGRectMake(contentX, contentY, bubbleWidth, contentSize.height);
    
    
    self.bubbleV.frame = CGRectMake(bubbleX, bubbleY, bubbleWidth + kSCMessageTextLeftRightMargin*2, bubbleHeight);
    self.bubbleV.scRight = self.avatarIV.scLeft - kSCMessageAvatarLeftMargin;
    
    if (kScAuthMar.isLanguageForce) {
        self.avatarIV.scLeft = kSCMessageAvatarLeftMargin;
        self.contentL.scLeft = kSCMessageTextLeftRightMargin;
        self.bubbleV.scLeft = bubbleX;
    }
}



// 字典版本的静态方法
+(CGFloat) cellHeightWithDict:(NSDictionary *)dict{
    NSString *question = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                      forKey:SCDictionaryKeys.shared.kSCMyQuestionQuestionKey
                                                                defaultValue:@""];

    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    //文本高度
    CGSize contentSize = [UILabel labelSizeWithText:question font:kScUIFontRegular(15) maxSize:CGSizeMake(contentW, 1000)];

    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height  + kSCMessageTextBottomMargin ;
    bubbleHeight += 20;
    return bubbleHeight;
}

@end
