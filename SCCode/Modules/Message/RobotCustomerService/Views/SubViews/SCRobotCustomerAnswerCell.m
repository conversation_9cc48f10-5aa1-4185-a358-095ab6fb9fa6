//
//  SCRobotCustomerAnswerCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/2/7.
//

#import "SCRobotCustomerAnswerCell.h"
#import "SCIMUIConfig.h"
#import "SCTranslationService.h"
#import "SCRobotCustomerServiceViewController.h"
#import "SCRobotCustomerDictionaryHelper.h"

@interface SCRobotCustomerAnswerCell ()
//头像
@property(nonatomic,weak) UIImageView *avatarIV;
//泡泡
@property(nonatomic,weak) UIView *bubbleV;
//文本内容
@property(nonatomic,weak) UILabel *contentL;
//翻译分割线
@property(nonatomic,weak) UIView *translateLineV;
//翻译文本
@property(nonatomic,weak) UILabel *translateLabel;
///翻译加载框
@property(nonatomic,weak) UIActivityIndicatorView * translateLoadingV;
///翻译失败的提示
@property(nonatomic,weak) UILabel * translateErrorL;
///翻译状态
@property(nonatomic,assign) SCTranslationStatus translateStatus;
///翻译按钮
@property(nonatomic,weak) UIButton * translateBtn;

//分割线
@property(nonatomic,weak) UIView *lineV;
//点赞按钮
@property(nonatomic,weak) UIButton *likeBtn;
//不喜欢按钮
@property(nonatomic,weak) UIButton *dislikeBtn;

@end

@implementation SCRobotCustomerAnswerCell

- (void)prepareForReuse {
    [super prepareForReuse];
    [self.likeBtn setSelected:NO];
    [self.dislikeBtn setSelected:NO];
}

- (void)initUI{
    [super initUI];
    self.backgroundColor = [UIColor scChatBgColor];    
    self.contentView.backgroundColor = [UIColor scChatBgColor];
    
    _avatarIV = [UIImageView new].setImageName(@"avatar_robot_customer").addSuperView(self.contentView);
    _bubbleV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#32312F")).setCornerRadius(18.0f).addSuperView(self.contentView);
    
    _contentL = UILabel.new.setTextColor([UIColor.scWhite colorWithAlphaComponent:0.85]).setFont(kScUIFontRegular(14.0f)).setNumberLines(0).addSuperView(_bubbleV);
    kSCAddTapGesture(self.contentL, self, onTapQuestionLabel:);
    
    _translateLineV = UIView.new.setBackgroundColor([UIColor.scWhite colorWithAlphaComponent:0.27]).addSuperView(_bubbleV);
    
    _translateLabel = UILabel.new.setTextColor(kSCColorWithHexStr(@"#FFFFFF")).setFont(kScUIFontRegular(14.0f)).setNumberLines(0).setText(@"Translate".translateString).addSuperView(_bubbleV);
    
    
    UIActivityIndicatorView * translateLoadingV = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium].addSuperView(self.bubbleV);
    translateLoadingV.color = [UIColor colorWithHexString:@"#FFF549"];
    translateLoadingV.hidesWhenStopped = YES;
    translateLoadingV.hidden = YES;
    self.translateLoadingV = translateLoadingV;
    
    _translateErrorL = UILabel.new.setTextColor(kSCColorWithHexStr(@"#FFFFFF")).setFont(kScUIFontRegular(15)).setText(@"Translation failed.".translateString).addSuperView(_bubbleV);
    kSCAddTapGesture(_translateErrorL, self, onTranslate);
    
    UIButton * translateBtn = [UIButton buttonWithTitle:@"Click to translate".translateString titleColor:kSCColorWithHexStr(@"#FFC200") font:kScUIFontRegular(12) image:[SCResourceManager loadImageWithName:@"ic_message_translate"] backgroundImage:nil target:self action:@selector(onTranslate)];
    [translateBtn setTitleEdgeInsets: kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(0, 0, 0, 4) : UIEdgeInsetsMake(0, 4, 0, 0)];
    [self.contentView addSubview:translateBtn];
    self.translateBtn = translateBtn;

    
    _lineV = [UIView new].setBackgroundColor([kSCColorWithHexStr(@"#1C1C1C") colorWithAlphaComponent:0.47]).addSuperView(self.bubbleV);
    
    _likeBtn = [UIButton buttonWithTitle:@"Helpful".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(12) image:[SCResourceManager loadImageWithName:@"ic_like_small"] backgroundColor:nil cornerRadius:0].addSuperView(self.bubbleV);
    [_likeBtn setImage:[SCResourceManager loadImageWithName:@"ic_like_small_selected"] forState:UIControlStateSelected];
    [_likeBtn setTitleEdgeInsets: kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(0, 0, 0, 6) : UIEdgeInsetsMake(0, 6, 0, 0)];
    [self.likeBtn addTarget:self action:@selector(onLike) forControlEvents:UIControlEventTouchUpInside];
    
    _dislikeBtn = [UIButton buttonWithTitle:@"Unhelpful".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(12) image:[SCResourceManager loadImageWithName:@"ic_unlike_small"] backgroundColor:nil cornerRadius:0].addSuperView(self.bubbleV);
    [_dislikeBtn setImage:[SCResourceManager loadImageWithName:@"ic_unlike_small_selected"] forState:UIControlStateSelected];
    [_dislikeBtn setTitleEdgeInsets: kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(0, 0, 0, 6) : UIEdgeInsetsMake(0, 6, 0, 0)];
    [self.dislikeBtn addTarget:self action:@selector(onDislike) forControlEvents:UIControlEventTouchUpInside];
    
    [self updateLikeOrDisLikeUI];
}

- (void)updateLikeOrDisLikeUI {
    NSInteger isLike = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:self.modelDict
                                                                     forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoIsLikeKey
                                                               defaultValue:0];

    if (isLike == 1) {
        [self.likeBtn setSelected:YES];
        self.likeBtn.userInteractionEnabled = NO;
        self.dislikeBtn.userInteractionEnabled = NO;
    } else if (isLike == 2) {
        [self.dislikeBtn setSelected:YES];
        self.likeBtn.userInteractionEnabled = NO;
        self.dislikeBtn.userInteractionEnabled = NO;
    } else {
        self.likeBtn.userInteractionEnabled = YES;
        self.dislikeBtn.userInteractionEnabled = YES;
    }
}



// 字典版本的setter方法
- (void)setModelDict:(NSDictionary *)modelDict{
    _modelDict = modelDict;

    self.contentL.attributedText = [SCRobotCustomerAnswerCell questionAttributedTextWithDict:modelDict];
    self.contentL.lineBreakMode = NSLineBreakByWordWrapping; // 换行模式为按单词换行

    //需要翻译的文案
    NSString *translateText = self.contentL.attributedText.string;

    // 获取faqId用于翻译状态管理
    NSString *faqId = [SCRobotCustomerDictionaryHelper safeStringFromDict:modelDict
                                                                   forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey
                                                             defaultValue:@""];
    if ([self.scDelegate respondsToSelector:@selector(getTranslationStatusWithKey:)]) {
        self.translateStatus = [self.scDelegate getTranslationStatusWithKey:faqId.md5String];
    } else {
        self.translateStatus = SCTranslationStatusNormal;
    }

    if(kSCAuthTranslaService.isAutoTranslation && self.translateStatus == SCTranslationStatusNormal){
        ///如果开启自动翻译，那么全部为开始翻译的都会默认变成加载中
        self.translateStatus = SCTranslationStatusTranslating;
    }

    [self updateTranslateUI:translateText];
    [self updateLikeOrDisLikeUI];
}
-(void) updateTranslateUI:(NSString *)translateText{
    self.translateBtn.hidden = YES;
    [self.translateLoadingV stopAnimating];
    self.translateLoadingV.hidden = YES;
    self.translateErrorL.hidden = YES;
    self.translateLabel.hidden = YES;
    self.translateLineV.hidden = YES;


    
    switch (self.translateStatus) {
        case SCTranslationStatusTranslating:
            //翻译中
            self.translateLoadingV.hidden = NO;
            [self.translateLoadingV startAnimating];
            self.translateLineV.hidden = NO;
            break;
        case SCTranslationStatusFail:
            //翻译失败
            self.translateErrorL.hidden = NO;
            self.translateLineV.hidden = NO;
            break;
        case SCTranslationStatusSuccess:
            //翻译成功
            self.translateLabel.hidden = NO;
            self.translateLineV.hidden = NO;
            self.translateLabel.text = [kSCAuthTranslaService getCacheWithText:translateText];
            break;
        default:
            self.translateBtn.hidden = NO;
            break;
    }
}
///



- (void)layoutSubviews{
    [super layoutSubviews];
    
    self.avatarIV.frame = CGRectMake(kSCMessageAvatarLeftMargin, 0, kSCMessageAvatarWH, kSCMessageAvatarWH);
    
    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    //文本高度
    NSMutableAttributedString *contentAtt = [SCRobotCustomerAnswerCell questionAttributedTextWithDict:self.modelDict];
    CGSize contentSize = [contentAtt boundingRectWithSize:CGSizeMake(contentW, 1000) options:NSStringDrawingUsesLineFragmentOrigin context:nil].size;

    CGSize translateSize = [UILabel labelSizeWithText:self.translateLabel.text font:self.translateLabel.font maxSize:CGSizeMake(contentW, 1000)];
    CGSize translateFailSize = [UILabel labelSizeWithText:self.translateErrorL.text font:self.translateErrorL.font maxSize:CGSizeMake(contentW, 1000)];
    
    CGFloat bubbleY = 0;
    CGFloat bubbleX = kSCMessageAvatarLeftMargin + kSCMessageAvatarWH + kSCMessageContentLeftMargin;
    CGFloat contentX = kSCMessageTextTopMargin;
    CGFloat contentY = kSCMessageTextLeftRightMargin;
    
    
    CGFloat bubbleWidth = MAX(contentSize.width,kSCMessageLikeBtnMinWidth*2);
    
    ///更新内容布局
    self.contentL.frame = CGRectMake(contentX, contentY, bubbleWidth, contentSize.height);
    
    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height  + kSCMessageTextBottomMargin;
    
    switch (self.translateStatus) {
        case SCTranslationStatusTranslating:
            //翻译中
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译中
            self.translateLoadingV.scCenterX = bubbleWidth/2.0;
            self.translateLoadingV.scTop = self.translateLineV.scBottom + kSCMessageTextTopMargin;
            
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight ;
            break;
        case SCTranslationStatusFail:
            bubbleWidth = MAX(bubbleWidth,translateFailSize.width);
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译失败
            self.translateErrorL.frame = CGRectMake(contentX, self.translateLineV.scBottom, bubbleWidth, kSCTranslateLoadingErrorHeight);
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight ;
            break;
        case SCTranslationStatusSuccess:
        {
            bubbleWidth = MAX(bubbleWidth,translateSize.width);
            self.translateLineV.frame = CGRectMake(contentX, bubbleHeight, bubbleWidth, kSCDividingLineHeight);
            //翻译成功
            self.translateLabel.frame = CGRectMake(contentX, self.translateLineV.scBottom + kSCMessageTextTopMargin, bubbleWidth, translateSize.height);
            bubbleHeight +=  kSCDividingLineHeight +  kSCMessageTextTopMargin*2 + translateSize.height;
            
        }
            break;
        default:
            
            break;
    }
    self.lineV.frame = CGRectMake(0, bubbleHeight, kSCMessageContentMaxWidth, kSCDividingLineHeight);
    bubbleHeight += kSCDividingLineHeight;
    
    self.likeBtn.frame = CGRectMake(contentX, self.lineV.scBottom, bubbleWidth / 2.0, kSCMessageLikeBtnHeight);
    self.dislikeBtn.frame = CGRectMake(contentX + bubbleWidth / 2.0, bubbleHeight, bubbleWidth / 2.0, kSCMessageLikeBtnHeight);
    
    bubbleHeight += kSCMessageLikeBtnHeight;
    self.bubbleV.frame = CGRectMake(bubbleX, bubbleY, bubbleWidth + kSCMessageTextLeftRightMargin*2, bubbleHeight);
    
    if(!self.translateBtn.hidden){
        // 计算翻译按钮文本的实际宽度
        NSString *translateText = self.translateBtn.titleLabel.text;
        UIFont *translateFont = self.translateBtn.titleLabel.font;
        CGSize textSize = [translateText boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16.0f)
                                                    options:NSStringDrawingUsesLineFragmentOrigin
                                                 attributes:@{NSFontAttributeName: translateFont}
                                                    context:nil].size;
        
        // 添加左右边距和图标宽度
        CGFloat iconWidth = 16.0f;
        CGFloat padding = 4.0f;    // 文本与图标的间距
        CGFloat btnWidth = textSize.width + iconWidth + padding;
        
        self.translateBtn.frame = CGRectMake(bubbleX, self.bubbleV.scBottom + 5, btnWidth, 16.0f);
    }
    
    if (kScAuthMar.isLanguageForce) {
        self.avatarIV.scRight = self.contentView.scWidth - kSCMessageAvatarLeftMargin;
        self.bubbleV.scRight = self.contentView.scWidth - bubbleX;
        self.contentL.scRight = self.bubbleV.scWidth - contentX;
        self.translateBtn.scRight = self.bubbleV.scWidth + bubbleX;
    }
}



#pragma mark - action
-(void)onTranslate{
    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(robotCustomerAnswerCell:tapTranslation:)]){
        NSMutableAttributedString *questionAtt = [SCRobotCustomerAnswerCell questionAttributedTextWithDict:self.modelDict];
        [self.scDelegate robotCustomerAnswerCell:self tapTranslation:questionAtt.string];
    }
}

-(void)onTapQuestionLabel:(UITapGestureRecognizer *)gesture{
    UILabel *label = (UILabel *)gesture.view;
    if (![label isKindOfClass:[UILabel class]]) {
        return;
    }
    
    CGPoint location = [gesture locationInView:label];
    
    // 获取被点击的链接
    NSTextStorage *textStorage = [[NSTextStorage alloc] initWithAttributedString:label.attributedText];
    NSLayoutManager *layoutManager = [[NSLayoutManager alloc] init];
    [textStorage addLayoutManager:layoutManager];
    
    NSTextContainer *textContainer = [[NSTextContainer alloc] initWithSize:label.bounds.size];
    [layoutManager addTextContainer:textContainer];
    
    NSUInteger characterIndex = [layoutManager characterIndexForPoint:location inTextContainer:textContainer fractionOfDistanceBetweenInsertionPoints:nil];
    
    if (characterIndex < label.attributedText.length) {
        NSDictionary *attributes = [label.attributedText attributesAtIndex:characterIndex effectiveRange:NULL];
        
        NSURL *url = attributes[NSLinkAttributeName];
        if ([url isKindOfClass:[NSURL class]]) {
            //SC://robot.question?questionCode=%ld
            if([url.scheme isEqualToString:@"SC"]){
                //内部协议
                if([url.host isEqualToString:@"robot.question.event"]){
                    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
                    
                    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
                    for (NSURLQueryItem *item in components.queryItems) {
                            [parameters setValue:item.value forKey:item.name];
                        }
                    //充值链接
                    NSInteger handleType = [[parameters objectForKey:@"handleType"] integerValue];
                    NSString *toUrl;
                    if([[parameters objectForKey:@"toUrl"] isKindOfClass:[NSString class]]){
                        toUrl = [parameters objectForKey:@"toUrl"];
                    }
                    
                    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(robotCustomerAnswerCell:tapEventHandleType:toUrl:)]){
                        [self.scDelegate robotCustomerAnswerCell:self tapEventHandleType:handleType toUrl:toUrl];
                    }
                }else if([url.host isEqualToString:@"robot.question.viewExample"]){
                    NSURLComponents *components = [NSURLComponents componentsWithURL:url resolvingAgainstBaseURL:NO];
                    
                    NSMutableDictionary *parameters = [NSMutableDictionary dictionary];
                    for (NSURLQueryItem *item in components.queryItems) {
                            [parameters setValue:item.value forKey:item.name];
                        }
                    //充值链接
                    NSString *url;
                    if([[parameters objectForKey:@"url"] isKindOfClass:[NSString class]]){
                        url = [parameters objectForKey:@"url"];
                    }
                    if(self.scDelegate != nil && [self.scDelegate respondsToSelector:@selector(robotCustomerAnswerCell:viewExample:)]){
                        [self.scDelegate robotCustomerAnswerCell:self viewExample:url];
                    }
                }
            }else {
                //其他业务
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
            
        }
    }
}

-(void)onLike{
    // 字典版本的处理
    NSMutableDictionary *mutableDict = [self.modelDict mutableCopy];
    mutableDict[SCDictionaryKeys.shared.kSCRobotFAQInfoIsLikeKey] = @(1);
    self.modelDict = [mutableDict copy];
    [self updateLikeOrDisLikeUI];
    if (self.feedbackDictBlock) {
        self.feedbackDictBlock(self.modelDict, 1);
    }
}

-(void)onDislike{
    // 字典版本的处理
    NSMutableDictionary *mutableDict = [self.modelDict mutableCopy];
    mutableDict[SCDictionaryKeys.shared.kSCRobotFAQInfoIsLikeKey] = @(2);
    self.modelDict = [mutableDict copy];
    [self updateLikeOrDisLikeUI];
    if (self.feedbackDictBlock) {
        self.feedbackDictBlock(self.modelDict, 2);
    }
}

#pragma mark - 字典版本的静态方法

+ (NSMutableAttributedString *)questionAttributedTextWithDict:(NSDictionary *)dict {
    // 获取messageAnswer字典
    NSDictionary *messageAnswerDict = [SCRobotCustomerDictionaryHelper safeDictFromDict:dict
                                                                                 forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoMessageAnswerKey];

    NSString *content = [SCRobotCustomerDictionaryHelper safeStringFromDict:messageAnswerDict
                                                                     forKey:SCDictionaryKeys.shared.kSCRobotMessageAnswerContentKey
                                                               defaultValue:@""];
    NSString *fullStr = content;
    NSString *exampleStr = @"VIEW EXAMPLE";

    BOOL isViewExample = [SCRobotCustomerDictionaryHelper safeBoolFromDict:dict
                                                                    forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoIsViewExampleKey
                                                              defaultValue:NO];
    if(isViewExample){
        fullStr = [NSString stringWithFormat:@"%@\n%@\n", fullStr, exampleStr];
    }

    NSMutableAttributedString *question = [[NSMutableAttributedString alloc]
                                          initWithString:fullStr
                                          attributes:@{NSForegroundColorAttributeName:[UIColor.scWhite colorWithAlphaComponent:0.85],
                                                      NSFontAttributeName:kScUIFontRegular(14.0f)}];

    // 处理事件处理列表
    NSArray *eventHandleList = [SCRobotCustomerDictionaryHelper safeArrayFromDict:messageAnswerDict
                                                                            forKey:SCDictionaryKeys.shared.kSCRobotMessageAnswerEventHandleListKey];
    for (NSDictionary *eventDict in eventHandleList) {
        NSInteger handleType = [SCRobotCustomerDictionaryHelper safeIntegerFromDict:eventDict
                                                                             forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventHandleTypeKey
                                                                       defaultValue:0];
        NSString *toUrl = [SCRobotCustomerDictionaryHelper safeStringFromDict:eventDict
                                                                       forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventToUrlKey
                                                                 defaultValue:@""];
        NSString *matchStr = [SCRobotCustomerDictionaryHelper safeStringFromDict:eventDict
                                                                          forKey:SCDictionaryKeys.shared.kSCRobotAnswerEventMatchStrKey
                                                                    defaultValue:@""];

        NSURLComponents *components = [NSURLComponents componentsWithURL:[NSURL URLWithString:@"SC://robot.question.event"]
                                                   resolvingAgainstBaseURL:NO];
        [components setQueryItems:@[[NSURLQueryItem queryItemWithName:@"handleType" value:[NSString stringWithFormat:@"%ld", handleType]],
                                   [NSURLQueryItem queryItemWithName:@"toUrl" value:toUrl]]];

        NSRange range = [fullStr rangeOfString:matchStr];
        [question addAttributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#007AFF"),
                                 NSLinkAttributeName:components.URL}
                          range:range];
    }

    if(isViewExample){
        //VIEW EXAMPLE 蓝色字体，15 下划线
        NSString *imageUrl = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                          forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoImageUrlKey
                                                                    defaultValue:@""];
        NSURLComponents *components = [NSURLComponents componentsWithURL:[NSURL URLWithString:@"SC://robot.question.viewExample"]
                                                   resolvingAgainstBaseURL:NO];
        [components setQueryItems:@[[NSURLQueryItem queryItemWithName:@"url" value:imageUrl]]];
        NSRange range = [fullStr rangeOfString:exampleStr];
        [question addAttributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#007AFF"),
                                 NSUnderlineStyleAttributeName:@(NSUnderlineStyleSingle),
                                 NSLinkAttributeName:components.URL}
                          range:range];
    }

    return question;
}

+ (CGFloat)cellHeightWithDict:(NSDictionary *)dict {
    CGFloat contentW = kSCMessageContentMaxWidth - kSCMessageTextLeftRightMargin*2;
    NSMutableAttributedString *contentAtt = [self questionAttributedTextWithDict:dict];
    CGSize contentSize = [contentAtt boundingRectWithSize:CGSizeMake(contentW, 1000)
                                                  options:NSStringDrawingUsesLineFragmentOrigin
                                                  context:nil].size;

    CGSize translateFailSize = [UILabel labelSizeWithText:@"Translation failed.".translateString
                                                     font:kScUIFontRegular(15)
                                                  maxSize:CGSizeMake(contentW, 1000)];

    CGFloat bubbleWidth = MAX(contentSize.width, kSCMessageLikeBtnMinWidth*2);
    CGFloat bubbleHeight = kSCMessageTextTopMargin + contentSize.height + kSCMessageTextBottomMargin;

    // 获取翻译状态
    NSString *translateText = contentAtt.string;
    SCTranslationStatus translateStatus = SCTranslationStatusNormal;

    // 获取faqId用于翻译状态查询
    NSString *faqId = [SCRobotCustomerDictionaryHelper safeStringFromDict:dict
                                                                   forKey:SCDictionaryKeys.shared.kSCRobotFAQInfoFaqIdKey
                                                             defaultValue:@""];

    UIViewController *viewController = [UIViewController currentViewController];
    if ([viewController isKindOfClass:[SCRobotCustomerServiceViewController class]]) {
        SCRobotCustomerServiceViewController *vc = (SCRobotCustomerServiceViewController *)viewController;
        translateStatus = [vc translationStatusWithKey:faqId.md5String];
    }

    CGSize translateSize = [UILabel labelSizeWithText:translateText
                                                 font:kScUIFontRegular(15)
                                              maxSize:CGSizeMake(contentW, 1000)];

    // 根据翻译状态调整高度
    switch (translateStatus) {
        case SCTranslationStatusTranslating:
            bubbleHeight += kSCDividingLineHeight + kSCTranslateLoadingErrorHeight;
            break;
        case SCTranslationStatusFail:
            bubbleWidth = MAX(bubbleWidth, translateFailSize.width);
            bubbleHeight += kSCDividingLineHeight + translateFailSize.height;
            break;
        case SCTranslationStatusSuccess:
        {
            bubbleWidth = MAX(bubbleWidth, translateSize.width);
            bubbleHeight += kSCDividingLineHeight + kSCMessageTextTopMargin*2 + translateSize.height;
        }
            break;
        default:
            bubbleHeight += 11.0f + 5;
            break;
    }

    // 添加点赞按钮高度
    bubbleHeight += kSCDividingLineHeight + kSCMessageLikeBtnHeight;

    // 底部间距
    bubbleHeight += 20;

    return bubbleHeight;
}

@end
