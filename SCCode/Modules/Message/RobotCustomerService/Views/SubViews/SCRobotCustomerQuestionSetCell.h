//
//  SCRobotCustomerProblemSetCell.h
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/2/7.
//

#import "SCBaseTableViewCell.h"
#import "SCTranslationService.h"

NS_ASSUME_NONNULL_BEGIN

@class SCRobotCustomerQuestionSetCell;

@protocol SCTextMessageCellDelegate <NSObject>

-(void) robotCustomerQuestionSetCell:(SCRobotCustomerQuestionSetCell *)cell scTextMessageCellDidTapTranslation:(NSString *)translationStr;

///点击问题
-(void) robotCustomerQuestionSetCell:(SCRobotCustomerQuestionSetCell *)cell tapQuestion:(NSInteger )questionCode;

- (SCTranslationStatus)getTranslationStatusWithKey:(NSString *)key;

@end

@interface SCRobotCustomerQuestionSetCell : SCBaseTableViewCell

@property(nonatomic,strong) NSDictionary * modelDict;
@property(nonatomic,weak) id<SCTextMessageCellDelegate> scDelegate;

// 字典版本方法
+(CGFloat) cellHeightWithDict:(NSDictionary *)dict;
+ (NSMutableAttributedString *) questionAttributedTextWithDict:(NSDictionary *)dict;

@end

NS_ASSUME_NONNULL_END
