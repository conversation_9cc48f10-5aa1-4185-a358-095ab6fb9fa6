//
//  SCFlashChatViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCFlashChatViewController.h"
//Service
#import "SCCallService.h"
#import "SCCategoryAPIManagerFlashChat.h"
//View
#import "SCFlashRouletteView.h"
//VC
#import "SCFlashChatMatchViewController.h"
#import "SCHomeViewController.h"
#import "SCCoinsPopupViewController.h"
#import "SCAnchorInfoViewController.h"
#import "SCCoinsPopupViewController.h"
#import "SCNoEnoughCoinsAlertViewController.h"
#import "SCCoinsFullScreenViewController.h"
//Model
// #import "SCStrategyModel.h" // 已移除，使用字典替代
//Permission
#import "SCPermissionManager.h"
#import "SCFontManager.h"
#import "Lottie/Lottie-Swift.h"
#import "SCCoinsService.h"

@interface SCFlashChatViewController ()<SCFlashChatMatchViewControllerDelegate>

// 标题
@property (nonatomic, strong) UILabel *titleLabel;

//我的金币布局
@property (nonatomic, strong) UIView *myCoinsV;
///金币图标
@property (nonatomic, strong) UIImageView *coinsIconIV;
///金币数量
@property (nonatomic, strong) UILabel *coinsCountL;

/// 轮播话术
@property (nonatomic, strong) UILabel *contentLabel;
/// 轮播内容
@property (nonatomic, strong) NSArray *contentTips;
/// 轮播索引
@property (nonatomic, assign) NSInteger contentIndex;
/// 轮播定时器
@property (nonatomic, strong) NSTimer *contentTimer;

//匹配按钮布局
@property (nonatomic, strong) UIView *matchBtnV;
///匹配按钮
@property (nonatomic, strong) UIButton *matchBtn;
///匹配价格
@property (nonatomic, strong) UILabel *matchPriceLabel;
///免费标签
@property (nonatomic, strong) UIButton *freeBtn;
///匹配动画
@property (nonatomic, strong) CompatibleAnimationView *matchAnimationView;
///渐变蒙层
@property (nonatomic, strong) UIImageView *gradientImageView;
@end

@implementation SCFlashChatViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    //刷新金币
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
    [self.matchAnimationView play];
    [self startContentTimer];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [self.matchAnimationView stop];
    [self stopContentTimer];
}

- (void)dealloc
{
    [self.matchAnimationView stop];
    [self stopContentTimer];
}

- (void)initData {
    [super initData];
    [self requestData];
}

- (void)initUI{
    [super initUI];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#4E1313"];
    self.scContentView.backgroundColor = [UIColor colorWithHexString:@"#4E1313"];
    [self setIsHiddenSCNavigationBar:YES];
    [self _buildMatchAnimationView];
    [self _buildGradientView];
    [self _buildTitleView];
    [self _buildMyCoinsView];
    [self _buildMatchBtn];
    [self _buildRandomLabelView];
    [self _updateLayout];
    [self matchSubscribe];
}

-(void) _updateLayout{
    
    [self.myCoinsV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight+10.0f);
        make.trailing.offset(-15);
        make.height.mas_equalTo(30);
    }];
    
    //匹配按钮底部对齐
    [self.matchBtnV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.leading.trailing.equalTo(self.view);
    }];
    
    if (self.matchAnimationView) {
        [self.matchAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.myCoinsV.mas_bottom).offset(24.0f);
            make.leading.trailing.equalTo(self.view);
            make.bottom.equalTo(self.matchBtnV.mas_top);
        }];
        
        [self.gradientImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.equalTo(self.view);
            make.bottom.equalTo(self.matchAnimationView.mas_bottom).offset(2);
            make.height.equalTo(@230);
        }];
    }
}

- (void)_buildMatchAnimationView {
    // 创建 Lottie 动画视图
    NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/match_animation.json"];
    NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
    
    NSError *error = nil;
    NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
    if (jsonData) {
        self.matchAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
        self.matchAnimationView.contentMode = UIViewContentModeScaleAspectFill;
        self.matchAnimationView.loopAnimationCount = -1;
        kSCAddTapGesture(self.matchAnimationView, self, matchAction:);
        [self.view addSubview:self.matchAnimationView];
    }
}

- (void)_buildGradientView {
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[
        [UIColor clearColor],
        [UIColor colorWithHexString:@"#4E1313"]
    ] orientation:SCGradientColorsOrientationVertical];
    
    UIImage *gradientImage = [[UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(kSCScreenWidth, 230)] resizableImageWithCapInsets:UIEdgeInsetsMake(10, 10, 10, 10) resizingMode:UIImageResizingModeStretch];
   
    // 创建渐变层的容器视图
    self.gradientImageView = [[UIImageView alloc] init];
    self.gradientImageView.image = gradientImage;
    [self.view addSubview:self.gradientImageView];
}

- (void)_buildTitleView {
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[[UIColor colorWithHexString:@"#FF0000"], [UIColor colorWithHexString:@"#460606"]] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *lineImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(17, 8)];
    UIImageView *lineImageView = [[UIImageView alloc]initWithImage:kScAuthMar.isLanguageForce ? [lineImage imageWithHorizontallyFlippedOrientation] : lineImage];
    lineImageView.alpha = 0.9;
    [self.view addSubview:lineImageView];
    
    self.titleLabel = [UILabel new].setFont([SCFontManager boldItalicFontWithSize:20.0f]).setTextColor([UIColor scWhite]);
    self.titleLabel.text = @"Match".translateString;
    [self.view addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight+10.0f);
        make.leading.equalTo(self.view).offset(15);
    }];

    [lineImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(8);
        make.width.equalTo(self.titleLabel.mas_width);
        make.leading.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(-8);
    }];
}

-(void) _buildMyCoinsView{
    self.myCoinsV = [UIView new].setBackgroundColor(UIColor.clearColor).addSuperView(self.view);
    kSCAddTapGesture(self.myCoinsV, self, onClickCoins);
    
    self.coinsIconIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_coins_middle"]];
    [self.myCoinsV addSubview:self.coinsIconIV];
    [self.coinsIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.myCoinsV);
        make.leading.equalTo(self.myCoinsV).offset(7);
        make.size.mas_equalTo(CGSizeMake(21, 16));
    }];
    
    self.coinsCountL = [UILabel new].setTextColor(UIColor.scWhite).setFontSemiboldSize(14).setTextAlignment(NSTextAlignmentRight).addSuperView(self.myCoinsV);
    [self.coinsCountL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.myCoinsV);
        make.leading.equalTo(self.coinsIconIV.mas_trailing).offset(4);
        make.trailing.equalTo(self.myCoinsV);
    }];
}

- (void)matchSubscribe{
    kWeakSelf(self);
    [kScAuthMar.availableCoinsObx subscribe:^(NSNumber * _Nullable value) {
        
        weakself.coinsCountL.text = [NSString stringWithFormat:@"%ld",value.integerValue];
        
    } error:nil disposeBag:self.disposeBag];
    
    
    [kScAuthMar.strategyObs subscribe:^(NSDictionary * _Nullable strategyDict) {
        // 从策略字典中获取性别匹配金币配置和闪聊配置
        NSDictionary *genderMatchCoin = [SCDictionaryHelper dictionaryFromDictionary:strategyDict forKey:@"genderMatchCoin" defaultValue:@{}];
        NSInteger femaleCoins = [SCDictionaryHelper integerFromDictionary:genderMatchCoin forKey:@"femaleCoins" defaultValue:0];

        NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:strategyDict forKey:@"flashChatConfig" defaultValue:@{}];
        BOOL isFreeCall = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isFreeCall" defaultValue:NO];

        self.matchPriceLabel.attributedText = [self priceAttributeWithImage:[SCResourceManager loadImageWithName:@"ic_coins_small"] text:[NSString stringWithFormat:@"%ld ",femaleCoins] after:@"/Match".translateString];
        if(isFreeCall){
            //免费
            self.freeBtn.hidden = NO;
            self.matchPriceLabel.hidden = YES;
        }else{
            //不免费
            self.freeBtn.hidden = YES;
            self.matchPriceLabel.hidden = NO;
        }


    } error:nil disposeBag:self.disposeBag];
}

- (void)_buildRandomLabelView {
    // 添加轮播提示文字
    self.contentLabel = [[UILabel alloc] init];
    self.contentLabel.font = kScUIFontRegular(14.0f);
    self.contentLabel.textColor = [UIColor whiteColor];
    self.contentLabel.textAlignment = NSTextAlignmentCenter;
    self.contentLabel.numberOfLines = 0;
    [self.view addSubview:self.contentLabel];
    
    [self.contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(30);
        make.trailing.equalTo(self.view).offset(-30);
        make.bottom.equalTo(self.matchBtnV.mas_top).offset(-5);
    }];
}

-(void) _buildMatchBtn{
    self.matchBtnV = [UIView new].addSuperView(self.view);
    self.matchBtn = [UIButton buttonWithTitle:@"Random Match".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius].addSuperView(self.matchBtnV);
    [self.matchBtn sc_setThemeGradientBackground];
    self.matchBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 10, 0, 10);
    [self.matchBtn addTarget:self action:@selector(matchAction:) forControlEvents:UIControlEventTouchUpInside];
    
    self.matchPriceLabel = [UILabel new].setTextColor([UIColor scWhite]).setFontMediumSize(12).setTextAlignment(NSTextAlignmentCenter).addSuperView(self.matchBtnV);
    self.matchPriceLabel.attributedText = [self priceAttributeWithImage:[SCResourceManager loadImageWithName:@"ic_coins_small"] text:@"100" after:@"/Match".translateString];
    UIFont *freefont = [SCFontManager boldItalicFontWithSize:14.0f];
    UIImage *freeBgImage = [SCResourceManager loadImageWithName:@"bg_flash_free" isAutoForce:YES];
    self.freeBtn = [UIButton buttonWithTitle:@"Free".translateString titleColor:UIColor.scWhite font:freefont image:nil backgroundImage:freeBgImage target:nil action:nil].addSuperView(self.matchBtnV);
    self.freeBtn.titleEdgeInsets = UIEdgeInsetsMake(0, 0, 8.0f, 0);
    self.freeBtn.userInteractionEnabled = NO;
    
    [self.matchPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.matchBtnV);
        make.height.mas_equalTo(20.0f);
        make.bottom.equalTo(self.matchBtnV).offset(kSCScaleHeight(-15));
    }];
    
    [self.matchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.matchBtnV);
        make.height.mas_equalTo(46);
        make.width.mas_greaterThanOrEqualTo(kSCScaleWidth(206));
        make.bottom.equalTo(self.matchPriceLabel.mas_top).offset(-5);
    }];
    
    //免费标签对齐按钮的右上角
    [self.freeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.matchBtn);
        make.bottom.equalTo(self.matchBtn.mas_top).offset(4);
        make.size.mas_equalTo(CGSizeMake(kSCScaleWidth(53), kSCScaleWidth(36)));
        make.top.equalTo(self.matchBtnV);
    }];
    
}
-(NSMutableAttributedString *) priceAttributeWithImage:(UIImage *)image text:(NSString *)text after:(NSString *)after{
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
    CGSize imageSize = CGSizeMake(10, 8);
    textAttachment.bounds = CGRectMake(0, 0, imageSize.width, imageSize.height);
    textAttachment.image = image;
    NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:textAttachment];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:text attributes:@{NSBaselineOffsetAttributeName:@(1.5)}]];
    [attributedString appendAttributedString:imageString];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:after attributes:@{NSBaselineOffsetAttributeName:@(1.5)}]];
    return attributedString;
}

#pragma mark - Action

-(void) matchAction:(UIButton *)sender {
    if(!kSCAuthCallService.isCanCall){
        [kSCKeyWindow toast:@"There is a call in progress, please hang up first".translateString];
        return;
    }
    
    kWeakSelf(self);
    // 检查相机和麦克风权限
    [[SCPermissionManager shared] checkPermissions:@[@(SCPermissionTypeCamera), @(SCPermissionTypeMicrophone)] 
                                      completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            [weakself startMatch];
        } else if (shouldShowAlert) {
            // 如果权限不足，先检查相机权限
            [weakself checkPermission];
        } else {
            // 如果不需要显示权限提示，直接开始匹配
            [weakself startMatch];
        }
    }];
}

- (void)checkPermission{
    kWeakSelf(self);
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeCamera
                                     completion:^(BOOL cameraGranted, BOOL cameraShouldShowAlert) {
        if (!cameraGranted && cameraShouldShowAlert) {
            [weakself showPermissionAlert];
        } else {
            // 如果相机权限已授权，检查麦克风权限
            [weakself showPermissionAlertDissMicrophone];
        }
    }];
}

- (void)showPermissionAlertDissMicrophone {
    kWeakSelf(self);
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                 fromViewController:weakself
                                      cancelBlock:^{
        [weakself startMatch];
    }];
}


- (void)showPermissionAlert {
    kWeakSelf(self);
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeCamera
                                 fromViewController:self
                                      cancelBlock:^{
        // 检查麦克风权限
        [weakself checkPermissionMicrophone];
    }];
}

- (void)checkPermissionMicrophone {
    kWeakSelf(self);
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeMicrophone
                                     completion:^(BOOL micGranted, BOOL micShouldShowAlert) {
        if (!micGranted && micShouldShowAlert) {
            [weakself showPermissionAlertDissMicrophone];
        } else {
            [weakself startMatch];
        }
    }];
}

// 将开始匹配的逻辑抽取为单独的方法
- (void)startMatch {    
    // 从策略字典中获取性别匹配金币配置和闪聊配置
    NSDictionary *genderMatchCoin = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.strategyObs.value forKey:@"genderMatchCoin" defaultValue:@{}];
    NSInteger femaleCoins = [SCDictionaryHelper integerFromDictionary:genderMatchCoin forKey:@"femaleCoins" defaultValue:0];

    NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.strategyObs.value forKey:@"flashChatConfig" defaultValue:@{}];
    BOOL isFreeCall = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isFreeCall" defaultValue:NO];

    if(kScAuthMar.availableCoinsObx.value.integerValue >= femaleCoins || isFreeCall){
        SCFlashChatMatchViewController *vc = [[SCFlashChatMatchViewController alloc] init];
        vc.delegate = self;
        [self.navigationController pushViewController:vc animated:YES];
    }else {
        [kSCKeyWindow toast:@"Coins not enough".translateString];
        [SCCoinsPopupViewController showWithFromVC:[UIViewController currentViewController] entry:SCPayEntry.shared.kPayEntrySourceMatchResultCall];
    }
}

-(void) onClickCoins{
    //转跳充值页面
    [SCCoinsPopupViewController showWithFromVC:[UIViewController currentViewController] entry:SCPayEntry.shared.kPayEntrySourceMatchTitle];
}

- (void)requestData {
    // 获取轮播话术
    kWeakSelf(self)
    [SCAPIServiceManager requestConfigContentWithSuccess:^(NSArray<NSString *> * _Nonnull data) {
        kStrongSelf;
        if (!strongSelf) return;
        
        // 保存轮播内容
        strongSelf.contentTips = data;
        
        if (data.count > 0) {
            // 设置初始文本
            strongSelf.contentIndex = 0;
            strongSelf.contentLabel.text = data[0];
            
            // 创建定时器
            [strongSelf startContentTimer];
        }
        
    } failure:^(SCXErrorModel * _Nonnull error) {
        
    }];
}

- (void)startContentTimer {
    [self stopContentTimer];
    
    kWeakSelf(self);
    self.contentTimer = [NSTimer scheduledTimerWithTimeInterval:5.0 repeats:YES block:^(NSTimer * _Nonnull timer) {
        kStrongSelf;
        if (!strongSelf) return;
        
        // 更新索引
        strongSelf.contentIndex++;
        if (strongSelf.contentIndex >= strongSelf.contentTips.count) {
            strongSelf.contentIndex = 0;
        }
        
        // 更新文本
        if (strongSelf.contentIndex < strongSelf.contentTips.count) {
            strongSelf.contentLabel.text = strongSelf.contentTips[strongSelf.contentIndex];
        }
    }];
    
    [[NSRunLoop mainRunLoop] addTimer:self.contentTimer forMode:NSRunLoopCommonModes];
}

- (void)stopContentTimer {
    if (self.contentTimer) {
        [self.contentTimer invalidate];
        self.contentTimer = nil;
    }
}

#pragma mark - SCFlashChatMatchViewControllerDelegate
- (void)onInsufficientBalance {
    dispatch_async(dispatch_get_main_queue(), ^{
        [kSCKeyWindow toast:@"Coins not enough".translateString];
        [SCCoinsPopupViewController showWithFromVC:[UIViewController currentViewController] entry:SCPayEntry.shared.kPayEntrySourceMatchResultCall];
    });
}

@end
