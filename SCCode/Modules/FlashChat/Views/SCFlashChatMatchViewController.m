//
//  SCFlashChatMatchViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCFlashChatMatchViewController.h"
#import "SCFlashRouletteView.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCCallService.h"
#import "SCCategoryAPIManagerCall.h"
#import "SCCategoryAPIManagerFlashChat.h"
#import "SCFlashMatchSuccessPopupView.h"
#import "SCNoEnoughCoinsAlertViewController.h"
#import "SCBaseNavigationController.h"
#import "SCCoinsFullScreenViewController.h"
#import "SCNavigationBar.h"
#import "SCGradientLabel.h"
#import "SCFontManager.h"
#import "Lottie/Lottie-Swift.h"
#import "SCCoinsService.h"
@interface SCFlashChatMatchViewController ()<SCCallServiceDelegate>

//匹配中的提示
//@property (nonatomic, strong) UIImageView * matchingTextIV;
@property (nonatomic, strong) SCGradientLabel *matchingLabel;

///匹配成功弹窗
@property (nonatomic, strong) SCFlashMatchSuccessPopupView *matchSuccessPopupView;

@property (nonatomic, strong) dispatch_queue_t queue;
@property (nonatomic, strong) dispatch_group_t group;
@property (nonatomic, assign) BOOL isTimeout;
@property (nonatomic, assign) BOOL isMatchding;
@property (nonatomic, strong) NSDictionary *userInfoDict;
@property (nonatomic, strong) NSString *toUserID;
@property (nonatomic, assign) BOOL isCloseTip;
@property (nonatomic, assign) BOOL isPop;
@property (nonatomic, assign) BOOL interruptMatching;
@property (nonatomic, strong) NSTimer *matchTimer;
@property (nonatomic, assign) NSInteger matchTimerCount;
@property (nonatomic,assign) BOOL previousIdleTimerDisabled;

//我的金币布局
@property (nonatomic, strong) UIView *myCoinsV;
///金币图标
@property (nonatomic, strong) UIImageView *coinsIconIV;
///金币数量
@property (nonatomic, strong) UILabel *coinsCountL;

@property (nonatomic, assign) BOOL isToVideo;

///匹配动画
@property (nonatomic, strong) CompatibleAnimationView *matchAnimationView;
///渐变蒙层
@property (nonatomic, strong) UIImageView *gradientImageView;

@property (atomic, assign) NSInteger groupCounter;

//匹配使用
@property (nonatomic,strong) SCXErrorModel *serror;

@property (nonatomic,strong) NSDictionary *result;



@end

@implementation SCFlashChatMatchViewController
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    // 保存之前的息屏状态并开启防息屏
    //刷新金币
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
    self.previousIdleTimerDisabled = [UIApplication sharedApplication].idleTimerDisabled;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [self.scNavigationBar.backButton setHidden:NO];
    [self _doMatch];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    self.isToVideo = NO;
    kSCAuthCallService.isMatch = YES;
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    // 恢复之前的息屏状态
    [UIApplication sharedApplication].idleTimerDisabled = self.previousIdleTimerDisabled;
    
    if (kSCAuthCallService.callingSessionModel.clientSessionId != nil && !self.isToVideo) {
        [kSCAuthCallService chancelFlashMath];
    }
    if (!self.isToVideo) {
        kSCAuthCallService.callingSessionModel = nil;
    }
    
    kSCAuthCallService.isMatch = NO;
   
}



- (void)dealloc
{
    //页面销毁，如果还没清空匹配
    kSCAuthCallService.callingSessionModel = nil;
    
}

- (void)initData {
    [super initData];
}

- (void)initUI{
    [super initUI];
    kSCAuthCallService.isMatch = YES;
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    [self _buildMyCoinsView];
    [self _buildMatchAnimationView];
    [self _buildGradientView];
    
    self.matchingLabel = [[SCGradientLabel alloc] init];
    self.matchingLabel.numberOfLines = 0;
    self.matchingLabel.textAlignment = NSTextAlignmentCenter;
    self.matchingLabel.contentInsets = UIEdgeInsetsMake(0, 15, 0, 15);
    self.matchingLabel.font = [SCFontManager boldItalicFontWithSize:32.0f];
    self.matchingLabel.text = @"MATCHING...".translateString;
    [self.matchingLabel setGradientColors:@[
        [UIColor colorWithHexString:@"#FF423E"],
        [UIColor colorWithHexString:@"#740A31"]
    ]];
    [self.view addSubview:self.matchingLabel];

    // 约束
    [self.matchingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(10);
        make.trailing.equalTo(self.view).offset(-10);
        make.bottom.equalTo(self.view).offset(-(kSCSafeAreaBottomHeight+40.0f));
    }];
}

- (void)_buildMatchAnimationView {
    // 创建 Lottie 动画视图
    NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/match_animation.json"];
    NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
    
    NSError *error = nil;
    NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
    if (jsonData) {
        self.matchAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
        self.matchAnimationView.contentMode = UIViewContentModeScaleAspectFill;
        self.matchAnimationView.loopAnimationCount = -1;
        self.matchAnimationView.animationSpeed = 2.6;
        [self.view addSubview:self.matchAnimationView];
        [self.matchAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.myCoinsV.mas_bottom).offset(24.0f);
            make.leading.bottom.trailing.equalTo(self.view);
        }];
    }
}

- (void)_buildGradientView {
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[
        [UIColor clearColor],
        [UIColor colorWithHexString:@"#4E1313"]
    ] orientation:SCGradientColorsOrientationVertical];
    
    UIImage *gradientImage = [[UIImage imagGradientWithSCGradientColors:colors startPoint:CGPointMake(0.5, 0.0) endPoint:CGPointMake(0.5, 0.6) size:CGSizeMake(kSCScreenWidth, 320)] resizableImageWithCapInsets:UIEdgeInsetsMake(10, 10, 10, 10) resizingMode:UIImageResizingModeStretch];
   
    // 创建渐变层的容器视图
    self.gradientImageView = [[UIImageView alloc] init];
    self.gradientImageView.image = gradientImage;
    [self.view addSubview:self.gradientImageView];
    [self.gradientImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
        make.height.equalTo(@320);
    }];
}

-(void) _buildMyCoinsView{
    self.myCoinsV = [UIView new].setBackgroundColor(UIColor.clearColor).addSuperView(self.view);
    [self.myCoinsV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight+10.0f);
        make.trailing.offset(-15);
        make.height.mas_equalTo(30);
    }];
    
    self.coinsIconIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_coins_middle"]];
    [self.myCoinsV addSubview:self.coinsIconIV];
    [self.coinsIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.myCoinsV);
        make.leading.equalTo(self.myCoinsV).offset(7);
        make.size.mas_equalTo(CGSizeMake(21, 16));
    }];
    
    self.coinsCountL = [UILabel new].setTextColor(UIColor.scWhite).setFontSemiboldSize(14).setTextAlignment(NSTextAlignmentRight).addSuperView(self.myCoinsV);
    [self.coinsCountL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.myCoinsV);
        make.leading.equalTo(self.coinsIconIV.mas_trailing).offset(4);
        make.trailing.equalTo(self.myCoinsV);
    }];
    
    kWeakSelf(self);
    [kScAuthMar.availableCoinsObx subscribe:^(NSNumber * _Nullable value) {
        
        weakself.coinsCountL.text = [NSString stringWithFormat:@"%ld",value.integerValue];
        
    } error:nil disposeBag:self.disposeBag];
}

-(void) _doMatch{
    if(self.isMatchding){
        return;
    }
    [self.matchingLabel setText:@"MATCHING...".translateString];
    self.isMatchding = YES;
    [self.matchAnimationView play];
    [self.scNavigationBar.backButton setHidden:NO];
    self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    
    _queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    _group = dispatch_group_create();
    kWeakSelf(self);
    self.result = nil;
    self.serror = nil;
   
    /*用于卡时间， 匹配时长至少2~3秒 */
    [self safeGroupEnter];
    dispatch_group_async(_group, _queue, ^{
        //
        //随机 2~3秒
        [weakself doMatchGroup_1];
    });
    
    
    
    /*匹配接口 */
    [self safeGroupEnter];
    self.isTimeout = NO;
    
    _userInfoDict = nil;
    
    dispatch_group_async(_group, _queue, ^{
        [weakself doMatchGroup_2];
    });
    
    // 在主线程设置定时器
    dispatch_async(dispatch_get_main_queue(), ^{
        
        [weakself doMatchGroup_3];
    });
    
    dispatch_group_notify(self.group,dispatch_get_main_queue(), ^{
        if (weakself == nil) {
            return;
        }
        [weakself doMatchGroup_4];
    });
    
}


- (void)doMatchGroup_1 {
    NSInteger time = arc4random() % 2 + 2;
    kWeakSelf(self)
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
       
        if(weakself == nil){
            return;
        }
        [weakself safeGroupLeave];
    });
}

- (void)doMatchGroup_2{
//    kSCAuthCallService.isMatch = YES;
    self.toUserID = nil;
    
    if([self isCanMatch]){
        //余额足够。跑匹配接口 或者当前有免费次数
        kWeakSelf(self)
        [kSCAuthCallService startFlashMathWithDelegate:weakself success:^(NSDictionary * _Nonnull value) {
            [weakself startFlashMathReslut:value];
        } failure:^(SCXErrorModel * _Nonnull error) {
            
            if(weakself != nil && !weakself.isTimeout){
                //其他异常
                weakself.serror = error;
                [weakself safeGroupLeave];
            }
        }];
//        [kSCAuthCallService startFlashMathWithDelegate:weakself success:^(SCFlashMatchModel * _Nonnull value) {
//            [weakself startFlashMathReslut:value];
//        } failure:^(SCXErrorModel * _Nonnull error) {
//            
//            if(weakself != nil && !weakself.isTimeout){
//                //其他异常
//                weakself.serror = error;
//                [weakself safeGroupLeave];
//            }
//        }];
        
        
    }else{
        //余额不足。跑随机主播接口
        if ([self.delegate respondsToSelector:@selector(onInsufficientBalance)]) {
            [self.delegate onInsufficientBalance];
        }
        [self onClose];
        [self safeGroupLeave];
    }

}

- (void)startFlashMathReslut:(NSDictionary *)value{
    
    if(!self.isTimeout){
        //如果没有超时
        self.result = value;
        NSString *toUserId = [SCDictionaryHelper stringFromDictionary:self.result forKey:@"toUserId" defaultValue:@""];
        if([toUserId length] > 1){
            self.toUserID = toUserId;
            //有UserID 之前请求用户信息
            kWeakSelf(self)
            // 使用字典版本的API调用
            [SCAPIServiceManager requestUserInfoWithUserId:toUserId cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(NSDictionary * _Nonnull userDict) {

                // 直接使用字典数据
                [weakself requestUserInfoDictResult:userDict];

            } failure:nil];
        }
        
        
    }
}

- (void)requestUserInfoDictResult:(NSDictionary *)userDict{
    if(self == nil){
        return;
    }
    if(!self.isTimeout){
        // 保存字典数据
        self.userInfoDict = userDict;
        // 从字典中获取用户ID
        self.toUserID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        [self safeGroupLeave];
    }
}



- (void)doMatchGroup_3{
    if (self.matchTimer) {
        [self.matchTimer invalidate];
        self.matchTimer = nil;
    }
    
    self.matchTimerCount = 0;
    kWeakSelf(self)
    self.matchTimer = [NSTimer scheduledTimerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        if (weakself == nil) {
            return;
        }
        [weakself matchTimerResult:timer];
    }];
    
    [[NSRunLoop mainRunLoop] addTimer:weakself.matchTimer forMode:NSRunLoopCommonModes];
}

- (void)matchTimerResult:(NSTimer *)timer{
    if (self == nil || self.matchTimer == nil) {
        [timer invalidate];
        return;
    }
    
    if (self.matchTimerCount >= 30) {
        if(self.serror == nil && self.userInfoDict == nil){
            self.isTimeout = YES;
            [self safeGroupLeave];
            [timer invalidate];
            self.matchTimer = nil;
        }
    }
    self.matchTimerCount += 1;
}

- (void)doMatchGroup_4{
    
    
    [self.matchTimer invalidate];
    self.matchTimer = nil;
    self.isMatchding = NO;
    if(self.userInfoDict != nil){
        // 禁用手势返回
        self.navigationController.interactivePopGestureRecognizer.enabled = NO;
        [self.scNavigationBar.backButton setHidden:YES];
        [self showMatchSuccessWithUserDict:self.userInfoDict];
        [self.matchTimer invalidate];
        self.matchTimer = nil;
       
        //随机 1~3秒
        NSInteger time = arc4random() % 2 + 1;
        ///延迟1~3秒后关闭
        kWeakSelf(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (weakself == nil) {
                return;
            }
            [weakself doMatchGroup_4_result];
                    });
        
        
    }else{
        if (!self.isCloseTip) {
            self.isCloseTip = true;
            if (!kSCAuthCallService.isVideo) {
                [kSCKeyWindow toast:kSCIsStrEmpty(self.serror.msg) ? @"There's no users available now, please try again later".translateString : self.serror.msg];
            }
        }
        
        [self onClose];
    }
}

- (void)doMatchGroup_4_result{
    if (self.interruptMatching) {
        return;
    }
    if (self.matchSuccessPopupView.isHidden) {
        return;
    }
    [self.matchSuccessPopupView hide];
    if(self.result != nil){
        //匹配成功
        [self toVideoCall];
    }else{
        //走随机用户的逻辑
        //关闭当前页面
        [self onClose];
        ///延迟0.5秒打开弹框
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // TODO: 是否需要修改半屏提示，以及提示金币不足。
            [SCNoEnoughCoinsAlertViewController showWithFromVC:[UIViewController currentViewController] getCoinsBlock:^(SCNoEnoughCoinsAlertViewController * _Nonnull vc) {
                [vc dismissViewControllerAnimated:YES completion:^{
                    
                    [[UIViewController currentViewController].navigationController pushViewController:[[SCCoinsFullScreenViewController alloc] init] animated:YES];
                }];
            } closeBlock:^(SCNoEnoughCoinsAlertViewController * _Nonnull vc) {
                [vc dismissViewControllerAnimated:YES completion:^{
                    
                }];
            }];
        });
        
    }

}

- (BOOL) isCanMatch {
    // 从策略字典中获取性别匹配金币配置和闪聊配置
    NSDictionary *genderMatchCoin = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.strategyObs.value forKey:@"genderMatchCoin" defaultValue:@{}];
    NSInteger femaleCoins = [SCDictionaryHelper integerFromDictionary:genderMatchCoin forKey:@"femaleCoins" defaultValue:0];

    NSDictionary *flashChatConfig = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.strategyObs.value forKey:@"flashChatConfig" defaultValue:@{}];
    BOOL isFreeCall = [SCDictionaryHelper boolFromDictionary:flashChatConfig forKey:@"isFreeCall" defaultValue:NO];

    return (kScAuthMar.availableCoinsObx.value.integerValue >= femaleCoins || isFreeCall);
}

-(void) toVideoCall{
//    kSCAuthCallService.callingSessionModel.clientSessionId = nil;
    self.isToVideo = YES;
    SCVideoCallViewController *vc = [[SCVideoCallViewController alloc] initWithSession:kSCAuthCallService.callingSessionModel];
    vc.delegate = kSCAuthCallService;
    kSCAuthCallService.delegate = vc;
    [self.navigationController pushViewController:vc animated:YES];
}


// 字典版本的匹配成功显示
-(void)showMatchSuccessWithUserDict:(NSDictionary *)userDict{
    [self.matchAnimationView stop];
    NSString *tranText = [@"WE’RE###A MATCH!".translateString stringByReplacingOccurrencesOfString:@"###" withString:@"\n"];
    [self.matchingLabel setText:tranText];

    //弹出匹配成功
    if(self.matchSuccessPopupView == nil){
        self.matchSuccessPopupView = [SCFlashMatchSuccessPopupView new];
    }
    // 直接使用字典版本的方法
    [self.matchSuccessPopupView setUserDict:userDict];
    [self.matchSuccessPopupView showWithSuperView:self.view];
}

-(void) onClose{
    if (kSCAuthCallService.isVideo) {
        return;
    }
    [self onBlack];
}
- (void)onBlack{
    if (!self.isPop) {
        self.isPop = YES;
        [self.matchAnimationView stop];
        [kSCAuthCallService chancelFlashMath];
        [super onBlack];
    }
    
}


#pragma mark - SCCallServiceDelegate

- (void)onCallCountDown:(NSInteger)countDown session:(SCCallSessionModel *)session {
    
}

// 字典版本的聊天消息回调
- (void)onChatEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session {
    // SCFlashChatMatchViewController不处理聊天消息
}

- (void)onCreatChannelFailWithSession:(SCCallSessionModel *)session {

}

- (void)onCreatChannelSuccessWithSession:(SCCallSessionModel *)session {

}

// 字典版本的预计挂断时间回调
- (void)onEstimatedHangUpTimeEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session {
    // SCFlashChatMatchViewController不处理预计挂断时间事件
}

// 字典版本的索取礼物消息回调
- (void)onGiftAskEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session {
    // SCFlashChatMatchViewController不处理礼物索取消息
}

- (void)onHangUpWithSession:(SCCallSessionModel *)session {
    
    if (self.isToVideo) {
        return;
    }
    
    if ([session.channelName isEqualToString:kSCAuthCallService.callingSessionModel.channelName]) {
        if ([self.toUserID isEqualToString:session.toUserId]) {
            //关闭匹配接口，重新匹配
            self.isMatchding = NO;
            [self.matchSuccessPopupView hide];
            self.interruptMatching = YES;
            kWeakSelf(self)
            [kSCKeyWindow toast:@"The anchor has left and will be matched again for you".translateString];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                
                if(weakself == nil){
                    return;
                }
                weakself.interruptMatching = NO;
            });
            [self _doMatch];
        }else if (!self.toUserID) {
            //全部挂断
            if (!self.isCloseTip) {
                self.isCloseTip = true;
                [kSCKeyWindow toast:@"User hung up, please try again".translateString];
            }
            
            [self onClose];
        }
    }
    
}

- (void)onJoinChannelCountDown:(NSInteger)countDown session:(SCCallSessionModel *)session {
    
}

- (void)onJoinChannelSuccessWithSession:(SCCallSessionModel *)session {
    
}

- (void)onJoinChannelTimeOutWithSession:(SCCallSessionModel *)session {
    
}

- (void)onPickUpWithSession:(SCCallSessionModel *)session {
    //如果已经接听
    
    if (self.isPop) {
        return;
    }
    if([session.toUserId length] > 1
       && self.isMatchding
       && [session.channelName isEqualToString:kSCAuthCallService.callingSessionModel.channelName]
       && [session.fromUserId isEqualToString:kSCCurrentUserID]
       ){
        kWeakSelf(self);
        __block bool isSuccess = NO;
        // 使用字典版本的API调用
        [SCAPIServiceManager requestUserInfoWithUserId:session.toUserId cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(NSDictionary * _Nonnull userDict) {

            if(weakself == nil){
                return;
            }
            if(!weakself.isTimeout){
                // 直接使用字典数据
                [weakself requestUserInfoDictResult:userDict];
                isSuccess = YES;
            }
        } failure:^(SCXErrorModel * _Nonnull error) {
            
            if(weakself == nil){
                return;
            }
            if(!weakself.isTimeout){
                if(isSuccess == NO){
                    [weakself safeGroupLeave];
                }
            }
        }];
    }
    
}

- (void)onTimeOutWithSession:(SCCallSessionModel *)session {
    if(!self.isTimeout){
        [self safeGroupLeave];
    }
}

- (void)safeGroupEnter {
    @synchronized (self) {
        self.groupCounter++;
        dispatch_group_enter(self.group);
    }
}

- (void)safeGroupLeave {
    @synchronized (self) {
        if (self.groupCounter > 0) {
            self.groupCounter--;
            dispatch_group_leave(self.group);
        }
    }
}

@end
