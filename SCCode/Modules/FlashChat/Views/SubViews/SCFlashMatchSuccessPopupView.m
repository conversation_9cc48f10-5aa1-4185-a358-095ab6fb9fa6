//
//  SCFlashMatchSuccessPopupView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCFlashMatchSuccessPopupView.h"
#import "SCFontManager.h"
#import "SCCountryService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCFlashMatchSuccessPopupView()
//内容布局
@property (nonatomic, strong) UIView *contentV;
//头像 长方形圆角
@property (nonatomic, strong) UIImageView *avatarIV;
//昵称
@property (nonatomic, strong) UILabel *nickNameL;
///年龄
@property (nonatomic, strong) UILabel *ageL;
///国家
@property (nonatomic, strong) UILabel *countryL;
///连接中按钮，圆角主题色背景
@property (nonatomic, strong) UIButton *connectBtn;

@end


@implementation SCFlashMatchSuccessPopupView

- (void)initUI{
    [super initUI];
    _contentV = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#282828"]).setCornerRadius(kSCNormalCornerRadius).addSuperView(self);
    _contentV.layer.masksToBounds = NO;
    _contentV.layer.shadowColor = [[UIColor colorWithHexString:@"#FF0000"] colorWithAlphaComponent:0.5].CGColor;
    _contentV.layer.shadowOffset = CGSizeMake(0, 2);
    _contentV.layer.shadowRadius = 10.0f;
    _contentV.layer.shadowOpacity = 1.0f;
    
    _avatarIV = [UIImageView new].setCornerRadius(kSCNormalCornerRadius).setContentMode(UIViewContentModeScaleAspectFill).addSuperView(self.contentV);
    _nickNameL = [UILabel new].setTextColor(UIColor.scWhite).addSuperView(self.contentV);
    _nickNameL.font = [SCFontManager semiBoldItalicFontWithSize:16.0f];
    _ageL = [UILabel new].setTextColor(UIColor.scWhite).setFontRegularSize(12.0f).setTextAlignment(NSTextAlignmentCenter).addSuperView(self.contentV);
    _countryL = [UILabel new].setTextColor(UIColor.scWhite).setFontRegularSize(12.0f).setTextAlignment(NSTextAlignmentCenter).addSuperView(self.contentV);
    _connectBtn = [UIButton buttonWithTitle:@"Connecting…".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(14.0f) image:nil backgroundColor:nil cornerRadius:10.0f].addSuperView(self.contentV);
    [_connectBtn sc_setThemeGradientBackgroundWithCornerRadius:10.0f];
    
    [_contentV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.width.mas_equalTo(kSCScaleWidth(296.0f));
    }];
    
    [_avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentV).offset(10.0f);
        make.top.equalTo(self.contentV).offset(8.0f);
        make.trailing.equalTo(self.contentV).offset(-10.0f);
        make.height.equalTo(self.avatarIV.mas_width).multipliedBy(321.0f / 276.0f);
    }];
    
    [_nickNameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.avatarIV.mas_bottom).offset(10.0f);
        make.height.mas_equalTo(20.0f);
        make.leading.equalTo(self.avatarIV);
        make.trailing.equalTo(self.connectBtn.mas_leading).offset(-10.0f);
    }];
    
    [_countryL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarIV);
        make.top.equalTo(self.nickNameL.mas_bottom).offset(2.0f);
        make.height.mas_equalTo(16.0f);
    }];
    
    [_ageL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nickNameL.mas_bottom).offset(2.0f);
        make.height.mas_equalTo(16.0f);
        make.leading.equalTo(self.countryL.mas_trailing).offset(5.0f);
        make.trailing.mas_lessThanOrEqualTo(self.connectBtn.mas_leading).offset(-10.0f);
    }];
    
    [_connectBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nickNameL.mas_top);
        make.trailing.equalTo(self.contentV).offset(-10.0f);
        make.bottom.equalTo(self.contentV).offset(-14.0f);
        make.width.mas_equalTo(144.0f);
        make.height.mas_equalTo(38.0f);
    }];
    
}

// 字典版本的用户信息设置方法
- (void)setUserDict:(NSDictionary *)userDict{
    _userDict = userDict;

    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    [self.avatarIV sc_setImageWithURL:avatarUrl];

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    [self.nickNameL setText:nickname];

    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    self.ageL.text = [NSString stringWithFormat:kScAuthMar.isLanguageForce ? @"%ld |" : @"| %ld", age];

    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
    self.countryL.text = [NSString stringWithFormat:@"%@ %@",[SCCountryService emojiForCountry:country] ,country];
}

- (void)showWithSuperView:(UIView *) superView{
    [self setHidden:NO];
    self.frame = superView.bounds;
    [superView addSubview:self];
    [UIView animateWithDuration:0.2 animations:^{
        self.alpha = 1.0;
    }];
}

- (void)hide {
    [self setHidden:YES];
    [UIView animateWithDuration:0.1 animations:^{
        self.alpha = 0.0;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}


@end
