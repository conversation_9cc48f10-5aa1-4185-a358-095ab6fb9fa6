//
//  SCFlashRouletteView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCFlashRouletteView.h"

@interface SCFlashRouletteView ()
//转盘图片
@property (nonatomic, strong) UIImageView * rouletteIV;
//中间的图标
@property (nonatomic, strong) UIImageView * centerIconIV;
//定时器
@property (nonatomic, strong) NSTimer *imageChangeTimer;
@property (nonatomic, assign) NSInteger currentImageIndex;  // 添加当前图片索引属性
@property (nonatomic, strong) CALayer *glowLayer;  // 添加发光层

@end

@implementation SCFlashRouletteView

- (void)initUI{
    [super initUI];
    
    _rouletteIV = [UIImageView new].setImageName(@"ic_flash_roulette_border").setContentMode(UIViewContentModeScaleToFill).addSuperView(self);
    
    // 创建发光层
    _glowLayer = [CALayer layer];
    _glowLayer.backgroundColor = [UIColor whiteColor].CGColor;
    _glowLayer.shadowColor = [UIColor colorWithHexString:@"#F29F1A"].CGColor;
    _glowLayer.shadowOffset = CGSizeZero;
    _glowLayer.shadowOpacity = 0.8;
    _glowLayer.shadowRadius = 15.0;
    [self.layer addSublayer:_glowLayer];
    
    _centerIconIV = [UIImageView new].setContentMode(UIViewContentModeScaleAspectFill).addSuperView(self);
    _centerIconIV.clipsToBounds = YES;
    
    // 初始化索引
    _currentImageIndex = 1;
    
    [self _updateLayout];
    // 开始图片切换定时器
    [self startImageChangeTimer];
}

- (void)_updateLayout{
    [_rouletteIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [_centerIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.centerY.equalTo(self);
        make.width.height.equalTo(self).multipliedBy(0.68);
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 设置圆形
    _centerIconIV.layer.cornerRadius = _centerIconIV.bounds.size.width / 2;
    
    // 更新发光层位置和大小
    _glowLayer.frame = _centerIconIV.frame;
    _glowLayer.cornerRadius = _centerIconIV.bounds.size.width / 2;
}

//开始转动
- (void)startRotateDuration:(CFTimeInterval)duration {
    CABasicAnimation * rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.fromValue = @(0);
    rotationAnimation.toValue = [NSNumber numberWithFloat:M_PI * 2.0];
    rotationAnimation.duration = duration;
    rotationAnimation.cumulative = YES;
    rotationAnimation.repeatCount = INFINITY;
    [_rouletteIV.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
}

//停止
- (void)stopRotate{
    [_rouletteIV.layer removeAnimationForKey:@"rotationAnimation"];
    //恢复到初始位置
    _rouletteIV.transform = CGAffineTransformIdentity;
}

// 开始图片切换定时器
- (void)startImageChangeTimer {
    [self stopImageChangeTimer]; // 确保之前的定时器被清除
    self.imageChangeTimer = [NSTimer scheduledTimerWithTimeInterval:3.0
                                                           target:self
                                                         selector:@selector(updateCenterImage)
                                                         userInfo:nil
                                                          repeats:YES];
    [self updateCenterImage]; // 立即更新一次图片
}

// 停止图片切换定时器
- (void)stopImageChangeTimer {
    [self.imageChangeTimer invalidate];
    self.imageChangeTimer = nil;
}

// 更新中心图片
- (void)updateCenterImage {
    NSArray *images = @[
                       @"ic_flash_match_girl_1",
                       @"ic_flash_match_girl_2",
                       @"ic_flash_match_girl_3",
                       @"ic_flash_match_girl_4",
                       @"ic_flash_match_girl_5",
                       ];
    // 构建图片名
    NSString *imageName = images[self.currentImageIndex - 1];
    self.centerIconIV.image = [SCResourceManager loadImageWithName:imageName];
    
    // 添加：更新发光效果
    [CATransaction begin];
    [CATransaction setAnimationDuration:0.3];
    _glowLayer.shadowOpacity = 0.8;
    [CATransaction commit];
    
    // 更新索引
    self.currentImageIndex++;
    if (self.currentImageIndex > 5) {  // 超过5后重置为1
        self.currentImageIndex = 1;
    }
}

// 在视图将要消失时清理定时器
- (void)dealloc {
    [self stopImageChangeTimer];
}

@end
