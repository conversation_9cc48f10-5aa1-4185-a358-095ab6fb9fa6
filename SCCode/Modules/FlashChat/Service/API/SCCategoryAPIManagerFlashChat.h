//
//  SCCategoryAPIManagerFlashChat.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerFlashChat)

///获取随机主播数据
+(void) requestRandomBroadcasterWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull userInfoDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

///获取轮播话术
+(void)requestConfigContentWithSuccess:(void (^_Nullable)(NSArray<NSString*> *_Nonnull data))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
@end

NS_ASSUME_NONNULL_END
