//
//  SCCategoryAPIManagerFlashChat.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/18.
//

#import "SCCategoryAPIManagerFlashChat.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerFlashChat)

///获取随机主播数据
+(void) requestRandomBroadcasterWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull userInfoDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    //先返回缓存然后再返回最新结果
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIRandomRecommendAnchor method:SCNetMethodGET parameters:nil headers:nil success:^(id responseObject) {
        NSDictionary *userInfoDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(userInfoDict){
            kSCBlockExeNotNil(success,userInfoDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get Recommend Anchor Fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+ (void)requestConfigContentWithSuccess:(void (^)(NSArray<NSString *> * _Nonnull))success failure:(void (^)(SCXErrorModel * _Nonnull))failure {
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIFlashChatConfig method:SCNetMethodPOST parameters:@{ @"type": @1 } headers:nil success:^(id responseObject) {
        if ([responseObject isKindOfClass:[NSArray class]]) {
            kSCBlockExeNotNil(success,(NSArray*)responseObject);
        } else {
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@""]);
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

@end
