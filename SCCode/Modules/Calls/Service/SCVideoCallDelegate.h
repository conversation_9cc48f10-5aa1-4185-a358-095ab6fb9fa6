//
//  SCVideoCallDelegate.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#ifndef SCVideoCallDelegate_h
#define SCVideoCallDelegate_h
@class SCVideoCallViewController;

@protocol SCVideoCallDelegate <NSObject>

//自己加入频道成功
- (void)videoCall:(SCVideoCallViewController *)vc joinChannelSucess:(NSString *)channelName uid:(NSUInteger)uid;
///对方加入成功
- (void)videoCall:(SCVideoCallViewController *)vc tagJoinChannelSuccess:(NSString *)channelName uid:(NSUInteger)uid;

@end

#endif /* SCVideoCallDelegate_h */
