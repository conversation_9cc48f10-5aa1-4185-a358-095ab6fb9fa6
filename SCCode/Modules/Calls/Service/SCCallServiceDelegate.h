//
//  SCCallServiceDelegate.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#ifndef SCCallServiceDelegate_h
#define SCCallServiceDelegate_h
#import "SCCallSessionModel.h"
///呼叫的代理
@protocol SCCallServiceDelegate <NSObject>
//接听
-(void) onPickUpWithSession:(SCCallSessionModel *)session;
//挂断
-(void) onHangUpWithSession:(SCCallSessionModel *)session;
//创建Channel失败
-(void) onCreatChannelFailWithSession:(SCCallSessionModel *)session;
//创建Channel成功
-(void) onCreatChannelSuccessWithSession:(SCCallSessionModel *)session;
//呼叫/被呼叫倒计时
-(void) onCallCountDown:(NSInteger) countDown session:(SCCallSessionModel *)session;
//超时
-(void) onTimeOutWithSession:(SCCallSessionModel *)session;
//joinChannel倒计时
-(void) onJoinChannelCountDown:(NSInteger) countDown session:(SCCallSessionModel *)session;
//joinChannel 超时
-(void) onJoinChannelTimeOutWithSession:(SCCallSessionModel *)session;
//joinChannel 成功
-(void) onJoinChannelSuccessWithSession:(SCCallSessionModel *)session;//索取礼物消息回调

#pragma mark - 字典版本的方法
//onChat 接收到1对1聊天消息 - 字典版本
-(void) onChatEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session;
//索取礼物消息回调 - 字典版本
-(void) onGiftAskEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session;
//挂断剩余时间回调 - 字典版本
-(void) onEstimatedHangUpTimeEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session;

@end

#endif /* SCCallServiceDelegate_h */
