//
//  SCCallService.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCBaseAppService.h"
#import "SCVideoCallViewController.h"
#import "SCCallServiceDelegate.h"
#import "SCVideoCallDelegate.h"
#import "SCCallNotificationPopup.h"

@class SCSocketEventModel,SCCallSessionModel;
NS_ASSUME_NONNULL_BEGIN
typedef NS_ENUM(NSInteger, SCCallSource) {
    SCCallSourceConversation = 1, // 会话界面
    SCCallSourceJSAPI = 2, // 页端调用
    SCCallSourceMatchResult = 3, // 匹配结果页
    SCCallSourcePopularWall = 4, // 主播/用户墙
    SCCallSourceAnotherAnchor = 5, // 呼叫失败时推荐另一批
    SCCallSourceDetailPage = 6, // 主播主页调用
    SCCallSourceDetailVideoPage = 7, // 主播视频详情页
    SCCallSourceMissCallPage = 8, // miss call 弹框回拨
    SCCallSourceConnectionErrorCallBack = 9, // 用户网络不好弹框回拨
    SCCallSourceMediaWall = 10, // 视频流
    SCCallSourceCallsPage = 11, // 通话记录
    SCCallSourceFastMatch = 12, // 快速匹配
    SCCallSourceAutoCall = 13, // auto call
    SCCallSourceMultiple = 14, // 多人连线
    SCCallSourceRecommendGoddess = 15, // 女神推荐
    SCCallSourceDetailPhotoPage = 16 //主播相册详情页
};


@interface SCCallService : SCBaseAppService<SCVideoCallDelegate>

//当前进行中的会话session
@property(nonatomic,strong,nullable) SCCallSessionModel *callingSessionModel;

@property(assign,nonatomic) BOOL isCanCall;//用界面控制
@property(assign,nonatomic) BOOL isCalling;//包含拨打界面和被呼叫界面
@property(assign,nonatomic) BOOL isCallVc;//只在拨打界面
@property(assign,nonatomic) BOOL isVideo;
@property(nonatomic,assign) BOOL isMatch;//是否正在匹配

@property(nonatomic,weak) id<SCCallServiceDelegate> delegate;

@property(nonatomic, weak) UIViewController *preViewController;

//被呼叫弹框状态
@property(nonatomic,nullable,strong) SCObservable<NSNumber *> *callingShowObx;

//通话异常处理
-(void) handleCallExceptionsWithContent:(NSString *) content;

///字典版本的事件回调，如果符合当前事件则返回YES进行拦截
-(BOOL) handleEventDict:(NSDictionary *) eventDict;


///开始呼叫 - 字典版本
-(void)startCallingWithUserDict:(NSDictionary *) userDict callSource:(SCCallSource) callSource;

///更新声网ID
+(void) requestUpdateWithAgoraUid:(NSString *)agoraUid success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;


///执行接听操作
-(BOOL) doPickUpWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
//挂断
-(void) doHangUpWithReason:(SCCallHangUpReason)reason;

///开始匹配
-(void)startFlashMathWithDelegate:(id<SCCallServiceDelegate>) delegate success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///取消快速匹配
-(void) chancelFlashMath;
/// 检查是不是还在被呼叫中，在切换语言后，需要将弹窗移到 Window 上层。
-(void) checkBeCallingPopup;

@end

NS_ASSUME_NONNULL_END
