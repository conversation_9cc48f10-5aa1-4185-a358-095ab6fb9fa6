//
//  SCCallService.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCCallService.h"
#import "SCSocketEventModel.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCModelCompatibility.h"
#import "SCCallNotificationPopup.h"
#import "SCCallSessionModel.h"
#import "SCCategoryAPIManagerCall.h"
#import "SCCallViewController.h"
#import "SCCoinsPopupViewController.h"

#import "SCCallExceptionalAlert.h"
#import "SCStoreScoreAlertViewController.h"
#import "SCAnchorCoinsViewController.h"
#import "SCAnchorEvaluateViewController.h"
#import "SCCallDBManager.h"
#import "SCResourceManager.h"
#import "SCCoinsService.h"
#import "SCPermissionManager.h"
#import "SCAVAudioSessionUtils.h"
#import "SCPopupManager.h"
#import "SCMessagePopupManager.h"

#import "SCOrderedDictionary.h"
@interface SCCallService ()<AVAudioPlayerDelegate>{
    AVAudioPlayer *_ringPlayer;
}


//被呼叫弹框
@property(nonatomic,strong) SCCallNotificationPopup * beCallingPopup;
///被呼叫的超时
@property(nonatomic,strong) NSTimer * callingTimeOut;
///呼叫/被呼叫倒计时
@property(nonatomic,assign) NSInteger callingTimeCountDown;

///开始进入频道倒计时
@property(nonatomic,strong) NSTimer * joinChannelTimeOut;
///倒计时数量
@property(nonatomic,assign) NSInteger joinChannelTimeOutCountDown;

@property(nonatomic, strong) dispatch_source_t vibrateTimer; // 添加震动定时器属性

@property(nonatomic,strong) SCOrderedDictionary<NSString *,SCSocketEventModel *> *messageCacheMap;

@end
@implementation SCCallService

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        [self _init];
    }
    return self;
}
-(void) _init{
    _callingShowObx = [[SCObservable<NSNumber *> alloc] initWithValue:@(0)];
    // 注册前后台切换通知
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                           selector:@selector(handleAppStateChange:) 
                                               name:UIApplicationDidEnterBackgroundNotification 
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                           selector:@selector(handleAppStateChange:) 
                                               name:UIApplicationWillEnterForegroundNotification 
                                             object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)handleAppStateChange:(NSNotification *)notification {
    if ([notification.name isEqualToString:UIApplicationDidEnterBackgroundNotification]) {
        // App 进入后台
        [self stopRing];
        [self stopVibrate];
    } else if ([notification.name isEqualToString:UIApplicationWillEnterForegroundNotification]) {
        // App 回到前台
        if (self.callingSessionModel && 
            (self.callingSessionModel.status == SCCallSessionStatusCallding) && !self.callingSessionModel.isMyCall) {
            // 如果被呼叫状态,恢复声音和震动
            [self startRingPay];
            [self startVibrate];
        }
    }
}

- (BOOL)isCanCall {
    if (!self.isCalling
        && !self.isMatch
        && !self.isVideo) {
        return YES;
    }
    return NO;
}

#pragma mark - 入口

// 移除Model版本的被呼叫方法，统一使用字典版本

///开始被呼叫 - 字典版本
-(void)startBeCalledWithDict:(NSDictionary *) onCallDict{
    self.callingSessionModel = [[SCCallSessionModel alloc] initWithOnCallDict:onCallDict];
    //保存
    [SCCallDBManager.sharedManager insertCallSessionDict:[SCCallDBManager.sharedManager _dictionaryFromCallSession:self.callingSessionModel]];

    dispatch_async(dispatch_get_main_queue(), ^{
        //打开呼叫页面
        SCCallViewController *callVC = [SCCallViewController showCallingFromVC:[UIViewController currentViewController] sessionModel:self.callingSessionModel];
        //设置代理
        kSCAuthCallService.delegate = callVC;
    });
    [self _startCallingTimeOut];
    [self startRingPay];
    [self startVibrate]; // 添加震动
}

// 移除Model版本的方法，统一使用字典版本

///开始呼叫 - 字典版本
-(void)startCallingWithUserDict:(NSDictionary *) userDict callSource:(SCCallSource) callSource{
    NSString *userID = kSCUserIDFromDict(userDict);
    if (kSCIsStrEmpty(userID)) {
        return;
    }
    if (!self.isCanCall) {
        [kSCKeyWindow toast:@"Incoming call, please try again later".translateString];
        return;
    }

    NSInteger unitPrice = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserUnitPriceKey defaultValue:0];
    if(kScAuthMar.availableCoinsObx.value.integerValue < unitPrice){
        NSString *source = SCPayEntry.shared.kPayEntrySourceCall;
        switch (callSource) {
            case SCCallSourcePopularWall:
                source = SCPayEntry.shared.kPayEntrySourceAnchorWall;
                break;
            case SCCallSourceConversation:
                source = SCPayEntry.shared.kPayEntrySourceConversation;
                break;
            case SCCallSourceDetailVideoPage:
                source = SCPayEntry.shared.kPayEntrySourceAnchorProfileVideoCall;
                break;
            case SCCallSourceDetailPhotoPage:
                source = SCPayEntry.shared.kPayEntrySourceAnchorProfilePhotoCall;
                break;
            case SCCallSourceDetailPage:
                source = SCPayEntry.shared.kPayEntrySourceAnchorProfileCall;
                break;
            default:
                break;
        }

        //转跳充值页面
        [SCCoinsPopupViewController showWithFromVC:[UIViewController currentViewController] entry:source];
        //延迟
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [kSCKeyWindow toast:@"Coins not enough".translateString];
        });
        return;
    }

    NSString *statusString = kSCUserStatusFromDict(userDict);
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:statusString];
    if (status != AnchorStatusOnline) {
        //判读对应的状态 并且Toast User is inCall
        if(status == AnchorStatusOffline){
            [kSCKeyWindow toast:@"The user is currently offline. Please try again later.".translateString];
        }else if(status == AnchorStatusBusy){
            [kSCKeyWindow toast:@"The user is currently busy. Please try again later.".translateString];
        }else if(status == AnchorStatusIncall){
            [kSCKeyWindow toast:@"The user is on a call right now. Please try again later.".translateString];
        }else{
            [kSCKeyWindow toast:@"The user is currently busy. Please try again later.".translateString];
        }
        return;
    }

    kWeakSelf(self)

    void (^startCall)(void) = ^{

        dispatch_async(dispatch_get_main_queue(), ^{
            //开启超时
            [weakself _startCallingTimeOut];
            //开启铃声
            [weakself startRingPay];
            //创建Session
            weakself.callingSessionModel = [kSCAuthCallService createSessionWithToUserId:userID unitPrice:unitPrice];
            //打开呼叫页面
            SCCallViewController *callVC = [SCCallViewController showCallingFromVC:[UIViewController currentViewController] sessionModel:weakself.callingSessionModel];
            //设置代理
            weakself.delegate = callVC;
            //创建频道[需要放置在callingSession]创建之后
            [weakself createChannelWithCallSource:[NSString stringWithFormat:@"%ld",callSource]];
        });

    };

    // 检查相机和麦克风权限
    [[SCPermissionManager shared] checkPermissions:@[@(SCPermissionTypeCamera), @(SCPermissionTypeMicrophone)]
                                      completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            startCall();
        } else if (shouldShowAlert) {
            // 如果权限不足，先检查相机权限
            [weakself checkPermissionCamera:startCall];
        } else {
            // 如果不需要显示权限提示，直接开始匹配
            startCall();
        }
    }];

}

- (void)checkPermissionCamera:(void (^)(void))startCall {
    kWeakSelf(self)
    
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeCamera
                                     completion:^(BOOL cameraGranted, BOOL cameraShouldShowAlert) {
        if (!cameraGranted && cameraShouldShowAlert) {
            [weakself checkPermissionMicrophone:startCall];
        } else {
            // 如果相机权限已授权，检查麦克风权限
            [weakself showPermissionAlertMicrophone:startCall];
        }
    }];
}

- (void)showPermissionAlertMicrophone:(void (^)(void))startCall {
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                 fromViewController:[UIViewController currentViewController]
                                      cancelBlock:^{
        startCall();
    }];
}

- (void)checkPermissionMicrophone:(void (^)(void))startCall {
    kWeakSelf(self)
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeCamera
                                 fromViewController:[UIViewController currentViewController]
                                      cancelBlock:^{
        // 检查麦克风权限
        [weakself checkPermissionDissMicrophone:startCall];
    }];
}

- (void)checkPermissionDissMicrophone:(void (^)(void))startCall {
    kWeakSelf(self)
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeMicrophone
                                     completion:^(BOOL micGranted, BOOL micShouldShowAlert) {
        if (!micGranted && micShouldShowAlert) {
            [weakself showPermissionAlertMicrophone:startCall];
        } else {
            startCall();
        }
    }];
}


-(void)startFlashMathWithDelegate:(id<SCCallServiceDelegate>) delegate success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    self.delegate = delegate;
    NSDate *date = [NSDate date];
    NSTimeInterval timeInterval = [date timeIntervalSince1970] * 1000;
    NSString *time = [NSString stringWithFormat:@"%lld", (long long)timeInterval];
    
    NSString *batchId = [NSString stringWithFormat:@"%@%d", [time substringFromIndex:[time length] - 3], (int)(arc4random_uniform(9) + 1) * 100000];
    kWeakSelf(self);
    
    self.callingSessionModel = [self createSessionFlashMatch];
    NSString *clientSessionId = self.callingSessionModel.clientSessionId;
    [SCAPIServiceManager requestFlashMatchWithBatchId:batchId clientSessionId:self.callingSessionModel.clientSessionId success:^(NSDictionary * _Nonnull flashMatchDict) {

        if([clientSessionId isEqualToString:self.callingSessionModel.clientSessionId]){
            //防止回调过程中，会话已经更新
            [weakself.callingSessionModel configWithFlashMatchDict:flashMatchDict];
            dispatch_async(dispatch_get_main_queue(), ^{
                kSCBlockExeNotNil(success,flashMatchDict);
            });

        }else{
            
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@""]);
            BOOL cllid = weakself.isMatch;
            NSString *cllod = weakself.callingSessionModel.clientSessionId;
            if (![clientSessionId isEqualToString:weakself.callingSessionModel.clientSessionId] || !weakself.isMatch) {//界面消失，接口还未完成的情况
                [SCAPIServiceManager requestCancelFlashMatchWithClientSessionId:clientSessionId success:nil failure:nil];
            }
        }
        
    } failure:^(SCXErrorModel * _Nonnull error) {
        weakself.callingSessionModel = nil;
        kSCBlockExeNotNil(failure, error);
        
    }];
}

///取消快速匹配
-(void) chancelFlashMath{
    if(self.isMatch ){
        [SCAPIServiceManager requestCancelFlashMatchWithClientSessionId:self.callingSessionModel.clientSessionId success:nil failure:nil];
        self.callingSessionModel = nil;
    }
    
}

///执行接听操作
-(BOOL) doPickUpWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    if (self.callingSessionModel == nil) {
        return NO;
    }
    
    //判断余额是否足够
    if(!self.callingSessionModel.isFree){
        NSInteger unitPrice = self.callingSessionModel.unitPrice;
        if(![kSCAuthCoinsService checkEnoughCoinsWithModel:unitPrice entry:SCPayEntry.shared.kPayEntrySourceOnCall]){
            return NO;
        }
    }

    
    [SCAPIServiceManager requestCallPickUpChannelName:self.callingSessionModel.channelName success:nil failure:nil];
    
    //标记为等待加入
    self.callingSessionModel.status = SCCallSessionStatusWaitJoin;
    //关闭呼叫超时
    [self _stopCallingTimeOut];
    //关闭铃声
    [self stopRing];
    [self stopVibrate]; // 添加停止震动
    //关闭悬浮框
    [self closeBeCallPopup];
    //开启等待加入倒计时
    [self _startJoinChannelTimeOut];
    
    return YES;
}



-(void) closeBeCallPopup{
    [self.beCallingPopup hide];
    self.beCallingPopup = nil;
}

#pragma mark - 提供外部调用的Action
-(void) doHangUpWithReason:(SCCallHangUpReason)reason{
    //主动挂断
    self.callingSessionModel.endType = self.callingSessionModel.status == SCCallSessionStatusOnCalled ? SCCallSessionEndTypeNormal : SCCallSessionEndTypeCancelMyself;
    [self _doHangUpWithReason:SCCallHangUpReasonNormal];
}

///执行加入频道
-(void) doJoinChannelWithChannelName:(NSString *)channelName success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    ///加入频道
    kWeakSelf(self)
    [SCAPIServiceManager requestJoinWithChannelName:channelName success:^{
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
    
}

- (void)sc_blank_empty{
    
}

///创建会话ID
-(SCCallSessionModel *) createSessionWithToUserId:(NSString *)toUserId unitPrice:(NSInteger)unitPrice{
    
    NSString * clientSessionId = [[NSUUID UUID] UUIDString];
    NSString *fromUserId = self.userId;
    self.callingSessionModel = [[SCCallSessionModel alloc] initWithClientSessionId:clientSessionId unitPrice:unitPrice fromUserId:fromUserId toUserId:toUserId];
    [SCCallDBManager.sharedManager insertCallSession:self.callingSessionModel];
    return self.callingSessionModel;
}
///创建会话ID
-(SCCallSessionModel *) createSessionFlashMatch{
    
    NSString * clientSessionId = [[NSUUID UUID] UUIDString];
    NSString *fromUserId = self.userId;
    self.callingSessionModel = [[SCCallSessionModel alloc] initWithClientSessionId:clientSessionId isMatch:YES fromUserId:fromUserId];
    [SCCallDBManager.sharedManager insertCallSession:self.callingSessionModel];
    return self.callingSessionModel;
}


///创建Channel 并且返回计时器，如果30秒内没返回数据则返回超时
-(void) createChannelWithCallSource:(NSString *)callSource{
    
    NSString * clientSessionId = self.callingSessionModel.clientSessionId;
    NSString *toUserId = self.callingSessionModel.toUserId;
    kWeakSelf(self);
    [SCAPIServiceManager requestCreatChannelWithToUserId:self.callingSessionModel.toUserId clientSessionId:clientSessionId callType:@"1" callSource:callSource success:^(NSDictionary *  channelDict) {

        if (!weakself.isCallVc) {

            //不在拨打界面,快速挂断，界面还未完成的情况
            NSString *channelName = [SCDictionaryHelper stringFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:nil];
            [weakself quickCancelCallToUserId:toUserId channelName:channelName];
        }
        if([clientSessionId isEqualToString:weakself.callingSessionModel.clientSessionId]){
            //防止回调过程中，会话已经更新
            [weakself.callingSessionModel configWithChannelDict:channelDict];

            dispatch_async(dispatch_get_main_queue(), ^{
                if(weakself.delegate){
                    [weakself.delegate onCreatChannelSuccessWithSession:weakself.callingSessionModel];
                }
            });
        }else{
            [weakself _handelCreateChannelFail];
        }
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself _handelCreateChannelFail];
    }];
}

//不在拨打界面,快速挂断，界面还未完成的情况
- (void)quickCancelCallToUserId:(NSString *)oppositeUserId channelName:(NSString *)channelName{
    [SCAPIServiceManager requestCallHangUpWithToUserId:oppositeUserId channelName:channelName handUpReason:@"1" success:nil failure:nil];
}

-(void) checkBeCallingPopup {
    if (self.beCallingPopup != nil) {
        [kSCKeyWindow bringSubviewToFront:self.beCallingPopup];
    }
}


#pragma mark - 执行操作
-(void) _doHangUpWithReason:(SCCallHangUpReason)handUpReason{
    //记录挂断原因
    self.callingSessionModel.hangUpReason = handUpReason;
    //停止进入频道倒计时
    [self _stopJoinChannelTimeOut];
    //中断超时倒计时
    [self _stopCallingTimeOut];
    //关闭悬浮的呼叫框
    [self closeBeCallPopup];
    //通知响铃
    [self stopRing];
    [self stopVibrate]; // 添加停止震动
    if(!kSCIsStrEmpty(self.callingSessionModel.channelName)){
        //调用挂断接口
        [SCAPIServiceManager requestCallHangUpWithToUserId:self.callingSessionModel.tagId channelName:self.callingSessionModel.channelName handUpReason:[NSString stringWithFormat:@"%ld",handUpReason] success:nil failure:nil];
    }
    if(self.delegate){
        ///如果超时 回调超时
        if(handUpReason == SCCallHangUpReasonCallTimeout){
            [self.delegate onTimeOutWithSession:self.callingSessionModel];
        }else if(handUpReason == SCCallHangUpReasonNetworkException){
            //因为创建订单失败也会执行挂断流程所以，使用的是网络请求的挂断回调
            [self.delegate onCreatChannelFailWithSession:self.callingSessionModel];
        }else{
            [self.delegate onHangUpWithSession:self.callingSessionModel];
        }
    }
    //判断挂断的业务
    if(self.callingSessionModel.status == SCCallSessionStatusOnCalled){
        //只有在通话过后关闭才需要检测弹出操作
        [self doCloseAction];
    }
    
    //标记为挂断
    self.callingSessionModel.status = SCCallSessionStatusHangUp ;
    //保存通话记录
    if (self.callingSessionModel != nil) {
        [SCCallDBManager.sharedManager saveCallSession:self.callingSessionModel];
    }
    //清空当前session
    if (!self.callingSessionModel.isMatch) {
        self.callingSessionModel = nil;
        self.isVideo = false;
    }
    
    // 添加通知，0表示没有被呼叫
    self.callingShowObx.value = @(0);
//    self.callingSessionModel = nil;
}
-(void) _handelCreateChannelFail{
    self.callingSessionModel.endType = SCCallSessionEndTypeUnknown;
    [self _doHangUpWithReason:SCCallHangUpReasonNetworkException];
}
///执行关闭呼叫后的事情
-(void) doCloseAction{
   
    SCCallSessionModel *session = self.callingSessionModel;
    //获取会话结果
    kWeakSelf(self)
    if(!kSCIsStrEmpty(self.callingSessionModel.channelName)){
        [SCAPIServiceManager requestCallResultWithChannelName:self.callingSessionModel.channelName success:^(NSDictionary * _Nullable resultDict) {
            NSInteger duration = [SCDictionaryHelper integerFromDictionary:resultDict forKey:SCDictionaryKeys.shared.kSCCallDurationKey defaultValue:0];
            session.callDuration = duration;
            //保存时长
            [SCCallDBManager.sharedManager saveCallSession:session];
            [weakself sc_blank_empty];
        } failure:nil];
    }
    if(session.isMatch){
        if(session.isFree && session.callFreeSeconds > 0){
            //不弹出评价和引导
            return;
        }
    }
    
    void(^ doCheck)(void) = ^{
        [self hideAnchorEvaluatePopupIfNeeded];
        NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
        BOOL isRecharge = [SCDictionaryHelper boolFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIsRechargeKey defaultValue:NO];
        if(isRecharge){
            //充值用户 提示评分
            [SCAnchorEvaluateViewController checkAnchorEvaluateWithChannelNameDict:session.channelName];
        }else{
//            //免费用户 充值引导
            [SCAPIServiceManager requestUserInfoWithUserId:session.tagId cachePolicy:SCNetCachePolicyOnlyCache success:^(NSDictionary * _Nonnull userInfoDict) {
                [SCAnchorCoinsViewController showWithFromVC:weakself.preViewController anchorDict:userInfoDict];
                [weakself sc_blank_empty];
            } failure:nil];
        }
        
    };
    
    
    if(![SCStoreScoreAlertViewController checkIsCanOpenStoreScoreAlertWithCompletion:^{
        doCheck();
    }]){
        //如果没有显示商店评价 ，那么直接开始检查 主播评价或者，引导充值
        doCheck();
    }
    
}

- (void)hideAnchorEvaluatePopupIfNeeded {
    // 遍历当前活跃的弹窗
    for (UIViewController *popup in [SCPopupManager shared].activePopups) {
        if ([popup isKindOfClass:[SCAnchorEvaluateViewController class]]) {
            // 找到评价弹窗后，使用 PopupManager 将其关闭
            [[SCPopupManager shared] dismissPopup:popup];
        }
    }
}


#pragma mark- 铃声
//开始铃声
-(void) startRingPay{
    [self stopRing];
    
    NSError *error = nil;
    [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategorySoloAmbient error:&error];
    ///开始播放音乐
    NSURL *audioURL = [NSURL URLWithString:[NSString stringWithFormat:@"file://%@", [SCResourceManager.assterPath stringByAppendingString:@"/mp3/ring_call.mp3"]]];
    error = nil;
    _ringPlayer = [[AVAudioPlayer alloc] initWithContentsOfURL:audioURL error:&error];
    if (_ringPlayer) {
        // 设置音量为最大
        _ringPlayer.volume = 1.0;
        _ringPlayer.numberOfLoops = -1; // 设置循环播放，-1表示无限循环
        
        // 添加播放完成回调，在每次循环前检查应用状态
        _ringPlayer.delegate = self;
        [_ringPlayer play]; // 开始播放音频
    } else {
        
    }
}

// AVAudioPlayerDelegate 方法
- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    // 在每次循环播放完成时检查应用状态
    if ([UIApplication sharedApplication].applicationState == UIApplicationStateBackground) {
        [self stopRing];
    }
}

//结束铃声
-(void) stopRing{
    if(_ringPlayer){
        [_ringPlayer stop];
        _ringPlayer = nil;
        
    }
}
#pragma mark - 呼叫超时
//开始呼叫超时
-(void) _startCallingTimeOut{
    [self _stopCallingTimeOut];
    ///倒计时30秒
    kWeakSelf(self);
    self.callingTimeCountDown = 30;
    
    // 修改为 repeats:YES，使定时器重复触发
    _callingTimeOut = [NSTimer timerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        if(timer != weakself.callingTimeOut){
            [timer invalidate];
            return;
        }
        [weakself _handelCallTimeout];
    }];
    
    [[NSRunLoop currentRunLoop] addTimer:_callingTimeOut forMode:NSRunLoopCommonModes];
}
//停止呼叫超时
-(void) _stopCallingTimeOut{
    if(self.callingTimeOut){
        [self.callingTimeOut invalidate];
        _callingTimeOut = nil;
    }
}
-(void) _handelCallTimeout{
    kWeakSelf(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        weakself.callingTimeCountDown--;
        
        // 通知代理更新倒计时
        if(weakself.delegate) {
            [weakself.delegate onCallCountDown:weakself.callingTimeCountDown 
                                     session:weakself.callingSessionModel];
        }
        
        // 检查是否超时
        if(weakself.callingTimeCountDown <= 0) {
            // 设置结束类型
            weakself.callingSessionModel.endType = weakself.callingSessionModel.isMyCall ? 
                SCCallSessionEndTypeUnanswered : SCCallSessionEndTypeMissed;
            
            // 停止定时器
            [weakself _stopCallingTimeOut];
            
            // 执行挂断
            [weakself _doHangUpWithReason:SCCallHangUpReasonCallTimeout];
        }
    });
}
#pragma mark - 倒计时

///开始加入频道倒计时
-(void) _startJoinChannelTimeOut{
    [self _stopCallingTimeOut];
    ///倒计时30秒
    kWeakSelf(self);
    self.joinChannelTimeOutCountDown = 20;
    _joinChannelTimeOut = [NSTimer timerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        if(timer != weakself.joinChannelTimeOut){
            [timer invalidate];
            return;
        }
        [weakself _handleJoinChannelTimeOut];

    }];
    [[NSRunLoop currentRunLoop] addTimer:_joinChannelTimeOut forMode:NSRunLoopCommonModes];
}
//停止加入频道倒计时
-(void) _stopJoinChannelTimeOut{
    if(self.joinChannelTimeOut){
        [self.joinChannelTimeOut invalidate];
        _joinChannelTimeOut = nil;
    }
}
//处理超时
-(void) _handleJoinChannelTimeOut{
    kWeakSelf(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        weakself.joinChannelTimeOutCountDown --;
        if(weakself.joinChannelTimeOutCountDown == 0){
            //执行挂断
            [weakself _stopJoinChannelTimeOut];
                       
            if (weakself.callingSessionModel.status == SCCallSessionStatusWaitJoin && weakself.delegate) {
                [weakself.delegate onJoinChannelTimeOutWithSession:weakself.callingSessionModel];
            }
        }
        
        if(weakself.delegate){
            [weakself.delegate onJoinChannelCountDown:weakself.joinChannelTimeOutCountDown session:weakself.callingSessionModel];
        }
    });
}

#pragma mark - 事件分发

//通话异常处理
-(void) handleCallExceptionsWithContent:(NSString *) content{
    //主线程
    dispatch_async(dispatch_get_main_queue(), ^{
        [SCCallExceptionalAlert showWithContent:content];
    });
   
}
// 移除Model版本的事件处理方法，统一使用字典版本handleEventDict

///字典版本的事件回调，如果符合当前事件则返回YES进行拦截
-(BOOL) handleEventDict:(NSDictionary *) eventDict{

    NSString *command = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"command" defaultValue:@""];
    NSString *commandId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"commandId" defaultValue:@""];
    NSInteger timestamp = [SCDictionaryHelper integerFromDictionary:eventDict forKey:@"timestamp" defaultValue:0];
    id data = [eventDict objectForKey:@"data"];

    NSString *times = nil;

    if (times == nil && [data isKindOfClass:[NSDictionary class]]) {
        NSDictionary *dataDict = (NSDictionary *)data;
        if (dataDict[@"timestamp"]) {
            times = [NSString stringWithFormat:@"%@", dataDict[@"timestamp"]];
        }
    }
    if (timestamp != 0 && times == nil) {
        times = [NSString stringWithFormat:@"%ld", timestamp];
    }

    if (self.messageCacheMap == nil) {
        self.messageCacheMap = [[SCOrderedDictionary<NSString *,SCSocketEventModel *> alloc] init];
    }
    if ([self.messageCacheMap objectForKey:times] && times != nil) {
        return YES;
    }
    if (times != nil) {
        if (self.messageCacheMap.count > 200) {
            self.messageCacheMap = [[SCOrderedDictionary<NSString *,SCSocketEventModel *> alloc] init];
        }
        // 暂时存储字典数据，后续可以改为存储字典
        SCSocketEventModel *tempEvent = [[SCSocketEventModel alloc] init];
        [self.messageCacheMap setObject:tempEvent forKey:times];
    }

    if([command isEqualToString:@"onCall"]){
        if(!self.isCanCall){
            /// 中断当前呼叫，因为已经进行中
            #warning 注意需要补充中断逻辑
            return YES;
        }

        // 直接使用字典数据而不是Model对象
        if ([data isKindOfClass:[NSDictionary class]]) {
            NSDictionary *onCallData = (NSDictionary *)data;
            // 隐藏消息弹窗，如果有的话。
            [[SCMessagePopupManager shared] hideCurrentPopup];
            //被呼叫 - 使用字典数据
            [self startBeCalledWithDict:onCallData];
            // 1 表示呼叫
            self.callingShowObx.value = @(1);
        }
    }else{
        if([command isEqualToString:@"onHangUp"]){
            //挂断
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *hangUpData = (NSDictionary *)data;
                NSString *fromUserId = [SCDictionaryHelper stringFromDictionary:hangUpData forKey:@"fromUserId" defaultValue:@""];
                NSString *channelName = [SCDictionaryHelper stringFromDictionary:hangUpData forKey:@"channelName" defaultValue:@""];

                if (self.callingSessionModel.isMatch) {
                    self.callingSessionModel.toUserId = fromUserId;
                }
                if ([channelName isEqualToString:self.callingSessionModel.channelName]) {
                    [self _onHangUpWithEventDict:hangUpData];
                }
            }
            return YES;
        }else if ([command isEqualToString:@"onPickUp"]){
            //对方接听
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *pickUpData = (NSDictionary *)data;
                NSString *channelName = [SCDictionaryHelper stringFromDictionary:pickUpData forKey:@"channelName" defaultValue:@""];

                if ([channelName isEqualToString:self.callingSessionModel.channelName]) {
                    [self _onPickUpWithEventDict:pickUpData];
                }
            }
            return YES;
        }else if([command isEqualToString:@"onChat"]){
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *chatData = (NSDictionary *)data;
                NSString *fromUserId = [SCDictionaryHelper stringFromDictionary:chatData forKey:@"fromUserId" defaultValue:@""];

                //会话聊天消息
                if([fromUserId isEqualToString:self.callingSessionModel.tagId]){
                    //只会处理当前会话中的消息
                    if(self.delegate && [self.delegate respondsToSelector:@selector(onChatEventDict:successWithSession:)]){
                        [self.delegate onChatEventDict:chatData successWithSession:self.callingSessionModel];
                    }
                    return YES;
                }
            }
        }else if([command isEqualToString:@"onGiftAsk"]){
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *giftAskData = (NSDictionary *)data;
                NSString *fromUserId = [SCDictionaryHelper stringFromDictionary:giftAskData forKey:@"fromUserId" defaultValue:@""];

                //会话聊天消息
                if([fromUserId isEqualToString:self.callingSessionModel.tagId]){
                    //只会处理当前会话中的消息
                    if(self.delegate && [self.delegate respondsToSelector:@selector(onGiftAskEventDict:successWithSession:)]){
                        [self.delegate onGiftAskEventDict:giftAskData successWithSession:self.callingSessionModel];
                    }
                    return YES;
                }
            }
        }else if([command isEqualToString:@"estimatedHangUpTime"]){
            if ([data isKindOfClass:[NSDictionary class]]) {
                NSDictionary *estimatedData = (NSDictionary *)data;
                NSString *channelName = [SCDictionaryHelper stringFromDictionary:estimatedData forKey:@"channelName" defaultValue:@""];

                //会话聊天消息
                if([channelName isEqualToString:self.callingSessionModel.channelName]){
                    //只会处理当前会话中的消息
                    if(self.delegate && [self.delegate respondsToSelector:@selector(onEstimatedHangUpTimeEventDict:successWithSession:)]){
                        [self.delegate onEstimatedHangUpTimeEventDict:estimatedData successWithSession:self.callingSessionModel];
                    }
                    return YES;
                }
            }
        }
    }
    return NO;
}

// 移除Model版本的挂断处理方法，统一使用字典版本

///接收到挂断 - 字典版本
-(void) _onHangUpWithEventDict:(NSDictionary *) eventDict{
    self.callingSessionModel.endType = self.callingSessionModel.status == SCCallSessionStatusOnCalled ? SCCallSessionEndTypeNormal : self.callingSessionModel.isMyCall ? SCCallSessionEndTypeUnanswered : SCCallSessionEndTypeMissed;
    NSInteger reason = [SCDictionaryHelper integerFromDictionary:eventDict forKey:@"reason" defaultValue:1];
    if (reason == SCCallHangUpReasonNoCoins) {
        [self _doHangUpWithReason:SCCallHangUpReasonNoCoins];
    } else {
        [self _doHangUpWithReason:SCCallHangUpReasonRemoteUserLeft];
    }
}
// 移除Model版本的接听处理方法，统一使用字典版本

//接收到接听 - 字典版本
-(void) _onPickUpWithEventDict:(NSDictionary *)eventDict{
    NSString *rtcToken = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"rtcToken" defaultValue:@""];
    self.callingSessionModel.rtcToken = rtcToken;
    //UI端开始执行声网的相关初始化
    //关闭呼叫超时
    [self _stopCallingTimeOut];
    //关闭铃声
    [self stopRing];
    //关闭悬浮框
    [self closeBeCallPopup];

    //标记为等待加入
    self.callingSessionModel.status = SCCallSessionStatusWaitJoin;
    //如果是快速匹配则需要捕获用户ID
    if(self.callingSessionModel.isMatch && self.callingSessionModel.toUserId.length <= 1){
        NSString *fromUserId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"fromUserId" defaultValue:@""];
        NSString *toUserId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"toUserId" defaultValue:@""];
        self.callingSessionModel.toUserId = fromUserId;
        self.callingSessionModel.fromUserId = toUserId;
    }
    if(self.delegate){
        [self.delegate onPickUpWithSession:self.callingSessionModel];
    }
    //开启等待加入倒计时
    [self _startJoinChannelTimeOut];
}

#pragma mark - 网络请求
///更新声网ID
+(void) requestUpdateWithAgoraUid:(NSString *)agoraUid success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCAPIServiceManager requestUpdateWithAgoraUid:agoraUid success:success failure:failure];
}

#pragma mark - SCVideoCallDelegate

///视频加入声网成功
- (void)videoCall:(SCVideoCallViewController *)vc joinChannelSucess:(NSString *)channelName uid:(NSUInteger)uid{
    kWeakSelf(self)
    [self doJoinChannelWithChannelName:channelName success:nil failure:nil];
    //更新融云
    [SCAPIServiceManager requestUpdateWithAgoraUid:[NSString stringWithFormat:@"%ld",uid] success:nil failure:nil];
}
- (void)videoCall:(SCVideoCallViewController *)vc tagJoinChannelSuccess:(NSString *)channelName uid:(NSUInteger)uid{
    [self _stopJoinChannelTimeOut];
    //标记为已经通话中
    self.callingSessionModel.status = SCCallSessionStatusOnCalled;
}

- (void)startVibrate {
    // 先停止之前的震动（如果有）
    [self stopVibrate];
    
    // 创建定时器，每1秒震动一次
    self.vibrateTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, dispatch_get_main_queue());
    dispatch_source_set_timer(self.vibrateTimer, DISPATCH_TIME_NOW, 1.0 * NSEC_PER_SEC, 0.1 * NSEC_PER_SEC);
    
    dispatch_source_set_event_handler(self.vibrateTimer, ^{
        // 在每次震动前检查应用状态
        if ([UIApplication sharedApplication].applicationState == UIApplicationStateBackground) {
            [self stopVibrate];
            return;
        }
        AudioServicesPlaySystemSound(kSystemSoundID_Vibrate);
    });
    
    dispatch_resume(self.vibrateTimer);
}

- (void)stopVibrate {
    if (self.vibrateTimer) {
        dispatch_source_cancel(self.vibrateTimer);
        self.vibrateTimer = nil;
    }
}

@end
