//
//  SCCategoryAPIManagerCall.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCCategoryAPIManagerCall.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerCall)



///创建频道
/**
 CONVERSATION = 1 //会话界面
 JSAPI = 2 //页端调用
 MATCH_RESULT = 3 //匹配结果页4POPULAR_WALL = 4 //主播/用户墙
 ANOTHER_ANCHOR = 5 //呼叫失败时推荐另一批
 DETAIL_PAGE = 6 //主播主页调用
 DETAIL_VIDEO_PAGE = 7 //主播视频详情页
 MISS_CALL_PAGE = 8 //miss call 弹框回拨
 CONNECTION_ERROR_CALL_BACK = 9 //用户网络不好弹框回拨
 MEDIA_WALL = 10 //视频流
 CALLS_PAGE = 11 //通话记录
 FAST_MATCH = 12 //快速匹配
 AUTO_CALL = 13 //auto call
 MULTIPLE = 14 //多人连线
 RECOMMEND_GODDESS = 15 //女神推荐
 */
+(void) requestCreatChannelWithToUserId:(NSString *)toUserId clientSessionId:(NSString *)clientSessionId callType:(NSString *)callType callSource:(NSString *)callSource success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"toUserId":toUserId,@"clientSessionId":clientSessionId,@"callType":callType,@"callSource":callSource,@"playSupportType":@"1",@"isFree":@"false",@"supportVideoSdks":@[@1]};


    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallCreateChannel method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        NSDictionary *result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        NSString *channelName = [SCDictionaryHelper stringFromDictionary:result forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:nil];
        if(!kSCIsStrEmpty(channelName)){
            kSCBlockExeNotNil(success,result);
        }else{
            if(failure){
                failure([[SCXErrorModel alloc] initWitMsg:@"Creat Channel Fail".translateString]);
            }
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}





/**
 NORMAL = 1; // 常规
 NO_COINS = 2; // 金币不足
 CL_EXCEPTION = 3; // 客户端异常
 CL_CALL_TIMEOUT = 4; // 呼叫超时
 CL_REMOTE_USER_LEFT = 5; // 对方退出
 SER_EXCEPTION = 6; // 服务端错误
 NET_EXCEPTION = 7; // 网络错误
 SER_CONNECT_TIMEOUT = 8;//服务器强制结
 */
+(void) requestCallHangUpWithToUserId:(NSString *)oppositeUserId channelName:(NSString *)channelName handUpReason:(NSString *)handUpReason success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"oppositeUserId":oppositeUserId,@"channelName":channelName,@"handUpReason":handUpReason};
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallHangUp method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Hang Up Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///接听
+(void) requestCallPickUpChannelName:(NSString *)channelName success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"channelName":channelName};
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallPickUp method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Hang Up Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///加入频道
+(void) requestJoinWithChannelName:(NSString *)channelName success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"channelName":channelName};
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallJoinChannel method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Join Channel Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///更新声网ID
+(void) requestUpdateWithAgoraUid:(NSString *)agoraUid success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"agoraUid":agoraUid};
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallUpdateAgoraUid method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Update Agora Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取后置摄像头配置
+(void) requestRearCameraConfigWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull cameraConfigDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    //先返回缓存然后再返回最新结果
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallRealCameraConfig method:SCNetMethodPOST parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSDictionary *cameraConfigDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(cameraConfigDict){
            kSCBlockExeNotNil(success,cameraConfigDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get Camera Fail".translateString]);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///打开后置摄像头
+(void) requestOpenRearCameraWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallOpenRealCamera method:SCNetMethodPOST parameters:@{} headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Open Front Camera Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///请求视频呼叫结果
+(void) requestCallResultWithChannelName:(NSString *)channelName success:(void (^_Nullable)(NSDictionary * _Nullable resultDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallGetCallResult method:SCNetMethodPOST parameters:@{@"channelName":channelName} headers:nil cachePolicy:SCNetCachePolicyOnlyCache success:^(id responseObject) {
        NSDictionary *resultDict = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(resultDict){
            kSCBlockExeNotNil(success,resultDict);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Get Video Call Result Fail".translateString]);
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///提交主播评价
+(void) requestAnchorEvaluateSubmitWithChannelName:(NSString *)channelName isGood:(BOOL)isGood tags:(NSArray *)tags  success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPICallAnchorEvaluate method:SCNetMethodPOST parameters:@{@"channelName":channelName,@"score":isGood ? @(1):@(0),@"tags":tags} headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Commit Fail".translateString]);
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


///快速匹配
+(void) requestFlashMatchWithBatchId:(NSString *)batchId clientSessionId:(NSString *)clientSessionId success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    NSDictionary *parameters = @{@"clientSessionId":clientSessionId,@"batchId":batchId,@"callSource":@(12),@"supportVideoSdks":@[@1]};


    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIFlashMatch method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        NSDictionary *result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        NSString *channelName = [SCDictionaryHelper stringFromDictionary:result forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:nil];
        if(!kSCIsStrEmpty(channelName)){
            kSCBlockExeNotNil(success,result);
        }else{
            if(failure){
                failure([[SCXErrorModel alloc] initWitMsg:@"Flash Match Fail".translateString]);
            }
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///取消快速匹配
+(void) requestCancelFlashMatchWithClientSessionId:(NSString *)clientSessionId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    if (!clientSessionId) {
        return;
    }
    NSDictionary *parameters = @{@"clientSessionId":clientSessionId};
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIFlashMatchCancel method:SCNetMethodPOST parameters:parameters headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSNumber class]] && [responseObject boolValue]){
            kSCBlockExeNotNil(success);
        }else{
            kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@"Cancel Flash Match Fail".translateString]);
        }
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

@end
