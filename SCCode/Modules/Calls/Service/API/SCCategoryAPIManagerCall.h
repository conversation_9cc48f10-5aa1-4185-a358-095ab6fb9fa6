//
//  SCCategoryAPIManagerCall.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN


@interface SCAPIServiceManager (SCCategoryAPIManagerCall)
///创建频道
+(void) requestCreatChannelWithToUserId:(NSString *)toUserId clientSessionId:(NSString *)clientSessionId callType:(NSString *)callType callSource:(NSString *)callSource   success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///挂断
+(void) requestCallHangUpWithToUserId:(NSString *)oppositeUserId channelName:(NSString *)channelName handUpReason:(NSString *)handUpReason success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///接听
+(void) requestCallPickUpChannelName:(NSString *)channelName success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///加入频道
+(void) requestJoinWithChannelName:(NSString *)channelName success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///更新声网ID
+(void) requestUpdateWithAgoraUid:(NSString *)agoraUid success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取后置摄像头配置
+(void) requestRearCameraConfigWithSuccess:(void (^_Nullable)(NSDictionary * _Nonnull cameraConfigDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///打开后置摄像头
+(void) requestOpenRearCameraWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///请求视频呼叫结果
+(void) requestCallResultWithChannelName:(NSString *)channelName success:(void (^_Nullable)(NSDictionary * _Nullable resultDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///提交主播评价
+(void) requestAnchorEvaluateSubmitWithChannelName:(NSString *)channelName isGood:(BOOL)isGood tags:(NSArray *)tags  success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///快速匹配
+(void) requestFlashMatchWithBatchId:(NSString *)batchId clientSessionId:(NSString *)clientSessionId success:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///取消快速匹配
+(void) requestCancelFlashMatchWithClientSessionId:(NSString *)clientSessionId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
