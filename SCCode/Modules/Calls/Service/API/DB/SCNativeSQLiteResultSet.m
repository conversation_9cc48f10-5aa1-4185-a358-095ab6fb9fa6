//
//  SCNativeSQLiteResultSet.m
//  Supercall
//
//  Created by AI Assistant on 2025/7/9.
//  原生SQLite结果集包装类，用于替代FMDB的FMResultSet
//

#import "SCNativeSQLiteResultSet.h"

@interface SCNativeSQLiteResultSet ()

@property (nonatomic, assign) sqlite3_stmt *statement;
@property (nonatomic, assign) BOOL hasRow;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *columnNameToIndexMap;

@end

@implementation SCNativeSQLiteResultSet

- (instancetype)initWithStatement:(sqlite3_stmt *)stmt {
    self = [super init];
    if (self) {
        _statement = stmt;
        _hasRow = NO;
        _columnNameToIndexMap = [NSMutableDictionary dictionary];
        
        // 构建列名到索引的映射
        [self buildColumnNameToIndexMap];
    }
    return self;
}

- (void)dealloc {
    [self close];
}

- (void)buildColumnNameToIndexMap {
    if (!_statement) return;
    
    int columnCount = sqlite3_column_count(_statement);
    for (int i = 0; i < columnCount; i++) {
        const char *columnName = sqlite3_column_name(_statement, i);
        if (columnName) {
            NSString *name = [NSString stringWithUTF8String:columnName];
            _columnNameToIndexMap[name] = @(i);
        }
    }
}

- (BOOL)next {
    if (!_statement) {
        _hasRow = NO;
        return NO;
    }
    
    int result = sqlite3_step(_statement);
    _hasRow = (result == SQLITE_ROW);
    return _hasRow;
}

- (void)close {
    if (_statement) {
        sqlite3_finalize(_statement);
        _statement = NULL;
    }
    _hasRow = NO;
    [_columnNameToIndexMap removeAllObjects];
}

- (int)columnIndexForName:(NSString *)columnName {
    NSNumber *index = _columnNameToIndexMap[columnName];
    return index ? [index intValue] : -1;
}

- (nullable NSString *)stringForColumn:(NSString *)columnName {
    int columnIndex = [self columnIndexForName:columnName];
    if (columnIndex == -1) return nil;
    return [self stringForColumnIndex:columnIndex];
}

- (int)intForColumn:(NSString *)columnName {
    int columnIndex = [self columnIndexForName:columnName];
    if (columnIndex == -1) return 0;
    return [self intForColumnIndex:columnIndex];
}

- (BOOL)boolForColumn:(NSString *)columnName {
    int columnIndex = [self columnIndexForName:columnName];
    if (columnIndex == -1) return NO;
    return [self boolForColumnIndex:columnIndex];
}

- (double)doubleForColumn:(NSString *)columnName {
    int columnIndex = [self columnIndexForName:columnName];
    if (columnIndex == -1) return 0.0;
    return [self doubleForColumnIndex:columnIndex];
}

- (long long)longLongIntForColumn:(NSString *)columnName {
    int columnIndex = [self columnIndexForName:columnName];
    if (columnIndex == -1) return 0;
    
    if (!_statement || !_hasRow) return 0;
    return sqlite3_column_int64(_statement, columnIndex);
}

- (nullable NSString *)stringForColumnIndex:(int)columnIdx {
    if (!_statement || !_hasRow) return nil;
    
    const char *text = (const char *)sqlite3_column_text(_statement, columnIdx);
    if (text == NULL) return nil;
    
    return [NSString stringWithUTF8String:text];
}

- (int)intForColumnIndex:(int)columnIdx {
    if (!_statement || !_hasRow) return 0;
    return sqlite3_column_int(_statement, columnIdx);
}

- (BOOL)boolForColumnIndex:(int)columnIdx {
    if (!_statement || !_hasRow) return NO;
    return sqlite3_column_int(_statement, columnIdx) != 0;
}

- (double)doubleForColumnIndex:(int)columnIdx {
    if (!_statement || !_hasRow) return 0.0;
    return sqlite3_column_double(_statement, columnIdx);
}

- (int)columnCount {
    if (!_statement) return 0;
    return sqlite3_column_count(_statement);
}

- (nullable NSString *)columnNameForIndex:(int)columnIdx {
    if (!_statement) return nil;
    
    const char *columnName = sqlite3_column_name(_statement, columnIdx);
    if (columnName == NULL) return nil;
    
    return [NSString stringWithUTF8String:columnName];
}

@end
