//
//  SCNativeSQLiteDatabase.h
//  Supercall
//
//  Created by AI Assistant on 2025/7/9.
//  原生SQLite数据库包装类，用于替代FMDB的FMDatabase
//

#import <Foundation/Foundation.h>
#import <sqlite3.h>

@class SCNativeSQLiteResultSet;

NS_ASSUME_NONNULL_BEGIN

/**
 * 原生SQLite数据库包装类
 * 提供与FMDatabase兼容的接口，但使用原生SQLite实现
 */
@interface SCNativeSQLiteDatabase : NSObject

/**
 * 使用数据库路径创建数据库实例
 * @param path 数据库文件路径
 * @return 数据库实例
 */
+ (instancetype)databaseWithPath:(NSString *)path;

/**
 * 使用数据库路径初始化数据库实例
 * @param path 数据库文件路径
 * @return 初始化的数据库实例
 */
- (instancetype)initWithPath:(NSString *)path;

/**
 * 打开数据库连接
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)open;

/**
 * 关闭数据库连接
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)close;

/**
 * 检查数据库是否已打开
 * @return 已打开返回YES，否则返回NO
 */
- (BOOL)isOpen;

/**
 * 执行更新操作（INSERT、UPDATE、DELETE等）
 * @param sql SQL语句
 * @param ... 参数列表
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)executeUpdate:(NSString *)sql, ...;

/**
 * 执行更新操作（INSERT、UPDATE、DELETE等）
 * @param sql SQL语句
 * @param arguments 参数数组
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)executeUpdate:(NSString *)sql withArgumentsInArray:(nullable NSArray *)arguments;

/**
 * 执行查询操作
 * @param sql SQL语句
 * @param ... 参数列表
 * @return 结果集，如果失败返回nil
 */
- (nullable SCNativeSQLiteResultSet *)executeQuery:(NSString *)sql, ...;

/**
 * 执行查询操作
 * @param sql SQL语句
 * @param arguments 参数数组
 * @return 结果集，如果失败返回nil
 */
- (nullable SCNativeSQLiteResultSet *)executeQuery:(NSString *)sql withArgumentsInArray:(nullable NSArray *)arguments;

/**
 * 获取最后一次操作的错误信息
 * @return 错误信息字符串
 */
- (nullable NSString *)lastErrorMessage;

/**
 * 获取最后一次操作的错误代码
 * @return 错误代码
 */
- (int)lastErrorCode;

/**
 * 获取最后插入行的ID
 * @return 最后插入行的ID
 */
- (sqlite3_int64)lastInsertRowId;

/**
 * 获取最后一次操作影响的行数
 * @return 影响的行数
 */
- (int)changes;

/**
 * 开始事务
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)beginTransaction;

/**
 * 提交事务
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)commit;

/**
 * 回滚事务
 * @return 成功返回YES，失败返回NO
 */
- (BOOL)rollback;

/**
 * 检查表是否存在
 * @param tableName 表名
 * @return 存在返回YES，否则返回NO
 */
- (BOOL)tableExists:(NSString *)tableName;

/**
 * 获取数据库路径
 * @return 数据库文件路径
 */
@property (nonatomic, readonly, copy) NSString *databasePath;

@end

NS_ASSUME_NONNULL_END
