//
//  SCNativeSQLiteResultSet.h
//  Supercall
//
//  Created by AI Assistant on 2025/7/9.
//  原生SQLite结果集包装类，用于替代FMDB的FMResultSet
//

#import <Foundation/Foundation.h>
#import <sqlite3.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 原生SQLite结果集包装类
 * 提供与FMResultSet兼容的接口，但使用原生SQLite实现
 */
@interface SCNativeSQLiteResultSet : NSObject

/**
 * 使用sqlite3_stmt初始化结果集
 * @param stmt 已准备好的SQLite语句
 * @return 初始化的结果集实例
 */
- (instancetype)initWithStatement:(sqlite3_stmt *)stmt;

/**
 * 移动到下一行数据
 * @return 如果有下一行数据返回YES，否则返回NO
 */
- (BOOL)next;

/**
 * 关闭结果集并释放资源
 */
- (void)close;

/**
 * 根据列名获取字符串值
 * @param columnName 列名
 * @return 字符串值，如果为NULL则返回nil
 */
- (nullable NSString *)stringForColumn:(NSString *)columnName;

/**
 * 根据列名获取整数值
 * @param columnName 列名
 * @return 整数值
 */
- (int)intForColumn:(NSString *)columnName;

/**
 * 根据列名获取布尔值
 * @param columnName 列名
 * @return 布尔值
 */
- (BOOL)boolForColumn:(NSString *)columnName;

/**
 * 根据列名获取双精度浮点数值
 * @param columnName 列名
 * @return 双精度浮点数值
 */
- (double)doubleForColumn:(NSString *)columnName;

/**
 * 根据列名获取长整数值
 * @param columnName 列名
 * @return 长整数值
 */
- (long long)longLongIntForColumn:(NSString *)columnName;

/**
 * 根据列索引获取字符串值
 * @param columnIdx 列索引（从0开始）
 * @return 字符串值，如果为NULL则返回nil
 */
- (nullable NSString *)stringForColumnIndex:(int)columnIdx;

/**
 * 根据列索引获取整数值
 * @param columnIdx 列索引（从0开始）
 * @return 整数值
 */
- (int)intForColumnIndex:(int)columnIdx;

/**
 * 根据列索引获取布尔值
 * @param columnIdx 列索引（从0开始）
 * @return 布尔值
 */
- (BOOL)boolForColumnIndex:(int)columnIdx;

/**
 * 根据列索引获取双精度浮点数值
 * @param columnIdx 列索引（从0开始）
 * @return 双精度浮点数值
 */
- (double)doubleForColumnIndex:(int)columnIdx;

/**
 * 获取列数
 * @return 结果集中的列数
 */
- (int)columnCount;

/**
 * 根据列名获取列索引
 * @param columnName 列名
 * @return 列索引，如果列不存在返回-1
 */
- (int)columnIndexForName:(NSString *)columnName;

/**
 * 根据列索引获取列名
 * @param columnIdx 列索引
 * @return 列名
 */
- (nullable NSString *)columnNameForIndex:(int)columnIdx;

@end

NS_ASSUME_NONNULL_END
