//
//  SCCallDBManager.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/2.
//

#import "SCCallDBManager.h"
#import "SCNativeSQLiteDatabase.h"
#import "SCNativeSQLiteResultSet.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCCallDBManager()

@property (nonatomic, strong) SCNativeSQLiteDatabase *database;



@end

@implementation SCCallDBManager


+ (instancetype)sharedManager {
    static SCCallDBManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[SCCallDBManager alloc] init];
    });
    return manager;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _callSessionChangeObs = [[SCObservable<SCDBActionModel<SCCallSessionModel *> *> alloc] initWithValue:nil];
        _callSessionDictChangeObs = [[SCObservable<SCDBActionModel<NSDictionary *> *> alloc] initWithValue:nil];
        NSString *dbPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        dbPath = [dbPath stringByAppendingPathComponent:@"sc_call_session2.db"];
        self.database = [SCNativeSQLiteDatabase databaseWithPath:dbPath];
        if (![self.database open]) {
            
        } else {
            [self createCallSessionTable];
        }
    }
    return self;
}

#pragma mark - Private methods

- (void)createCallSessionTable {
    
    NSString *createTableSQL1 = @"CREATE TABLE IF NOT EXISTS SC_CALL_SESSIONS";
    NSString *createTableSQL2 = @" (";
//    NSString *createTableSQL3 = @"sessionId TEXT PRIMARY KEY, status INTEGER, isFree INTEGER, callFreeSeconds INTEGER, fromUserId TEXT, toUserId TEXT, rtcToken TEXT, clientSessionId TEXT, chooseVideoSdk INTEGER, isGreenMode INTEGER, channelName TEXT, broadcasterUnitPrice INTEGER, hangUpReason INTEGER, endType INTEGER,callDuration INTEGER,creatAt REAL";
    NSArray *createTableSQL3Array = @[
        @"sessionId TEXT PRIMARY KEY",
        @"status INTEGER",
        @"isFree INTEGER",
        @"callFreeSeconds INTEGER",
        @"fromUserId TEXT",
        @"toUserId TEXT",
        @"rtcToken TEXT",
        @"clientSessionId TEXT",
        @"channelName TEXT",
        @"broadcasterUnitPrice INTEGER",
        @"hangUpReason INTEGER",
        @"endType INTEGER",
        @"callDuration INTEGER",
        @"creatAt REAL"
    ];
    NSString *createTableSQL3 = @"";
    for (NSInteger i = 0; i < createTableSQL3Array.count; i ++) {
        if (i == (createTableSQL3Array.count - 1)) {
            createTableSQL3 = [NSString stringWithFormat:@"%@%@",createTableSQL3,createTableSQL3Array[i]];
        }else {
            createTableSQL3 = [NSString stringWithFormat:@"%@%@,",createTableSQL3,createTableSQL3Array[i]];
        }
    }
    
//    NSString *createTableSQL3 = @" status INTEGER, isFree INTEGER, callFreeSeconds INTEGER, fromUserId TEXT, toUserId TEXT, rtcToken TEXT, clientSessionId TEXT, chooseVideoSdk INTEGER, isGreenMode INTEGER, channelName TEXT, broadcasterUnitPrice INTEGER, hangUpReason INTEGER, endType INTEGER,callDuration INTEGER,creatAt REAL";
    NSString *createTableSQL4 = @")";
    
    NSString *createTableSQL = [NSString stringWithFormat:@"%@%@%@%@",createTableSQL1,createTableSQL2,createTableSQL3,createTableSQL4];
    BOOL success = [self.database executeUpdate:createTableSQL];
    if (!success) {
        
    }
}

#pragma mark - Public methods

- (BOOL)insertCallSession:(SCCallSessionModel *)callSession {
    if (callSession.clientSessionId == nil) {
        return  NO;
    }
    NSString *insertSQL1 = @"INSERT INTO SC_CALL_SESSIONS";
    NSString *insertSQL2 = @" (";
    NSString *insertSQL3 = @"sessionId, status, isFree, callFreeSeconds, fromUserId, toUserId, rtcToken, clientSessionId, channelName, broadcasterUnitPrice, hangUpReason,endType,callDuration,creatAt";
    NSString *insertSQL4 = @") ";
    NSString *insertSQL5 = @"VALUES";
    NSString *insertSQL6 = @" (";
    NSString *insertSQL7 = @"?, ?, ?, ?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?";
    NSString *insertSQL8 = @")";
    
    NSString *insertSQL = [NSString stringWithFormat:@"%@%@%@%@%@%@%@%@",insertSQL1,insertSQL2,insertSQL3,insertSQL4,insertSQL5,insertSQL6,insertSQL7,insertSQL8];
    if (!callSession.fromUserId || !callSession.toUserId) {
        return NO;
    }
    BOOL success = [self.database executeUpdate:insertSQL, callSession.clientSessionId, @(callSession.status), @(callSession.isFree), @(callSession.callFreeSeconds), callSession.fromUserId, callSession.toUserId, callSession.rtcToken, callSession.clientSessionId, callSession.channelName, @(callSession.broadcasterUnitPrice),@(callSession.hangUpReason),@(callSession.endType),@(callSession.callDuration),@(callSession.creatAt)];
    self.callSessionChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeAdd value:callSession];
    return success;
}

- (BOOL)saveCallSession:(SCCallSessionModel *)callSession {
    
    SCCallSessionModel * oldCallSession = [self getCallSessionWithSessionId:callSession.clientSessionId];
    if(oldCallSession != nil){
        //有数据则修改
//        NSString *updateSQL = @"UPDATE SC_CALL_SESSIONS SET status = ?, isFree = ?, callFreeSeconds = ?, fromUserId = ?, toUserId = ?, rtcToken = ?, clientSessionId = ?, chooseVideoSdk = ?, isGreenMode = ?, channelName = ?, broadcasterUnitPrice = ?, hangUpReason = ?, endType = ?, callDuration = ?, creatAt = ? WHERE sessionId = ?";
        NSString *updateSQL = @"UPDATE SC_CALL_SESSIONS SET ";
        NSString *sql_connect = @"?";
        NSArray *array = @[@"status",
                           @"isFree",
                           @"callFreeSeconds",
                           @"fromUserId",
                           @"toUserId",
                           @"rtcToken",
                           @"clientSessionId",
                           @"channelName",
                           @"broadcasterUnitPrice",
                           @"hangUpReason",
                           @"endType",
                           @"callDuration",
                           @"creatAt",
                           @"WHERE sessionId"];
        for (NSInteger i = 0;i < array.count; i ++) {
            
            if (i == (array.count - 1)) {
                updateSQL = [NSString stringWithFormat:@"%@%@ = %@",updateSQL,array[i],sql_connect];
            }else {
                updateSQL = [NSString stringWithFormat:@"%@%@ = %@",updateSQL,array[i],sql_connect];
                if (i != (array.count - 2)) {
                    updateSQL = [NSString stringWithFormat:@"%@,",updateSQL];
                }else {
                    updateSQL = [NSString stringWithFormat:@"%@ ",updateSQL];
                }
            }
            
        }
        
        
        BOOL success = [self.database executeUpdate:updateSQL, @(callSession.status), @(callSession.isFree), @(callSession.callFreeSeconds), callSession.fromUserId, callSession.toUserId, callSession.rtcToken, callSession.clientSessionId, callSession.channelName, @(callSession.broadcasterUnitPrice),@(callSession.hangUpReason),@(callSession.endType),@(callSession.callDuration),@(callSession.creatAt), callSession.clientSessionId];
        self.callSessionChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeChange value:callSession];
        return success;
    }else{
        //没有数据则插入
        return [self insertCallSession:callSession];
    }
    
    
}

- (BOOL)deleteCallSession:(SCCallSessionModel *)callSession {
    NSString *deleteSQL = @"DELETE FROM SC_CALL_SESSIONS WHERE sessionId = ?";
    BOOL success = [self.database executeUpdate:deleteSQL, callSession.clientSessionId];
    self.callSessionChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeDelete value:callSession];
    return success;
}
- (BOOL) truncateDB {
    NSString *deleteSQL = @"DELETE FROM SC_CALL_SESSIONS";
    BOOL success = [self.database executeUpdate:deleteSQL];
    return success;
}


- (SCCallSessionModel *)getCallSessionWithSessionId:(NSString *)sessionId {
    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS WHERE sessionId = ?";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:@[sessionId]];
    SCCallSessionModel *callSession = nil;
    if ([resultSet next]) {
        callSession = [SCCallSessionModel sessionWithNativeSQLiteResultSet:resultSet];
    }
    [resultSet close];
    return callSession;
}


- (NSArray<SCCallSessionModel *> *)getAllCallSessions {
    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS ORDER BY creatAt DESC";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:nil];
    NSMutableArray<SCCallSessionModel *> *callSessions = [NSMutableArray array];
    while ([resultSet next]) {
        SCCallSessionModel *callSession = [SCCallSessionModel sessionWithNativeSQLiteResultSet:resultSet];
        [callSessions addObject:callSession];
    }
    [resultSet close];
    return callSessions;
}

- (NSArray<SCCallSessionModel *> *)getCallSessionsWithPage:(NSInteger)page pageSize:(NSInteger)pageSize {
    NSInteger offset = (page - 1) * pageSize;
    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS ORDER BY creatAt DESC LIMIT ? OFFSET ?";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:@[@(pageSize), @(offset)]];
    NSMutableArray<SCCallSessionModel *> *callSessions = [NSMutableArray array];
    while ([resultSet next]) {
        SCCallSessionModel *callSession = [SCCallSessionModel sessionWithNativeSQLiteResultSet:resultSet];
        [callSessions addObject:callSession];
    }
    [resultSet close];
    return callSessions;
}

#pragma mark - 字典支持方法

// Model转字典的辅助方法
- (NSDictionary *)_dictionaryFromCallSession:(SCCallSessionModel *)callSession {
    if (!callSession) {
        return nil;
    }

    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];

    // 基本属性
    dict[SCDictionaryKeys.shared.kSCCallSessionIdKey] = callSession.clientSessionId ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallStatusKey] = @(callSession.status);
    dict[SCDictionaryKeys.shared.kSCCallIsFreeKey] = @(callSession.isFree);
    dict[SCDictionaryKeys.shared.kSCCallFreeSecondsKey] = @(callSession.callFreeSeconds);
    dict[SCDictionaryKeys.shared.kSCCallFromUserIdKey] = callSession.fromUserId ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallToUserIdKey] = callSession.toUserId ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallRtcTokenKey] = callSession.rtcToken ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallChannelNameKey] = callSession.channelName ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey] = @(callSession.chooseVideoSdk);
    dict[SCDictionaryKeys.shared.kSCCallIsGreenModeKey] = @(callSession.isGreenMode);
    dict[SCDictionaryKeys.shared.kSCCallUnitPriceKey] = @(callSession.broadcasterUnitPrice);
    dict[SCDictionaryKeys.shared.kSCCallHangUpReasonKey] = @(callSession.hangUpReason);
    dict[SCDictionaryKeys.shared.kSCCallEndTypeKey] = @(callSession.endType);
    dict[SCDictionaryKeys.shared.kSCCallDurationKey] = @(callSession.callDuration);
    dict[SCDictionaryKeys.shared.kSCCallCreatAtKey] = @(callSession.creatAt);

    return [dict copy];
}

// 字典转Model的辅助方法
- (SCCallSessionModel *)_callSessionFromDictionary:(NSDictionary *)dict {
    if (!dict) {
        return nil;
    }

    SCCallSessionModel *callSession = [[SCCallSessionModel alloc] init];

    // 基本属性
    callSession.clientSessionId = [SCDictionaryHelper stringFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallSessionIdKey defaultValue:@""];
    callSession.status = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallStatusKey defaultValue:0];
    callSession.isFree = [SCDictionaryHelper boolFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallIsFreeKey defaultValue:NO];
    callSession.callFreeSeconds = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0];
    callSession.fromUserId = [SCDictionaryHelper stringFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:@""];
    callSession.toUserId = [SCDictionaryHelper stringFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:@""];
    callSession.rtcToken = [SCDictionaryHelper stringFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""];
    callSession.channelName = [SCDictionaryHelper stringFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallChannelNameKey defaultValue:@""];
    callSession.chooseVideoSdk = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:0];
    callSession.isGreenMode = [SCDictionaryHelper boolFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO];
    callSession.broadcasterUnitPrice = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallUnitPriceKey defaultValue:0];
    callSession.hangUpReason = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallHangUpReasonKey defaultValue:0];
    callSession.endType = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallEndTypeKey defaultValue:0];
    callSession.callDuration = [SCDictionaryHelper integerFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallDurationKey defaultValue:0];
    callSession.creatAt = [SCDictionaryHelper doubleFromDictionary:dict forKey:SCDictionaryKeys.shared.kSCCallCreatAtKey defaultValue:0.0];

    return callSession;
}

// ResultSet转字典的辅助方法
- (NSDictionary *)_dictionaryFromResultSet:(SCNativeSQLiteResultSet *)resultSet {
    if (!resultSet) {
        return nil;
    }

    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];

    // 从ResultSet中读取数据
    dict[SCDictionaryKeys.shared.kSCCallSessionIdKey] = [resultSet stringForColumn:@"sessionId"] ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallStatusKey] = @([resultSet intForColumn:@"status"]);
    dict[SCDictionaryKeys.shared.kSCCallIsFreeKey] = @([resultSet boolForColumn:@"isFree"]);
    dict[SCDictionaryKeys.shared.kSCCallFreeSecondsKey] = @([resultSet intForColumn:@"callFreeSeconds"]);
    dict[SCDictionaryKeys.shared.kSCCallFromUserIdKey] = [resultSet stringForColumn:@"fromUserId"] ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallToUserIdKey] = [resultSet stringForColumn:@"toUserId"] ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallRtcTokenKey] = [resultSet stringForColumn:@"rtcToken"] ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallChannelNameKey] = [resultSet stringForColumn:@"channelName"] ?: @"";
    dict[SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey] = @([resultSet intForColumn:@"chooseVideoSdk"]);
    dict[SCDictionaryKeys.shared.kSCCallIsGreenModeKey] = @([resultSet boolForColumn:@"isGreenMode"]);
    dict[SCDictionaryKeys.shared.kSCCallUnitPriceKey] = @([resultSet intForColumn:@"broadcasterUnitPrice"]);
    dict[SCDictionaryKeys.shared.kSCCallHangUpReasonKey] = @([resultSet intForColumn:@"hangUpReason"]);
    dict[SCDictionaryKeys.shared.kSCCallEndTypeKey] = @([resultSet intForColumn:@"endType"]);
    dict[SCDictionaryKeys.shared.kSCCallDurationKey] = @([resultSet intForColumn:@"callDuration"]);
    dict[SCDictionaryKeys.shared.kSCCallCreatAtKey] = @([resultSet doubleForColumn:@"creatAt"]);

    return [dict copy];
}

- (BOOL)insertCallSessionDict:(NSDictionary *)callSessionDict {
    if (!callSessionDict) {
        return NO;
    }

    NSString *sessionId = [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallSessionIdKey defaultValue:nil];
    if (!sessionId || sessionId.length == 0) {
        return NO;
    }

    NSString *fromUserId = [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:nil];
    NSString *toUserId = [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:nil];
    if (!fromUserId || !toUserId) {
        return NO;
    }

    NSString *insertSQL = @"INSERT INTO SC_CALL_SESSIONS (sessionId, status, isFree, callFreeSeconds, fromUserId, toUserId, rtcToken, clientSessionId, chooseVideoSdk, isGreenMode, channelName, broadcasterUnitPrice, hangUpReason, endType, callDuration, creatAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    BOOL success = [self.database executeUpdate:insertSQL,
                    sessionId,
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallStatusKey defaultValue:0]),
                    @([SCDictionaryHelper boolFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallIsFreeKey defaultValue:NO]),
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0]),
                    fromUserId,
                    toUserId,
                    [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""],
                    sessionId, // clientSessionId 与 sessionId 相同
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:0]),
                    @([SCDictionaryHelper boolFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO]),
                    [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallChannelNameKey defaultValue:@""],
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallUnitPriceKey defaultValue:0]),
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallHangUpReasonKey defaultValue:0]),
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallEndTypeKey defaultValue:0]),
                    @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallDurationKey defaultValue:0]),
                    @([SCDictionaryHelper doubleFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallCreatAtKey defaultValue:0.0])];

    if (success) {
        self.callSessionDictChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeAdd value:callSessionDict];
    }

    return success;
}

- (BOOL)saveCallSessionDict:(NSDictionary *)callSessionDict {
    if (!callSessionDict) {
        return NO;
    }

    NSString *sessionId = [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallSessionIdKey defaultValue:nil];
    if (!sessionId || sessionId.length == 0) {
        return NO;
    }

    // 检查是否已存在
    NSDictionary *oldCallSessionDict = [self getCallSessionDictWithSessionId:sessionId];
    if (oldCallSessionDict) {
        // 有数据则修改
        NSString *updateSQL = @"UPDATE SC_CALL_SESSIONS SET status = ?, isFree = ?, callFreeSeconds = ?, fromUserId = ?, toUserId = ?, rtcToken = ?, clientSessionId = ?, chooseVideoSdk = ?, isGreenMode = ?, channelName = ?, broadcasterUnitPrice = ?, hangUpReason = ?, endType = ?, callDuration = ?, creatAt = ? WHERE sessionId = ?";

        BOOL success = [self.database executeUpdate:updateSQL,
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallStatusKey defaultValue:0]),
                        @([SCDictionaryHelper boolFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallIsFreeKey defaultValue:NO]),
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0]),
                        [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:@""],
                        [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:@""],
                        [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""],
                        sessionId, // clientSessionId
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:0]),
                        @([SCDictionaryHelper boolFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO]),
                        [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallChannelNameKey defaultValue:@""],
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallUnitPriceKey defaultValue:0]),
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallHangUpReasonKey defaultValue:0]),
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallEndTypeKey defaultValue:0]),
                        @([SCDictionaryHelper integerFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallDurationKey defaultValue:0]),
                        @([SCDictionaryHelper doubleFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallCreatAtKey defaultValue:0.0]),
                        sessionId];

        if (success) {
            self.callSessionDictChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeChange value:callSessionDict];
        }

        return success;
    } else {
        // 没有数据则插入
        return [self insertCallSessionDict:callSessionDict];
    }
}

- (BOOL)deleteCallSessionDict:(NSDictionary *)callSessionDict {
    if (!callSessionDict) {
        return NO;
    }

    NSString *sessionId = [SCDictionaryHelper stringFromDictionary:callSessionDict forKey:SCDictionaryKeys.shared.kSCCallSessionIdKey defaultValue:nil];
    if (!sessionId || sessionId.length == 0) {
        return NO;
    }

    NSString *deleteSQL = @"DELETE FROM SC_CALL_SESSIONS WHERE sessionId = ?";
    BOOL success = [self.database executeUpdate:deleteSQL, sessionId];

    if (success) {
        self.callSessionDictChangeObs.value = [[SCDBActionModel alloc] initWithAction:SCDBActionModelTypeDelete value:callSessionDict];
    }

    return success;
}

- (NSDictionary *)getCallSessionDictWithSessionId:(NSString *)sessionId {
    if (!sessionId || sessionId.length == 0) {
        return nil;
    }

    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS WHERE sessionId = ?";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:@[sessionId]];
    NSDictionary *callSessionDict = nil;
    if ([resultSet next]) {
        callSessionDict = [self _dictionaryFromResultSet:resultSet];
    }
    [resultSet close];
    return callSessionDict;
}

- (NSArray<NSDictionary *> *)getAllCallSessionDicts {
    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS ORDER BY creatAt DESC";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:nil];
    NSMutableArray<NSDictionary *> *callSessionDicts = [NSMutableArray array];
    while ([resultSet next]) {
        NSDictionary *callSessionDict = [self _dictionaryFromResultSet:resultSet];
        if (callSessionDict) {
            [callSessionDicts addObject:callSessionDict];
        }
    }
    [resultSet close];
    return callSessionDicts;
}

- (NSArray<NSDictionary *> *)getCallSessionDictsWithPage:(NSInteger)page pageSize:(NSInteger)pageSize {
    NSInteger offset = (page - 1) * pageSize;
    NSString *querySQL = @"SELECT * FROM SC_CALL_SESSIONS ORDER BY creatAt DESC LIMIT ? OFFSET ?";
    SCNativeSQLiteResultSet *resultSet = [self.database executeQuery:querySQL withArgumentsInArray:@[@(pageSize), @(offset)]];
    NSMutableArray<NSDictionary *> *callSessionDicts = [NSMutableArray array];
    while ([resultSet next]) {
        NSDictionary *callSessionDict = [self _dictionaryFromResultSet:resultSet];
        if (callSessionDict) {
            [callSessionDicts addObject:callSessionDict];
        }
    }
    [resultSet close];
    return callSessionDicts;
}

@end
