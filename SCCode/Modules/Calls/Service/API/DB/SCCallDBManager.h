//
//  SCCallDBManager.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/2.
//

#import <Foundation/Foundation.h>
#import "SCCallSessionModel.h"
#import "SCDBActionModel.h"
#import "SCNativeSQLiteDatabase.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCCallDBManager : NSObject
+ (instancetype)sharedManager;

@property(nonnull,strong) SCObservable<SCDBActionModel<SCCallSessionModel *> *> * callSessionChangeObs;
@property(nonnull,strong) SCObservable<SCDBActionModel<NSDictionary *> *> * callSessionDictChangeObs;

- (BOOL)insertCallSession:(SCCallSessionModel *)callSession;
- (BOOL)saveCallSession:(SCCallSessionModel *)callSession;
- (BOOL)deleteCallSession:(SCCallSessionModel *)callSession;
//截断表
- (BOOL) truncateDB;
- (SCCallSessionModel *)getCallSessionWithSessionId:(NSString *)sessionId;
- (NSArray<SCCallSessionModel *> *)getAllCallSessions;
- (NSArray<SCCallSessionModel *> *)getCallSessionsWithPage:(NSInteger)page pageSize:(NSInteger)pageSize;

// 字典支持方法
- (BOOL)insertCallSessionDict:(NSDictionary *)callSessionDict;
- (BOOL)saveCallSessionDict:(NSDictionary *)callSessionDict;
- (BOOL)deleteCallSessionDict:(NSDictionary *)callSessionDict;
- (NSDictionary *)getCallSessionDictWithSessionId:(NSString *)sessionId;
- (NSArray<NSDictionary *> *)getAllCallSessionDicts;
- (NSArray<NSDictionary *> *)getCallSessionDictsWithPage:(NSInteger)page pageSize:(NSInteger)pageSize;
- (NSDictionary *)_dictionaryFromCallSession:(SCCallSessionModel *)callSession;



@end

NS_ASSUME_NONNULL_END
