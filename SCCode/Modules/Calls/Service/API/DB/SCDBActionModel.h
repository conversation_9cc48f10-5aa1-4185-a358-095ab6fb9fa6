//
//  SCDBActionModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/4.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    SCDBActionModelTypeAdd,
    SCDBActionModelTypeChange,
    SCDBActionModelTypeDelete,
} SCDBActionModelType;

@interface SCDBActionModel<__covariant T> : NSObject

///SCCallSessionModel
@property(nonatomic,strong) T value;
@property(nonatomic,assign) SCDBActionModelType action;

- (instancetype)initWithAction:(SCDBActionModelType) actionType value:(T)value;

@end

NS_ASSUME_NONNULL_END
