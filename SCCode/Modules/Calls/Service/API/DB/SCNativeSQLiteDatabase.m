//
//  SCNativeSQLiteDatabase.m
//  Supercall
//
//  Created by AI Assistant on 2025/7/9.
//  原生SQLite数据库包装类，用于替代FMDB的FMDatabase
//

#import "SCNativeSQLiteDatabase.h"
#import "SCNativeSQLiteResultSet.h"

@interface SCNativeSQLiteDatabase ()

@property (nonatomic, assign) sqlite3 *database;
@property (nonatomic, copy) NSString *databasePath;
@property (nonatomic, assign) BOOL isOpenFlag;

@end

@implementation SCNativeSQLiteDatabase

+ (instancetype)databaseWithPath:(NSString *)path {
    return [[self alloc] initWithPath:path];
}

- (instancetype)initWithPath:(NSString *)path {
    self = [super init];
    if (self) {
        _databasePath = [path copy];
        _database = NULL;
        _isOpenFlag = NO;
    }
    return self;
}

- (void)dealloc {
    [self close];
}

- (BOOL)open {
    if (_isOpenFlag) {
        return YES;
    }
    
    const char *dbPath = [_databasePath UTF8String];
    int result = sqlite3_open(dbPath, &_database);
    
    if (result == SQLITE_OK) {
        _isOpenFlag = YES;
        return YES;
    } else {
        
        if (_database) {
            sqlite3_close(_database);
            _database = NULL;
        }
        _isOpenFlag = NO;
        return NO;
    }
}

- (BOOL)close {
    if (!_isOpenFlag || !_database) {
        return YES;
    }
    
    int result = sqlite3_close(_database);
    if (result == SQLITE_OK) {
        _database = NULL;
        _isOpenFlag = NO;
        return YES;
    } else {
        
        return NO;
    }
}

- (BOOL)isOpen {
    return _isOpenFlag && _database != NULL;
}

- (BOOL)executeUpdate:(NSString *)sql, ... {
    va_list args;
    va_start(args, sql);

    NSMutableArray *arguments = [NSMutableArray array];

    // 计算SQL中的参数占位符数量来确定参数个数
    NSUInteger parameterCount = [[sql componentsSeparatedByString:@"?"] count] - 1;

    for (NSUInteger i = 0; i < parameterCount; i++) {
        id arg = va_arg(args, id);
        if (arg != nil) {
            [arguments addObject:arg];
        }
    }
    va_end(args);

    return [self executeUpdate:sql withArgumentsInArray:arguments];
}

- (BOOL)executeUpdate:(NSString *)sql withArgumentsInArray:(nullable NSArray *)arguments {
    if (![self isOpen]) {
        
        return NO;
    }
    
    sqlite3_stmt *statement = NULL;
    const char *sqlCString = [sql UTF8String];
    
    int result = sqlite3_prepare_v2(_database, sqlCString, -1, &statement, NULL);
    if (result != SQLITE_OK) {
        
        return NO;
    }
    
    // 绑定参数
    if (arguments && arguments.count > 0) {
        for (int i = 0; i < arguments.count; i++) {
            id argument = arguments[i];
            [self bindValue:argument toStatement:statement atIndex:i + 1];
        }
    }
    
    result = sqlite3_step(statement);
    sqlite3_finalize(statement);
    
    if (result == SQLITE_DONE) {
        return YES;
    } else {
        
        return NO;
    }
}

- (nullable SCNativeSQLiteResultSet *)executeQuery:(NSString *)sql, ... {
    va_list args;
    va_start(args, sql);

    NSMutableArray *arguments = [NSMutableArray array];

    // 计算SQL中的参数占位符数量来确定参数个数
    NSUInteger parameterCount = [[sql componentsSeparatedByString:@"?"] count] - 1;

    for (NSUInteger i = 0; i < parameterCount; i++) {
        id arg = va_arg(args, id);
        if (arg != nil) {
            [arguments addObject:arg];
        }
    }
    va_end(args);

    return [self executeQuery:sql withArgumentsInArray:arguments];
}

- (nullable SCNativeSQLiteResultSet *)executeQuery:(NSString *)sql withArgumentsInArray:(nullable NSArray *)arguments {
    if (![self isOpen]) {
        
        return nil;
    }

    sqlite3_stmt *statement = NULL;
    const char *sqlCString = [sql UTF8String];

    int result = sqlite3_prepare_v2(_database, sqlCString, -1, &statement, NULL);
    if (result != SQLITE_OK) {
        
        return nil;
    }

    // 绑定参数
    if (arguments && arguments.count > 0) {
        for (int i = 0; i < arguments.count; i++) {
            id argument = arguments[i];
            [self bindValue:argument toStatement:statement atIndex:i + 1];
        }
    }

    return [[SCNativeSQLiteResultSet alloc] initWithStatement:statement];
}

- (void)bindValue:(id)value toStatement:(sqlite3_stmt *)statement atIndex:(int)index {
    if (value == nil || [value isKindOfClass:[NSNull class]]) {
        sqlite3_bind_null(statement, index);
    } else if ([value isKindOfClass:[NSString class]]) {
        NSString *stringValue = (NSString *)value;
        sqlite3_bind_text(statement, index, [stringValue UTF8String], -1, SQLITE_TRANSIENT);
    } else if ([value isKindOfClass:[NSNumber class]]) {
        NSNumber *numberValue = (NSNumber *)value;
        const char *objCType = [numberValue objCType];

        if (strcmp(objCType, @encode(BOOL)) == 0 || strcmp(objCType, @encode(char)) == 0) {
            sqlite3_bind_int(statement, index, [numberValue boolValue] ? 1 : 0);
        } else if (strcmp(objCType, @encode(int)) == 0 ||
                   strcmp(objCType, @encode(short)) == 0 ||
                   strcmp(objCType, @encode(long)) == 0) {
            sqlite3_bind_int(statement, index, [numberValue intValue]);
        } else if (strcmp(objCType, @encode(long long)) == 0) {
            sqlite3_bind_int64(statement, index, [numberValue longLongValue]);
        } else if (strcmp(objCType, @encode(float)) == 0 ||
                   strcmp(objCType, @encode(double)) == 0) {
            sqlite3_bind_double(statement, index, [numberValue doubleValue]);
        } else {
            sqlite3_bind_int(statement, index, [numberValue intValue]);
        }
    } else if ([value isKindOfClass:[NSData class]]) {
        NSData *dataValue = (NSData *)value;
        sqlite3_bind_blob(statement, index, [dataValue bytes], (int)[dataValue length], SQLITE_TRANSIENT);
    } else {
        // 其他类型转换为字符串
        NSString *stringValue = [value description];
        sqlite3_bind_text(statement, index, [stringValue UTF8String], -1, SQLITE_TRANSIENT);
    }
}

- (nullable NSString *)lastErrorMessage {
    if (!_database) {
        return nil;
    }
    const char *errorMessage = sqlite3_errmsg(_database);
    return errorMessage ? [NSString stringWithUTF8String:errorMessage] : nil;
}

- (int)lastErrorCode {
    if (!_database) {
        return SQLITE_OK;
    }
    return sqlite3_errcode(_database);
}

- (sqlite3_int64)lastInsertRowId {
    if (!_database) {
        return 0;
    }
    return sqlite3_last_insert_rowid(_database);
}

- (int)changes {
    if (!_database) {
        return 0;
    }
    return sqlite3_changes(_database);
}

- (BOOL)beginTransaction {
    return [self executeUpdate:@"BEGIN TRANSACTION" withArgumentsInArray:nil];
}

- (BOOL)commit {
    return [self executeUpdate:@"COMMIT" withArgumentsInArray:nil];
}

- (BOOL)rollback {
    return [self executeUpdate:@"ROLLBACK" withArgumentsInArray:nil];
}

- (BOOL)tableExists:(NSString *)tableName {
    NSString *sql = @"SELECT name FROM sqlite_master WHERE type='table' AND name=?";
    SCNativeSQLiteResultSet *resultSet = [self executeQuery:sql withArgumentsInArray:@[tableName]];

    if (!resultSet) {
        return NO;
    }

    BOOL exists = [resultSet next];
    [resultSet close];
    return exists;
}

@end
