//
//  SCCallHangupPopup.m
//  Supercall
//
//  Created by sumengli<PERSON> on 2024/12/4.
//

#import "SCCallHangupPopup.h"
#import "SCPopupManager.h"
#import "SCFontManager.h"

@interface SCCallHangupPopup ()
@property(nonatomic,weak) UIImageView *contentBgIV;
//标题
@property (nonatomic, weak) UILabel *titleLabel;
//取消按钮
@property (nonatomic, weak) UIButton *cancelBtn;
//确认按钮
@property(nonatomic,weak) UIButton *confirmBtn;

@end

@implementation SCCallHangupPopup

- (void)viewDidLoad {
    [super viewDidLoad];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    //背景宽高比
    CGFloat whScale = 318.0f/383.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(28.0*2);
    CGFloat hScale = (contentWidth/whScale) / 383.0f;
    CGFloat wScale = contentWidth / 318.0f;
    
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"bg_calling_hangup"]];
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentBgIV = contentBgIV;
    
    UILabel *titleLabel = [[UILabel alloc]init];
    titleLabel.font = [SCFontManager semiBoldItalicFontWithSize:16.0f];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    titleLabel.textColor = UIColor.scWhite;
    titleLabel.text = @"Are you sure to hang up?".translateString;
    [self.contentBgIV addSubview:titleLabel];
    self.titleLabel = titleLabel;
    
    UIButton *cancelBtn = [UIButton buttonWithTitle:@"Cancel".translateString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(dismiss)];
    [cancelBtn sc_setThemeGradientBackground];
    [self.contentBgIV addSubview:cancelBtn];
    self.cancelBtn = cancelBtn;
    
    UIButton *confirmBtn = [UIButton buttonWithTitle:@"Confirm".translateString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16) image:nil backgroundColor:[UIColor colorWithHexString:@"#7B7B7B"] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onConfirm)];
    [self.contentBgIV addSubview:confirmBtn];
    self.confirmBtn = confirmBtn;
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(162.0f*hScale);
        make.centerX.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentBgIV).offset(30.0f*wScale);
        make.trailing.equalTo(self.contentBgIV).offset(-30.0f*wScale);
        make.top.equalTo(self.contentBgIV).offset(190.0f*hScale);
        make.height.mas_equalTo(20.0f*hScale);
    }];
    
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentBgIV).offset(30.0f*wScale);
        make.trailing.equalTo(self.contentBgIV).offset(-30.0f*wScale);
        make.top.equalTo(self.contentBgIV).offset(253.0f*hScale);
        make.height.mas_equalTo(46.0f*hScale);
    }];
    
    [self.confirmBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentBgIV).offset(30.0f*wScale);
        make.trailing.equalTo(self.contentBgIV).offset(-30.0f*wScale);
        make.top.equalTo(self.cancelBtn.mas_bottom).offset(12.0f*hScale);
        make.height.mas_equalTo(46.0f*hScale);
    }];
}

-(void)onConfirm{
    if (self.confirmBlock) {
        self.confirmBlock();
    }
    [self dismiss];
}

+ (void)showInViewController:(UIViewController *)viewController confirmBlock:(SCHangupAlertConfirmBlock)confirmBlock {
    SCCallHangupPopup *hangupPopup = [[SCCallHangupPopup alloc]init];
    hangupPopup.confirmBlock = confirmBlock;
    [[SCPopupManager shared] showPopup:hangupPopup inViewController:viewController animationStyle:SCPopupAnimationStyleCenter];
}

- (void)dismiss {
    [[SCPopupManager shared] dismissPopup:self];
}

@end
