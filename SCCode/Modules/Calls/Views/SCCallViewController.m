//
//  SCCallingViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCCallViewController.h"
#import "SCCallSessionModel.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCCoinsPopupViewController.h"
#import "SCAPIServiceManager.h"
#import "SCCallService.h"
#import "SCBaseNavigationController.h"
#import "SCCallingView.h"
#import "SCPermissionManager.h"
#import "SCWebViewController.h"

@interface SCCallViewController ()

@property (nonatomic,strong) NSDictionary * targetUserInfoDict;
///会话
@property (nonatomic,strong) SCCallSessionModel * session;
///呼叫中
@property (nonatomic,weak) SCCallingView * callingView;
// 添加防息屏属性
@property (nonatomic,assign) BOOL previousIdleTimerDisabled;
@property (nonatomic, assign) BOOL needsClose; // 添加标记属性

@end

@implementation SCCallViewController

- (instancetype)initWithSessionModel:(SCCallSessionModel *) sessionModel{
    self = [super init];
    if (self) {
        _session = sessionModel;
    }
    return self;
}


- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    // 检查是否需要关闭页面
    if (self.needsClose) {
        [self continueClose];
        return;
    }
  
    // 保存之前的息屏状态并开启防息屏
    self.previousIdleTimerDisabled = [UIApplication sharedApplication].idleTimerDisabled;
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // TODO: 融云消息静音
//    RCKitConfigCenter.message.disableMessageAlertSound = YES;
    
    kSCAuthCallService.isCalling = YES;
    kSCAuthCallService.isCallVc = YES;
}
- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    // 恢复之前的息屏状态
    [UIApplication sharedApplication].idleTimerDisabled = self.previousIdleTimerDisabled;
    //TODO: 融云恢复消息提示音
//    RCKitConfigCenter.message.disableMessageAlertSound = NO;
    
    kSCAuthCallService.isCalling = NO;
    kSCAuthCallService.isCallVc = NO;
}
- (void)initUI{
    [super initUI];
    kSCAuthCallService.isCalling = YES;
    kSCAuthCallService.isCallVc = YES;
    [self setIsHiddenSCNavigationBar:YES];
    [kSCKeyWindow endEditing:YES];
   
    SCCallingView *callingView = [[SCCallingView alloc] initWithIsMyCall:self.session.isMyCall];
    [callingView.hangUpButton addTarget:self action:@selector(onHangUp) forControlEvents:UIControlEventTouchUpInside];
    kSCAddTapGesture(callingView.answerAnimationView, self, onPickUp)
    [self.view addSubview:callingView];
    self.callingView = callingView;
    [callingView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    kWeakSelf(self);
    //获取用户详情 - 使用字典版本API
    [SCAPIServiceManager requestUserInfoWithUserId:self.session.tagId cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(NSDictionary * _Nonnull userDict) {
        // 存储字典数据
        weakself.targetUserInfoDict = userDict;
        [weakself updateUI];
    } failure:^(SCXErrorModel * _Nonnull err) {

    }];
    
}
-(void) doTimeOut{
    [self close];
}

// 检查当前页面的 present 栈中是否有 SCWebViewController，为了解决中途充值不能关闭的问题。
- (BOOL)hasWebViewControllerInPresentedStack {
    UIViewController *presentedVC = [UIViewController currentViewController];
    while (presentedVC != nil) {
        if ([presentedVC isKindOfClass:[SCWebViewController class]]) {
            return YES;
        }
        presentedVC = presentedVC.presentedViewController;
    }
    return NO;
}

-(void) close{
    // 检查是否存在 WebViewController，如果存在则标记需要关闭
    if ([self hasWebViewControllerInPresentedStack]) {
        self.needsClose = YES;
        return;
    }
    
    [self continueClose];
}

- (void)continueClose {
    dispatch_async(dispatch_get_main_queue(), ^{
        if(self.presentingViewController != nil  && self.presentedViewController != nil){
            [self.presentingViewController dismissViewControllerAnimated:YES completion:nil];
        }else{
            [self dismissViewControllerAnimated:YES completion:nil];
        }
    });
}

-(void) updateUI{
    ///更新呼叫页面 - 使用字典版本方法
    [self.callingView updateUIWithTargetUserDict:self.targetUserInfoDict session:self.session];
}


-(void)onHangUp{
    [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonNormal];
    [self close];
}

-(void)onPickUp{
    void (^pickUpAction)(void) = ^{
        BOOL isPickUp = [kSCAuthCallService doPickUpWithSuccess:nil failure:nil];
        if(isPickUp){
            [self.callingView.avatarWaveAnimationView stop];
            [self toVideoCall];
        }
    };
    kWeakSelf(self)
    // 检查相机和麦克风权限
    [[SCPermissionManager shared] checkPermissions:@[@(SCPermissionTypeCamera), @(SCPermissionTypeMicrophone)]
                                      completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            pickUpAction();
        } else if (shouldShowAlert) {
            // 如果权限不足，先检查相机权限
            [weakself checkPermissionCamera:pickUpAction];
        } else {
            // 如果不需要显示权限提示，直接开始匹���
            pickUpAction();
        }
    }];
    
}

- (void)checkPermissionCamera:(void (^)(void))pickUpAction {
    kWeakSelf(self)
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeCamera
                                     completion:^(BOOL cameraGranted, BOOL cameraShouldShowAlert) {
        if (!cameraGranted && cameraShouldShowAlert) {
            [weakself showPermissionAlertCamera:pickUpAction];
        } else {
            // 如果相机权限已授权，检查麦克风权限
            [weakself showPermissionAlertMicrophone:pickUpAction];
        }
    }];
}

- (void)showPermissionAlertCamera:(void (^)(void))pickUpAction {
    kWeakSelf(self)
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeCamera
                                 fromViewController:[UIViewController currentViewController]
                                      cancelBlock:^{
        // 检查麦克风权限
        [weakself checkPermissionMicrophone:pickUpAction];
    }];
}

- (void)checkPermissionMicrophone:(void (^)(void))pickUpAction {
    kWeakSelf(self)
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeMicrophone
                                     completion:^(BOOL micGranted, BOOL micShouldShowAlert) {
        if (!micGranted && micShouldShowAlert) {
            [weakself showPermissionAlertMicrophone:pickUpAction];
        } else {
            pickUpAction();
        }
    }];
}

- (void)showPermissionAlertMicrophone:(void (^)(void))pickUpAction {
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                 fromViewController:[UIViewController currentViewController]
                                      cancelBlock:^{
        pickUpAction();
    }];
}

-(void) toVideoCall{
    // 走融云通道会有线程问题，所以用主线包着
    dispatch_async(dispatch_get_main_queue(), ^{
        SCVideoCallViewController *vc = [[SCVideoCallViewController alloc] initWithSession:self.session];
        vc.delegate = kSCAuthCallService;
        kSCAuthCallService.delegate = vc;
        [self.navigationController pushViewController:vc animated:YES];
    });
}

#pragma mark - SCCallServiceDelegate

- (void)onCreatChannelSuccessWithSession:(SCCallSessionModel *)session{
    //需要刷新UI防止，显示挂断按钮，因为没有Channel无法挂断
    [self updateUI];
}
- (void)onPickUpWithSession:(SCCallSessionModel *)session{
    //对方同意，转跳视频页面
    [self toVideoCall];
}
- (void)onTimeOutWithSession:(SCCallSessionModel *)session{
    if(session.isMyCall){//超时未接听
        [kSCKeyWindow toast:@"The user is not available now, please try again later".translateString];
    }
    
    [self close];
}

- (void)onHangUpWithSession:(SCCallSessionModel *)session{
    //被挂断
    if(session.isMyCall && session.hangUpReason != SCCallHangUpReasonNormal){//超时未接听；自己挂断不用提醒
        [kSCKeyWindow toast:@"The user is not available now, please try again later".translateString];
    }
    [self close];
}

- (void)onCreatChannelFailWithSession:(SCCallSessionModel *)session{
    //创建频道失败
    if(session.isMyCall){//超时未接听
        if (session.hangUpReason == SCCallHangUpReasonNetworkException) {
            [kSCKeyWindow toast:@"The user is not available now, please try again later".translateString];
        } else {
            [kSCKeyWindow toast:@"Calling is busy".translateString];
        }
    }
    [self close];
}
- (void)onCallCountDown:(NSInteger)countDown session:(SCCallSessionModel *)session{
    if(session.isMyCall){
        //只有发起的呼叫才会有toast
        if(countDown == 20){
            //剩余20秒。即为第10秒
            [kSCKeyWindow toast:@"The user's phone may not be around,please try again later".translateString];
        }
    }
    
}
- (void)onJoinChannelCountDown:(NSInteger)countDown session:(SCCallSessionModel *)session {
    
}


- (void)onJoinChannelSuccessWithSession:(SCCallSessionModel *)session {
    
}


- (void)onJoinChannelTimeOutWithSession:(SCCallSessionModel *)session {
    
}




#pragma mark - 入口
///主动呼叫
+(SCCallViewController *) showCallingFromVC:(UIViewController *) fromVC sessionModel:(SCCallSessionModel *) sessionModel{
    SCCallViewController * vc = [[SCCallViewController alloc] initWithSessionModel:sessionModel];
    SCBaseNavigationController * nav = [[SCBaseNavigationController alloc] initWithRootViewController:vc];
    [nav setModalPresentationStyle:UIModalPresentationFullScreen];
    [nav setModalTransitionStyle:UIModalTransitionStyleCrossDissolve];
    [fromVC presentViewController:nav animated:YES completion:nil];
    return vc;
}


@end
