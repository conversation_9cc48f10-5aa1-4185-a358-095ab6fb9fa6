//
//  SCCallHistoryListViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/1/18.
//

#import "SCCallHistoryListViewController.h"
#import "SCCallHistoryCell.h"
#import "SCCallSessionModel.h"
#import "SCCallDBManager.h"
#import "SCCallHistoryCell.h"
#import "SCAPIServiceManager.h"
#import "SCCallService.h"
#import "SCOnlineStatesService.h"
#import "MJRefresh.h"
#import "SCAnchorInfoViewController.h"
#import "SCIMService.h"
#import "UIScrollView+EmptyDataSet.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCCallHistoryListViewController ()<UITableViewDelegate,UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>

@property(nonatomic,weak)UITableView *tableView;
@property(nonatomic,strong)NSMutableArray<SCCallSessionModel *> * callHistoryList;
@property(nonatomic,assign) NSUInteger page;
@property(nonatomic,assign) NSUInteger pageSize;
@property(nonatomic,strong) NSMutableDictionary<NSString *,NSDictionary *> *userInfoDis;
//@property(nonatomic,strong) NSMutableDictionary<NSString *,NSDictionary *> *userInfoDictDis;
@end

@implementation SCCallHistoryListViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if([self.callHistoryList count] == 0){
        [self.tableView.mj_header beginRefreshing];
    }
}

- (void)initData{
    [super initData];
    _callHistoryList = [[NSMutableArray<SCCallSessionModel *>  alloc] init];
    _pageSize = 50;
    _userInfoDis = [[NSMutableDictionary alloc] init];
    [self requestCallHistoryList];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    _tableView = [UITableView tableViewWithFrame:self.view.bounds style:UITableViewStylePlain delegate:self dataSource:self].addSuperView(self.view);
    _tableView.backgroundColor = [UIColor scGlobalBgColor];
    _tableView.rowHeight = 86;
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 创建下拉刷新控件
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(onHeaderRefresh)];
    header.stateLabel.textColor = UIColor.scWhite;
    [header setTitle:@"Pull down to refresh".translateString forState:MJRefreshStateIdle];
    [header setTitle:@"Release to refresh".translateString forState:MJRefreshStatePulling];
    [header setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    header.lastUpdatedTimeLabel.hidden = YES;
    self.tableView.mj_header = header;
    
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(onLoadMore)];
    footer.stateLabel.textColor = UIColor.scWhite;
    [footer setTitle:@"Pull up to load more".translateString forState:MJRefreshStateIdle];
    [footer setTitle:@"Release and load more".translateString forState:MJRefreshStatePulling];
    [footer setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    [footer setTitle:@"".translateString forState:MJRefreshStateNoMoreData];
    footer.triggerAutomaticallyRefreshPercent = -20;
    self.tableView.mj_footer = footer;
    
    self.tableView.emptyDataSetSource = self;
    self.tableView.emptyDataSetDelegate = self;
    
    kWeakSelf(self);
    [SCCallDBManager.sharedManager.callSessionChangeObs subscribe:^(SCDBActionModel<SCCallSessionModel *> * _Nullable value) {
        if(value.action == SCDBActionModelTypeAdd){
            
            [weakself.callHistoryList insertObject:value.value atIndex:0];
            [weakself requestUserInfoWithUserId:value.value.tagId];
            
            
        }else if(value.action == SCDBActionModelTypeChange){
            SCCallSessionModel * updateModel = [SCCallDBManager.sharedManager getCallSessionWithSessionId:value.value.clientSessionId];
            for (int i = 0; i < [weakself.callHistoryList count]; i++) {
                if([weakself.callHistoryList[i].clientSessionId isEqualToString:updateModel.clientSessionId]){
                    weakself.callHistoryList[i] = updateModel;
                    break;
                }
            }
        }else if(value.action == SCDBActionModelTypeDelete){
            for (int i = 0; i < [weakself.callHistoryList count]; i++) {
                if([weakself.callHistoryList[i].clientSessionId isEqualToString:value.value.clientSessionId]){
                    [weakself.callHistoryList removeObjectAtIndex:i];
                    break;
                }
            }
        }
        [weakself.tableView reloadData];
        
    } error:nil disposeBag:self.disposeBag];
}

-(void)requestUserInfoWithUserId:(NSString *) userId{
    //添加到状态同步器
    if(userId == nil){
        return;
    }
    if(self.userInfoDis[userId] == nil){
        kWeakSelf(self);
        [SCAPIServiceManager requestUserBaseWithUserId:userId success:^(NSDictionary * _Nonnull userBaseDict) {
            dispatch_async(dispatch_get_main_queue(), ^{
                weakself.userInfoDis[userId] = userBaseDict;
                [weakself.tableView reloadData];
            });
        } failure:nil];
        
    }
}


-(void)onHeaderRefresh{
    self.page = 1;
    [self requestCallHistoryList];
}
-(void)onLoadMore{
    self.page += 1;
    [self requestCallHistoryList];
}

- (void)requestCallHistoryList{
    
    NSArray *newList = [SCCallDBManager.sharedManager getCallSessionsWithPage:self.page pageSize:_pageSize];
    
    if(self.page == 1){
        [self.callHistoryList removeAllObjects];
    }
    
    for (SCCallSessionModel *item in newList) {
        [self requestUserInfoWithUserId:item.tagId];
    }
    if(self.page == 1){
        [self.tableView.mj_header endRefreshing];
    }
    if([newList count] <= self.pageSize){
        [self.tableView.mj_footer endRefreshingWithNoMoreData];
    }else{
        [self.tableView.mj_footer endRefreshing];
    }
    
    [self.callHistoryList addObjectsFromArray:newList];
    [self.tableView reloadData];
}

#pragma mark - UITableViewDelegate,UITableViewDataSource
- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCCallHistoryCell *cell = [SCCallHistoryCell initWithFormTableView:tableView];
    SCCallSessionModel *session = self.callHistoryList[indexPath.row];

    // 优先使用字典数据
    NSDictionary *userDict = [self.userInfoDis objectForKey:session.tagId];
    if (userDict) {
        [cell configWithModel:session userDict:userDict];
    } else {
        NSDictionary *userInfoDic = self.userInfoDis[session.tagId];
        [cell configWithModel:session userDict:userInfoDic];
//        SCUserInfoBaseModel *userInfo = [self.userInfoDis objectForKey:session.tagId];
//        [cell configWithModel:session userInfo:userInfoDic];
    }

    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.callHistoryList count];
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    SCCallSessionModel *session = self.callHistoryList[indexPath.row];

    // 优先使用字典数据
    NSDictionary *userDict = [self.userInfoDis objectForKey:session.tagId];
    NSString *userId = nil;

    if (userDict) {
        userId = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        if (userId.length == 0) {
            return;
        }
        if([kScAuthMar.imService isSystemWithId:userId] || [kScAuthMar.imService isUserServiceAccountWithId:userId] ){
            return;
        }
        [SCAnchorInfoViewController openWithUserDict:userDict from:self];
    } else {
        NSDictionary *userInfoDic = self.userInfoDis[session.tagId];
//        SCUserInfoBaseModel *userInfo = [self.userInfoDis objectForKey:session.tagId];
        userId = userInfoDic[@"userId"];
        if (userId == nil) {
            return;
        }
        if([kScAuthMar.imService isSystemWithId:userId] || [kScAuthMar.imService isUserServiceAccountWithId:userId] ){
            return;
        }
        [SCAnchorInfoViewController openWith:userId from:self];
    }
}
- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCCallSessionModel *session = self.callHistoryList[indexPath.row];
    
    UIContextualAction * action = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"Delete".translateString handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        [SCCallDBManager.sharedManager deleteCallSession:session];
        completionHandler(YES);
    }];
    action.backgroundColor = kSCColorWithHexStr(@"#666666");
    
    UISwipeActionsConfiguration * config = [UISwipeActionsConfiguration configurationWithActions:@[action]];
    config.performsFirstActionWithFullSwipe = false;
    return config;
}

#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [SCResourceManager loadImageWithName:@"ic_list_empty"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"No Data".translateString;
    NSDictionary *attributes = @{NSFontAttributeName: kScUIFontMedium(24),
                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B7010D"]};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView
{
    return YES;
}

#pragma mark - JXCategoryListContentViewDelegate
- (UIView *)listView{
    return self.view;
}
- (void)listWillAppear{
   
}

- (void)listWillDisappear{
  
}
@end
