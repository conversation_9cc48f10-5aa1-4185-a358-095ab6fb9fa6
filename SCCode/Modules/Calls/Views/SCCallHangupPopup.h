//
//  SCCallHangupPopup.h
//  Supercall
//
//  Created by sumengliu on 2024/12/4.
//

#import "SCBaseViewController.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^SCHangupAlertConfirmBlock)(void);

@interface SCCallHangupPopup : SCBaseViewController

@property (nonatomic, copy) SCHangupAlertConfirmBlock confirmBlock;

+ (void)showInViewController:(UIViewController *)viewController confirmBlock:(SCHangupAlertConfirmBlock)confirmBlock;
- (void)dismiss;
@end

NS_ASSUME_NONNULL_END
