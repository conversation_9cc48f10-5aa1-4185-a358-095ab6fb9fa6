//
//  SCCallNotificationPopup.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCCallNotificationPopup.h"
#import "SCCallService.h"
#import "SCCallViewController.h"
#import "SCBaseNavigationController.h"
#import "SCPermissionManager.h"
#import "SCWebViewController.h"

@interface SCCallNotificationPopup()


//价格
@property (nonatomic, weak) UILabel *priceL;

//头像
@property (nonatomic, weak) UIImageView *avatarIV;
//昵称
@property (nonatomic, weak) UILabel *nameL;
//年龄
@property (nonatomic, weak) UILabel *ageL;
//国家
@property (nonatomic, weak) UILabel *countryL;

// 添加防息屏属性
@property (nonatomic,assign) BOOL previousIdleTimerDisabled;

@end

@implementation SCCallNotificationPopup

- (void)initUI{
    [super initUI];
    [kSCKeyWindow endEditing:YES];
    // 添加手势识别器
    UIPanGestureRecognizer *panGestureRecognizer = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePan:)];
    [self addGestureRecognizer:panGestureRecognizer];
    kSCAddTapGesture(self, self, onTap);
    
    UIImageView *bgIV = [UIImageView new].setImage([[[UIImage imageGradientWithSCGradientColors:[SCGradientColors themeColorWithOrientation:SCGradientColorsOrientationHorizontal] size:CGSizeMake(344, 92)] imageWithRadius:kSCNormalCornerRadius] resizableImageWithCapInsets:UIEdgeInsetsMake(kSCNormalCornerRadius, kSCNormalCornerRadius, kSCNormalCornerRadius, kSCNormalCornerRadius) resizingMode:UIImageResizingModeStretch]);
    [self addSubview:bgIV];
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[
        [UIColor colorWithHexString:@"#E7CB81"],
        [UIColor colorWithHexString:@"#F1E2BA"],
        [UIColor colorWithHexString:@"#CE9757"],
    ] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *gradientImg = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(87, 22)];
    
    UIImage *priceBgImg = [[gradientImg imageWithRoundedCorners:SCCornerRadiusMake(11, 0, 0, 11)] resizableImageWithCapInsets:UIEdgeInsetsMake(11, 11, 11, 11) resizingMode:UIImageResizingModeStretch];
    UIImageView * priceBgIV = [[UIImageView alloc] initWithImage: kScAuthMar.isLanguageForce ? [priceBgImg imageWithHorizontallyFlippedOrientation] : priceBgImg];
    [self addSubview:priceBgIV];
    
    UIImageView * priceIconIV = [UIImageView new].setImageName(@"ic_coins_single").addSuperView(priceBgIV);
    
    _priceL = [UILabel labelWithText:@"" textColor:kSCColorWithHexStr(@"#140826") font:kScUIFontRegular(12) alignment:NSTextAlignmentLeft].addSuperView(priceBgIV);
    
    _avatarIV = [UIImageView new].setCornerRadius(52/2.0).addSuperView(self);
    
    
    self.nameL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontSemibold(16) ].setTextAlignment(kScAuthMar.isLanguageForce ? NSTextAlignmentRight:NSTextAlignmentLeft).addSuperView(self);
    
    self.ageL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontRegular(12) ].addSuperView(self);
    self.ageL.setCornerRadius(kSCSmallCornerRadius);
    self.ageL.backgroundColor = UIColor.scTheme;
    
    self.countryL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontRegular(12) ].addSuperView(self);
    self.countryL.setCornerRadius(kSCSmallCornerRadius);
    self.countryL.backgroundColor = kSCColorWithHexStr(@"#5183FF");
    
    UIButton * acceptBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_accep_call"]].addSuperView(self);
    [acceptBtn addTarget:self action:@selector(onPickUp) forControlEvents:UIControlEventTouchUpInside];
    [acceptBtn setImage:[SCResourceManager loadImageWithName:@"btn_accep_call_highlight"] forState:UIControlStateHighlighted];
    
    [bgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(bgIV.superview);
    }];
    
    [priceBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.equalTo(priceBgIV.superview);
    }];
    
    [priceIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(18, 18));
        make.centerY.equalTo(priceIconIV.superview);
        make.leading.equalTo(priceIconIV.superview).offset(6);
    }];
    
    [self.priceL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(priceIconIV.mas_trailing).offset(4);
        make.centerY.equalTo(priceIconIV);
        make.trailing.equalTo(self.priceL.superview).offset(-11);
    }];
    
    [self.avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(52, 52));
        make.leading.equalTo(self.avatarIV.superview).offset(kSCScaleWidth(26.0));
        make.top.equalTo(priceBgIV.mas_bottom).offset(8);
    }];
    
    [self.nameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarIV.mas_trailing).offset(10);
        make.trailing.equalTo(acceptBtn.mas_leading).offset(-10);
        make.centerY.equalTo(self.avatarIV).offset(-10);
        make.height.mas_equalTo(20);
    }];
    
    [self.ageL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.nameL.mas_leading).offset(0);
        make.top.equalTo(self.nameL.mas_bottom).offset(3);
        make.height.mas_equalTo(16);
    }];
    
    [self.countryL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.ageL);
        make.height.mas_equalTo(16);
        make.leading.equalTo(self.ageL.mas_trailing).offset(7.0f);
    }];
    
    [acceptBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(38, 38));
        make.trailing.equalTo(acceptBtn.superview).offset(-kSCScaleWidth(26.0));
        make.centerY.equalTo(acceptBtn.superview);
    }];
}


-(void)show{
    kWeakSelf(self);
    kSCAuthCallService.isCalling = YES;
    dispatch_async(dispatch_get_main_queue(), ^{
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        
        // 保存之前的息屏状态并开启防息屏
        strongSelf.previousIdleTimerDisabled = [UIApplication sharedApplication].idleTimerDisabled;
        [UIApplication sharedApplication].idleTimerDisabled = YES;
        
        if(strongSelf.superview){
            [strongSelf removeFromSuperview];
        }
        CGFloat width = kSCScaleWidth(344) ;
        CGFloat x = (kSCScreenWidth - width)/2.0;
        CGFloat height = 92.0f;
        
        strongSelf.frame = CGRectMake(x, - height, width, height);
        [kSCKeyWindow addSubview:strongSelf];
        [UIView animateWithDuration:0.3 animations:^{
            strongSelf.frame = CGRectMake(x, kSCSafeAreaTopHeight , width, height);
        } completion:^(BOOL finished) {
            
        }];
    });
}
-(void)hide{
    kWeakSelf(self);
    kSCAuthCallService.isCalling = NO;
    
    // 恢复之前的息屏状态
    [UIApplication sharedApplication].idleTimerDisabled = self.previousIdleTimerDisabled;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        CGFloat width = kSCScaleWidth(344) ;
        CGFloat x = (kSCScreenWidth - width)/2.0;
        CGFloat height = 92.0f;
        
        [UIView animateWithDuration:0.3 animations:^{
            strongSelf.frame = CGRectMake(x, - height , width, height);
        } completion:^(BOOL finished) {
            [strongSelf removeFromSuperview];
        }];
    });
}

-(void)onTap{
    //打开呼叫页面
    SCCallViewController *callVC = [SCCallViewController showCallingFromVC:[UIViewController currentViewController] sessionModel:kSCAuthCallService.callingSessionModel];
    //设置代理
    kSCAuthCallService.delegate = callVC;
}


- (void)handlePan:(UIPanGestureRecognizer *)gestureRecognizer {
    CGFloat width = kSCScaleWidth(344) ;
    CGFloat x = (kSCScreenWidth - width)/2.0;
    CGFloat height = 92.0f;
    
    CGPoint translation = [gestureRecognizer translationInView:self];
    CGFloat newY = translation.y + self.frame.origin.y;
    
    
    
    
    if (gestureRecognizer.state == UIGestureRecognizerStateChanged) {
        // 移动视图
        self.setY(newY);
        [gestureRecognizer setTranslation:CGPointZero inView:self];
    } else if (gestureRecognizer.state == UIGestureRecognizerStateEnded) {
        // 判断手势结束时的位置，如果视图向下偏移超过一定距离，则关闭视图
        if (newY - kSCSafeAreaTopHeight < -15) {
            [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonNormal];
            [self hide];
        } else {
            [UIView animateWithDuration:0.3 animations:^{
                self.frame = CGRectMake(x, kSCSafeAreaTopHeight , width, height);
            }];
        }
    }
}

-(void) onPickUp {
    kWeakSelf(self)
    void (^pickUpAction)(void) = ^{
        if (kSCAuthCallService.callingSessionModel.channelName == nil) {
            return;
        }
        BOOL isPickUp = [kSCAuthCallService doPickUpWithSuccess:nil failure:nil];
        if(isPickUp){
            SCVideoCallViewController *vc = [[SCVideoCallViewController alloc] initWithSession:kSCAuthCallService.callingSessionModel];
            vc.delegate = kSCAuthCallService;
            kSCAuthCallService.delegate = vc;
            SCBaseNavigationController *nv = [[SCBaseNavigationController alloc] initWithRootViewController:vc];
            [nv setModalPresentationStyle:UIModalPresentationFullScreen];
            [nv setModalTransitionStyle:UIModalTransitionStyleCrossDissolve];
            UIViewController *viewcontroller = [UIViewController currentViewController];
            [viewcontroller presentViewController:nv animated:YES completion:^{
            }];
        }
    };
    
    // 检查相机和麦克风权限
    [[SCPermissionManager shared] checkPermissions:@[@(SCPermissionTypeCamera), @(SCPermissionTypeMicrophone)]
                                        completion:^(BOOL granted, BOOL shouldShowAlert) {
        if (granted) {
            pickUpAction();
        } else if (shouldShowAlert) {
            // 如果权限不足，先检查相机权限
            [weakself checkPermissionCamera:pickUpAction];
        } else {
            // 如果不需要显示权限提示，直接开始匹配
            pickUpAction();
        }
    }];
    
}

- (void)checkPermissionCamera:(void (^)(void))pickUpAction{
    kWeakSelf(self)
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeCamera
                                       completion:^(BOOL cameraGranted, BOOL cameraShouldShowAlert) {
        if (!cameraGranted && cameraShouldShowAlert) {
            [weakself showPermissionAlertCamera:pickUpAction];
        } else {
            // 如果相机权限已授权，检查麦克风权限
            [weakself showPermissionAlertDisMicrophone:pickUpAction];
        }
    }];
}

- (void)showPermissionAlertDisMicrophone:(void (^)(void))pickUpAction {
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                   fromViewController:[UIViewController currentViewController]
                                          cancelBlock:^{
        pickUpAction();
    }];
}

- (void)showPermissionAlertCamera:(void (^)(void))pickUpAction {
    kWeakSelf(self)
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeCamera
                                   fromViewController:[UIViewController currentViewController]
                                          cancelBlock:^{
        [weakself checkPermissionMicrophone:pickUpAction];
    }];
}

- (void)checkPermissionMicrophone:(void (^)(void))pickUpAction {
    kWeakSelf(self)
    // 检查麦克风权限
    [[SCPermissionManager shared] checkPermission:SCPermissionTypeMicrophone
                                       completion:^(BOOL micGranted, BOOL micShouldShowAlert) {
        if (!micGranted && micShouldShowAlert) {
            [weakself showPermissionAlertMicrophone:pickUpAction];
        } else {
            pickUpAction();
        }
    }];
}

- (void)showPermissionAlertMicrophone:(void (^)(void))pickUpAction {
    [[SCPermissionManager shared] showPermissionAlert:SCPermissionTypeMicrophone
                                   fromViewController:[UIViewController currentViewController]
                                          cancelBlock:^{
        pickUpAction();
    }];
}

@end
