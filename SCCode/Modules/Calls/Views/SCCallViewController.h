//
//  SCCallingViewController.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCBaseViewController.h"
#import "SCCallService.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCCallViewController : SCBaseViewController<SCCallServiceDelegate>
- (instancetype)initWithSessionModel:(SCCallSessionModel *) sessionModel;
///主动呼叫
+(SCCallViewController *) showCallingFromVC:(UIViewController *) fromVC sessionModel:(SCCallSessionModel *) sessionModel;
@end

NS_ASSUME_NONNULL_END
