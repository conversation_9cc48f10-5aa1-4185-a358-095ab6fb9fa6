//
//  SCAnchorEvaluateViewController.m
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/31.
//

#import "SCAnchorEvaluateViewController.h"
#import "SCLabelListView.h"
#import "SCTryAnchorAvatarCell.h"
#import "SCAPIServiceManager.h"
#import "SCCategoryAPIManagerCall.h"
#import "SCBaseNavigationController.h"
#import "SCAnchorInfoViewController.h"
#import "SCPopupManager.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
// #import "SCModelCompatibility.h" // 已移除，不再需要Model转换

@interface SCAnchorEvaluateViewController ()<UICollectionViewDelegate,UICollectionViewDataSource>

//内容布局
@property (nonatomic, strong) UIView *contentView;

//用户详情布局
@property (nonatomic, strong) UIView *userInfView;
//用户头像
@property (nonatomic, strong) UIImageView *avatarIV;
//用户昵称
@property (nonatomic, strong) UILabel *userNameLabel;
//通话时长
@property (nonatomic, strong) UILabel *callTimeLabel;

//关闭按钮
@property (nonatomic, strong) UIButton *closeButton;
//分割线
@property (nonatomic, strong) UIView *lineView;
//评价布局
@property (nonatomic, strong) UIView *evaluateView;
//点赞布局
@property (nonatomic, strong) UIView *likeView;
//喜欢按钮
@property (nonatomic, strong) UIButton *likeButton;
//不喜欢按钮
@property (nonatomic, strong) UIButton *dislikeButton;


//标签布局
@property (nonatomic, strong) UIView *tagView;
//标签标题
@property (nonatomic, strong) UILabel *tagL;
//标签布局
@property(nonatomic,strong) SCLabelListView * impressionListView;

//下一步按钮
@property(nonatomic,strong) UIButton *nextBtn;

//评价成功提示语
@property(nonatomic,strong) UILabel *successL;



////推荐主播布局
//@property(nonatomic,strong) UIView * tryAnchorV;
////推荐主播标题
//@property(nonatomic,strong) UILabel * tryAnchorL;
////推荐主播布局
//@property(nonatomic,strong) UICollectionView * tryAnchorCV;

//nextLoading
@property(nonatomic,strong) UIView *nextLoadingView;
@property(nonatomic,strong) UIActivityIndicatorView * nextAtyV;



//数据
@property(nonatomic,assign) BOOL isLike;
// 已移除resultModel属性，使用字典版本替代

// 字典版本数据
@property(nonatomic,strong) NSDictionary *resultDict;

//用户详细信息 - 改为字典存储
@property(nonatomic,strong) NSMutableDictionary<NSString *,NSDictionary *> *userInfoByIDDic;
@property(nonatomic,strong) NSDictionary *tagerUserInfoDict;

//提交状态
//0 未提交 1提交中  2提交成功
@property(nonatomic,assign) int postStatus;
@end

@implementation SCAnchorEvaluateViewController

//头像
static CGFloat kAnchorAvatarWidth = 72.0f;

- (void)initData{
    [super initData];
    _userInfoByIDDic = [NSMutableDictionary new];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    [self.view setBackgroundColor:[UIColor clearColor]];
    _contentView = [[UIView alloc] init].setBackgroundColor([UIColor colorWithHexString:@"#282828"]).addSuperView(self.view);
    //顶部圆角
    _contentView.layer.cornerRadius = kSCBigCornerRadius;
    _contentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self _initUserInfoView];
    
    //关闭按钮
    _closeButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close_white"] target:self action:@selector(onClose)].addSuperView(self.view);
    
    //分割线
    _lineView = [[UIView alloc] init].setBackgroundColor(UIColor.clearColor).addSuperView(self.contentView);
    
    //评价布局
    _evaluateView = [[UIView alloc] init].addSuperView(self.contentView);
    _evaluateView.clipsToBounds = YES;
    //点赞布局
    [self showLikeView];
    kWeakSelf(self);
//    //预加载推荐的用户信息
//    for (SCRecommendAnchorModel * item in self.resultModel.recommendList) {
//        if(!kSCIsStrEmpty(item.broadcasterId)){
//            
//            [SCAPIServiceManager requestUserInfoWithUserId:item.broadcasterId cachePolicy:SCNetCachePolicyOnlyCache success:^(SCUserInfoModel * _Nonnull model) {
//                [weakself.userInfoByIDDic setObject:model forKey:item.broadcasterId];
//                [weakself.tryAnchorCV reloadData];
//            } failure:^(SCXErrorModel * _Nonnull error) {
//                
//            }];
//        }
//       
//    }
    // 使用字典版本的API调用
    NSString *broadcasterId = [SCDictionaryHelper stringFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultBroadcasterIdKey defaultValue:@""];
    if (!kSCIsStrEmpty(broadcasterId)) {
        [SCAPIServiceManager requestUserInfoWithUserId:broadcasterId cachePolicy:SCNetCachePolicyOnlyCache success:^(NSDictionary * _Nonnull userDict) {
            // 直接使用字典数据
            weakself.tagerUserInfoDict = userDict;
            [weakself _updateUserView];
        } failure:nil];
    }
    
}


-(void) showLikeView{
    
    CGFloat contentHeight = 168.0f;
    CGFloat contentWidth = kSCScreenWidth;
    CGSize likeSize = CGSizeMake(62, 62);
    
    CGFloat likeX = (contentWidth/2.0 - likeSize.width) /2.0;
    CGFloat likeY = (contentHeight - likeSize.height) /2.0 - 10;
    
    //点赞布局
    _likeView = [[UIView alloc] init].addSuperView(self.evaluateView);
    //喜欢按钮
    _likeButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_anchor_evaluate_goods"] target:self action:@selector(onLike)].addSuperView(self.likeView);
    //不喜欢按钮
    _dislikeButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_anchor_evaluate_bad"] target:self action:@selector(onDislike)].addSuperView(self.likeView);
    self.likeButton.frame = CGRectMake(likeX, likeY, likeSize.width, likeSize.height);
    self.dislikeButton.frame = CGRectMake(likeX + (contentWidth/2.0), likeY, likeSize.width, likeSize.height);
    self.likeView.setX(0).setY(0).setSize(CGSizeMake(contentWidth, contentHeight));
    [self updateLayoutWithcontentHeight:contentHeight];
    
}
///显示标签
-(void) showTagView{
    CGFloat left = 16.0f;
    CGFloat viewWidth = kSCScreenWidth - left*2;
    //标签布局
    _tagView = [[UIView alloc] init].addSuperView(self.evaluateView);
    //先设置宽度，不影响_impressionListView 根据父视图宽度进行计算
    _tagView.scWidth = viewWidth;
    //标签标题
    _tagL = [[UILabel alloc] init].setText([NSString stringWithFormat:@"%@:", @"Impression".translateString]).setTextColor(UIColor.scWhite).addSuperView(self.tagView);
    _tagL.font = [SCFontManager boldItalicFontWithSize:16.0f];
    //标签布局
    _impressionListView = [[SCLabelListView alloc] initWithFrame:CGRectMake(0, 0, viewWidth, 200)].addSuperView(self.tagView);
    _impressionListView.tags = [self tagList];
    _impressionListView.isCanSelect = YES;
    _impressionListView.isMust = YES;
    _impressionListView.maxSelectNum = 3;
    kWeakSelf(self);
    [_impressionListView.selectChangeObx subscribe:^(NSArray<NSString *> * _Nullable value) {
        [weakself changeNextBtnStyle];
    } error:^(SCXErrorModel * _Nonnull error) {
        NSString *maxSelectNum = [NSString stringWithFormat:@"%ld", weakself.impressionListView.maxSelectNum];
        [weakself.view toast:[@"You can only choose ### impressions".translateString stringByReplacingOccurrencesOfString:@"###" withString: maxSelectNum]];
    } disposeBag:self.disposeBag];
    //nextBtn
    _nextBtn = [UIButton buttonWithTitle:@"NEXT".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius].addSuperView(self.tagView);
    [_nextBtn sc_setThemeGradientBackground];
    [self changeNextBtnStyle];
    [_nextBtn addTarget:self action:@selector(onNext) forControlEvents:UIControlEventTouchUpInside];
    
    
    
    CGFloat tagLTop = 9.0f;
    
    CGFloat tagLHeight = 23.0f;
    
    CGFloat impressionListViewTop = 11.0f;
    CGFloat impressionListViewHeight = [_impressionListView calculateViewHeightWithData];
    ///做下限制
    if(impressionListViewHeight > 400 ){
        impressionListViewHeight = 400;
    }
    
    CGSize nextSize = CGSizeMake(342, 46);
    CGFloat nextTopPadding = 16.0f;
    CGFloat nextLeft = (viewWidth - nextSize.width) / 2.0f;
    
    self.tagL.frame = CGRectMake(0, tagLTop, viewWidth, tagLHeight);
    self.impressionListView.scTop = self.tagL.scBottom + impressionListViewTop;
    self.impressionListView.scLeft = 0;
    self.impressionListView.setSize(CGSizeMake(viewWidth, impressionListViewHeight));
    
    self.nextBtn.frame = CGRectMake(nextLeft, self.impressionListView.scBottom + nextTopPadding, nextSize.width, nextSize.height);
    
    self.tagView.frame = CGRectMake(left, self.likeView.scBottom, viewWidth, self.nextBtn.scBottom);
    
    
    CGFloat anchorLTop = 9.0f;
    
    CGFloat anchorLHeight = 23.0f;
    CGFloat anchorLCollentionViewTop = 10.0f;
    
    CGFloat anchorItemHeight = 60;
    CGFloat anchorItemRowPadding = 29;
    NSInteger colNum = 4;
    CGFloat anchorItemColPadding = (viewWidth - (anchorItemHeight*colNum))/(colNum - 1);
    // 使用字典方式获取推荐主播列表
    NSArray *recommendList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultRecommendListKey defaultValue:@[]];
    CGFloat anchorLCollentionViewHeight = ([recommendList count] / colNum) * (anchorItemHeight + anchorItemColPadding);
    if(([recommendList count] % colNum) != 0){
        anchorLCollentionViewHeight += anchorItemHeight + anchorItemColPadding;
    }
    
    if(anchorLCollentionViewHeight > 300){
        //防止高度溢出
        anchorLCollentionViewHeight = 300;
    }
    
    
//    //半个推荐布局高度
//    CGFloat anchoreBaseHeight = anchorLTop + anchorLHeight + anchorLCollentionViewTop + anchorItemHeight + anchorItemRowPadding ;
//    
//    _tryAnchorV = [UIView new].addSuperView(self.evaluateView);
//    //推荐主播标题
//    _tryAnchorL = [[UILabel alloc] init].setText(@"Try another girl".translateString).setFontMediumSize(16).setTextColor(UIColor.scBlack).addSuperView(self.tryAnchorV);
//    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
//    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
//    layout.itemSize = CGSizeMake(anchorItemHeight, anchorItemHeight);
//    layout.minimumLineSpacing = anchorItemColPadding;
//    layout.minimumInteritemSpacing = anchorItemRowPadding;
//    
//    ///设置推荐主播
//    _tryAnchorCV = [UICollectionView collectionViewWithLayout:layout]
//        .setDelegate(self)
//        .setDataSource(self)
//        .registerCell([SCTryAnchorAvatarCell class],[SCTryAnchorAvatarCell cellIdentifier])
//        .setShowsVerticalScrollIndicator(NO)
//        .setShowsHorizontalScrollIndicator(NO)
//        .setContentInset(UIEdgeInsetsMake(0, 0, 0, 0))
//        .addSuperView(self.tryAnchorV);
//    
//    //标题
//    _tryAnchorL.setX(0).setY(anchorLTop).setWidth(viewWidth).setHeight(anchorLHeight);
//    //
//    _tryAnchorCV.setX(0).setY(self.tryAnchorL.scBottom + anchorLCollentionViewTop).setWidth(viewWidth).setHeight(anchorLCollentionViewHeight);
//    _tryAnchorV.setX(left).setY(self.tagView.scBottom).setWidth(viewWidth).setHeight(_tryAnchorCV.scBottom);
    
    //底部安全区高度
    CGFloat safeBottom = kSCSafeAreaBottomHeight;
    [UIView animateWithDuration:0.3 animations:^{
        [self updateLayoutWithcontentHeight: self.tagView.scHeight+MAX(safeBottom, 15)];
        self.likeView.scTop = - self.likeView.scHeight;
        self.tagView.scTop = self.likeView.scBottom;
//        self.tryAnchorV.scTop =self.tagView.scBottom;
    }completion:^(BOOL finished) {

    }];
}
-(void)changeNextBtnStyle{
    if([self.impressionListView.selectList count] == 0){
        [self.nextBtn setBackgroundImage:[UIImage imageWithColor:UIColor.scGrayBtn imageSize:CGSizeMake(259, 46) cornerRadius:kSCNormalCornerRadius] forState:UIControlStateNormal];
    }else{
        [self.nextBtn sc_setThemeGradientBackground];
    }
}


//更新布局
-(void) updateLayoutWithcontentHeight:(CGFloat) scontentHeight{
    CGFloat linkTop = 86.0f;
    CGFloat linkHeight = 0.5f;
    
    CGFloat contentHeight = linkTop + linkHeight + scontentHeight;
    
    //调整内容布局
    self.contentView.scWidth = kSCScreenWidth;
    self.contentView.scHeight = contentHeight;
    self.contentView.scBottom = kSCScreenHeight;
    self.contentView.scLeft = 0.0f;
    //分割线
    self.lineView.frame = CGRectMake(0, linkTop, self.contentView.scWidth, linkHeight);
    //关闭按钮
    self.closeButton.scSize = CGSizeMake(40, 40);
    self.closeButton.setY(self.contentView.scTop+5);
    //判断是是否右读语言
    if(kScAuthMar.isLanguageForce){
        self.closeButton.setX(5);
    }else{
        self.closeButton.setX(self.contentView.scWidth - 40 - 5);
    }
   

    //评论布局
    self.evaluateView.scLeft = 0;
    self.evaluateView.scTop = self.lineView.scBottom;
    self.evaluateView.scSize = CGSizeMake(self.contentView.scWidth, scontentHeight+kSCSafeAreaBottomHeight);
    
    CGFloat userLeft = 15.0f;
    //用户信息布局
    _userInfView.setSize(CGSizeMake(kSCScreenWidth - userLeft*2, kAnchorAvatarWidth));
    _userInfView.setX(userLeft).scBottom = self.contentView.scTop + self.lineView.scTop - 10;
    
}

///更新用户视图
-(void)_updateUserView{
    // 优先使用字典数据
    if (self.tagerUserInfoDict) {
        self.userInfView.hidden = NO;
        NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:self.tagerUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
        NSString *nickname = [SCDictionaryHelper stringFromDictionary:self.tagerUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
        [self.avatarIV sc_setImageWithURL:avatarUrl];
        [self.userNameLabel setText:nickname];
        // 使用字典方式获取通话时长
        NSInteger duration = [SCDictionaryHelper integerFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultDurationKey defaultValue:0];
        [self.callTimeLabel setText:[NSString stringWithFormat:@"%@%@",@"Call duration：".translateString,[NSString hmsWithSeconds:duration]]];
        return;
    }
}
///初始化LoadingView
-(void) _initLoadingView{
    self.nextLoadingView = [UIView new].setBackgroundColor([UIColor colorWithHexString:@"#282828"]).addSuperView(self.evaluateView);
    self.nextLoadingView.hidden = YES;
    ///拦截点击事件
    self.nextLoadingView.userInteractionEnabled = YES;
    self.nextAtyV = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleMedium].addSuperView(self.nextLoadingView);
    self.nextAtyV.color = [UIColor scWhite];
    self.nextAtyV.hidesWhenStopped = YES;
    NSString *tips = [NSString stringWithFormat:@"🎉%@🎉", @"Thanks for your comment!".translateString];
    self.successL = [UILabel new].setText(tips)
        .setTextColor(UIColor.scWhite)
        .setTextAlignment(NSTextAlignmentCenter)
        .setFontMediumSize(18.0f)
        .addSuperView(self.nextLoadingView);
    
    [self.successL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.nextLoadingView);
        make.top.equalTo(self.nextLoadingView).offset(6.0f);
    }];
    [self.nextAtyV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.nextLoadingView);
        make.top.equalTo(self.nextLoadingView).offset(6.0f);
    }];
}

//用户信息布局
-(void) _initUserInfoView{
    ///用户信息
    _userInfView = [[UIView alloc] init].addSuperView(self.view);
    ///头像
    _avatarIV = [[UIImageView alloc] init].setCornerRadius(kAnchorAvatarWidth/2.0).addSuperView(self.userInfView);
    ///昵称
    _userNameLabel = [[UILabel alloc] init].setTextColor(UIColor.scWhite).addSuperView(self.userInfView);
    _userNameLabel.font = [SCFontManager semiBoldFontWithSize:16.0f];
    ///通话时长
    _callTimeLabel = [[UILabel alloc] init].setFontRegularSize(14).setTextColor(UIColor.scWhite).addSuperView(self.userInfView);
    
    //头像
    [_avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.equalTo(self.userInfView).offset(0);
        make.size.mas_equalTo(CGSizeMake(kAnchorAvatarWidth, kAnchorAvatarWidth));
    }];
    
    //昵称
    [_userNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarIV.mas_trailing).offset(10);
        make.centerY.equalTo(self.avatarIV).offset(-10);
        make.trailing.equalTo(self.userInfView).offset(-10);
        make.height.mas_equalTo(23);
    }];
    
    //通话时长
    [_callTimeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.userNameLabel);
        make.top.equalTo(self.userNameLabel.mas_bottom).offset(5);
        make.trailing.equalTo(self.userNameLabel);
        make.height.mas_equalTo(17);
    }];
}

-(NSArray<NSString *>  *) tagList{
    // 使用字典方式获取标签列表
    NSArray *tagList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultTagListKey defaultValue:@[]];
    NSArray *badTagList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultBadTagListKey defaultValue:@[]];
    return self.isLike ? tagList : badTagList;
}

-(void)showLoading{
    if(self.nextLoadingView == nil){
        [self _initLoadingView];
    }
    [self.evaluateView bringSubviewToFront:self.nextLoadingView];
    self.successL.hidden = YES;
    self.nextLoadingView.frame = self.tagView.frame;
    self.nextLoadingView.hidden = NO;
    self.nextLoadingView.alpha = 0;
    
    [UIView animateWithDuration:0.3 animations:^{
        self.nextLoadingView.alpha = 1;
        self.tagView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.nextAtyV startAnimating];
    }];
}
-(void)showPostFail{
    [self.view toast:@"Comment Failed".translateString];
    self.nextLoadingView.frame = self.tagView.frame;
   
    [UIView animateWithDuration:0.3 animations:^{
        self.nextLoadingView.alpha = 0;
        self.tagView.alpha = 1;
    } completion:^(BOOL finished) {
        self.nextLoadingView.hidden = YES;
    }];
}
-(void)showPostSuccess{
    //成功后修改布局
    self.successL.hidden = NO;
    CGFloat loadingHeight = 84.0f;
    [UIView animateWithDuration:0.3 animations:^{
        self.nextLoadingView.setSize(CGSizeMake(self.nextLoadingView.scWidth, loadingHeight));
        [self updateLayoutWithcontentHeight: self.nextLoadingView.scBottom];
    } completion:^(BOOL finished) {
        self.nextLoadingView.setSize(CGSizeMake(self.nextLoadingView.scWidth, loadingHeight));
        [self updateLayoutWithcontentHeight: self.nextLoadingView.scBottom];
        [self.nextAtyV stopAnimating];
    }];
    
    
}

#pragma mark - Action
-(void) onClose{
    [[SCPopupManager shared] dismissPopup:self];
}

-(void) onLike{
    self.isLike = YES;
    [self showTagView];
}

-(void) onDislike{
    self.isLike = NO;
    [self showTagView];
}
-(void) onNext{
    if(self.postStatus != 0){
        return;
    }
    
    NSArray<NSString *> *tags = self.impressionListView.selectList;
    if([tags count] == 0){
        //没有选择标签
        [self.view toast:@"You have to give your impression".translateString];
        return;
    }
    [self showLoading];
    kWeakSelf(self);
    // 使用字典方式获取频道名称
    NSString *channelName = [SCDictionaryHelper stringFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultChannelNameKey defaultValue:@""];
    [SCAPIServiceManager requestAnchorEvaluateSubmitWithChannelName:channelName isGood:self.isLike tags:tags success:^{
        [weakself showPostSuccess];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself showPostFail];
    }];
    
}
#pragma mark - UICollectionViewDelegate,UICollectionViewDataSource


- (nonnull __kindof UICollectionViewCell *)collectionView:(nonnull UICollectionView *)collectionView cellForItemAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCTryAnchorAvatarCell *cell = [SCTryAnchorAvatarCell initWithFormCollectionView:collectionView forIndexPath:indexPath];

    // 使用字典方式获取推荐主播列表
    NSArray *recommendList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultRecommendListKey defaultValue:@[]];
    if (indexPath.row < recommendList.count) {
        NSDictionary *anchorDict = recommendList[indexPath.row];
        NSString *broadcasterId = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:@"broadcasterId" defaultValue:@""];

        if(kSCIsStrEmpty(broadcasterId)){
            [cell.avatarIV sc_setPlaceholderImage];
        }else{
            NSDictionary *userDict = [self.userInfoByIDDic objectForKey:broadcasterId];
            NSString *avatarUrl = kSCUserAvatarUrlFromDict(userDict);
            [cell.avatarIV sc_setImageWithURL:avatarUrl];
        }
    }


    return cell;
}

- (NSInteger)collectionView:(nonnull UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    // 使用字典方式获取推荐主播列表数量
    NSArray *recommendList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultRecommendListKey defaultValue:@[]];
    return [recommendList count];
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    // 使用字典方式获取推荐主播信息
    NSArray *recommendList = [SCDictionaryHelper arrayFromDictionary:self.resultDict forKey:SCDictionaryKeys.shared.kSCVideoCallResultRecommendListKey defaultValue:@[]];
    if (indexPath.row < recommendList.count) {
        NSDictionary *anchorDict = recommendList[indexPath.row];
        NSString *broadcasterId = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:@"broadcasterId" defaultValue:@""];
        [SCAnchorInfoViewController openWith:broadcasterId from:self];
    }
}



// 已移除Model版本方法，使用字典版本替代
// +(void) showWithChannelResult:(SCVideoCallResultModel *)channelResult
// +(void)checkAnchorEvaluateWithChannelName:(NSString *) channelName

// 字典版本方法实现
+(void)showWithChannelResultDict:(NSDictionary *)channelResultDict{
    SCAnchorEvaluateViewController * vc = [[SCAnchorEvaluateViewController alloc] init];
    vc.resultDict = channelResultDict;
    // 已移除Model版本，完全使用字典数据
    [[SCPopupManager shared] showPopup:vc inViewController:nil animationStyle:SCPopupAnimationStyleBottom];
}

+(void)checkAnchorEvaluateWithChannelNameDict:(NSString *)channelName{
    [SCAPIServiceManager requestCallResultWithChannelName:channelName success:^(NSDictionary * _Nullable resultDict) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [SCAnchorEvaluateViewController showWithChannelResultDict:resultDict];
        });
    } failure:nil];
}


@end
