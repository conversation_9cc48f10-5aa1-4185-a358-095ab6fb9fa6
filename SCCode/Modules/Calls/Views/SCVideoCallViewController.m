//
//  SCVideoCallViewController.m
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/25.
//

#import "SCVideoCallViewController.h"
#import <AgoraRtcKit/AgoraRtcKit.h>
// #import "SCAppConfigModel.h" // 已迁移到字典
#import "SCJoinChannelProgressView.h"
#import "SCUserItemView.h"
#import "SCVideoCallChatInputView.h"
#import "SCVideoCallChatCell.h"
#import "SCVideoCallChatMessageModel.h"
#import "SCAPIServiceManager.h"
#import "SCGiftPopupViewController.h"
#import "SCGiftTipView.h"
#import "SCIMService.h"
#import "SCGiftService.h"
#import "SCCoinsPopupViewController.h"
#import "SCCallDisablePrompt.h"
#import "SCAlertViewController.h"
#import "SCVideoCallRemainTimeCountdownView.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCCategoryAPIManagerCall.h"
#import "SCRearCameraConfigModel.h"
#import "SCAlertViewController.h"
#import "SCTranslationService.h"
#import "SCActionService.h"
#import "SCAnchorActionSheetViewController.h"
#import "SCStoreScoreAlertViewController.h"
#import "SCFreeTimeCountdownView.h"
#import "SCAskGiftTipView.h"
#import "SCGiftAnimPlayView.h"
#import "SCPopupManager.h"
#import "SCAnchorEvaluateViewController.h"
#import "SCCallHangupPopup.h"
#import "SCWebViewController.h"
#import "SCThrottle.h"
#import "AGEmojiKeyboardView.h"

//Tools
#import "SCAVAudioSessionUtils.h"

//#import "UINavigationController+FDFullscreenPopGesture.h"

@interface SCVideoCallViewController ()<UITableViewDelegate,UITableViewDataSource,AgoraRtcEngineDelegate,UIScrollViewDelegate,AGEmojiKeyboardViewDelegate, AGEmojiKeyboardViewDataSource>

///用户信息
@property(nonatomic,weak) SCUserItemView * userInfoV;
//关闭按钮
@property(nonatomic,weak) UIButton *closeBtn;
//举报按钮
@property(nonatomic,weak) UIButton *reportBtn;
//关注按钮
@property(nonatomic,weak) UIButton *followBtn;
///远程视频View
@property(nonatomic,strong) UIView *remoteView;
///本地视频View
@property(nonatomic,strong) UIView *localView;
///切换前置摄像头按钮
@property(nonatomic,weak) UIButton *switchCameraBtn;
///后置摄像头切换按钮的红点&点击过后就不在显示
@property(nonatomic,strong) UIImageView *switchCameraRedDot;

@property(nonatomic,strong) AgoraRtcEngineKit *rtcEngine;
@property(nonatomic,strong) SCCallSessionModel *session;
@property(nonatomic,weak) SCJoinChannelProgressView *waitJoinView;
@property(nonatomic,strong) NSDictionary *targetUserDict;

//内容布局，会根据键盘来顶起
@property(nonatomic,strong) UIView *contentLayoutView;
////内容touchView
//@property(nonatomic,strong) UIView *contentTouchView;
///聊天布局
@property(nonatomic,strong) UITableView *chatListView;
///聊天内容
@property(nonatomic,strong) NSMutableArray<SCVideoCallChatMessageModel *> *chatList;
///聊天输入框
@property(nonatomic,strong) SCVideoCallChatInputView *chatInputView;
///底部高斯模糊，包含输入框，礼物和金币按钮
@property(nonatomic,strong) UIView *chatInputContainView;
///礼物横幅
@property(nonatomic,strong) SCGiftTipView *giftTipView;
@property(nonatomic,strong) SCAskGiftTipView *askGiftTipView;
//表情输入框容器
@property(nonatomic,strong) UIView *emojiBoardContainer;
//表情输入框
@property(nonatomic,strong) AGEmojiKeyboardView *emojiBoardView;

///消息框隐藏倒计时
@property(nonatomic,strong) NSTimer * hideChatTimer;
//是否正在打开键盘
@property(nonatomic,assign) BOOL isOpenKeyboard;
//是否打开Emoji输入框
@property(nonatomic,assign) BOOL isOpenEmoji;
//是否当前正在输入
@property(nonatomic,assign) BOOL isInputing;

@property(nonatomic,weak) SCCallDisablePrompt * disablePromptView;

///倒计时
@property(nonatomic,weak) SCVideoCallRemainTimeCountdownView *remainTimeCountView;

///后置摄像头
@property(nonatomic,strong) SCRearCameraConfigModel * rearCameraConfig;


///翻译
/// 翻译缓存
@property(nonatomic,strong) NSMutableDictionary<NSString *,NSString *> * translateCache;

//免费时长倒计时
@property(nonatomic,strong) NSTimer *freeTimeCountdownTimer;
@property(nonatomic,weak) SCFreeTimeCountdownView * freeTimeCountdownView;

//是否已经关闭 用于处理当前页面打开其他页面的情况下无法关闭
@property(nonatomic,assign) BOOL isClosed;
//当前是否显示
@property(nonatomic,assign) BOOL isShow;
//是否已经进行了pop 处理多次进行pop 情况
@property(nonatomic,assign) BOOL isPop;

// 用于记录之前的息屏状态
@property(nonatomic,assign) BOOL previousIdleTimerDisabled;

// 记录多少秒后关闭通话, 在FlashChat时，有 20s 免费通话，不用显示倒计时。
@property (nonatomic, assign) NSInteger estimateTime;

//记录对方的uid
@property (nonatomic, assign) NSUInteger taUid;
//记录等待第一帧的秒数
@property (nonatomic, assign) NSInteger waitFirstFrameTime;

@end





@implementation SCVideoCallViewController


-(Boolean) isIsInputing{
    return _isOpenEmoji || _isOpenKeyboard;
}

- (void)dealloc
{
    
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    kSCAuthCallService.isVideo = YES;
    // 保存之前的息屏状态
    _previousIdleTimerDisabled = [UIApplication sharedApplication].idleTimerDisabled;
    // 开启防息屏
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    // 融云消息静音
//    RCKitConfigCenter.message.disableMessageAlertSound = YES;
    
    self.isShow = YES;
   
    // 注册键盘通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:nil];
    [self.disablePromptView startListen];
    
    // 添加: 隐藏评价弹窗
    [self hideAnchorEvaluatePopupIfNeeded];
    
    // 检查是否需要关闭页面
    [self checkCloseWithIsActionClose:NO];
}
- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    // 禁用手势返回
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    }
    
    [self checkCloseWithIsActionClose:NO];
    
    // 添加: 隐藏评价弹窗
    [self hideAnchorEvaluatePopupIfNeeded];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    
    // 恢复手势返回
    if ([self.navigationController respondsToSelector:@selector(interactivePopGestureRecognizer)]) {
        self.navigationController.interactivePopGestureRecognizer.enabled = YES;
    }
    self.isShow = NO;
    _isOpenKeyboard = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
    [self.disablePromptView stopListen];
    // 恢复之前的息屏状态
    [UIApplication sharedApplication].idleTimerDisabled = _previousIdleTimerDisabled;
    
    kSCAuthCallService.isVideo = NO;
    
    [kSCKeyWindow endEditing:YES];
    kSCAuthCallService.callingShowObx.value = @(0);
    
    // 融云恢复消息提示音
//    RCKitConfigCenter.message.disableMessageAlertSound = NO;
}

- (instancetype)initWithSession:(SCCallSessionModel *)session
{
    self = [super init];
    if (self) {
        _session = session;
    }
    return self;
}


- (void)initData{
    [super initData];
    
    _chatList = [NSMutableArray new];
    _translateCache = [NSMutableDictionary new];
    _waitFirstFrameTime = -1;
}
- (void)viewWillLayoutSubviews{
    [super viewWillLayoutSubviews];
    //置顶
    [self.disablePromptView.superview bringSubviewToFront:self.disablePromptView];

}

- (void)initUI{
    [super initUI];
    
    [self _initDisablePromptUI];
   
    
    _remoteView = [UIView new].setBackgroundColor(UIColor.scBlack).addSuperView(self.view);
    
    _contentLayoutView = [UIView new].addSuperView(self.view);
    //聊天需要放在 视频上面，本地视频的下面
    [self _initChatUI];
    _localView =[UIView new].setCornerRadius(kSCNormalCornerRadius).setBackgroundColor(UIColor.scBlack).addSuperView(self.view);
    UIPanGestureRecognizer *panGesture = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGestureLocalView:)];
    [self.localView addGestureRecognizer:panGesture];
    [self _initGiftTipView];
    
    
    _switchCameraBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_switch_camera_small"] target:self action:@selector(onSwitchCamera)].addSuperView(self.localView);
    [_switchCameraBtn setBackgroundImage:[UIImage imageWithColor:[[UIColor scWhite] colorWithAlphaComponent:0.2]] forState:UIControlStateNormal];
    _switchCameraBtn.layer.masksToBounds = YES;
    _switchCameraBtn.layer.cornerRadius = 8;
    _switchCameraBtn.hidden = YES;
    _userInfoV = [[SCUserItemView alloc] init].addSuperView(self.view);
    _switchCameraRedDot = [UIImageView new].setBackgroundColor(UIColor.scRed).setCornerRadius(kSCScaleWidth(3)).addSuperView(self.localView);
    [self checkRedPoint];
    
    UIButton *followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_follow"] forState:UIControlStateNormal];
    [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_followed"] forState:UIControlStateSelected];
    [followBtn addTarget:self action:@selector(onFollow) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:followBtn];
    self.followBtn = followBtn;
    
    _closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close_white"] target:self action:@selector(onClose)].addSuperView(self.view);
    
    _reportBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_white_more"] target:self action:@selector(onReport)].addSuperView(self.view);
    
    [self.remoteView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    [self.contentLayoutView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.top.equalTo(self.view);
    }];
    
    [self.localView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(kSCScaleWidth(94), kSCScaleWidth(122)));
        make.trailing.equalTo(self.view).offset(-15.0f);
        make.top.equalTo(self.view).offset(60+kSCSafeAreaTopHeight);
    }];
    [self.switchCameraBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.localView);
        make.centerX.equalTo(self.localView);
        make.size.mas_equalTo(CGSizeMake(34, 16));
    }];
    //_switchCameraRedDot 显示在 switchCameraBtn 的右上角兼容阿拉伯
    [self.switchCameraRedDot mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.switchCameraBtn);
        make.size.mas_equalTo(CGSizeMake(kSCScaleWidth(6), kSCScaleWidth(6)));
    }];
    
    self.closeBtn.imageView.setSize(CGSizeMake(15, 15));
    self.closeBtn.backgroundColor = [UIColor sc50TranBlackBGColor];
    self.closeBtn.layer.cornerRadius = 14.0f;
    self.closeBtn.layer.masksToBounds = YES;
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.leading.equalTo(self.view).offset(15.0f);
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight);
    }];
    
    // 主播信息
    [self.userInfoV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.closeBtn.mas_trailing).offset(10.0f);
        make.centerY.equalTo(self.closeBtn);
        make.height.mas_equalTo(36.0f);
    }];
    
    self.reportBtn.imageView.setSize(CGSizeMake(20, 5));
    self.reportBtn.backgroundColor = [UIColor sc50TranBlackBGColor];
    self.reportBtn.layer.cornerRadius = 14.0f;
    self.reportBtn.layer.masksToBounds = YES;
    [self.reportBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.leading.equalTo(self.userInfoV.mas_trailing).offset(10.0f);
        make.centerY.equalTo(self.closeBtn);
    }];
    
    [followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.view).offset(-15);
        make.centerY.equalTo(self.closeBtn);
        make.size.mas_equalTo(CGSizeMake(36.0f, 36.0f));
    }];
        
    [self _initAgoraRtc];
    [self _setupLocalVideo];
    [self _joinChannel];
    [self showWaitJoinView];
    kWeakSelf(self);
    // 使用字典版本的API调用
    [SCAPIServiceManager requestUserInfoWithUserId:self.session.tagId cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(NSDictionary * _Nonnull userDict) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakself requestUserInfoResultWithDict:userDict];
        });
    } failure:nil];
    
    // 从策略字典中获取后置摄像头配置
    BOOL isCallRearCamera = [SCDictionaryHelper boolFromDictionary:kScAuthMar.strategyObs.value forKey:@"isCallRearCamera" defaultValue:NO];
    if(isCallRearCamera){
        //允许使用后置摄像头
        [SCAPIServiceManager requestRearCameraConfigWithSuccess:^(NSDictionary * _Nonnull cameraConfigDict) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakself requestRearCameraConfigResultWithDict:cameraConfigDict];
            });

        } failure:nil];
    }
    
    
}



// 字典版本的用户信息结果处理
- (void)requestUserInfoResultWithDict:(NSDictionary * _Nonnull)userDict{
    self.targetUserDict = userDict;
    [self updateUIWitUserInf];
}

- (void)requestRearCameraConfigReslut:(SCRearCameraConfigModel *)model {
    self.rearCameraConfig = model;
    self.switchCameraBtn.hidden = NO;
}

// 字典版本的后置摄像头配置结果处理
- (void)requestRearCameraConfigResultWithDict:(NSDictionary *)cameraConfigDict {
    // 为了兼容性，创建临时Model对象
    SCRearCameraConfigModel *model = [[SCRearCameraConfigModel alloc] init];
    model.isOpenCamera = [SCDictionaryHelper boolFromDictionary:cameraConfigDict forKey:@"isOpenCamera" defaultValue:NO];
    model.openCoins = [SCDictionaryHelper integerFromDictionary:cameraConfigDict forKey:@"openCoins" defaultValue:0];
    model.openDay = [SCDictionaryHelper integerFromDictionary:cameraConfigDict forKey:@"openDay" defaultValue:0];

    self.rearCameraConfig = model;
    self.switchCameraBtn.hidden = NO;
}

- (void)showFreeTimeCountdownView {
    if(self.session.isMatch && self.session.callFreeSeconds > 0){
        kWeakSelf(self);
        _freeTimeCountdownTimer = [NSTimer scheduledTimerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
            [weakself countdownTimerReslut];
        }];
    }
}

- (void)countdownTimerReslut{
    self.session.callFreeSeconds --;
    if(self.session.callFreeSeconds > 0){
        if(self.targetUserDict != nil){
            self.freeTimeCountdownView.hidden = NO;
            NSInteger unitPrice = [SCDictionaryHelper integerFromDictionary:self.targetUserDict forKey:@"unitPrice" defaultValue:0];
            [self.freeTimeCountdownView comfigWithSeconds:self.session.callFreeSeconds price:unitPrice];
        }else{
            self.freeTimeCountdownView.hidden = YES;
        }
        
    }else{
        //停止倒计时
        [self stopFreeTimeCountdownTimer];
        self.freeTimeCountdownTimer = nil;
        self.freeTimeCountdownView.hidden = YES;
        
        if (self.estimateTime > 0) {
            [self startRemainTimeCountdown:self.estimateTime];
        }
    }
}

-(void) stopFreeTimeCountdownTimer{
    if(_freeTimeCountdownTimer){
        [_freeTimeCountdownTimer invalidate];
        _freeTimeCountdownTimer = nil;
        
    }
    _freeTimeCountdownView.hidden = YES;
}

-(void) _initDisablePromptUI{
    self.disablePromptView = [[SCCallDisablePrompt alloc] init].addSuperView(self.view);
    [self.disablePromptView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}
     
 -(void) updateUIWitUserInf{
     [self.chatListView reloadData];
     BOOL isFriend = [SCDictionaryHelper boolFromDictionary:self.targetUserDict forKey:@"isFriend" defaultValue:NO];
     self.followBtn.selected = isFriend;
     // 直接使用字典版本的configModel方法
     [self.userInfoV configWithUserDict:self.targetUserDict];
}

-(void)_initChatUI{
    
    _chatInputContainView = [[UIView alloc]init].addSuperView(self.contentLayoutView);
    
    ///悬浮礼物
    UIButton *giftBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_gift_entrance"] target:self action:@selector(onGift)];
    [self.chatInputContainView addSubview:giftBtn];
    
    //金币按钮
    UIButton *coinBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_video_chat_coin"] target:self action:@selector(onCoin)];
    [self.chatInputContainView addSubview:coinBtn];
    
    // 创建表情键盘容器
    _emojiBoardContainer = [[UIView alloc] init];
    _emojiBoardContainer.hidden = YES;
    _emojiBoardContainer.alpha = 0;
    _emojiBoardContainer.backgroundColor = [UIColor scGlobalBgColor];
    [self.contentLayoutView addSubview:_emojiBoardContainer];

    // 创建表情键盘
    _emojiBoardView = [[AGEmojiKeyboardView alloc] initWithFrame:CGRectMake(0, 0, kSCScreenWidth, 227.5f) dataSource:self];
    _emojiBoardView.delegate = self;
    _emojiBoardView.backgroundColor = [UIColor scGlobalBgColor];
    [_emojiBoardContainer addSubview:_emojiBoardView];
    _chatListView = [UITableView tableViewWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self].addSuperView(self.contentLayoutView);
    self.chatListView.rowHeight = UITableViewAutomaticDimension;
    self.chatListView.estimatedRowHeight = 41;
    self.chatListView.backgroundColor = [UIColor clearColor];
    //倒序
    self.chatListView.transform = CGAffineTransformMakeScale(1, -1);
    
    kSCAddTapGesture(self.chatListView , self, onTapBG);
    
    _chatInputView = [[SCVideoCallChatInputView alloc] init].addSuperView(self.chatInputContainView);
    [_chatInputView.emojiBtn addTarget:self action:@selector(onEmoji) forControlEvents:UIControlEventTouchUpInside];
    kWeakSelf(self)
    [_chatInputView setSendMsgBlock:^(NSString * _Nonnull text) {
        if(text.length == 0){
            return;
        }
        weakself.chatInputView.inputTF.text = @"";
        __block BOOL isSuccess = NO;
        NSString *targetUserId = kSCUserIDFromDict(weakself.targetUserDict);
        [kScAuthMar.imService sendVideoTextMessageWithText:text targetId:targetUserId success:^(RCMessage * _Nonnull message) {
            if(!isSuccess){
                isSuccess = YES;
                //去重。因为双通道会回调两次
                [weakself sendVideoTextMessageReslut:message text:text];
                
            }
            
        } error:nil];
    }];
    
    [self.chatListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentLayoutView);
        make.trailing.equalTo(self.contentLayoutView).offset(-100);
        make.height.mas_equalTo(300);
        make.bottom.equalTo(self.chatInputContainView.mas_top);
    }];
   
    [self.chatInputContainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.emojiBoardContainer.mas_top);
        make.height.mas_greaterThanOrEqualTo(57.0f + kSCSafeAreaBottomHeight);
    }];
    
    [self.chatInputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.chatInputContainView).offset(8.0f);
        make.leading.equalTo(self.chatInputContainView).offset(15.0f);
        make.height.mas_greaterThanOrEqualTo(43.0f);
        make.bottom.equalTo(self.chatInputContainView).offset(-(7.0f + kSCSafeAreaBottomHeight));
    }];
    
    [giftBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.leading.equalTo(self.chatInputView.mas_trailing).offset(7.0f);
        make.bottom.equalTo(self.chatInputView).offset(-3);
    }];
    
    [coinBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.leading.equalTo(giftBtn.mas_trailing).offset(15.0f);
        make.trailing.equalTo(self.chatInputContainView).offset(-22.0f);
        make.bottom.equalTo(self.chatInputView).offset(-3);
    }];
    
    // 设置表情键盘容器约束
    [_emojiBoardContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentLayoutView);
        make.height.mas_equalTo(0); // 初始高度为0
        make.bottom.equalTo(self.contentLayoutView);
    }];

    // 设置表情键盘在容器内的约束
    [_emojiBoardView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.emojiBoardContainer);
        make.top.equalTo(self.emojiBoardContainer);
        make.bottom.equalTo(self.emojiBoardContainer).offset(-kSCSafeAreaBottomHeight);
        make.height.mas_equalTo(227.5f);
    }];
    
}

- (void)sendVideoTextMessageReslut:(RCMessage *)message text:(NSString *)text{
    
    
    SCVideoCallChatMessageModel *addMessage = [[SCVideoCallChatMessageModel alloc] init];
    addMessage.fromUserId = kSCCurrentUserID;
    addMessage.toUserId = message.targetId;
    addMessage.type = SCVideoCallChatMessageTypeText;
    addMessage.content = text;
    [self addChatMessage:addMessage];
    
}
//初始化礼物视图
-(void) _initGiftTipView{
    _askGiftTipView = [[SCAskGiftTipView alloc] init].addSuperView(self.view);
    [_askGiftTipView hideCompletion:nil];
    kWeakSelf(self)
    [_askGiftTipView setSendGiftBlock:^(NSString * _Nonnull giftCode) {
        [weakself setSendGiftReslut:giftCode];
    }];
    _giftTipView = [[SCGiftTipView alloc] init].addSuperView(self.view);
    _freeTimeCountdownView = [[SCFreeTimeCountdownView alloc] init].addSuperView(self.view);
    self.freeTimeCountdownView.hidden = YES;
    
    
    [self.askGiftTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentLayoutView);
        make.leading.equalTo(self.view).offset(15);
        make.width.lessThanOrEqualTo(self.view);
    }];
    [self.giftTipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(15);
        make.bottom.equalTo(self.askGiftTipView.mas_top);
        make.height.mas_equalTo(0);
    }];
    
    [self.freeTimeCountdownView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.bottom.mas_equalTo(self.chatInputContainView.mas_top).offset(-8);
        make.leading.equalTo(self.view).offset(20);
        make.trailing.equalTo(self.view).offset(-20);
    }];
    

}

- (void)setSendGiftReslut:(NSString *)giftCode{
    // 使用字典版本的礼物获取
    NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:giftCode];
    NSInteger coinPrice = kSCGiftCoinPriceFromDict(giftDict);
    if(coinPrice > kScAuthMar.availableCoinsObx.value.integerValue){
        [self toCoinsAndToastCoinsNotWithEntry:SCPayEntry.shared.kPayEntrySourceVideoAskForGift];
        return;
    }
    // 使用同一个 strongSelf 的 weak 引用
    kWeakSelf(self)
    if (weakself == nil) {
        return;
    }
    [SCThrottle throttleWithTag:kSCThrottleSendGiftKey duration:10 onExecute:^{
        [weakself sendMessageWithGiftDict:giftDict];
    }];
}

// 字典版本的礼物消息发送方法
- (void)sendMessageWithGiftDict:(NSDictionary *)giftDict{
    kWeakSelf(self)
    NSString *giftCode = kSCGiftCodeFromDict(giftDict);
    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
    [kSCAuthGiftService sendMessageWithGiftCode:giftCode
                                         userId:targetUserId
                                    changelName:self.session.channelName
                                        success:^(NSInteger coins) {
        // 确保在主线程执行 UI 操作
        dispatch_async(dispatch_get_main_queue(), ^{
            //播放动画
            [weakself sendMessageWithGiftCodeReslut:coins giftCode:giftCode];
        });
    } failure:^(SCXErrorModel * _Nonnull error) {
        
        [weakself sc_blank_empty];
        [SCThrottle cancelWithTag:kSCThrottleSendGiftKey];
        dispatch_async(dispatch_get_main_queue(), ^{
            [kSCKeyWindow toast:@"Failed to send gift, please try again later".translateString];
        });
    }];
}

// 保留原方法以兼容旧代码
//- (void)sendMessageWithGiftCode:(SCGiftModel *)giftModel{
//    kWeakSelf(self)
//    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
//    [kSCAuthGiftService sendMessageWithGiftCode:giftModel.code
//                                         userId:targetUserId
//                                    changelName:self.session.channelName
//                                        success:^(NSInteger coins) {
//        // 确保在主线程执行 UI 操作
//        dispatch_async(dispatch_get_main_queue(), ^{
//            //播放动画
//            [weakself sendMessageWithGiftCodeReslut:coins giftCode:giftModel.code];
//        });
//    } failure:^(SCXErrorModel * _Nonnull error) {
//        
//        [weakself sc_blank_empty];
//        [SCThrottle cancelWithTag:kSCThrottleSendGiftKey];
//        dispatch_async(dispatch_get_main_queue(), ^{
//            [kSCKeyWindow toast:@"Failed to send gift, please try again later".translateString];
//        });
//    }];
//}

- (void)sendMessageWithGiftCodeReslut:(NSInteger)coins giftCode:(NSString *)giftCode{
    [SCGiftAnimPlayView show:giftCode inView:self.view];
    
    //礼物发送完成，记录消息
    SCVideoCallChatMessageModel *message = [[SCVideoCallChatMessageModel alloc] init];
    message.fromUserId = kSCCurrentUserID;
    message.toUserId = kSCUserIDFromDict(self.targetUserDict);
    message.type = SCVideoCallChatMessageTypeGift;
    message.giftCode = giftCode;
    message.giftNum = 1;
    [self addChatMessage:message];
    [self.askGiftTipView hideCompletion:^(BOOL finished) {
        [SCThrottle cancelWithTag:kSCThrottleSendGiftKey];
    }];
}

- (void)sc_blank_empty{
    
}

///初始化声网
-(void)_initAgoraRtc{
    [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayAndRecord withMode:AVAudioSessionModeVideoChat error:NULL];
    NSString *rtck = [SCDictionaryHelper configItemDataFromDict:kScAuthMar.appConfig itemName:@"rtck"];
    if (![rtck isKindOfClass:[NSString class]]) {
        rtck = @"";
    }
    _rtcEngine = [AgoraRtcEngineKit sharedEngineWithAppId:rtck delegate:self];
    //必须设置为通信场景
    [self.rtcEngine setChannelProfile:AgoraChannelProfileCommunication];
}
//设置本地视频
- (void)_setupLocalVideo {
// 启用视频模块
    [self.rtcEngine enableVideo];
    UIView *videoView = [UIView new].addSuperView(self.localView);
    AgoraRtcVideoCanvas *videoCanvas = [[AgoraRtcVideoCanvas alloc] init];
    videoCanvas.uid = 0;
    videoCanvas.renderMode = AgoraVideoRenderModeHidden;
    videoCanvas.view = videoView;
    // 设置本地视图
    [self.rtcEngine setupLocalVideo:videoCanvas];
    [videoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.localView);
    }];
    [self.localView bringSubviewToFront:self.switchCameraBtn];
    [self.localView bringSubviewToFront:self.switchCameraRedDot];
}
//加入频道
-(void) _joinChannel{
    NSString *myUserId = kSCCurrentUserID;
    kWeakSelf(self);
    [self.rtcEngine joinChannelByToken:self.session.rtcToken channelId:self.session.channelName userAccount:myUserId joinSuccess:^(NSString * _Nonnull channel, NSUInteger uid, NSInteger elapsed) {
        [weakself joinChannelByTokenRelsult:channel uid:uid];
    }];
}

- (void)joinChannelByTokenRelsult:(NSString *)channel uid:(NSUInteger)uid{
    if(self.delegate){
        [self.delegate videoCall:self joinChannelSucess:channel uid:uid];
    }
}

//退出
-(void)close{
    kSCAuthCallService.preViewController = [self previousViewController];
    [self stopFreeTimeCountdownTimer];
    [self.rtcEngine leaveChannel:nil];
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // 执行 Agora 销毁
        [AgoraRtcEngineKit destroy];
    });
    
    // 检查是否有 WebViewController
    if ([self hasWebViewControllerInPresentedStack]) {
        // 如果有 WebViewController，记录需要关闭的状态
        self.isClosed = YES;
        // 等待 viewWillAppear 时再关闭
        return;
    }

    self.isClosed = YES;
    //关闭基于当前页面打开的全部页面
    kWeakSelf(self);
    [self dismissPresentedViewControllersWithCompletion:^{
        [weakself checkCloseWithIsActionClose:YES];
    }];
}
//检测是否关闭页面
-(void)checkCloseWithIsActionClose:(BOOL)isActionClose{
    if(!self.isClosed || !self.isShow){
        return;
    }
    
    // 如果还有 WebViewController 在显示，等待下一次 viewWillAppear
    if ([self hasWebViewControllerInPresentedStack]) {
        return;
    }
    
    kWeakSelf(self);
    void(^onClose)(void) = ^{
        [weakself onCloseReslut];
    };
    
    if(isActionClose){
        onClose();
    }else{
        //关闭基于当前页面打开的全部页面
        [self dismissPresentedViewControllersWithCompletion:^{
            onClose();
        }];
    }
    
    
    
}

- (void)onCloseReslut{
    if (self.isPop) {
        return;
    }
    self.isPop = YES;
    [kSCKeyWindow endEditing:YES];
    if(self.session.isMatch){
        if(self.session.isFree && self.session.callFreeSeconds > 0){
            //如果免费时长内退出则重新匹配
            [self.navigationController popViewControllerAnimated:YES];
        }else{
            //直接回退到 匹配首页
            [self.navigationController popToRootViewControllerAnimated:YES];
        }
    }else{
        
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

//显示等待加入框
-(void) showWaitJoinView{
    if(self.waitJoinView == nil){
        SCJoinChannelProgressView *waitJoinView = [[SCJoinChannelProgressView alloc] initWithFrame:self.view.bounds];
        waitJoinView.userInteractionEnabled = NO;
        [self.view addSubview:waitJoinView];
        self.waitJoinView = waitJoinView;
        [self.waitJoinView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.view);
        }];
    }
    
}

-(void) hideWaitJoinView{
    // 加入成功后才开始倒计时
    [self showFreeTimeCountdownView];
    
    if(self.waitJoinView != nil){
        [self.waitJoinView removeFromSuperview];
        self.waitJoinView = nil;
    }
}

//开始隐藏倒计时
-(void)startHideChatTimer{
    [self stopHideChatTimer];
    kWeakSelf(self);
    self.hideChatTimer = [NSTimer scheduledTimerWithTimeInterval:5.0 repeats:NO block:^(NSTimer * _Nonnull timer) {
        [timer invalidate];
        
        if(weakself != nil){
           
            if(weakself.isOpenEmoji || weakself.isOpenKeyboard){
                return;
            }
            //隐藏
            weakself.chatListView.alpha = 0;
        }
    }];
    
}
//停止倒计时
-(void)stopHideChatTimer{
    if(self.hideChatTimer){
        [self.hideChatTimer invalidate];
        self.hideChatTimer = nil;
    }
    self.chatListView.alpha = 1;
}


//隐藏Emoji键盘
-(void)hideEmoji{
    self.isOpenEmoji = NO;

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.emojiBoardContainer.alpha = 0;
        [self.emojiBoardContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
        [self.chatInputContainView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.emojiBoardContainer.mas_top);
        }];
        [self.view layoutIfNeeded];
    } completion:^(BOOL finished) {
        [self.emojiBoardContainer setHidden:YES];
        if(!self.isOpenKeyboard){
            [self startHideChatTimer];
        }
    }];
}

//显示Emoji
-(void)showEmoji{
    self.isOpenEmoji = YES;
    self.emojiBoardContainer.alpha = 0;
    [self.emojiBoardContainer setHidden:NO];

    // 添加动画
    [UIView animateWithDuration:0.25 animations:^{
        self.emojiBoardContainer.alpha = 1;
        [self.emojiBoardContainer mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(227.5f + kSCSafeAreaBottomHeight);
        }];
        [self.chatInputContainView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.emojiBoardContainer.mas_top).offset(kSCSafeAreaBottomHeight);
        }];
        [self.view layoutIfNeeded];
    }];

    [self stopHideChatTimer];
}

// 键盘弹出时的处理方法
- (void)keyboardWillShow:(NSNotification *)notification {
    if(self.isOpenEmoji){
        //收起Emoji键盘
        [self hideEmoji];
    }
    // 获取键盘的信息
    NSDictionary *info = notification.userInfo;
    CGRect keyboardFrame = [info[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;
    _isOpenKeyboard = YES;
    kWeakSelf(self);
    // 动画地调整输入框的位置
    [UIView animateWithDuration:0.3 animations:^{
        [weakself.contentLayoutView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view).offset(-(keyboardHeight - kSCSafeAreaBottomHeight));
        }];
        [weakself.view layoutIfNeeded];
    }];
    [self stopHideChatTimer];
}
- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event{
    [super touchesBegan:touches withEvent:event];
    [self onTapBG];
}


// 键盘隐藏时的处理方法
- (void)keyboardWillHide:(NSNotification *)notification {
    _isOpenKeyboard = NO;
    kWeakSelf(self);
    // 恢复输入框的初始位置
    [UIView animateWithDuration:0.3 animations:^{
        [weakself.contentLayoutView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view).offset(0);
        }];
        [weakself.view layoutIfNeeded];
    }];
[self startHideChatTimer];
}

#pragma mark 判断红点显示逻辑
///检查是否显示红点
-(void)checkRedPoint{
    //读取本地存储状态  是否已经点击过后置摄像头按钮
    BOOL isHaseClick = [[NSUserDefaults standardUserDefaults] boolForKey:kSCIsClickBackCameraKey];

    if(!isHaseClick){
        self.switchCameraRedDot.hidden = NO;
    }else{
        self.switchCameraRedDot.hidden = YES;
    }
}
///保存红点状态
-(void)saveRedPointStatus{
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kSCIsClickBackCameraKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (UIViewController *)previousViewController {
    // 获取前一个控制器，用作弹引导充值弹窗
    UIViewController *previousVC = self.navigationController.presentingViewController;
    // 如果 previousVC 是导航控制器，获取其根视图控制器
    if ([previousVC isKindOfClass:[UINavigationController class]]) {
        previousVC = ((UINavigationController *)previousVC).viewControllers.lastObject;
    }
    return previousVC;
}

#pragma mark - Action
-(void)handlePanGestureLocalView:(UIPanGestureRecognizer *)gesture{
    CGPoint translation = [gesture translationInView:self.localView];
    CGFloat offX = gesture.view.center.x + translation.x;
    CGFloat offY = gesture.view.center.y + translation.y;
    
    CGFloat width = self.localView.frame.size.width;
    CGFloat height = self.localView.frame.size.height;
    
    if(offX - (width/2.0) < 15){
        //超出屏幕
        offX = 15 + (width/2.0);
    }else if(offX + (width/2.0) > self.view.bounds.size.width - 15){
        //超出屏幕
        offX = self.view.bounds.size.width - 15 -  (width/2.0);
    }
    
    if(offY - (height/2.0f) < self.userInfoV.scBottom){
        offY = self.userInfoV.scBottom + (height/2.0f);
    }else if(offY + (height/2.0f) > self.chatInputContainView.scTop){
        offY = self.chatInputContainView.scTop - (height/2.0f);
    }
    
    CGPoint newCenter = CGPointMake(offX, offY);
    gesture.view.center = newCenter;
    [gesture setTranslation:CGPointZero inView:self.view];
}

-(void)onGift{
    [self.chatInputView.inputTF endEditing:YES];
    kWeakSelf(self);
    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
    SCGiftPopupViewController *vc = [SCGiftPopupViewController showWithFromVC:self userId:targetUserId channelName:self.session.channelName isMulti:NO hideService:YES isFromVideoCall:YES];
    [vc.giftSendNumChangeObs afterSubscribe:^(SCGiftSendNumModel * _Nullable value) {
        [weakself giftSendNumChangeObsReslut:value];
    } error:nil disposeBag:self.disposeBag];
    [vc.giftCompleteObs afterSubscribe:^(SCGiftSendNumModel * _Nonnull value) {
        //礼物发送完成，记录消息
        [weakself giftCompleteObsReslut:value];
    } error:nil disposeBag:self.disposeBag];
    
}

- (void)giftSendNumChangeObsReslut:(SCGiftSendNumModel *)value{
    if(value != nil){
        //刷新数量
        [self.giftTipView showWithGiftNum:value];
    }
   
}


- (void)giftCompleteObsReslut:(SCGiftSendNumModel *)value{
    SCVideoCallChatMessageModel *message = [[SCVideoCallChatMessageModel alloc] init];
    message.fromUserId = kSCCurrentUserID;
    message.toUserId = kSCUserIDFromDict(self.targetUserDict);
    message.type = SCVideoCallChatMessageTypeGift;
    message.giftCode = [SCDictionaryHelper stringFromDictionary:value.giftModel forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:@""];
    message.giftNum = value.giftNum;
    [self addChatMessage:message];
}

- (void)onCoin {
    [self.view endEditing:YES];
    [SCCoinsPopupViewController showWithFromVC:self hideService:YES entry:SCPayEntry.shared.kPayEntrySourceChatting];
}

-(void) onClose{
    [self.view endEditing:YES];
    kWeakSelf(self);
    [SCCallHangupPopup showInViewController:self confirmBlock:^{
        
        [weakself hangupPopupResult];
    }];
}

- (void)hangupPopupResult{
    self.isPop = NO;
    // 获取前一个控制器，用作弹引导充值弹窗
    kSCAuthCallService.preViewController = [self previousViewController];
    [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonNormal];
    
    // 在主线程延迟一帧执行关闭操作
    kWeakSelf(self)
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakself close];
    });
}

-(void)onReport{
    kWeakSelf(self);
    // 直接使用字典版本的showMoreActionSheetWithFromVC方法
    NSMutableDictionary *mutableUserDict = [self.targetUserDict mutableCopy];
    [SCAnchorActionSheetViewController showMoreActionSheetWithFromVC:self userDict:mutableUserDict actionBlock:^(NSInteger index) {
        // 将更新后的字典同步回targetUserDict
        weakself.targetUserDict = [mutableUserDict copy];
        [weakself updateUIWitUserInf];
    }];
}

-(void)onSwitchCamera{
    //处理红点逻辑
    [self saveRedPointStatus];
    [self checkRedPoint];
    BOOL wantOpenRearcamera = !self.switchCameraBtn.isSelected;
    if(wantOpenRearcamera){
        if(self.rearCameraConfig.isOpenCamera){
            //如果已经开启那么直接开启
            [self toSwitchCamera];
        }else{
            kWeakSelf(self);
            //弹框提示
            [SCAlertViewController showTrunOnRearCameraWithFromVC:self price:self.rearCameraConfig.openCoins days:self.rearCameraConfig.openDay leftBlock:^(SCAlertViewController * _Nonnull aler) {
                [aler dismissViewControllerAnimated:YES completion:nil];
            } rightBlock:^(SCAlertViewController * _Nonnull aler) {
                [aler dismissViewControllerAnimated:YES completion:nil];
                
                [weakself trunOnRearCameraReslut];
            }];
        }
        
        
    }else{
        //切换到前置，无需判断
        [self toSwitchCamera];
    }
}

- (void)trunOnRearCameraReslut{
    if(self.rearCameraConfig.openCoins > kScAuthMar.availableCoinsObx.value.integerValue){
        //判断金币是否足够
        kWeakSelf(self)
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 这里是您希望在主线程上执行的代码
            [weakself toCoinsAndToastCoinsNotWithEntry:SCPayEntry.shared.kPayEntrySourceSwitchCamera];
        });
        
        return;
    }
    kWeakSelf(self)
    [SCAPIServiceManager requestOpenRearCameraWithSuccess:^{
        [weakself requestOpenRearCameraResult];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        [kSCKeyWindow toast:@"Open Rear Camera Failed".translateString];
    }];
}

- (void)requestOpenRearCameraResult{
    [kSCKeyWindow toast:@"Buy successfull".translateString];
    [self toSwitchCamera];
    //标记为已经打开
    self.rearCameraConfig.isOpenCamera = YES;
    //刷新接口缓存
    [SCAPIServiceManager requestRearCameraConfigWithSuccess:^(NSDictionary * _Nonnull cameraConfigDict) {
        // 刷新缓存，不需要处理结果
    } failure:nil];
}

-(void)toSwitchCamera{
    if([self.rtcEngine switchCamera] == 0){
        //切换成功
        self.switchCameraBtn.selected = !self.switchCameraBtn.selected;
    }else{
        [kSCKeyWindow toast:@"Switch Camera Failed".translateString];
    }
}

-(void) translateWithText:(NSString *) text{
    if(text.length == 0){
        return;
    }
    if(self.translateCache[text] != nil){
        ///如果已经翻译过则无需再翻译
        return;
    }
    kWeakSelf(self);
    [kSCAuthTranslaService translateNoCachedText:text completion:^(NSString * _Nonnull translateStr, SCXErrorModel * _Nonnull error) {
        [weakself translateNoCachedResult:translateStr text:text];
    }];
}

- (void)translateNoCachedResult:(NSString *)translateStr text:(NSString *)text{
    self.translateCache[text] = translateStr;
    [self.chatListView reloadData];
}

-(void)onFollow{
    
    //关闭键盘和输入框
    [self.view endEditing:YES];
    [self hideEmoji];
    
    //先修改再请求接口
    BOOL currentIsFriend = [SCDictionaryHelper boolFromDictionary:self.targetUserDict forKey:@"isFriend" defaultValue:NO];
    self.followBtn.selected = !currentIsFriend;
    if(self.targetUserDict == nil){
        return;
    }
    kWeakSelf(self);
    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
    if(currentIsFriend){
        //取消关注
        [kSCAuthActionService requestUnFollowWithUserId:targetUserId success:^{
            [weakself requestUnFollowReslut];
        } failure:nil];
    }else{
        //关注
        [kSCAuthActionService requestFollowWithUserId:targetUserId success:^{
            [weakself requestFollowReslut];
        } failure:nil];
    }
    
}

- (void)requestUnFollowReslut{
    // 更新字典中的关注状态
    NSMutableDictionary *mutableDict = [self.targetUserDict mutableCopy];
    mutableDict[@"isFriend"] = @(NO);
    self.targetUserDict = [mutableDict copy];

    // 使用字典版本的API调用进行缓存刷新
    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
    [SCAPIServiceManager requestUserInfoWithUserId:targetUserId cachePolicy:SCNetCachePolicyCacheAndRefresh success:nil failure:nil];
    [kSCKeyWindow toast:@"UnFollow Successfully".translateString];
}

- (void)requestFollowReslut{
    // 更新字典中的关注状态
    NSMutableDictionary *mutableDict = [self.targetUserDict mutableCopy];
    mutableDict[@"isFriend"] = @(YES);
    self.targetUserDict = [mutableDict copy];

    // 使用字典版本的API调用进行缓存刷新
    NSString *targetUserId = kSCUserIDFromDict(self.targetUserDict);
    [SCAPIServiceManager requestUserInfoWithUserId:targetUserId cachePolicy:SCNetCachePolicyCacheAndRefresh success:nil failure:nil];
    [kSCKeyWindow toast:@"Follow Successfully".translateString];
}

-(void)onEmoji{
    _isOpenEmoji = !_isOpenEmoji;
    if(_isOpenEmoji){
        [self showEmoji];
        //收起键盘
        if(_isOpenKeyboard){
            [self.view endEditing:YES];
        }
    }else{
        [self hideEmoji];
        //打开键盘
        [self.chatInputView.inputTF becomeFirstResponder];
    }
    
}

-(void)onTapBG{
    //关闭键盘和输入框
    if(self.isOpenKeyboard || self.isOpenEmoji){
        [self.chatInputView.inputTF endEditing:YES];
        [self hideEmoji];
    }else{
        
    }
    if(self.chatListView.alpha == 1){
        //如果消息已经显示
        self.chatListView.alpha = 0;
    }else{
        [self startHideChatTimer];
    }
    
}


#pragma mark - 可用时长倒计时
-(void)startRemainTimeCountdown:(NSInteger) duration{
    if(self.remainTimeCountView == nil){
        self.remainTimeCountView = [[SCVideoCallRemainTimeCountdownView alloc] init].addSuperView(self.view);
        kWeakSelf(self);
        self.remainTimeCountView.rechTapBlock = ^{
            [weakself toCoinsAndToastCoinsNotWithEntry:SCPayEntry.shared.kPayEntrySourceCoinNotEnough];
        };
    }
    if(self.remainTimeCountView.superview == nil){
        self.remainTimeCountView.addSuperView(self.view);
    }
    ///图层排前
    [self.remainTimeCountView.superview bringSubviewToFront:self.remainTimeCountView];
    self.remainTimeCountView.duration = duration;
    [self.remainTimeCountView show];
}

//转跳金币页面并toast金币不足
- (void)toCoinsAndToastCoinsNotWithEntry:(NSString *)entry {
    //提示余额不足
    [kSCKeyWindow toast:@"Coins not enough".translateString];
    [SCCoinsPopupViewController showWithFromVC:self hideService:YES entry:entry];
}

// 添加新方法来处理评价弹窗
- (void)hideAnchorEvaluatePopupIfNeeded {
    // 遍历当前活跃的弹窗
    for (UIViewController *popup in [SCPopupManager shared].activePopups) {
        if ([popup isKindOfClass:[SCAnchorEvaluateViewController class]]) {
            // 找到评价弹窗后，使用 PopupManager 将其关闭
            [[SCPopupManager shared] dismissPopup:popup];
        }
    }
}

// 检查当前页面的 present 栈中是否有 UIWebViewController
- (BOOL)hasWebViewControllerInPresentedStack {
    UIViewController *presentedVC = [UIViewController currentViewController];
    while (presentedVC != nil) {
        if ([presentedVC isKindOfClass:[SCWebViewController class]]) {
            return YES;
        }
        presentedVC = presentedVC.presentedViewController;
    }
    return NO;
}


#pragma ===SCVideoCallAgoraRtc
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didJoinedOfUid:(NSUInteger)uid elapsed:(NSInteger)elapsed{
    
    //加入频道成功
    //设置远程视频
    AgoraRtcVideoCanvas *videoCanvas = [[AgoraRtcVideoCanvas alloc] init];
    videoCanvas.uid = uid;
    videoCanvas.renderMode = AgoraVideoRenderModeHidden;
    videoCanvas.view = self.remoteView;
    // 设置远端视图
    _taUid = uid;
    _waitFirstFrameTime = 0;
    [self.rtcEngine setupRemoteVideo:videoCanvas];

}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine firstRemoteVideoFrameOfUid:(NSUInteger)uid size:(CGSize)size elapsed:(NSInteger)elapsed{
    //获取到首帧 PS:是后面修改的逻辑，只有拿到首帧图片才算是加入成功
    if(self.delegate && [self.delegate respondsToSelector:@selector(videoCall:tagJoinChannelSuccess:uid:)]){
        [self.delegate videoCall:self tagJoinChannelSuccess:self.session.channelName uid:uid];
    }
    [self hideWaitJoinView];
}

- (void)rtcEngine:(AgoraRtcEngineKit *)engine connectionChangedToState:(AgoraConnectionState)state reason:(AgoraConnectionChangedReason)reason{
    if(state == AgoraConnectionStateFailed && reason == AgoraConnectionChangedReasonBannedByServer){
        [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonClientException];
    }
}
- (void)rtcEngine:(AgoraRtcEngineKit *)engine didOfflineOfUid:(NSUInteger)uid reason:(AgoraUserOfflineReason)reason{
    //离开频道
    //延迟3秒执行
    kWeakSelf(self)
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        //判断是否已经结束
        if( weakself.session == nil || weakself.session.status == SCCallSessionStatusHangUp){
            return;
        }
        
        [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonRemoteUserLeft];
    });
}
- (void)rtcEngineConnectionDidLost:(AgoraRtcEngineKit *)engine{
    [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonNetworkException];
}

#pragma ===SCCallService

//接听
-(void) onPickUpWithSession:(SCCallSessionModel *)session{
    
}
//挂断
-(void) onHangUpWithSession:(SCCallSessionModel *)session{
    if (session.hangUpReason == SCCallHangUpReasonRemoteUserLeft) {
        [kSCKeyWindow toast:@"User has hung up".translateString];
    }
    [self close];
}
//创建Channel失败
-(void) onCreatChannelFailWithSession:(SCCallSessionModel *)session{
    [kSCKeyWindow toast:@"Network error, please try again".translateString];
    [self close];
}
//创建Channel成功
-(void) onCreatChannelSuccessWithSession:(SCCallSessionModel *)session{
    
}

- (void)onCallCountDown:(NSInteger)countDown session:(SCCallSessionModel *)session {
    
}

//超时
-(void) onTimeOutWithSession:(SCCallSessionModel *)session{
    [kSCKeyWindow toast:@"Network error, please try again".translateString];
    [self close];
}
//joinChannel倒计时
-(void) onJoinChannelCountDown:(NSInteger) countDown session:(SCCallSessionModel *)session{
    
    
    if(_waitFirstFrameTime == 5){//当加入倒计时超过6秒的时候 还没join(当前join是按照第一帧来计算)进入，则需要关掉对方的视频流然后再拉取
        if(self.session.status == SCCallSessionStatusWaitJoin && _taUid != 0){
            [self.rtcEngine muteRemoteVideoStream:_taUid mute:YES];
            [self.rtcEngine muteRemoteVideoStream:_taUid mute:NO];
            _waitFirstFrameTime ++;
        }
        
    }else{
        if(_waitFirstFrameTime >= 0){
            _waitFirstFrameTime ++;
        }
    }
    
}
//joinChannel 超时
-(void) onJoinChannelTimeOutWithSession:(SCCallSessionModel *)session{
    [kSCKeyWindow toast:@"Network error, please try again".translateString];
    [kSCAuthCallService doHangUpWithReason:SCCallHangUpReasonNetworkException];
    kWeakSelf(self)
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakself close];
    });
}
//joinChannel 成功
-(void) onJoinChannelSuccessWithSession:(SCCallSessionModel *)session{
    //对方加入成功
    [self hideWaitJoinView];
}

// 字典版本的索取礼物消息回调
- (void)onGiftAskEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session{
    //收到索取礼物消息 - 字典版本
    SCVideoCallChatMessageModel *message = [[SCVideoCallChatMessageModel alloc] init];
    message.fromUserId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"fromUserId" defaultValue:@""];
    message.toUserId = kSCCurrentUserID;
    message.type = SCVideoCallChatMessageTypeAskGift;

    // 从字典中获取content对象，然后获取code
    NSString *contentString = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"content" defaultValue:nil];
    if (!kSCIsStrEmpty(contentString)) {
        NSDictionary *contentDict = [SCDataConverter safeDictionaryFromJSONString:contentString];
        if (contentDict) {
            message.giftCode = [SCDictionaryHelper stringFromDictionary:contentDict forKey:@"code" defaultValue:@""];
        }
    }

    //UI线程执行
    kWeakSelf(self)
    dispatch_async(dispatch_get_main_queue(), ^{
        [weakself onGiftAskResult:message];
    });
}

- (void)onGiftAskResult:(SCVideoCallChatMessageModel *)message{
    if (self == nil) {
        return;
    }
    // 使用字典版本的setModel方法
    [self.askGiftTipView setModel:message userDict:self.targetUserDict];
    [self.askGiftTipView show];
}

// 字典版本的聊天消息回调
- (void)onChatEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session{
    //接收到1v1聊天消息 - 字典版本
    SCVideoCallChatMessageModel *message = [[SCVideoCallChatMessageModel alloc] init];
    message.fromUserId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"fromUserId" defaultValue:@""];
    message.toUserId = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"toUserId" defaultValue:@""];
    message.type = SCVideoCallChatMessageTypeText;
    message.content = [SCDictionaryHelper stringFromDictionary:eventDict forKey:@"content" defaultValue:@""];
    [self addChatMessage:message];
}

// 字典版本的预计挂断时间回调
- (void)onEstimatedHangUpTimeEventDict:(NSDictionary *)eventDict successWithSession:(SCCallSessionModel *)session{
    NSInteger estimateTime = [SCDictionaryHelper integerFromDictionary:eventDict forKey:@"estimateTime" defaultValue:0];
    
    if(estimateTime <= 60 && estimateTime > 0) {
        if (self.session.isMatch && self.session.callFreeSeconds > 0) {
            self.estimateTime = estimateTime - self.session.callFreeSeconds;
        } else {
            self.estimateTime = 0;
            [self startRemainTimeCountdown:estimateTime];
        }
    } else {
        // 大于 60, 可能存在的情况：提示金币不足后，充值成功，需要隐藏倒计时弹窗。
        if (self.remainTimeCountView != nil) {
            [self.remainTimeCountView hide];
        }
    }
}

-(void)addChatMessage:(SCVideoCallChatMessageModel *)message{
    kWeakSelf(self);
    dispatch_async(dispatch_get_main_queue(), ^{
        // 用户自己发的消息不用翻译
        [weakself addChatMessageResult:message];
    });
    
}

- (void)addChatMessageResult:(SCVideoCallChatMessageModel *)message{
    if(message.type == SCVideoCallChatMessageTypeText && ![message.fromUserId isEqualToString:kSCCurrentUserID]){
        //如果是文本消息则需要翻译
        [self translateWithText:message.content];
    }
        
    
    [self.chatList insertObject:message atIndex:0];
    [self.chatListView reloadData];
    //滑动到最底部
    [self _scrollToBottom];
    if(self.isOpenKeyboard || self.isOpenEmoji){
        [self stopHideChatTimer];
    }else{
        [self startHideChatTimer];
    }
}

//滑动到最底部
-(void) _scrollToBottom{
    if (self.chatList.count > 0) {
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:0 inSection:0];
        [self.chatListView scrollToRowAtIndexPath:indexPath 
                                atScrollPosition:UITableViewScrollPositionTop 
                                      animated:YES];
    }
}


#pragma mark - TableView & SCVideoCallChatCellDelegate

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCVideoCallChatCell *cell = [SCVideoCallChatCell initWithFormTableView:tableView];
    
    if(indexPath.row < self.chatList.count){
        SCVideoCallChatMessageModel * model = self.chatList[indexPath.row];
        // 根据消息发送者获取用户信息字典
        NSDictionary *userDict;
        if ([model.fromUserId isEqualToString:kSCCurrentUserID]) {
            userDict = kSCCurrentUserInfoDict;
        } else {
            userDict = self.targetUserDict;
        }
        // 使用字典版本的setModel方法
        [cell setModel:model userDict:userDict translateText:[self.translateCache objectForKey:model.content]];
    }
    return cell;
}


- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.chatList count];
}

// 添加以下方法来处理点击事件
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
    // 触发背景点击事件
    [self onTapBG];
}

#pragma mark - UIScrollViewDelegate

//判断是否正在滑动
- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView{
    //停止倒计时
    [self stopHideChatTimer];
    
}
//是否停止滑动
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    //开始倒计时
    if(self.isOpenKeyboard || self.isOpenEmoji){
        return;
    }
    [self startHideChatTimer];
}

#pragma mark - AGEmojiKeyboardViewDelegate
///*!
// 点击表情的回调
//
// @param emojiView 表情输入的View
// @param string    点击的表情对应的字符串编码
// */
//- (void)didTouchEmojiView:(RCEmojiBoardView *)emojiView touchedEmoji:(NSString *)string{
//    //输入框加入表情内容
//    if(string == nil){
//        //删除一个Emoji表情
//        if(self.chatInputView.inputTF.text.length > 0){
//            [self.chatInputView.inputTF setText:[self.chatInputView.inputTF.text removedLastString]];
//        }
//        
//    }else{
//        [self.chatInputView.inputTF setText:[NSString stringWithFormat:@"%@%@",self.chatInputView.inputTF.text,string]];
//    }
//    self.chatInputView.placeholderLabel.hidden = self.chatInputView.inputTF.text.length > 0;
//}
//
///*!
// 点击发送按钮的回调
//
// @param emojiView  表情输入的View
// @param sendButton 发送按钮
// */
//- (void)didSendButtonEvent:(RCEmojiBoardView *)emojiView sendButton:(UIButton *)sendButton{
//    
//}

- (void)emojiKeyBoardView:(AGEmojiKeyboardView *)emojiKeyBoardView didUseEmoji:(NSString *)emoji {
    self.chatInputView.inputTF.text = [self.chatInputView.inputTF.text stringByAppendingString:emoji];
    self.chatInputView.placeholderLabel.hidden = self.chatInputView.inputTF.text.length > 0;
}

- (void)emojiKeyBoardViewDidPressBackSpace:(AGEmojiKeyboardView *)emojiKeyBoardView {
    [self.chatInputView.inputTF setText:[self.chatInputView.inputTF.text removedLastString]];
}

#pragma mark - AGEmojiKeyboardViewDataSource
- (UIImage *)emojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView imageForSelectedCategory:(AGEmojiKeyboardViewCategoryImage)category {
    NSString *imageName = @"";
    switch (category) {
        case AGEmojiKeyboardViewCategoryImageCar:
            imageName = @"car_s";
            break;
        case AGEmojiKeyboardViewCategoryImageBell:
            imageName = @"bell_s";
            break;;
        case  AGEmojiKeyboardViewCategoryImageFace:
            imageName = @"face_s";
            break;
        case AGEmojiKeyboardViewCategoryImageFlower:
            imageName = @"flower_s";
            break;
        case AGEmojiKeyboardViewCategoryImageRecent:
            imageName = @"recent_s";
            break;
        case AGEmojiKeyboardViewCategoryImageCharacters:
            imageName = @"characters_s";
        default:
            break;
    }
    UIImage *img = [SCResourceManager loadImageWithName:imageName];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (UIImage *)emojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView imageForNonSelectedCategory:(AGEmojiKeyboardViewCategoryImage)category {
    NSString *imageName = @"";
    switch (category) {
        case AGEmojiKeyboardViewCategoryImageCar:
            imageName = @"car_n";
            break;
        case AGEmojiKeyboardViewCategoryImageBell:
            imageName = @"bell_n";
            break;;
        case  AGEmojiKeyboardViewCategoryImageFace:
            imageName = @"face_n";
            break;
        case AGEmojiKeyboardViewCategoryImageFlower:
            imageName = @"flower_n";
            break;
        case AGEmojiKeyboardViewCategoryImageRecent:
            imageName = @"recent_n";
            break;
        case AGEmojiKeyboardViewCategoryImageCharacters:
            imageName = @"characters_n";
        default:
            break;
    }
    UIImage *img = [SCResourceManager loadImageWithName:imageName];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (UIImage *)backSpaceButtonImageForEmojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView {
    UIImage *img = [SCResourceManager loadImageWithName:@"backspace_n"];
    [img imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return img;
}

- (AGEmojiKeyboardViewCategoryImage)defaultCategoryForEmojiKeyboardView:(AGEmojiKeyboardView *)emojiKeyboardView {
    return  AGEmojiKeyboardViewCategoryImageFace;
}

@end
     
     
