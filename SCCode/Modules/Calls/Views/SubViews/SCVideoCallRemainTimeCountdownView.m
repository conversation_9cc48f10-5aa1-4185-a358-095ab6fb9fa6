//
//  SCVideoCallRemainTimeCountdownView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/2/1.
//

#import "SCVideoCallRemainTimeCountdownView.h"

@interface SCVideoCallRemainTimeCountdownView()

@property(nonatomic, weak) UIImageView *contentBgIV;
@property (nonatomic, weak) UIView *contentView;
@property (nonatomic, weak) UIImageView *coinImgView;
@property (nonatomic, weak) UILabel *titleL;
@property (nonatomic, weak) UILabel *subTileL;
@property (nonatomic, weak) UIButton *rechargeBtn;
@property (nonatomic, weak) UIButton *closeBtn;

@property (nonatomic, strong) NSTimer *timer;

@end

@implementation SCVideoCallRemainTimeCountdownView

- (void)initUI{
    [super initUI];
    
    self.backgroundColor = UIColor.clearColor;
    
    _contentView = [UIView new].setBackgroundColor(UIColor.scWhite).setCornerRadius(kSCNormalCornerRadius).addSuperView(self);
    
    _contentBgIV = [UIImageView new].setImage([[SCResourceManager loadImageWithName:@"bg_remain_time"] resizableImageWithCapInsets:UIEdgeInsetsMake(10, 10, 10, 10) resizingMode:UIImageResizingModeStretch]).addSuperView(_contentView);
    
    UIImageView *coinImgView = [UIImageView new].setImageName(@"ic_new_user_coins");
    _coinImgView = coinImgView;
    
    UILabel *titleL = [UILabel new].setText(@"Coins not enough".translateString).setTextColor(UIColor.scWhite).setFontSemiboldSize(16.0f);
    titleL.numberOfLines = 2;
    _titleL = titleL;
    
    UILabel *subTileL = [UILabel new].setText(@"Video duration remaining:--s").setTextColor(UIColor.scWhite).setFontRegularSize(14.0f);
    subTileL.numberOfLines = 2;
    _subTileL = subTileL;
    
    UIStackView *titleStackView = [[UIStackView alloc]init];
    titleStackView.spacing = 5;
    titleStackView.distribution = UIStackViewDistributionFill;
    titleStackView.alignment = UIStackViewAlignmentCenter;
    [titleStackView addArrangedSubview:_coinImgView];
    [titleStackView addArrangedSubview:_titleL];
    
    UIStackView *infoStackView = [[UIStackView alloc]init];
    infoStackView.axis = UILayoutConstraintAxisVertical;
    infoStackView.spacing = 2;
    infoStackView.distribution = UIStackViewDistributionFill;
    infoStackView.alignment = UIStackViewAlignmentFill;
    [infoStackView addArrangedSubview:titleStackView];
    [infoStackView addArrangedSubview:_subTileL];
    [_contentView addSubview:infoStackView];
    
    _rechargeBtn = [UIButton buttonWithTitle:@"Recharge".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(10.0f) image:nil backgroundColor:UIColor.scBlack cornerRadius:8.0f].addSuperView(self.contentView);
    [_rechargeBtn setContentEdgeInsets:UIEdgeInsetsMake(0, 8, 0, 8)];
    [_rechargeBtn addTarget:self action:@selector(onRecharge) forControlEvents:UIControlEventTouchUpInside];
    
    _closeBtn = [UIButton buttonWithImageName:@"btn_close_white" target:self action:@selector(onClose)].addSuperView(self);
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    //布局
    [self.coinImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(29.0f);
        make.height.mas_equalTo(23.0f);
    }];

    // 先设置 rechargeBtn 的约束
    [self.rechargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).offset(-6);
        make.centerY.equalTo(self.contentView);
        make.height.mas_equalTo(26.0f);
        // 设置最小宽度，确保按钮文字显示完整
        make.width.mas_greaterThanOrEqualTo(65);
    }];

    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(4);
        make.trailing.equalTo(self.contentView).offset(-4);
        make.width.height.mas_equalTo(18);
    }];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.leading.bottom.mas_equalTo(0);
        make.top.offset(8.0f);
        make.trailing.offset(-8.0f);
    }];
    
    [infoStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(10);
        make.centerY.equalTo(self.contentView);
        make.trailing.lessThanOrEqualTo(self.rechargeBtn.mas_leading).offset(-2);
    }];

    [self.titleL setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.subTileL setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    // 为 infoStackView 添加压缩阻力优先级
    [infoStackView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [infoStackView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    self.frame = CGRectMake(16, -92, kSCScreenWidth - 16 - 5, 92);
}
//剩余描述
- (void)setDuration:(NSInteger)duration{
    // 大于现有的倒计时，则不刷新，外层已经过滤大于 60s 的情况。
    if (duration > _duration && self.timer) {
        return;
    }
    
    _duration = duration;
    [self updateSubTitleL];
    [self startCountdown];
}

//开始倒计时
- (void)startCountdown{
    [self stopCountdown];
    kWeakSelf(self);
    _timer = [NSTimer scheduledTimerWithTimeInterval:1 repeats:YES block:^(NSTimer * _Nonnull timer) {
        if(weakself.timer != timer){
            [timer invalidate];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            //主线程操作
            kStrongSelf;
            if(strongSelf){
                strongSelf -> _duration --;
                [strongSelf updateSubTitleL];
                if(strongSelf.duration <= 0){
                    [weakself hide];
                    [strongSelf stopCountdown];
                }
                
            }else{
                [weakself hide];
                [timer invalidate];
            }
            
        });
    }];
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
    
}
//停止倒计时
- (void)stopCountdown{
    if(_timer){
        [_timer invalidate];
        _timer = nil;
    }
}

//更新_subTileL 倒计时数量，数字(duration)部分显示主题色.其他部分默认颜色
- (void)updateSubTitleL{
    NSString *numStr = [NSString stringWithFormat:@"%ld",self.duration];
    NSString *startStr = @"Video duration remaining:".translateString;
    NSString *endStr = @"s";
    NSString *text = [NSString stringWithFormat:@"%@%@%@",startStr,numStr,endStr];
    NSMutableAttributedString *attr = [[NSMutableAttributedString alloc] initWithString:text];
    [attr addAttributes:@{NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#FFD200"],
                          NSFontAttributeName: kScUIFontSemibold(14)
                        } range:NSMakeRange(startStr.length, numStr.length)];
    self.subTileL.attributedText = attr;
}

- (void)dealloc{
    [self stopCountdown];
}




#pragma mark - Action
- (void)onClose{
//    [self dismiss];
    [self hide];
}
-(void)onRecharge{
    kSCBlockExeNotNil(self.rechTapBlock);
}


- (void)show {
        
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        CGRect frame = self.frame;
        frame.origin.y = kSCSafeAreaTopHeight;
        self.frame = frame;
    } completion:^(BOOL finished) {
        // [self hide];
    }];
}

- (void)hide {
    [UIView animateWithDuration:0.3 delay:0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
        CGRect frame = self.frame;
        frame.origin.y = -self.frame.size.height;
        self.frame = frame;
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
    }];
}

@end
