//
//  SCVideoCallChatCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#import <UIKit/UIKit.h>
#import "SCBaseTableViewCell.h"
NS_ASSUME_NONNULL_BEGIN
@class SCVideoCallChatMessageModel,SCVideoCallChatCell;



@interface SCVideoCallChatCell : SCBaseTableViewCell
//头像
@property(nonatomic,weak) UIImageView * avatarIV;
//文本
@property(nonatomic,weak) UILabel *textL;
//翻译分割线
@property(nonatomic,weak) UIView *lineV;
//翻译文本
@property(nonatomic,weak) UILabel *translateTextL;
//翻译成功后的图标
@property(nonatomic,weak) UIImageView *translateDoneIV;

//消息背景
@property(nonatomic,weak) UIView *bubbleV;
@property(nonatomic,strong) UIColor *bubbleColor;



//model
// 字典版本的setModel方法
- (void)setModel:(SCVideoCallChatMessageModel *)model userDict:(NSDictionary *) userDict translateText:(NSString *)translateText;

@end

NS_ASSUME_NONNULL_END
