//
//  SCTryAnchorAvatarCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/2.
//

#import "SCTryAnchorAvatarCell.h"

@implementation SCTryAnchorAvatarCell

- (void)initUI{
    [super initUI];
    _avatarIV = [UIImageView new].setCornerRadius(30.0f).addSuperView(self.contentView);
    [self.avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
}


@end
