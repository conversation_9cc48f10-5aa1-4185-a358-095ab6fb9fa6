//
//  SCCallHistoryCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/4.
//

#import "SCCallHistoryCell.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCCallHistoryCell()

@property (nonatomic, weak) UIImageView *avatarImgView;
@property (nonatomic, weak) UILabel *nameLb;
@property (nonatomic, weak) UILabel *contentLb;
@property (nonatomic, weak) UIImageView *callSourceIV;
@property (nonatomic, weak) UILabel *timeLb;
@property (nonatomic, weak) UIView *lineV;

@end

@implementation SCCallHistoryCell

- (void)initUI{
    [super initUI];
    self.contentView.backgroundColor = [UIColor scGlobalBgColor];
    
    _avatarImgView = [UIImageView new].setCornerRadius(48.0/2.0f).setContentMode(UIViewContentModeScaleAspectFill).addSuperView(self.contentView);
    
    _nameLb = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontSemibold(14) alignment:NSTextAlignmentLeft].addSuperView(self.contentView);
    _nameLb.numberOfLines = 1;
    
    _contentLb = [UILabel labelWithText:@"" textColor:kSCColorWithHexStr(@"#8E8B8B") font:kScUIFontMedium(13) alignment:NSTextAlignmentLeft].addSuperView(self.contentView);
    _contentLb.numberOfLines = 1;
    
    _timeLb = [UILabel labelWithText:@"" textColor:[UIColor scGray] font:kScUIFontRegular(12)].addSuperView(self.contentView);
    _timeLb.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentLeft : NSTextAlignmentRight;
    
    _callSourceIV = [UIImageView new].setImage([[SCResourceManager loadImageWithName:@"ic_call_normal"] imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate]).addSuperView(self.contentView);
    _lineV = [UIView new].setBackgroundColor([UIColor.scWhite colorWithAlphaComponent:0.1]).addSuperView(self.contentView);
    
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16.0);
        make.centerY.equalTo(self.contentView);
        make.width.height.equalTo(@(48.0));
    }];
    
    [self.nameLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.avatarImgView).offset(-10);
        make.leading.equalTo(self.avatarImgView.mas_trailing).inset(7.0f);
        make.height.mas_equalTo(21.0f);
    }];
    
    
    [self.contentLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nameLb.mas_bottom).offset(0);
        make.leading.equalTo(self.nameLb);
        make.height.mas_equalTo(19.0f);
    }];
    
    [self.timeLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.nameLb);
        make.trailing.equalTo(self.contentView).offset(-15.0);
        make.width.greaterThanOrEqualTo(@(80.0));
        make.height.mas_equalTo(18.0f);
        make.leading.equalTo(self.nameLb.mas_trailing).inset(0);
    }];
    [self.callSourceIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.timeLb);
        make.top.equalTo(self.timeLb.mas_bottom).inset(5.0f);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
    
    [self.lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.leading.equalTo(self.contentView).offset(74.0f);
        make.trailing.equalTo(self.contentView).offset(-15.0f);
        make.height.mas_equalTo(1.0f);
    }];
}

-(void) configWithModel:(SCCallSessionModel *)model userDict:(NSDictionary *)userDict {
    // 使用字典数据配置用户信息
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];

    [self.avatarImgView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
    self.nameLb.text = nickname;

    // 通话信息配置（与原方法相同）
    NSString *startStr = model.callReasonStr;
    NSString *endStr = model.callDuration == 0 ? @"" : [NSString hmsWithSeconds:model.callDuration];
    NSString *text = [NSString stringWithFormat:@"%@ %@",startStr,endStr];
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:text];
    NSDictionary *otherAttributes = @{NSForegroundColorAttributeName: model.callReasonColor };
    [attributedString setAttributes:otherAttributes range:NSMakeRange(0, startStr.length)];

    [self.timeLb setText: [[[NSDate alloc] initWithTimeIntervalSince1970:model.creatAt] formattedTimeAgo]];

    self.contentLb.attributedText = attributedString;

    self.callSourceIV.setImage([SCResourceManager loadImageWithName:model.endType == SCCallSessionEndTypeNormal ? @"ic_call_normal":@"ic_call_miss"]);
}

@end
