//
//  SCVideoCallChatCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/26.
//

#import "SCVideoCallChatCell.h"
#import "SCVideoCallChatMessageModel.h"
#import "SCGiftService.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import <SDWebImage/SDWebImageManager.h>
#define kAvatarWidth 44.0f
@interface SCVideoCallChatCell(){
    SCVideoCallChatMessageModel * _model;
}

@property(nonatomic,weak) UIView *avatarBg;

//按钮
@property(nonatomic,weak) UIButton *sendBtn;

@end

@implementation SCVideoCallChatCell


- (void)initUI{
    [super initUI];
    self.transform = CGAffineTransformMakeScale(1, -1);
    self.backgroundColor = UIColor.clearColor;
    _bubbleColor = [UIColor.scBlack colorWithAlphaComponent:0.20];
    _bubbleV = [UIView new].setBackgroundColor(_bubbleColor).setCornerRadius(15.0f).addSuperView(self.contentView);
    _avatarIV = [UIImageView new].setCornerRadius(kAvatarWidth/2.0f).addSuperView(self.contentView);
    _textL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontMedium(14.0f)].addSuperView(self.bubbleV);
    _textL.numberOfLines = 0;
    ///分割线
    _lineV = [UIView new].setBackgroundColor(UIColor.scLineColor).addSuperView(self.bubbleV);
    //翻译成功的图标
    _translateDoneIV = [UIImageView new].setImageName(@"ic_video_call_translate_done").addSuperView(self.bubbleV);
    //翻译后的文案
    _translateTextL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontRegular(15)].addSuperView(self.bubbleV);
    
    //头像
    [_avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(kAvatarWidth, kAvatarWidth));
    }];
    
    //泡泡
    [_bubbleV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarIV.mas_trailing).offset(8);
        make.top.equalTo(self.avatarIV);
        make.bottom.lessThanOrEqualTo(self.contentView).offset(-27.0f);
        make.trailing.lessThanOrEqualTo(self.contentView).offset(-15);
    }];
    
    //文本
    [_textL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.bubbleV).offset(10);
        make.top.equalTo(self.bubbleV).offset(10);
        make.trailing.equalTo(self.bubbleV).offset(-10);
        make.bottom.equalTo(self.bubbleV).offset(-10);
    }];
    
    
    _avatarBg = [UIView new].setBackgroundColor(self.bubbleColor).addSuperView(self.contentView);
    [self.avatarBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.avatarIV);
        make.leading.equalTo(self.bubbleV.mas_leading).offset(22);
        make.height.equalTo(self.bubbleV);
    }];
    
    
    _sendBtn = [UIButton buttonWithTitle:@"Send".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(10) image:nil backgroundColor:UIColor.scTheme cornerRadius:15].addSuperView(self.bubbleV);
    [_sendBtn setHidden:YES];
    
    [self.sendBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.bubbleV);
        make.size.mas_equalTo(CGSizeMake(50, 30));
        make.trailing.equalTo(self.bubbleV).offset(-12);
    }];
    
    [self.lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.offset(11.0f);
        make.trailing.offset(-11.0f);
        make.height.mas_equalTo(1);
        make.top.equalTo(self.textL.mas_bottom).offset(14.0f);
    }];
    
    [self.translateDoneIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.lineV);
        make.top.equalTo(self.lineV.mas_bottom).offset(12.0f);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
    
    [self.translateTextL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.translateDoneIV.mas_trailing).offset(3.0f);
        make.top.equalTo(self.translateDoneIV);
        make.trailing.equalTo(self.lineV);
    }];
    
    
}

- (void)setModel:(SCVideoCallChatMessageModel *)model userDict:(NSDictionary *) userDict translateText:(NSString *)translateText{
    _model = model;
    
    //隐藏翻译
    self.translateDoneIV.hidden = YES;
    self.translateTextL.hidden = YES;
    self.lineV.hidden = YES;
    
    //发送按钮
    self.sendBtn.hidden = YES;
    
    
    
    if(model.type ==  SCVideoCallChatMessageTypeText){
        
        self.bubbleV.setCornerRadius(15);
        
        self.textL.text = model.content;
        self.translateTextL.text = @"";
        if(kSCIsStrEmpty(translateText)){
            self.translateDoneIV.hidden = YES;
            self.translateTextL.hidden = YES;
            self.lineV.hidden = YES;
            //没有翻译
            [self.textL mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.bubbleV).offset(10);
                make.top.equalTo(self.bubbleV).offset(10);
                make.trailing.equalTo(self.bubbleV).offset(-10);
                make.bottom.equalTo(self.bubbleV).offset(-10);
            }];
            [self.translateTextL mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.translateDoneIV.mas_trailing).offset(6.0f);
                make.top.equalTo(self.translateDoneIV);
                make.trailing.equalTo(self.lineV);
            }];
            
        }else{
            //翻译成功
            self.translateDoneIV.hidden = NO;
            self.translateTextL.hidden = NO;
            self.lineV.hidden = NO;
            self.translateTextL.text = translateText;
            [self.textL mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.bubbleV).offset(10);
                make.top.equalTo(self.bubbleV).offset(10);
                make.trailing.equalTo(self.bubbleV).offset(-10);
//                make.bottom.equalTo(self.lineV.mas_top).inset(14.0f);
            }];
            [self.translateTextL mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.translateDoneIV.mas_trailing).offset(6.0f);
                make.top.equalTo(self.translateDoneIV);
                make.trailing.equalTo(self.lineV);
                make.bottom.equalTo(self.bubbleV).offset(-10);
            }];
        }
        
        [self.bubbleV mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.avatarIV.mas_trailing).offset(8);
            make.top.equalTo(self.avatarIV);
            make.bottom.lessThanOrEqualTo(self.contentView).offset(-27.0f);
            make.trailing.lessThanOrEqualTo(self.contentView).offset(-15);
        }];
        
        // 添加布局刷新
        [self.bubbleV setNeedsLayout];
        [self.bubbleV layoutIfNeeded];
        
        [self setNeedsLayout];
        [self layoutIfNeeded];
        
        if (self.textL.scWidth < 40) {
            self.textL.textAlignment = NSTextAlignmentCenter;
        } else {
            self.textL.textAlignment = NSTextAlignmentNatural;
        }
        
    }else{
        //显示头像后面的背景
        self.avatarBg.hidden = NO;
        
        [self.textL mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.bubbleV).insets(UIEdgeInsetsMake(0, 10, 0, 10));
        }];
        [self.translateTextL mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.translateDoneIV.mas_trailing).offset(6.0f);
            make.top.equalTo(self.translateDoneIV);
            make.trailing.equalTo(self.lineV);
        }];
        
        if(model.type ==  SCVideoCallChatMessageTypeAskGift){
           
        }else if(model.type == SCVideoCallChatMessageTypeGift){
            self.sendBtn.hidden = YES;
            // Add numberOfLines setting to allow multiple lines
            self.textL.numberOfLines = 0;
            // Add line break mode to wrap text
            self.textL.lineBreakMode = NSLineBreakByWordWrapping;
            
            NSString *text = @"I send you a gift".translateString;
            NSString *after = [NSString stringWithFormat:@"x %ld", model.giftNum];
            // 使用字典版本的礼物图标获取
            NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:model.giftCode];
            NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);
            NSURL *imageUrl = [NSURL URLWithString:iconThumbPath];
            
            kWeakSelf(self)
            [[SDWebImageManager sharedManager] loadImageWithURL:imageUrl options:0 progress:nil completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
                if (error) {
                    
                } else {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        // 更新属性文本
                        weakself.textL.attributedText = [weakself giftAttributeWithImage:image text:text after:after];
                        // 添加布局刷新
                        [weakself.bubbleV setNeedsLayout];
                        [weakself.bubbleV layoutIfNeeded];
                        
                        [weakself setNeedsLayout];
                        [weakself layoutIfNeeded];
                    });
                }
            }];
            
            // 先用占位图设置一个初始状态
            self.textL.attributedText = [self giftAttributeWithImage:[SCResourceManager loadImageWithName:@"bg_global_image_placeholder"] text:text after:after];
            // 修改 textL 的约束
            [self.textL mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(self.bubbleV).offset(-10);
                make.leading.equalTo(self.bubbleV).offset(10+kAvatarWidth);
                make.top.bottom.equalTo(self.bubbleV).offset(0);
                make.width.lessThanOrEqualTo(self.bubbleV).offset(-20);  // 确保文本宽度不超过气泡
            }];
            
            self.bubbleV.layer.cornerRadius = kAvatarWidth / 2.0f;
            
            // 更新 bubbleV 的约束以适应内容
            [self.bubbleV mas_updateConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(self.avatarIV);
                make.bottom.equalTo(self.contentView).offset(-27.0f);
                make.height.greaterThanOrEqualTo(self.avatarIV.mas_height);
            }];
        }
    }
    
    NSString *avatarUrl = kSCUserAvatarUrlFromDict(userDict);
    [self.avatarIV sc_setImageWithURL:avatarUrl];
    
}

-(NSMutableAttributedString *) giftAttributeWithImage:(UIImage *)image text:(NSString *)text after:(NSString *)after {
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    
    // 添加文本
    NSDictionary *textAttributes = @{
        NSFontAttributeName: kScUIFontMedium(14),
        NSForegroundColorAttributeName: UIColor.scWhite,
        NSBaselineOffsetAttributeName: @(0)
    };
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:text attributes:textAttributes]];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:@" " attributes:textAttributes]];  // 添加空格
    
    // 添加图片
    NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
    CGFloat fixedHeight = 30.0f;  // 调整图片高度
    CGSize originalImageSize = image.size;
    CGFloat newWidth = (originalImageSize.width / originalImageSize.height) * fixedHeight;
    textAttachment.bounds = CGRectMake(0, -5, newWidth, fixedHeight);  // 调整垂直位置
    textAttachment.image = image;
    [attributedString appendAttributedString:[NSAttributedString attributedStringWithAttachment:textAttachment]];
    
    // 添加数量
    NSDictionary *afterAttributes = @{
        NSForegroundColorAttributeName: UIColor.scWhite,
        NSFontAttributeName: kScUIFontMedium(12),
        NSBaselineOffsetAttributeName: @(0)
    };
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:@" " attributes:afterAttributes]];  // 添加空格
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:after attributes:afterAttributes]];
    
    return attributedString;
}



@end
