//
//  SCJoinChannelProgressView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/26.
//

#import "SCJoinChannelProgressView.h"


@implementation SCJoinChannelProgressView

- (void)initData{
    [super initData];
}

- (void)initUI{
    [super initUI];
    
    self.progressLabel = [[UILabel alloc] init];
    self.progressLabel.font = kScUIFontMedium(15);
    self.progressLabel.text = @"Waiting for the host to connect...".translateString;
    self.progressLabel.textColor = [UIColor whiteColor];
    [self addSubview:self.progressLabel];
    [self.progressLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
}

@end
