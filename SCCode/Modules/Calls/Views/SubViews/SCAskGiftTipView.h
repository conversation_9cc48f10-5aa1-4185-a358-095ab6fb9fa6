//
//  SCAskGiftTipView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/10/28.
//

#import <UIKit/UIKit.h>
#import "SCBaseView.h"

NS_ASSUME_NONNULL_BEGIN
@class SCVideoCallChatMessageModel;
@interface SCAskGiftTipView : SCBaseView
//头像
@property(nonatomic,weak) UIImageView * avatarIV;
//文本
@property(nonatomic,weak) UILabel *textL;

//消息背景
@property(nonatomic,weak) UIView *bubbleV;
@property(nonatomic,strong) UIColor *bubbleColor;

//发送按钮回调
@property(nonatomic,copy)void (^sendGiftBlock)(NSString *);

//model
// 字典版本的setModel方法
- (void)setModel:(SCVideoCallChatMessageModel *)model userDict:(NSDictionary *) userDict;
//展示 10秒后自动隐藏
- (void)show;
-(void) hideCompletion:(void (^ __nullable)(BOOL finished))completion;
@end

NS_ASSUME_NONNULL_END
