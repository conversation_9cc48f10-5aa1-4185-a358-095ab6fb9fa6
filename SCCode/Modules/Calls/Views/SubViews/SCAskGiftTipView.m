//
//  SCAskGiftTipView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/10/28.
//

#import "SCAskGiftTipView.h"
#import "SCVideoCallChatMessageModel.h"
#import "SCGiftService.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#define kAvatarWidth 44.0f
@interface SCAskGiftTipView(){
    SCVideoCallChatMessageModel * _model;
}


//按钮
@property(nonatomic,weak) UIButton *sendBtn;
//定时器
@property(nonatomic,weak) NSTimer *timer;
//礼物图标
@property(nonatomic,weak) UIImageView *giftIV;

@end

@implementation SCAskGiftTipView


- (void)initUI{
    [super initUI];
    self.backgroundColor = UIColor.clearColor;
    _bubbleColor = [UIColor.scBlack colorWithAlphaComponent:0.2];
    _bubbleV = [UIView new].setBackgroundColor(_bubbleColor).setCornerRadius(22).addSuperView(self);
    _avatarIV = [UIImageView new].setCornerRadius(kAvatarWidth/2.0f).addSuperView(self.bubbleV);
    _textL = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontRegular(15)].addSuperView(self.bubbleV);
   
    
    _sendBtn = [UIButton buttonWithTitle:@"Send".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(10) image:nil backgroundColor:nil cornerRadius:20.0f].addSuperView(self.bubbleV);
    [_sendBtn sc_setThemeGradientBackgroundWithCornerRadius:20.0f];
    [_sendBtn addTarget:self action:@selector(onSend) forControlEvents:UIControlEventTouchUpInside];
    
    _giftIV = [UIImageView new].setContentMode(UIViewContentModeScaleAspectFit).addSuperView(self.bubbleV);
    
    
    //头像
    [_avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.bubbleV);
        make.centerY.equalTo(self.bubbleV);
        make.size.mas_equalTo(CGSizeMake(kAvatarWidth, kAvatarWidth));
    }];
    
    //泡泡
    [_bubbleV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(0);
        make.top.equalTo(self);
        make.bottom.equalTo(self).offset(-27.0f);
        make.trailing.lessThanOrEqualTo(self).offset(-15);
        make.height.mas_greaterThanOrEqualTo(44.0f);
    }];
    
    //文本
    [_textL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.avatarIV.mas_trailing).offset( 10);
        make.top.equalTo(self.bubbleV).offset(10);
        make.bottom.equalTo(self.bubbleV).offset(-10);
    }];
    
    //礼物图标
    [_giftIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.textL.mas_trailing).offset(5);
        make.centerY.equalTo(self.bubbleV);
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.trailing.equalTo(self.sendBtn.mas_leading).offset(-10);
    }];
    
    [self.sendBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.bubbleV);
        make.width.mas_greaterThanOrEqualTo(70);
        make.height.mas_equalTo(40);
        make.trailing.equalTo(self.bubbleV);
    }];
}

// 字典版本的setModel方法
- (void)setModel:(SCVideoCallChatMessageModel *)model userDict:(NSDictionary *) userDict {
    _model = model;
    NSString *nickname = kSCUserNicknameFromDict(userDict);
    NSString *text = [NSString stringWithFormat:@"%@ %@", [nickname replaceMoreThan8], @"ask you to send a".translateString];
    // 使用字典版本的礼物图标获取
    NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:model.giftCode];
    NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);

    self.textL.text = text;

    //头像
    NSString *avatarUrl = kSCUserAvatarUrlFromDict(userDict);
    [self.avatarIV sc_setImageWithURL:avatarUrl];

    //礼物图标
    NSURL *imageUrl = [NSURL URLWithString:iconThumbPath];
    [self.giftIV sd_setImageWithURL:imageUrl];
}



#pragma mark - Action
-(void) onSend{
    kSCBlockExeNotNil(_sendGiftBlock,_model.giftCode);
}

-(void) show{
    //渐变显示并且高度拉开
    self.alpha = 0;
    
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
   
    }];
    
    //定时器10秒后关闭
    if(_timer){
        [_timer invalidate];
        _timer = nil;
    }
    _timer = [NSTimer scheduledTimerWithTimeInterval:10 target:self selector:@selector(hide) userInfo:nil repeats:NO];
    
    
}
//隐藏
-(void) hideCompletion:(void (^ __nullable)(BOOL finished))completion {
    if(_timer){
        [_timer invalidate];
        _timer = nil;
    }
    
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
    } completion:completion];
}

- (void) hide {
    [self hideCompletion:nil];
}

@end
