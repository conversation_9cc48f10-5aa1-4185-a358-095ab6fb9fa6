//
//  SCVideoCallChatInputView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#import "SCVideoCallChatInputView.h"

@implementation SCVideoCallChatInputView

- (void)initUI{
    [super initUI];
    
    //背景
    self.setBackgroundColor([UIColor colorWithHexString:@"#464543"]).setCornerRadius(21);
    
    //表情
    _emojiBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_msg_tools_emoji"]].addSuperView(self);
    
    //输入框
    _inputTF = [[UITextView alloc] init].addSuperView(self);
    _inputTF.font = kScUIFontMedium(15);
    _inputTF.textColor = [UIColor scWhite];
    _inputTF.backgroundColor = [UIColor clearColor];
    _inputTF.returnKeyType = UIReturnKeySend;
    _inputTF.delegate = self;
    _inputTF.scrollEnabled = NO;  // 初始不可滚动
    _inputTF.textContainerInset = UIEdgeInsetsMake(11, 0, 11, 0); // 调整内边距
    
    // 计算最大高度
    CGFloat lineHeight = _inputTF.font.lineHeight;
    _maxHeight = lineHeight * 2 + _inputTF.textContainerInset.top + _inputTF.textContainerInset.bottom;
    
    // placeholder
    _placeholderLabel = [[UILabel alloc] init].addSuperView(self);
    _placeholderLabel.text = @"Type here...".translateString;
    _placeholderLabel.font = _inputTF.font;
    _placeholderLabel.textColor = [UIColor scGray];
    
    //发送按钮
    _sendBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_call_chat_send"] target:self action:@selector(onSend)].addSuperView(self);
    _sendBtn.layer.cornerRadius = 15.0f;
    _sendBtn.layer.masksToBounds = YES;
    [_sendBtn sc_setThemeGradientBackground];
    
    [self.emojiBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.mas_equalTo(6);
        make.bottom.equalTo(self).offset(-10);  // 底部对齐
        make.size.mas_equalTo(CGSizeMake(22, 22));
    }];
    
    [self.inputTF mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.emojiBtn.mas_trailing).offset(9);
        make.top.equalTo(self);
        make.bottom.equalTo(self);
        make.height.mas_equalTo(43);  // 初始高度
    }];
    
    [self.placeholderLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.inputTF).offset(5);
        make.centerY.equalTo(self.inputTF);
    }];
    
    [self.sendBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.inputTF.mas_trailing).offset(9);
        make.trailing.equalTo(self).offset(-7.0f);
        make.bottom.equalTo(self).offset(-6);  // 底部对齐
        make.size.mas_equalTo(CGSizeMake(49, 30));
    }];
}

#pragma mark - UITextViewDelegate

- (void)textViewDidChange:(UITextView *)textView {
    // 处理 placeholder 显示/隐藏
    self.placeholderLabel.hidden = textView.text.length > 0;
    
    // 计算新高度
    CGSize size = [textView sizeThatFits:CGSizeMake(textView.frame.size.width, CGFLOAT_MAX)];
    CGFloat newHeight = MIN(size.height, self.maxHeight);
    
    // 更新高度约束
    [textView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(newHeight);
    }];
    
    // 超过最大高度时允许滚动
    textView.scrollEnabled = size.height > self.maxHeight;
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    if ([text isEqualToString:@"\n"]) {
        [self onSend];
        return NO;
    }
    return YES;
}

-(void)onSend {
    if (self.sendMsgBlock && !kSCIsStrEmpty(self.inputTF.text)) {
        self.sendMsgBlock(self.inputTF.text);
        self.inputTF.text = @"";
        [self textViewDidChange:self.inputTF];  // 重置高度
    }
}

@end
