//
//  SCCallDisablePrompt.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/31.
//

#import "SCCallDisablePrompt.h"
#import <AVKit/AVKit.h>
#import <AVFoundation/AVFoundation.h>

@interface  SCCallDisablePrompt()

@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, strong) UILabel *textLabel;
//关闭按钮
@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) AVPlayerViewController *playerViewController;

@end

@implementation SCCallDisablePrompt

- (void)initUI{
    [super initUI];
    // 创建模糊效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];

    // 创建模糊视图
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    blurView.frame = UIScreen.mainScreen.bounds;

    // 将模糊视图添加到需要模糊背景的视图上
    [self addSubview:blurView];
    self.userInteractionEnabled = YES;
    // 创建空白的AVPlayerViewController
    self.playerViewController = [[AVPlayerViewController alloc] init];
    self.playerViewController.view.backgroundColor = [UIColor clearColor];
    self.playerViewController.showsPlaybackControls = NO;
    
    // 将AVPlayerViewController的视图覆盖在窗口上
    UIWindow *window = [[UIApplication sharedApplication].windows firstObject];
    [window addSubview:self.playerViewController.view];
    self.playerViewController.view.frame = window.bounds;
    self.playerViewController.view.userInteractionEnabled = NO;
    
    self.imageView = [UIImageView new].setImageName(@"bg_disable_prompt").addSuperView(self);
    self.imageView.userInteractionEnabled = YES;
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(330, 287));
    }];
    self.closeButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close_white"] target:self action:@selector(onClose)].addSuperView(self.imageView);
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.top.trailing.equalTo(self.imageView);
    }];
    
    self.textLabel = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontRegular(18) alignment:NSTextAlignmentCenter].addSuperView(self.imageView);
    
    [self.textLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.imageView).offset(20.0f);
        make.trailing.equalTo(self.imageView).offset(-20.0f);
        make.top.equalTo(self.imageView).offset(158);
    }];
    self.hidden = YES;
}

///开始监听
- (void)startListen{
    if( [UIScreen mainScreen].isCaptured){
        [self showStopRecord];
    }
    // 监听录屏状态的改变
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(screenCaptureStatusChanged) name:UIScreenCapturedDidChangeNotification object:nil];
        
        // 禁止截屏
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(preventScreenshot) name:UIApplicationUserDidTakeScreenshotNotification object:nil];
}
///结束监听
- (void)stopListen{
    [[NSNotificationCenter defaultCenter] removeObserver:self name:AVAudioSessionRouteChangeNotification object:nil];
}



- (void)screenCaptureStatusChanged {
    BOOL isCaptured = [UIScreen mainScreen].isCaptured;
    
    if (isCaptured) {
        // 录屏开始，禁止录屏
        [self.playerViewController.player play];
        [self showStopRecord];
    } else {
        // 录屏结束，允许录屏
        [self.playerViewController.player pause];
    }
}
-(void) showStopRecord{
    self.textLabel.text = @"To protect the user's privacy, you are not allowed to record video during video call.".translateString;
    self.hidden = NO;
}

///禁止截屏
-(void) showStopScreenshot{
    self.textLabel.text = @"To protect the user's privacy, you are not allowed to take screenshots during video call.".translateString;
    self.hidden = NO;
}


- (void)preventScreenshot {
    [self showStopScreenshot];
}

- (void)dealloc {
    // 移除通知监听
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIScreenCapturedDidChangeNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationUserDidTakeScreenshotNotification object:nil];
    [_playerViewController.player pause];
    [_playerViewController.view removeFromSuperview];
    
}

-(void) onClose{
    if([UIScreen mainScreen].isCaptured){
        //如果正在录屏中无法关闭页面
        [self toast:@"You are not allowed to close the page during the video call.".translateString];
        return;
    }
    self.hidden = YES;
}

@end
