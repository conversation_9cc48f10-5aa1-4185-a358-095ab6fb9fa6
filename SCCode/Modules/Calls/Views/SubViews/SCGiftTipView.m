//
//  SCGiftTipView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
//

#import "SCGiftTipView.h"
#import "SCGiftSendNumTipDisplayModel.h"

@interface SCGiftTipView()
@property(nonatomic,strong)SCGiftSendNumTipDisplayModel * showingCodeGiftModel ;
//5秒倒计时
@property(nonatomic,strong)NSTimer * timer;
//记录总数量
@property(nonatomic,assign)NSInteger number;

@end
@implementation SCGiftTipView
- (void)initData{
    [super initData];
}
- (void)initUI{
    [super initUI];
    _contentV = [[UIView alloc] init].addSuperView(self);
    _bgIV = [UIImageView new].addSuperView(self.contentV);
    UIImage *bgImage = [SCResourceManager loadImageWithName:@"ic_gift_tip_bg" isAutoForce:YES];
    _bgIV.image = [bgImage resizableImageWithCapInsets:kScAuthMar.isLanguageForce ? UIEdgeInsetsMake(2, 2, 2, 22) : UIEdgeInsetsMake(2, 22, 2, 2) resizingMode:UIImageResizingModeStretch];
    
    _iconIV = [UIImageView new].addSuperView(self.contentV);
    _iconIV.contentMode = UIViewContentModeScaleAspectFill;
    _xL = [UILabel labelWithText:@"x" textColor:UIColor.scWhite font:kScUIFontMedium(30)].addSuperView(self.contentV);
    _numL = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontMedium(40)].addSuperView(self.contentV);
    _numL.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
}


- (void)showWithGiftNum:(SCGiftSendNumModel *)giftNum{
    giftNum = [giftNum copy];
    //获取是否已经有显示的
    SCGiftSendNumTipDisplayModel * last = self.showingCodeGiftModel;
    NSTimeInterval now = [[NSDate now] timeIntervalSince1970];
    NSTimeInterval offset = now - last.lastReceiveTime;
    
    //布局
    [self.contentV mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.equalTo(self);
        make.width.mas_equalTo(136);
        make.height.mas_equalTo(55);
    }];

    [self.bgIV mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentV);
        make.top.equalTo(self.contentV).offset(11);
        make.height.mas_equalTo(38);
    }];

    [self.iconIV mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentV).offset(5);
        make.top.equalTo(self.contentV).offset(5);
        make.width.mas_equalTo(34);
        make.height.mas_equalTo(50);
    }];

    [self.xL mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.iconIV.mas_trailing).offset(10);
        make.top.bottom.equalTo(self.bgIV);
    }];

    [self.numL mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.xL.mas_trailing).offset(5);
        make.top.bottom.equalTo(self.bgIV);
    }];

    if(last == nil || (offset > 3.0) || ![kSCGiftCodeFromDict(last.giftSendNumModel.giftModel) isEqualToString:kSCGiftCodeFromDict(giftNum.giftModel)]){
        last = [[SCGiftSendNumTipDisplayModel alloc] init];
        last.lastReceiveTime = now;
        last.giftSendNumModel = giftNum;
        self.showingCodeGiftModel = last;
        self.number = 0;
        //如果之前的为空，或者超过3秒。则不累加，显示新礼物
        [self.iconIV sc_setImageWithURL:kSCGiftIconThumbPathFromDict(giftNum.giftModel)];
        self.numL.text = [NSString stringWithFormat:@"%ld",giftNum.giftNum];

        
        //显示动画
        self.contentV.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            self.contentV.alpha = 1;
        } completion:^(BOOL finished) {
            //显示动画
        }];
    
        
        
        
    }else{
        self.contentV.alpha = 1;
        //叠加数量 并且更新接收时间
        if(![last.giftSendNumModel.sessionId isEqualToString:giftNum.sessionId]){
            //如果session不一致，那么叠加数量
            self.number += last.giftSendNumModel.giftNum;
        }
        last.giftSendNumModel = giftNum;
        //更新时间
        last.lastReceiveTime = now;
        NSInteger showNum = self.number + last.giftSendNumModel.giftNum;
        //更新数量
        self.numL.text = [NSString stringWithFormat:@"%ld",showNum];
        // 初始缩放比例为1.0
           CGFloat initialScale = 2.0;
           
           // 缩放比例为2.0
           CGFloat finalScale = 1.0;
           
           // 设置视图的初始缩放比例
           self.numL.transform = CGAffineTransformMakeScale(initialScale, initialScale);
           
           // 缩小动画
           [UIView animateWithDuration:0.3 animations:^{
               self.numL.transform = CGAffineTransformMakeScale(finalScale, finalScale);
           } completion:^(BOOL finished) {
               
           }];
    }
    //开启倒计时
    [self startTimer];
    
}

//开始倒计时
- (void)startTimer{
    [self stopTimer];
    self.timer = [NSTimer scheduledTimerWithTimeInterval:5 target:self selector:@selector(doTimerAction:) userInfo:nil repeats:NO];
    [[NSRunLoop currentRunLoop] addTimer:self.timer forMode:NSRunLoopCommonModes];
}
-(void) doTimerAction:(NSTimer *)timer{
    [timer invalidate];
    //隐藏动画
    [self hide];
}
//停止倒计时
- (void)stopTimer{
    if(self.timer){
        [self.timer invalidate];
        self.timer = nil;
    }
}
//隐藏
- (void)hide{
    [self stopTimer];
    self.showingCodeGiftModel = nil;
    [UIView animateWithDuration:0.3 animations:^{
        self.contentV.alpha = 0;
    } completion:^(BOOL finished) {
        //隐藏动画
        [self mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(0);
        }];
        [self setNeedsLayout];
    }];
}

@end
