//
//  SCFreeTimeCountdownView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/2/19.
//

#import "SCFreeTimeCountdownView.h"
#import "SCLanguageManager.h"

@interface SCFreeTimeCountdownView ()

//文本
@property(nonatomic,weak)UILabel *textL;

@end

@implementation SCFreeTimeCountdownView

- (void)initUI{
    [super initUI];
        
    _textL = [UILabel new].setNumberLines(0).addSuperView(self);
    _textL.textAlignment = NSTextAlignmentCenter;

    [self.textL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.bottom.equalTo(self);
    }];
    
    /**
     NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
     NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
     CGSize imageSize = CGSizeMake(30, 44);
     textAttachment.bounds = CGRectMake(0, 0, imageSize.width, imageSize.height);
     textAttachment.image = image;
     NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:textAttachment];
     [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:text attributes:@{NSBaselineOffsetAttributeName:@(14)}]];
     [attributedString appendAttributedString:imageString];
     [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:after attributes:@{NSForegroundColorAttributeName:UIColor.scTheme,NSFontAttributeName:kScUIFontRegular(20),NSBaselineOffsetAttributeName:@(10)}]];
     */
}

-(void) comfigWithSeconds:(NSInteger) seconds price:(NSInteger) price{
    
    NSString * text = @"After ###, you'll be charged ### coins per minute".translateString;
    //根据 ### 分割字符串并加入内容 和图标
    NSArray * arr = [text componentsSeparatedByString:@"###"];
    NSString * path1 = arr[0];
    NSString * path2 = arr[1];
    NSString * path3 = arr[2];
    // path1 和 Path2 之间加入 seconds 并显示 黄色
    // path2 和 path3之间加入price 和 金币图标
    
    
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    
    // 设置金币图标
    NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
    CGSize imageSize = CGSizeMake(10, 8);
    textAttachment.bounds = CGRectMake(0, 0, imageSize.width, imageSize.height);
    textAttachment.image = [SCResourceManager loadImageWithName:@"ic_coins_small"];
    NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:textAttachment];

    //Afte10s, you'll be charged 100 coins per minut
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:path1 attributes:@{NSForegroundColorAttributeName:UIColor.scWhite,NSFontAttributeName:kScUIFontRegular(12)}]];
    if(kScAuthMar.isLanguageForce){
        [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld ",price] attributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#FE3E69"),NSFontAttributeName:kScUIFontRegular(12)}]];
        [attributedString appendAttributedString:imageString];
    }else{
        [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%lds",seconds] attributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#FE3E69"),NSFontAttributeName:kScUIFontRegular(12)}]];
    }
    
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:path2 attributes:@{NSForegroundColorAttributeName:UIColor.scWhite,NSFontAttributeName:kScUIFontRegular(12)}]];
    if(!kScAuthMar.isLanguageForce){
        [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%ld ",price] attributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#FE3E69"),NSFontAttributeName:kScUIFontRegular(12)}]];
        [attributedString appendAttributedString:imageString];
    }else{
        [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:[NSString stringWithFormat:@"%lds",seconds] attributes:@{NSForegroundColorAttributeName:kSCColorWithHexStr(@"#FE3E69"),NSFontAttributeName:kScUIFontRegular(12)}]];
    }
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:path3 attributes:@{NSForegroundColorAttributeName:UIColor.scWhite,NSFontAttributeName:kScUIFontRegular(12)}]];
    self.textL.attributedText = attributedString;
    self.textL.numberOfLines = 0;
    self.textL.lineBreakMode = NSLineBreakByWordWrapping;
}

@end
