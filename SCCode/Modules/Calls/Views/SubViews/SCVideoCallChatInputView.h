//
//  SCVideoCallChatInputView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#import "SCBaseView.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCVideoCallChatInputView : SCBaseView<UITextViewDelegate>

//表情按钮
@property(nonatomic,weak) UIButton * emojiBtn;
//输入框 透明背景
@property (nonatomic, strong) UITextView *inputTF;
//发送按钮
@property(nonatomic,weak) UIButton * sendBtn;
@property (nonatomic, strong) UILabel *placeholderLabel;
@property (nonatomic, assign) CGFloat maxHeight;  // 最大高度

//发送消息回调
@property(nonatomic,copy) void(^sendMsgBlock)(NSString * text);

@end

NS_ASSUME_NONNULL_END
