//
//  SCCallingView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/25.
//

#import "SCCallingView.h"
#import "SCCallSessionModel.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCFontManager.h"
#import "SCCountryService.h"

@implementation SCCallingView

- (instancetype)initWithIsMyCall:(BOOL) isMyCall
{
    self = [super init];
    if (self) {
        [self _initUIWithIsMyCall:isMyCall];
    }
    return self;
}

- (void)_initUIWithIsMyCall:(BOOL) isMyCall{
    //头像
    _avatarImageView = [UIImageView new].setContentMode(UIViewContentModeScaleAspectFill).setBackgroundColor(UIColor.clearColor).addSuperView(self);
    
    // 添加高斯模糊效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
    UIVisualEffectView *blurView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    [_avatarImageView addSubview:blurView];
    
    // 设置模糊视图的约束
    [blurView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(_avatarImageView);
    }];
    
    {
        // 创建 Lottie 动画视图
        NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/wave_animation.json"];
        NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
        
        NSError *error = nil;
        NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
        if (jsonData) {
            self.avatarWaveAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
            self.avatarWaveAnimationView.loopAnimationCount = -1;
            [self addSubview:self.avatarWaveAnimationView];
            [self.avatarWaveAnimationView play];
        }
    }
    
    _circleAvatarImageView = [UIImageView new].setContentMode(UIViewContentModeScaleAspectFill).setBackgroundColor(UIColor.clearColor).addSuperView(self);
    _circleAvatarImageView.image = [SCResourceManager loadImageWithName:@"ic_circle_avatar"];
    _circleAvatarImageView.layer.cornerRadius = 157.0f / 2.0f;
    _circleAvatarImageView.layer.masksToBounds = YES;

    _nickNameLabel = [UILabel labelWithTextColor:UIColor.scWhite font:[SCFontManager semiBoldFontWithSize:20.0f]].addSuperView(self);
    _nickNameLabel.textAlignment = NSTextAlignmentCenter;
    
    _countryLabel = [UILabel labelWithTextColor:UIColor.scWhite font:kScUIFontRegular(14)].addSuperView(self);
    _countryLabel.textAlignment = NSTextAlignmentCenter;
    
    //计费按钮
    UIButton * chargeBtn = [[UIButton alloc] init];
    chargeBtn.hidden = YES;
    [chargeBtn setTitleColor:[UIColor scWhite] forState:UIControlStateNormal];
    chargeBtn.titleLabel.font = kScUIFontMedium(10.0f);
    chargeBtn.layer.cornerRadius = 13.0f;
    chargeBtn.layer.masksToBounds = YES;
    [chargeBtn setImage:[SCResourceManager loadImageWithName:@"ic_coins_anchor_info"] forState:UIControlStateNormal];
    [chargeBtn sc_setThemeGradientBackgroundWithCornerRadius:13.0f];
    chargeBtn.userInteractionEnabled = NO;
    [self addSubview:chargeBtn];
    _chargeBtn = chargeBtn;
    if (kScAuthMar.isLanguageForce) {
        chargeBtn.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
    }
    
    _hangUpButton = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_reject_call"]].addSuperView(self);
    
    {
        // 创建 Lottie 动画视图
        NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/pickup_animation.json"];
        NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
        
        NSError *error = nil;
        NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
        if (jsonData) {
            self.answerAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
            self.answerAnimationView.loopAnimationCount = -1;
            [self addSubview:self.answerAnimationView];
        }
    }
    
    UIImageView *tagImageView = [[UIImageView alloc]initWithImage:[SCResourceManager loadImageWithName:@"ic_free_tag"]];
    tagImageView.hidden = YES;
    [self addSubview:tagImageView];
    _freeTagImageView = tagImageView;
    
    [self.avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [self.circleAvatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(149.0f);
        make.centerX.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(157.0f, 157.0f));
    }];
    
    [self.avatarWaveAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.circleAvatarImageView);
        make.size.mas_equalTo(200);
    }];
    
    [self.nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.circleAvatarImageView.mas_bottom).offset(28.0f);
        make.leading.trailing.equalTo(self).inset(10);
        make.height.mas_equalTo(38);
    }];
    
    [self.countryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nickNameLabel.mas_bottom).inset(2);
        make.leading.trailing.equalTo(self).inset(10);
        make.height.mas_equalTo(17.0);
    }];
    
    [chargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.countryLabel.mas_bottom).inset(4.0f);
        make.centerX.equalTo(self);
        make.height.mas_equalTo(26);
        make.width.mas_equalTo(86);
    }];
    
    if(isMyCall){
        ///主动呼叫
        self.answerAnimationView.hidden = YES;
        [self.answerAnimationView stop];
        
        [self.hangUpButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self);
            make.bottom.equalTo(self).inset(50+kSCSafeAreaBottomHeight);
            make.size.mas_equalTo(CGSizeMake(60, 60));
        }];
        
    }else{
        ///被动呼叫
        self.answerAnimationView.hidden = NO;
        [self.answerAnimationView play];
        
        CGFloat xOffset = kSCScaleWidth(60);
        [self.hangUpButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self).offset(xOffset);
            make.bottom.equalTo(self).inset(50+kSCSafeAreaBottomHeight);
            make.size.mas_equalTo(CGSizeMake(60, 60));
        }];
        
        [self.answerAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self).inset(xOffset);
            make.centerY.equalTo(self.hangUpButton);
            make.size.mas_equalTo(72);
        }];
        
        [self.freeTagImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.answerAnimationView.mas_right).offset(8.0f);
            make.top.equalTo(self.answerAnimationView.mas_top).offset(-18.0f);
            make.size.mas_equalTo(CGSizeMake(45.0f, 36.0f));
        }];
    }
    
}

// 字典版本的UI更新方法
-(void) updateUIWithTargetUserDict:(NSDictionary *) targetUserDict session:(SCCallSessionModel *) session{
    if(targetUserDict != nil){
        NSString *avatarThumbUrl = kSCUserAvatarThumbUrlFromDict(targetUserDict);
        NSString *avatarUrl = kSCUserAvatarUrlFromDict(targetUserDict);
        NSString *nickname = kSCUserNicknameFromDict(targetUserDict);
        NSString *country = [SCDictionaryHelper stringFromDictionary:targetUserDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
        
        [self.avatarImageView sc_setImageWithURL:avatarThumbUrl];
        [self.circleAvatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
        [self.nickNameLabel setText:nickname];
        [self.countryLabel setText:[NSString stringWithFormat:@"%@ %@",[SCCountryService emojiForCountry:country], country]];
    }
    
    if (session.isFree) {
        self.freeTagImageView.hidden = NO;
        self.chargeBtn.hidden = YES;
    } else {
        self.freeTagImageView.hidden = YES;
        self.chargeBtn.hidden = NO;
        NSInteger unitPrice = [SCDictionaryHelper integerFromDictionary:targetUserDict forKey:@"unitPrice" defaultValue:0];
        NSString * cellStr = [NSString stringWithFormat:@" %ld / min", unitPrice];
        ///计算文本宽度
        UIFont *cellFont = kScUIFontMedium(10.0f);
        CGSize cellTextSize = [cellStr sizeWithAttributes:@{NSFontAttributeName: cellFont}];
        CGFloat cellTextWidth = cellTextSize.width;
        [self.chargeBtn setTitle:cellStr forState:UIControlStateNormal];
        [self.chargeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(cellTextWidth + 40);
        }];
    }
    
    if (session.isFree) {
        self.freeTagImageView.hidden = NO;
        self.chargeBtn.hidden = YES;
    } else {
        self.freeTagImageView.hidden = YES;
        self.chargeBtn.hidden = NO;
        NSInteger unitPrice = [SCDictionaryHelper integerFromDictionary:targetUserDict forKey:@"unitPrice" defaultValue:0];
        NSString * cellStr = [NSString stringWithFormat:@" %ld / min", unitPrice];
        ///计算文本宽度
        UIFont *cellFont = kScUIFontMedium(10.0f);
        CGSize cellTextSize = [cellStr sizeWithAttributes:@{NSFontAttributeName: cellFont}];
        CGFloat cellTextWidth = cellTextSize.width;
        [self.chargeBtn setTitle:cellStr forState:UIControlStateNormal];
        [self.chargeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(cellTextWidth + 40);
        }];
    }
}

@end
