//
//  SCCallingView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/25.
//

#import "SCBaseView.h"
#import "Lottie/Lottie-Swift.h"

@class SCCallSessionModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCCallingView : SCBaseView
//背景头像
@property (weak, nonatomic)  UIImageView *avatarImageView;
//圆形头像
@property (weak, nonatomic) UIImageView *circleAvatarImageView;
//头像波纹动画
@property (nonatomic, strong) CompatibleAnimationView *avatarWaveAnimationView;
//昵称
@property (weak, nonatomic)  UILabel *nickNameLabel;
//国家
@property (weak, nonatomic)  UILabel *countryLabel;
//挂断按钮
@property (weak, nonatomic)  UIButton *hangUpButton;
//接听动画
@property (nonatomic, strong) CompatibleAnimationView *answerAnimationView;
//免费标签
@property (weak, nonatomic) UIImageView *freeTagImageView;
//计费按钮
@property(nonatomic,weak,nullable) UIButton * chargeBtn;

- (instancetype)initWithIsMyCall:(BOOL) isMyCall;

// 字典版本的UI更新方法
-(void) updateUIWithTargetUserDict:(NSDictionary *) targetUserDict session:(SCCallSessionModel *) session;
@end

NS_ASSUME_NONNULL_END
