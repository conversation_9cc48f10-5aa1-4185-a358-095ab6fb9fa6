//
//  SCVideoCallViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/25.
//

#import "SCSafetyViewController.h"
#import "SCCallService.h"
#import "SCCallServiceDelegate.h"
#import "SCVideoCallDelegate.h"

@class SCVideoCallViewController,SCCallSessionModel,SCVideoCallChatMessageModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCVideoCallViewController<SCCallServiceDelegate> : SCSafetyViewController

@property(nonatomic,weak) id<SCVideoCallDelegate> delegate;
- (instancetype)initWithSession:(SCCallSessionModel *)session;

-(void)addChatMessage:(SCVideoCallChatMessageModel *)message;
@end

NS_ASSUME_NONNULL_END
