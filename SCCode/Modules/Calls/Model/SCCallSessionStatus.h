//
//  SCCallSessionStatus.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#ifndef SCCallSessionStatus_h
#define SCCallSessionStatus_h

typedef enum : NSUInteger {
    ///呼叫中
    SCCallSessionStatusCallding = 1,
    //等待加入
    SCCallSessionStatusWaitJoin = 2,
    ///通话中
    SCCallSessionStatusOnCalled = 3,
    ///挂断
    SCCallSessionStatusHangUp = 4,
} SCCallSessionStatus;

typedef enum : NSUInteger {
    //异常
    SCCallSessionEndTypeUnknown = 0,
    ///正常结束通话
    SCCallSessionEndTypeNormal = 1,
    //自己取消 Cancel
    SCCallSessionEndTypeCancelMyself = 2,
    ///对方取消或者异常
    SCCallSessionEndTypeUnanswered = 3,
    ///没有接听
    SCCallSessionEndTypeMissed = 4,
} SCCallSessionEndType;

#endif /* SCCallSessionStatus_h */
