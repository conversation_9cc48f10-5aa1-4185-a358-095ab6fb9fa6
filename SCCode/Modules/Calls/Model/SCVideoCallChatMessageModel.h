//
//  SCVideoCallChatModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/26.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    //普通文本消息
    SCVideoCallChatMessageTypeText = 0,
    //礼物消息
    SCVideoCallChatMessageTypeGift,
    //请求礼物消息
    SCVideoCallChatMessageTypeAskGift,
} SCVideoCallChatMessageType;

@interface SCVideoCallChatMessageModel : NSObject

//SessionId
@property(nonatomic,copy) NSString * sessionId;
//发送方ID
@property(nonatomic,copy) NSString * fromUserId;
//接收方ID
@property(nonatomic,copy) NSString * toUserId;

//消息类型
@property(nonatomic,assign) SCVideoCallChatMessageType type;
//消息内容
@property(nonatomic,copy) NSString * content;
//消息时间
@property(nonatomic,assign) NSTimeInterval time;

///礼物字段，礼物消息才有
@property(nonatomic,copy) NSString * giftCode;
@property(nonatomic,assign) NSInteger giftNum;



@end

NS_ASSUME_NONNULL_END
