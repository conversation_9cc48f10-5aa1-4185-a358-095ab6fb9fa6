//
//  SCCallSessionModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import "SCCallSessionModel.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import <sqlite3.h>
#import "SCNativeSQLiteResultSet.h"

@implementation SCCallSessionModel


//字典版本的被呼叫初始化方法
- (instancetype)initWithOnCallDict:(NSDictionary *) onCallDict
{
    self = [super init];
    if (self) {

        //呼叫状态
        _status = SCCallSessionStatusCallding;

        ///是否免费
        _isFree = [SCDictionaryHelper boolFromDictionary:onCallDict forKey:@"isFree" defaultValue:NO];
        ///免费时长
        _callFreeSeconds = [SCDictionaryHelper integerFromDictionary:onCallDict forKey:@"callFreeSeconds" defaultValue:0];
        ///对方用户ID
        _fromUserId = [SCDictionaryHelper stringFromDictionary:onCallDict forKey:@"fromUserId" defaultValue:@""];
        ///被呼叫方的ID [应该为当前登录的用户ID]
        _toUserId = [SCDictionaryHelper stringFromDictionary:onCallDict forKey:@"toUserId" defaultValue:@""];
        ///声网token
        _rtcToken = [SCDictionaryHelper stringFromDictionary:onCallDict forKey:@"rtcToken" defaultValue:@""];
        ///通话价格
        _broadcasterUnitPrice = [SCDictionaryHelper integerFromDictionary:onCallDict forKey:@"broadcasterUnitPrice" defaultValue:0];
        _unitPrice = [SCDictionaryHelper integerFromDictionary:onCallDict forKey:@"unitPrice" defaultValue:0];
        ///客户端会话ID
        _clientSessionId = [SCDictionaryHelper stringFromDictionary:onCallDict forKey:@"clientSessionId" defaultValue:@""];

        // 选用的视频sdk: 1- > 声网；2 -> bigo；
        _chooseVideoSdk = [SCDictionaryHelper integerFromDictionary:onCallDict forKey:@"chooseVideoSdk" defaultValue:1];
        ///是否绿色模式
        _isGreenMode = [SCDictionaryHelper boolFromDictionary:onCallDict forKey:@"isGreenMode" defaultValue:NO];
        ///频道名称
        _channelName = [SCDictionaryHelper stringFromDictionary:onCallDict forKey:@"channelName" defaultValue:@""];

    }
    return self;
}

- (instancetype)initWithClientSessionId:(NSString *)clientSessionId isMatch:(BOOL)isMatch fromUserId:(NSString *)fromUserId{
    self = [super init];
    if (self) {
        //呼叫状态
        _status = SCCallSessionStatusCallding;
        ///客户端会话ID
        _clientSessionId = clientSessionId;
        ///免费时长
        _callFreeSeconds = 0;
        _fromUserId = fromUserId;
       
        ///是否快速匹配
        _isMatch = isMatch;
    }
    return self;
}


-(void) configWithFlashMatchDict:(NSDictionary *)flashMatchDict{

    //呼叫状态
    _status = SCCallSessionStatusCallding;

    ///是否免费
    NSInteger callFreeSeconds = [SCDictionaryHelper integerFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0];
    _isFree = callFreeSeconds > 0;
    ///免费时长
    _callFreeSeconds = callFreeSeconds;
    ///对方用户ID
    _fromUserId = [SCDictionaryHelper stringFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:@""];
    ///被呼叫方的ID [应该为当前登录的用户ID]
    _toUserId = [SCDictionaryHelper stringFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:@""];
    ///声网token
    _rtcToken = [SCDictionaryHelper stringFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""];
    ///通话价格
//        _broadcasterUnitPrice = flashMatchModel.broadcasterUnitPrice;

    // 选用的视频sdk: 1- > 声网；2 -> bigo；
    _chooseVideoSdk = [SCDictionaryHelper integerFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:1];
    ///是否绿色模式
    _isGreenMode = [SCDictionaryHelper boolFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO];
    ///频道名称
    _channelName = [SCDictionaryHelper stringFromDictionary:flashMatchDict forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:@""];

}

- (instancetype)initWithClientSessionId:(NSString *)clientSessionId unitPrice:(NSInteger ) unitPrice fromUserId:(NSString *)fromUserId toUserId:(NSString *)toUserId{
    self = [super init];
    if (self) {
        _status = SCCallSessionStatusCallding;
        _clientSessionId = clientSessionId;
        _fromUserId = fromUserId;
        _toUserId = toUserId;
        _unitPrice = unitPrice;
        ///是否免费
        _isFree = NO;
    }
    return self;
};


-(void) configWithChannelDict:(NSDictionary *)channelDict{
    ///免费时长
    _callFreeSeconds = [SCDictionaryHelper integerFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallFreeSecondsKey defaultValue:0];
    ///对方用户ID
    _fromUserId = [SCDictionaryHelper stringFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallFromUserIdKey defaultValue:@""];

    ///被呼叫方的ID
    _toUserId = [SCDictionaryHelper stringFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallToUserIdKey defaultValue:@""];
    ///声网token
    _rtcToken = [SCDictionaryHelper stringFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallRtcTokenKey defaultValue:@""];

    // 选用的视频sdk: 1- > 声网；2 -> bigo；
    _chooseVideoSdk = [SCDictionaryHelper integerFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallChooseVideoSdkKey defaultValue:1];
    ///是否绿色模式
    _isGreenMode = [SCDictionaryHelper boolFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallIsGreenModeKey defaultValue:NO];
    ///频道名称
    _channelName = [SCDictionaryHelper stringFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCDictKeyChannelName defaultValue:@""];
    //价格
    _unitPrice = [SCDictionaryHelper integerFromDictionary:channelDict forKey:SCDictionaryKeys.shared.kSCCallUnitPriceKey defaultValue:0];

}

- (NSTimeInterval)creatAt{
    if(_creatAt == 0){
        _creatAt = [NSDate new].timeIntervalSince1970;
    }
    return _creatAt;
}

- (BOOL)isMyCall{
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:nil];
    NSString *userId = [SCDictionaryHelper userIDFromDictionary:userInfo];
    if(kSCIsStrEmpty(userId) || kSCIsStrEmpty(self.fromUserId)){
        return NO;
    }
    return [self.fromUserId isEqualToString:userId];
}

- (NSString *)tagId{
    return self.isMyCall ? self.toUserId:self.fromUserId;
}

- (NSString *)callReasonStr{
    if(self.isMyCall){
        switch (self.endType) {
            case SCCallSessionEndTypeUnknown:
                
                if (self.callDuration > 0) {
                    return @"Outgoing call".translateString;
                }else {
                    return @"Unanswered call".translateString;
                }
                break;
            case SCCallSessionEndTypeNormal:
                //常规
                return @"Outgoing call".translateString;
                
            case SCCallSessionEndTypeCancelMyself:
                //自己取消
                if (self.callDuration > 0) {
                    return @"Outgoing call".translateString;
                }else {
                    return @"Cancelled call".translateString;
                }
                
            case SCCallSessionEndTypeUnanswered:
                //对方挂断
                if (self.callDuration > 0) {
                    return @"Outgoing call".translateString;
                }else {
                    return @"Unanswered call".translateString;
                }
               
            case SCCallSessionEndTypeMissed:
                //没有处理
                return @"Missed call".translateString;

        }
    }else{
        switch (self.endType) {
            case SCCallSessionEndTypeNormal:
                //常规
                return @"Incoming call".translateString;
                
            case SCCallSessionEndTypeCancelMyself:
                //自己取消
                return @"Rejected call".translateString;
            case SCCallSessionEndTypeUnanswered:
                //其他问题导致中断
                return @"Missed call".translateString;
            case SCCallSessionEndTypeMissed:
                //没有处理
                return @"Missed call".translateString;
            case SCCallSessionEndTypeUnknown:
                return @"Missed call".translateString;
      
        }
    }
    return @"Unknown";
    
}
- (UIColor *)callReasonColor{
    if(self.endType == SCCallSessionEndTypeNormal || self.callDuration > 0 ){
        return kSCColorWithHexStr(@"#8E8B8B");
    }
    return kSCColorWithHexStr(@"#F23030");
}





#pragma mark - 数据库
+(instancetype) sessionWithNativeSQLiteResultSet:(SCNativeSQLiteResultSet *) resultSet{
    SCCallSessionModel *callSession = [[SCCallSessionModel alloc] init];
    callSession.clientSessionId = [resultSet stringForColumn:@"sessionId"];
    callSession.status = [resultSet intForColumn:@"status"];
    callSession.isFree = [resultSet boolForColumn:@"isFree"];
    callSession.callFreeSeconds = [resultSet intForColumn:@"callFreeSeconds"];
    callSession.fromUserId = [resultSet stringForColumn:@"fromUserId"];
    callSession.toUserId = [resultSet stringForColumn:@"toUserId"];
    callSession.rtcToken = [resultSet stringForColumn:@"rtcToken"];
    callSession.clientSessionId = [resultSet stringForColumn:@"clientSessionId"];
    callSession.chooseVideoSdk = [resultSet intForColumn:@"chooseVideoSdk"];
    callSession.isGreenMode = [resultSet boolForColumn:@"isGreenMode"];
    callSession.channelName = [resultSet stringForColumn:@"channelName"];
    callSession.broadcasterUnitPrice = [resultSet intForColumn:@"broadcasterUnitPrice"];
    callSession.unitPrice = [resultSet intForColumn:@"unitPrice"];
    callSession.hangUpReason = [resultSet intForColumn:@"hangUpReason"];
    callSession.endType = [resultSet intForColumn:@"endType"];
    callSession.callDuration = [resultSet intForColumn:@"callDuration"];
    callSession.creatAt = [resultSet doubleForColumn:@"creatAt"];
    return callSession;
}




@end
