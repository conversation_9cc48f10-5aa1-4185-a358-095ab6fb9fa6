//
//  SCCallSessionModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/24.
//

#import <Foundation/Foundation.h>
#import "SCCallSessionStatus.h"
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, SCCallHangUpReason) {
    SCCallHangUpReasonNormal = 1, // 常规
    SCCallHangUpReasonNoCoins = 2, // 金币不足
    SCCallHangUpReasonClientException = 3, // 客户端异常
    SCCallHangUpReasonCallTimeout = 4, // 呼叫超时
    SCCallHangUpReasonRemoteUserLeft = 5, // 对方退出
    SCCallHangUpReasonServerException = 6, // 服务端错误
    SCCallHangUpReasonNetworkException = 7, // 网络错误
    SCCallHangUpReasonServerConnectTimeout = 8 // 服务器强制结束
};

@class SCNativeSQLiteResultSet;
@interface SCCallSessionModel : NSObject

//呼叫状态
@property(nonatomic,assign) SCCallSessionStatus status;

///是否免费
@property (nonatomic, assign) BOOL isFree;
///免费时长
@property (nonatomic, assign) NSInteger callFreeSeconds;
///对方用户ID
@property (nonatomic, copy) NSString *fromUserId;

///被呼叫方的ID
@property (nonatomic, copy) NSString *toUserId;
///声网token
@property (nonatomic, copy)   NSString *rtcToken;
///客户端会话ID
@property (nonatomic, copy)   NSString *clientSessionId;
// 选用的视频sdk: 1- > 声网；2 -> bigo；
@property (nonatomic, assign) NSInteger chooseVideoSdk;
///是否绿色模式
@property (nonatomic, assign) BOOL isGreenMode;
///频道名称
@property (nonatomic, copy)   NSString *channelName;
///通话价格[判断通话价格好像不正确目前是使用 unitPrice]
@property (nonatomic, assign) NSInteger broadcasterUnitPrice;
//通话价格
@property (nonatomic, assign) NSInteger unitPrice;
///
///挂断原因
@property (nonatomic, assign) SCCallHangUpReason hangUpReason;
//结束类型
@property (nonatomic, assign) SCCallSessionEndType endType;
//通话时长
@property (nonatomic,assign) NSInteger callDuration;
//创建时间
@property (nonatomic,assign) NSTimeInterval creatAt;
///是否快速匹配
@property (nonatomic,assign) BOOL isMatch;


#pragma mark - 构造函数
//字典版本的被呼叫初始化方法
- (instancetype)initWithOnCallDict:(NSDictionary *) onCallDict;
//根据快速匹配成功创建会话模型
- (instancetype)initWithClientSessionId:(NSString *)clientSessionId isMatch:(BOOL) isMatch fromUserId:(NSString *)fromUserId;
//新增字典配置方法
-(void) configWithFlashMatchDict:(NSDictionary *) flashMatchDict;
-(void) configWithChannelDict:(NSDictionary *)channelDict;
- (instancetype)initWithClientSessionId:(NSString *)clientSessionId unitPrice:(NSInteger ) unitPrice fromUserId:(NSString *)fromUserId toUserId:(NSString *)toUserId;

#pragma mark - 只读属性
//是否我发起的呼叫
@property (nonatomic,assign,readonly) BOOL isMyCall;
@property (nonatomic,copy,readonly) NSString * tagId;
@property (nonatomic,copy,readonly) NSString * callReasonStr;
@property (nonatomic,strong,readonly) UIColor * callReasonColor;

#pragma mark - 数据库
///数据库返回的结果转化为当前对象
+(instancetype) sessionWithNativeSQLiteResultSet:(SCNativeSQLiteResultSet *) resultSet;
@end

NS_ASSUME_NONNULL_END
