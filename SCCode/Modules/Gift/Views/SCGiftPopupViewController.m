//
//  SCGiftPopupViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCGiftPopupViewController.h"
#import "SCGiftCell.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import "SCAPIServiceManager.h"
#import "SCCustomPageControl.h"
#import "SCCoinsService.h"
#import "SCGiftService.h"
#import "SCCategoryAPIManagerGift.h"
#import "SCGiftAnimPlayView.h"
#import "SCCoinsPopupViewController.h"
#import "SCGiftSendNumModel.h"
// 移除YYModel依赖
#import "SCPopupManager.h"

// 定义常量
static const CGFloat kSCGiftLeftRightMargin = 14.0f;
static const CGFloat kSCGiftLineMargin = 17.0f;
static const CGFloat kSCGiftColumnMargin = 10.0f;
static const NSInteger kSCGiftColumnCount = 4;

@interface SCGiftPopupViewController (){
    dispatch_block_t lastOperation;
}

@property(nonatomic,weak) UIView *contentV;
@property(nonatomic,weak) UIButton *closeBtn;
@property(nonatomic,weak) UICollectionView *giftCV;
@property(nonatomic,weak) UIButton *getCoinsBtn;
@property(nonatomic,weak) UIView *myCoinsLayout;
@property(nonatomic,weak) UILabel *myCoinsLabel;
@property(nonatomic,weak) UIImageView *myCoinsIconV;
@property(nonatomic,weak) SCCustomPageControl *pageControl;

@property(nonatomic,strong) NSMutableArray<NSDictionary *> * gifts;

@property(nonatomic,copy) NSString * userId;
@property(nonatomic,copy) NSString * channelName;
///是否可以连续发送礼物
@property(nonatomic,assign) BOOL isMulti;
//当前可用的余额
@property(nonatomic,assign) NSInteger availableCoins;
///上一次发送礼物的时间
@property(nonatomic,assign) NSTimeInterval lastSendTime;
///等待发送
@property(nonatomic,strong) NSMutableDictionary<NSString *,SCGiftSendNumModel *> *waitSendGift;
//串行队列保证数据安全
@property (nonatomic, strong) dispatch_queue_t serialQueue;
// 是否隐藏客服入口
@property (nonatomic, assign) BOOL hideService;
@property (nonatomic, assign) BOOL isFromVideoCall;

@end

@implementation SCGiftPopupViewController

- (void)viewDidLoad {
[super viewDidLoad];
    
    //刷新金币
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
}
- (void)initData{
    [super initData];
    _serialQueue = dispatch_queue_create("com.sc.gift.serial", DISPATCH_QUEUE_SERIAL);
    _gifts = [NSMutableArray new];
    _giftSendNumChangeObs = [[SCObservable<SCGiftSendNumModel *> alloc] initWithValue:nil];
    _giftCompleteObs = [[SCObservable<SCGiftSendNumModel *> alloc] initWithValue:nil];
    _waitSendGift = [NSMutableDictionary new];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    UIView *contentV = [[UIView alloc] init].setBackgroundColor([UIColor colorWithHexString:@"#282828"]);
    ///顶部圆角
    contentV.layer.cornerRadius = kSCBigCornerRadius;
    contentV.layer.masksToBounds = YES;
    contentV.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.view addSubview:contentV];
    self.contentV = contentV;
    
    ///礼物item宽度
    CGFloat giftItemWidth = (kSCScreenWidth - kSCGiftLeftRightMargin * 2 - (kSCGiftColumnCount - 1)  * kSCGiftColumnMargin) / ((CGFloat)kSCGiftColumnCount);
    ///礼物item高度
    CGFloat giftItemHeight = (giftItemWidth / 79.0) * 88.0f;
    
    CGFloat totalHeight = giftItemHeight * 2 + kSCGiftLineMargin + 1;
    
    UICollectionViewCompositionalLayout *layout = [self createGiftCollectionViewLayout];
    
    UICollectionView *giftCV = [UICollectionView collectionViewWithFrame:CGRectZero layout:layout delegate:self dataSource:self cellClass:[SCGiftCell class] forCellReuseIdentifier:[SCGiftCell cellIdentifier]];
    giftCV.backgroundColor = [UIColor clearColor];
    giftCV.showsHorizontalScrollIndicator = NO;
    giftCV.showsVerticalScrollIndicator = NO;
    [contentV addSubview:giftCV];
    self.giftCV = giftCV;
    
    SCCustomPageControl * pageControl = [[SCCustomPageControl alloc] init];
    pageControl.activePageColor = UIColor.scTheme;
    pageControl.inactivePageColor = UIColor.scWhite;
    pageControl.currentPage = 0;
    if (kScAuthMar.isLanguageForce) {
        pageControl.transform = CGAffineTransformMakeScale(-1, 1);
    }
    
    [self.contentV addSubview:pageControl];
    self.pageControl = pageControl;
    
    UIButton *closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_close"]];
    [closeBtn addTarget:self action:@selector(onClose) forControlEvents:UIControlEventTouchUpInside];
    [contentV addSubview:closeBtn];
    self.closeBtn = closeBtn;
    
    UIView *myCoinsLayout = [[UIView alloc] init];
    myCoinsLayout.backgroundColor = [UIColor colorWithHexString:@"#282828"];
    [contentV addSubview:myCoinsLayout];
    self.myCoinsLayout = myCoinsLayout;
    
    UIImageView *myCoinsIconV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_coins_anchor_info"]];
    [myCoinsLayout addSubview:myCoinsIconV];
    self.myCoinsIconV = myCoinsIconV;
    
    UILabel *myCoinsLabel = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontMedium(16) alignment:NSTextAlignmentCenter];
    [myCoinsLayout addSubview:myCoinsLabel];
    self.myCoinsLabel = myCoinsLabel;
    
    
    UIButton *getCoinsBtn = [UIButton buttonWithTitle:@"Get Coins".translateString titleColor:UIColor.scWhite font:kScUIFontSemibold(16.0f) image:nil backgroundColor:nil cornerRadius:18.0f];
    [getCoinsBtn addTarget:self action:@selector(onGetCoins) forControlEvents:UIControlEventTouchUpInside];
    [getCoinsBtn sc_setThemeGradientBackgroundWithCornerRadius:18.0f];
    [self.contentV addSubview:getCoinsBtn];
    self.getCoinsBtn = getCoinsBtn;
    
    // 添加以下代码来设置按钮的内容边距
    [getCoinsBtn setContentEdgeInsets:UIEdgeInsetsMake(0, 9, 0, 9)]; // 左右各添加15点的内边距
    [self.getCoinsBtn sizeToFit];
    CGSize btnSize = self.getCoinsBtn.bounds.size;

    [self.getCoinsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(36);
        make.width.mas_equalTo(btnSize.width + 18); // 额外添加30点宽度，以适应内边距
        make.leading.greaterThanOrEqualTo(self.myCoinsLayout.mas_trailing).offset(10);
        make.trailing.equalTo(self.contentV).offset(-kSCGiftLeftRightMargin);
        make.centerY.equalTo(self.myCoinsLayout);
    }];
    
    [self.contentV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.top.trailing.equalTo(self.contentV);
    }];
    
    [self.giftCV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentV).offset(40.0f);
        make.leading.trailing.equalTo(self.contentV);
        make.height.mas_equalTo(totalHeight);
    }];
    
    [self.pageControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentV);
        make.top.equalTo(self.giftCV.mas_bottom).offset(0);
        make.size.mas_equalTo(CGSizeMake(200, 23));
    }];
    
    [self.myCoinsLayout mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.pageControl.mas_bottom).offset(0);
        make.leading.equalTo(self.contentV).offset(kSCGiftLeftRightMargin);
        make.height.mas_equalTo(36);
        make.bottom.equalTo(self.contentV).offset(-20 - kSCSafeAreaBottomHeight);
    }];
    
    [self.myCoinsIconV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.myCoinsLayout).offset(10);
        make.centerY.equalTo(self.myCoinsLayout);
        make.width.mas_equalTo(23);
        make.height.mas_equalTo(18);
    }];
    
    [self.myCoinsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.myCoinsIconV.mas_trailing).offset(15);
        make.centerY.equalTo(self.myCoinsLayout);
        make.trailing.equalTo(self.myCoinsLayout).offset(-10);
    }];
    if(kSCAuthGiftService.giftListObx.value.count > 0) {
        [self.gifts removeAllObjects];
        [self.gifts addObjectsFromArray:kSCAuthGiftService.giftListObx.value];
    }
    
    kWeakSelf(self);
    [kSCAuthGiftService.giftListObx subscribe:^(NSArray<NSDictionary *> * _Nonnull gifts) {
        if (gifts.count > 0) {
            [weakself.gifts removeAllObjects];
            [weakself.gifts addObjectsFromArray:gifts];
            [weakself.giftCV reloadData];
            pageControl.numberOfPages = gifts.count/8 + (gifts.count%8 == 0 ? 0 : 1);
            pageControl.currentPage = pageControl.currentPage;
        }
        
    } error:nil disposeBag:self.disposeBag];
    if([kSCAuthGiftService.giftListObx.value count] == 0){
        //请求礼物数据
        [kSCAuthGiftService remoteGiftListWithSuccess:nil failure:nil];
    }
    
    [kScAuthMar.availableCoinsObx subscribe:^(NSNumber * _Nonnull value) {
        weakself.availableCoins = value.integerValue;
        weakself.myCoinsLabel.text = [NSString stringWithFormat:@"%d",value.intValue];
    } error:nil disposeBag:self.disposeBag];
    
}


- (UICollectionViewCompositionalLayout *)createGiftCollectionViewLayout {
    CGFloat giftItemWidth = (kSCScreenWidth - kSCGiftLeftRightMargin * 2 - (kSCGiftColumnCount - 1) * kSCGiftColumnMargin) / kSCGiftColumnCount;
    CGFloat giftItemHeight = (giftItemWidth / 79.0) * 88.0f;

    NSCollectionLayoutSize *itemSize = [NSCollectionLayoutSize sizeWithWidthDimension:[NSCollectionLayoutDimension absoluteDimension:giftItemWidth]
                                                                     heightDimension:[NSCollectionLayoutDimension absoluteDimension:giftItemHeight]];
    
    NSCollectionLayoutItem *item = [NSCollectionLayoutItem itemWithLayoutSize:itemSize];
    item.contentInsets = NSDirectionalEdgeInsetsMake(0, 0, 0, 0);
    
    NSArray *subitems = @[item, item, item, item];
    
    NSCollectionLayoutSize *horizontalGroupSize = [NSCollectionLayoutSize sizeWithWidthDimension:[NSCollectionLayoutDimension absoluteDimension:4 * giftItemWidth + 3 * kSCGiftColumnMargin]
                                                                            heightDimension:[NSCollectionLayoutDimension absoluteDimension:giftItemHeight]];
    
    NSCollectionLayoutGroup *horizontalGroup = [NSCollectionLayoutGroup horizontalGroupWithLayoutSize:horizontalGroupSize subitems:subitems];
    horizontalGroup.interItemSpacing = [NSCollectionLayoutSpacing fixedSpacing:kSCGiftColumnMargin];
    
    NSCollectionLayoutSize *verticalGroupSize = [NSCollectionLayoutSize sizeWithWidthDimension:[NSCollectionLayoutDimension absoluteDimension:4 * giftItemWidth + 3 * kSCGiftColumnMargin]
                                                                          heightDimension:[NSCollectionLayoutDimension absoluteDimension:2 * giftItemHeight + kSCGiftLineMargin]];
    
    NSCollectionLayoutGroup *verticalGroup = [NSCollectionLayoutGroup verticalGroupWithLayoutSize:verticalGroupSize subitems:@[horizontalGroup, horizontalGroup]];
    verticalGroup.interItemSpacing = [NSCollectionLayoutSpacing fixedSpacing:kSCGiftLineMargin];
    
    NSCollectionLayoutSection *section = [NSCollectionLayoutSection sectionWithGroup:verticalGroup];
    
    section.contentInsets = NSDirectionalEdgeInsetsMake(0, kSCGiftLeftRightMargin, 0, kSCGiftLeftRightMargin);
    
    section.interGroupSpacing = kSCGiftLeftRightMargin;
    
    section.orthogonalScrollingBehavior = UICollectionLayoutSectionOrthogonalScrollingBehaviorGroupPaging;

    // 添加 visibleItemsInvalidationHandler
    kWeakSelf(self)
    section.visibleItemsInvalidationHandler = ^(NSArray<NSCollectionLayoutItem *> *visibleItems, CGPoint contentOffset, id<NSCollectionLayoutEnvironment> environment) {
        CGFloat pageWidth = CGRectGetWidth(weakself.giftCV.bounds);
        CGFloat scrollOffset = contentOffset.x;
        
        // 计算当前页
        NSInteger currentPage = round(scrollOffset / pageWidth);
        
        // 更新 pageControl
        if (weakself.pageControl.currentPage != currentPage) {
            weakself.pageControl.currentPage = currentPage;
        }
    };
    
    return [[UICollectionViewCompositionalLayout alloc] initWithSection:section];
}

#pragma mark - Action
-(void)onClose{
    [[SCPopupManager shared] dismissPopup:self];
}
-(void)onGetCoins{
    [self showCoinsPopupWithEntry:self.isFromVideoCall ? SCPayEntry.shared.kPayEntrySourceChatting : SCPayEntry.shared.kPayEntrySourceIM];
}

-(void)showCoinsPopupWithEntry:(NSString *)entry {
    [SCCoinsPopupViewController showWithFromVC:self hideService:self.hideService entry:entry];
}

- (nonnull __kindof UICollectionViewCell *)collectionView:(nonnull UICollectionView *)collectionView cellForItemAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCGiftCell * cell = [SCGiftCell initWithFormCollectionView:collectionView forIndexPath:indexPath];
    if (indexPath.item < self.gifts.count) {
        // 使用字典版本的配置方法
        [cell configureWithDict:self.gifts[indexPath.item]];
    }
    return cell;
}

- (NSInteger)collectionView:(nonnull UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return  self.gifts.count;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{

    // 使用字典数据
    NSDictionary *giftDict = self.gifts[indexPath.row];
    kWeakSelf(self);
    dispatch_async(self.serialQueue, ^{
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        NSTimeInterval nowTime = [NSDate new].timeIntervalSince1970;
        // 计算时间间隔
        NSTimeInterval off = nowTime - self.lastSendTime;
        // 如果是首次发送，或者间隔足够大，允许发送
        if (strongSelf.giftSendNumChangeObs.value == nil || off >= 0.5) {
            // 更新最后发送时间
            strongSelf.lastSendTime = nowTime;
            // 继续执行发送逻辑...
        } else {
            // 间隔太小，拦截发送
            return;
        }
        NSInteger coinPrice = kSCGiftCoinPriceFromDict(giftDict);
        if(coinPrice > self.availableCoins){
            
            dispatch_async(dispatch_get_main_queue(), ^{
                //提示余额不足
                [kSCKeyWindow toast:@"Coins not enough".translateString];
                [self showCoinsPopupWithEntry:self.isFromVideoCall ? SCPayEntry.shared.kPayEntrySourceChattingGift : SCPayEntry.shared.kPayEntrySourceIMGift];
            });
           
            return;
        }
        
        
        if(strongSelf.isMulti){
            //多次发送
            SCGiftSendNumModel * send;
            //先减掉余额
            strongSelf.availableCoins = strongSelf.availableCoins - coinPrice;
            NSInteger availableCoins = strongSelf.availableCoins;
            //调用礼物相关逻辑接口
            dispatch_async(dispatch_get_main_queue(), ^{
                strongSelf.myCoinsLabel.text = [NSString stringWithFormat:@"%ld",availableCoins];
            });
            
            NSString *giftCode = kSCGiftCodeFromDict(giftDict);
            if(off >= 0.5 || strongSelf.giftSendNumChangeObs.value == nil || ![kSCGiftCodeFromDict(strongSelf.giftSendNumChangeObs.value.giftModel) isEqualToString:giftCode]){
                
                //如果超过 0.1秒点击，或者 没有上一个礼物，  或者上一个礼物和当前礼物不是相同的礼物。则执行新的礼物发送
                //连续超过1秒则进入下一批
                send = [[SCGiftSendNumModel alloc] init];
                send.sessionId = [[NSUUID UUID] UUIDString];
                // 直接使用字典，无需转换
                send.giftModel = giftDict;
                send.lastTime = nowTime;
                send.giftNum = 1;
                
                
                
            }else{
                //获取上一个礼物
                send = strongSelf.giftSendNumChangeObs.value;
                send.lastTime = nowTime;
                send.giftNum ++;
                //刷新订阅
                
            }
            strongSelf.giftSendNumChangeObs.value = send;
            [strongSelf.waitSendGift setObject:send forKey:send.sessionId];
            
            NSArray *array = @[strongSelf.giftSendNumChangeObs.value.sessionId,kSCGiftCodeFromDict(strongSelf.giftSendNumChangeObs.value.giftModel),@(strongSelf.giftSendNumChangeObs.value.giftNum)];

            //延迟半秒执行
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), strongSelf.serialQueue, ^{
                [strongSelf sendGiftWithModel:array];
            });
           
            
            
            //刷新时间
            self.lastSendTime = nowTime;
            
            
        }else{
            
            SCGiftSendNumModel * send = [[SCGiftSendNumModel alloc] init];
            // 直接使用字典，无需转换
            send.giftModel = giftDict;
            send.giftNum = 1;
            strongSelf.giftSendNumChangeObs.value = send;
            //调用礼物相关逻辑接口
            [SCAPIServiceManager requestGiveUserGiftsWithRecipientUserId:self.userId giftCode:kSCGiftCodeFromDict(giftDict) num:1 channelName:self.channelName success:^(NSInteger coins) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    //更新余额
                    [kScAuthMar updateLocalAvailableCoins:coins];
                    //播放动画
                    [SCGiftAnimPlayView show:kSCGiftCodeFromDict(giftDict) inView:self.sourceViewController];
                    //更新通知
                    weakself.giftCompleteObs.value = send;
                });
                
            } failure:^(SCXErrorModel * _Nonnull error) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [weakself.view toast:error.msg];
                });
            }];
        }
       });
    
 
    
    
    
}
-(void) sendGiftWithModel:(NSArray *)array{
    NSString *sesion = array[0];
    NSString *giftCode = array[1];
    NSInteger num = [array[2] integerValue];
    

    
// 移除YYModel相关的注释代码
    
    SCGiftSendNumModel * newSend = self.waitSendGift[sesion];
    
    if(newSend != nil && num != newSend.giftNum){
        //还有未发送的数据等待
        
        return;
    }
    if(newSend == nil){
        //数据异常
        
        return;
    }
    
    [self.waitSendGift removeObjectForKey:sesion];
    if([sesion isEqualToString:self.giftSendNumChangeObs.value.sessionId]){
        self.giftSendNumChangeObs.value = nil;
    }
    
    kWeakSelf(self);
    [SCAPIServiceManager requestGiveUserGiftsWithRecipientUserId:self.userId giftCode:kSCGiftCodeFromDict(newSend.giftModel) num:newSend.giftNum channelName:self.channelName success:^(NSInteger coins) {
        dispatch_async(dispatch_get_main_queue(), ^{
            //更新余额
            [kScAuthMar updateLocalAvailableCoins:coins];
            //播放动画
            [SCGiftAnimPlayView show:kSCGiftCodeFromDict(newSend.giftModel) inView:self.sourceViewController];
            //更新通知
            weakself.giftCompleteObs.value = newSend;
        });
        
        
    } failure:^(SCXErrorModel * _Nonnull error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakself.view toast:error.msg];
            
            weakself.availableCoins = self.availableCoins + ( kSCGiftCoinPriceFromDict(newSend.giftModel) *newSend.giftNum);
            weakself.myCoinsLabel.text = [NSString stringWithFormat:@"%ld",weakself.availableCoins];
        });
    }];
}

+(SCGiftPopupViewController *) showWithFromVC:(UIViewController * _Nonnull)fromVC userId:(NSString * _Nullable)userId channelName:(NSString * _Nullable)channelName isMulti:(BOOL)isMulti isFromVideoCall:(BOOL)isFromVideoCall {
    SCGiftPopupViewController *vc = [[SCGiftPopupViewController alloc] init];
    vc.sourceViewController = fromVC.view;
    vc.userId = userId;
    vc.isMulti = isMulti;
    vc.channelName = channelName;
    vc.isFromVideoCall = isFromVideoCall;
    [[SCPopupManager shared] showPopup:vc inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
    
    return vc;
}

+(SCGiftPopupViewController *) showWithFromVC:(UIViewController * _Nonnull)fromVC userId:(NSString * _Nullable)userId channelName:(NSString * _Nullable)channelName isMulti:(BOOL)isMulti hideService:(BOOL)hideService isFromVideoCall:(BOOL)isFromVideoCall {
    SCGiftPopupViewController *vc = [[SCGiftPopupViewController alloc] init];
    vc.sourceViewController = fromVC.view;
    vc.userId = userId;
    vc.isMulti = isMulti;
    vc.channelName = channelName;
    vc.hideService = hideService;
    vc.isFromVideoCall = isFromVideoCall;
    [[SCPopupManager shared] showPopup:vc inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
    
    return vc;
}

@end


