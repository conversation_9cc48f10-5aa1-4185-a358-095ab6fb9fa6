//
//  SCGiftPopupViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCBaseViewController.h"
@class SCGiftSendNumModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCGiftPopupViewController<UICollectionViewDelegate,UICollectionViewDataSource> : SCBaseViewController

//点击变化 
@property(nonatomic,strong) SCObservable<SCGiftSendNumModel *> *giftSendNumChangeObs;
//完成
@property(nonatomic,strong) SCObservable<SCGiftSendNumModel *> *giftCompleteObs;

@property (nonatomic, weak) UIView *sourceViewController;

+(SCGiftPopupViewController *) showWithFromVC:(UIViewController * _Nonnull)fromVC userId:(NSString * _Nullable)userId channelName:(NSString * _Nullable)channelName isMulti:(BOOL)isMulti isFromVideoCall:(BOOL)isFromVideoCall;

+(SCGiftPopupViewController *) showWithFromVC:(UIViewController * _Nonnull)fromVC userId:(NSString * _Nullable)userId channelName:(NSString * _Nullable)channelName isMulti:(BOOL)isMulti hideService:(BOOL)hideService isFromVideoCall:(BOOL)isFromVideoCall;


@end

NS_ASSUME_NONNULL_END
