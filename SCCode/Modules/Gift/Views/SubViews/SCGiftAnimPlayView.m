//
//  SCGiftAnimPlayView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCGiftAnimPlayView.h"
#import "SCGiftService.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import "Lottie/Lottie-Swift.h"

@interface SCGiftAnimPlayView()
@property (nonatomic, strong) UIImageView *giftImageView;
@property (nonatomic, strong) CompatibleAnimationView *giftAnimationView;
@property (nonatomic, copy) NSString *giftCode;
@property (nonatomic, copy) void (^finishCallBack)(SCGiftAnimPlayView *);

@end

@implementation SCGiftAnimPlayView



static __weak SCGiftAnimPlayView *showingView = nil;

- (instancetype)initWithFrame:(CGRect)frame giftCode:(NSString *)giftCode finishCallBack:(void (^)(SCGiftAnimPlayView * view))finishCallBack {
    self = [super initWithFrame:frame];
    if (self) {
        _giftCode = giftCode;
        _finishCallBack = finishCallBack;
        [self initView];
    }
    return self;
}



- (void)initView {
    // 创建 Lottie 动画视图
    NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/gift_send_animation.json"];
    NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
    
    NSError *error = nil;
    NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
    if (jsonData) {
        self.giftAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
        self.giftAnimationView.userInteractionEnabled = NO;
        self.giftAnimationView.frame = self.bounds;
        self.giftAnimationView.loopAnimationCount = 2;
        self.giftAnimationView.hidden = YES;
        [self addSubview:self.giftAnimationView];
    }
    
    self.giftImageView = [[UIImageView alloc] initWithFrame:CGRectMake((self.frame.size.width - 183) / 2.0, (self.frame.size.height - 183) / 2.0, 183, 183)];
    self.giftImageView.contentMode = UIViewContentModeScaleAspectFit;
    self.giftImageView.hidden = YES;
    [self addSubview:self.giftImageView];
    
    self.userInteractionEnabled = NO;
    
    // 获取礼物字典数据
    NSDictionary *giftDict = [kSCAuthGiftService giftDictWithCode:self.giftCode];
    NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);
    NSString *iconPath = kSCGiftIconPathFromDict(giftDict);
    if (!kSCIsStrEmpty(iconThumbPath) && !kSCIsStrEmpty(iconPath)) {
        [self.giftImageView sc_setImageWithURL:iconPath thumbURL:iconThumbPath];
    }
    self.giftImageView.hidden = NO;
    self.giftAnimationView.hidden = NO;
    kWeakSelf(self);
    void(^completionBlock)(BOOL) = ^(BOOL completion) {
        kStrongSelf
        if (strongSelf && strongSelf.finishCallBack) {
            strongSelf.finishCallBack(strongSelf);
        }
    };
    [self.giftAnimationView playWithCompletion:completionBlock];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.giftAnimationView.frame = CGRectMake(0, (self.frame.size.height - self.frame.size.width) / 2.0, self.frame.size.width, self.frame.size.width);
    self.giftImageView.frame = CGRectMake((self.frame.size.width - 183) / 2.0, (self.frame.size.height - 183) / 2.0, 183, 183);
}

#pragma mark - 显示&隐藏

+ (void)show:(NSString *)giftCode inView:(nonnull UIView *)containView {
    if (showingView) {
        [showingView removeFromSuperview];
    }

    
    SCGiftAnimPlayView *giftView = [[SCGiftAnimPlayView alloc] initWithFrame:containView.bounds giftCode:giftCode finishCallBack:^(SCGiftAnimPlayView * _Nonnull view) {
        [view removeFromSuperview];
        
    }];
    
    if (giftView) {
        [containView addSubview:giftView];
        showingView = giftView;
        giftView.alpha = 0;
        [UIView animateWithDuration:0.3 animations:^{
            giftView.alpha = 1;
        }];
    }
}
@end
