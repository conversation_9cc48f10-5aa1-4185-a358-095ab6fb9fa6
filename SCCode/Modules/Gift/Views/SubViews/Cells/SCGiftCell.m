//
//  SCGiftCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCGiftCell.h"
// #import "SCGiftModel.h" // 已移除，使用字典替代
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCGiftCell()

@property(nonatomic,weak) UIImageView *iconIV;
@property(nonatomic,weak) UIView *coinLayoutV;
@property(nonatomic,weak) UIImageView *coinIV;
@property(nonatomic,weak) UILabel *coinLabel;

@end

@implementation SCGiftCell


- (void)initUI{
    [super initUI];
    
    //圆角线条
    self.contentView.layer.cornerRadius = kSCNormalCornerRadius;
    self.contentView.backgroundColor = [UIColor colorWithHexString:@"#343434"];
    
    UIImageView *iconIV = [[UIImageView alloc] init];
    [self.contentView addSubview:iconIV];
    self.iconIV = iconIV;
    
    UIView *coinLayoutV = [[UIView alloc] init];
    [self.contentView addSubview:coinLayoutV];
    self.coinLayoutV = coinLayoutV;
    
    UIImageView *coinIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_coins_small"]];
    [self.coinLayoutV addSubview:coinIV];
    self.coinIV = coinIV;
    
    UILabel *coinLabel = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontRegular(10) alignment:NSTextAlignmentLeft];
    [self.coinLayoutV addSubview:coinLabel];
    self.coinLabel = coinLabel;
    
    [self.iconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(10);
        make.centerX.equalTo(self.contentView);
        make.width.height.equalTo(@46);
    }];
    
    [self.coinLayoutV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconIV.mas_bottom).offset(5);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(17.0f);
    }];
    
    [self.coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.coinLayoutV).offset(0);
        make.centerY.equalTo(self.coinLayoutV);
        make.size.mas_equalTo(CGSizeMake(12, 9));
    }];
    
    [self.coinLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.coinIV.mas_trailing).offset(5);
        make.centerY.equalTo(self.coinLayoutV);
        make.trailing.equalTo(self.coinLayoutV).offset(0);
    }];
    
}


// 已移除setModel方法，使用configureWithDict替代
// - (void)setModel:(SCGiftModel *)model{
//     _model = model;
//
//     [self.iconIV sc_setImageWithURL:model.iconThumbPath];
//     self.coinLabel.text = [NSString stringWithFormat:@"%ld",model.coinPrice];
//
// }

- (void)configureWithDict:(NSDictionary *)giftDict {
    self.giftDict = giftDict;

    // 使用便捷访问宏配置UI
    NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftDict);
    [self.iconIV sc_setImageWithURL:iconThumbPath];

    NSInteger coinPrice = kSCGiftCoinPriceFromDict(giftDict);
    self.coinLabel.text = [NSString stringWithFormat:@"%ld", coinPrice];
}

@end
