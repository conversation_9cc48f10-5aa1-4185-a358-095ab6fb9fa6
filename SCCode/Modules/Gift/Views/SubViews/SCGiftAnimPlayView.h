//
//  SCGiftAnimPlayView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCBaseView.h"


NS_ASSUME_NONNULL_BEGIN

@interface SCGiftAnimPlayView : SCBaseView

- (instancetype)initWithFrame:(CGRect)frame giftCode:(NSString *)giftCode finishCallBack:(void (^)(SCGiftAnimPlayView * view))finishCallBack;

+ (void)show:(NSString *)giftCode inView:(UIView *)containView;
@end

NS_ASSUME_NONNULL_END
