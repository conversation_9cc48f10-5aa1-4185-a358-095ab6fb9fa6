//
//  SCGiftSendNumTipDisplayModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
//

#import <Foundation/Foundation.h>
#import "SCGiftSendNumModel.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCGiftSendNumTipDisplayModel : NSObject

//礼物发送模型
@property(nonatomic,strong) SCGiftSendNumModel *giftSendNumModel;
//上一次接收的时间
@property(nonatomic,assign) NSTimeInterval lastReceiveTime;

@end

NS_ASSUME_NONNULL_END
