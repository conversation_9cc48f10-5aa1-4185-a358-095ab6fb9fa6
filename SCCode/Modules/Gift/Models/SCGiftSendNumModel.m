//
//  SCGiftSendNumModel.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/29.
//

#import "SCGiftSendNumModel.h"
@interface SCGiftSendNumModel(){
    NSString *_sessionId;
}
@end
@implementation SCGiftSendNumModel
- (id)copyWithZone:(NSZone *)zone {
    SCGiftSendNumModel *copy = [[SCGiftSendNumModel allocWithZone:zone] init];
    copy.giftNum = self.giftNum;
    copy.lastTime = self.lastTime;
    copy.giftModel = [self.giftModel copy];
    copy.sessionId = [self.sessionId copy];
    return copy;
}

@end
