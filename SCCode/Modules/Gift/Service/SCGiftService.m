//
//  SCGiftService.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCGiftService.h"
#import "SCCategoryAPIManagerGift.h"
#import "SCGiftAnimPlayView.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCGiftService()

@property(nonatomic, strong) SCXErrorModel * requestError;
@property(nonatomic, strong) NSDictionary * giftCountDict;
@end

@implementation SCGiftService


- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        _giftListObx = [[SCObservable<NSArray<NSDictionary *> *> alloc] initWithValue:@[]];
    }
    return self;
}
///根据Code获取礼物字典
-(NSDictionary * _Nullable) giftDictWithCode:(NSString *)code{
    NSArray<NSDictionary *> *gifts = self.giftListObx.value;
    for (NSDictionary * giftDict in gifts) {
        NSString *giftCode = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:nil];
        if([giftCode isEqualToString:code]){
            return giftDict;
        }
    }
    return  nil;
}

- (void)remoteGiftListWithSuccess:(void (^)(NSArray<NSDictionary *> * _Nonnull))success failure:(void (^)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requesGiftsWithSuccess:^(NSArray<NSDictionary *> * _Nonnull giftDicts) {
        weakself.giftListObx.value = giftDicts;
        kSCBlockExeNotNil(success, giftDicts);
    } failure:^(SCXErrorModel * _Nonnull error) {
        weakself.giftListObx.error = error;
        kSCBlockExeNotNil(failure, error);
    }];
}


// 获取用户礼物数量（返回字典数组）
-(void) requestGiftsNumDictsWithUserId:(NSString * _Nullable) userId success:(void (^_Nullable)(NSArray<NSDictionary *> * _Nullable giftNumDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);

    self.requestError = nil;

    dispatch_group_t requestGroup = dispatch_group_create();
    dispatch_queue_t concurrentQueue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);


    // 请求礼物列表
    dispatch_group_enter(requestGroup);
    __block BOOL isSuccessGift = false;

    dispatch_async(concurrentQueue, ^{
        if([weakself.giftListObx.value count] == 0){
            [weakself remoteGiftListWithSuccess:^(NSArray<NSDictionary *> * _Nonnull giftDicts) {
                dispatch_group_leave(requestGroup);
            } failure:^(SCXErrorModel * _Nonnull error) {
                isSuccessGift = true;
                if(!isSuccessGift)
                    dispatch_group_leave(requestGroup);
            }];

        }else{
            dispatch_group_leave(requestGroup);
        }

    });
    self.giftCountDict = nil;

    //请求数量
    dispatch_group_enter(requestGroup);
    __block BOOL isSuccessGiftCount = false;
    dispatch_async(concurrentQueue, ^{
        [SCAPIServiceManager requesGiftCountsWithUserID:userId success:^(NSDictionary * _Nonnull giftCountDict) {
            
            weakself.giftCountDict = giftCountDict;
            isSuccessGiftCount = YES;
            dispatch_group_leave(requestGroup);
        } failure:^(SCXErrorModel * _Nonnull error) {
            
            weakself.requestError = error;
            if(!isSuccessGiftCount)
                dispatch_group_leave(requestGroup);
        }];
    });


    // 等待所有请求完成
    dispatch_group_notify(requestGroup, concurrentQueue, ^{

        if(self.requestError){
            dispatch_async(dispatch_get_main_queue(), ^{

                kSCBlockExeNotNil(failure,self.requestError);
            });
            return;
        }
        // 所有请求完成后执行的操作
        __strong SCGiftService * strongSelf = weakself;
        NSArray<NSDictionary *> *giftNumDicts = nil;
        if(strongSelf.giftListObx.value != nil && self.giftCountDict != nil){
            giftNumDicts = [SCGiftService giftNumDictsWithGiftDicts:strongSelf.giftListObx.value giftCountDict:self.giftCountDict];
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            kSCBlockExeNotNil(success,giftNumDicts);
        });
    });

}

// 字典版本的工厂方法：根据礼物字典数组和礼物数量字典创建礼物数量字典数组
+(NSArray<NSDictionary *> * _Nonnull) giftNumDictsWithGiftDicts:(NSArray<NSDictionary *> *_Nonnull) giftDicts giftCountDict:(NSDictionary *_Nonnull)giftCountDict{
    NSMutableArray<NSDictionary *> *array = @[].mutableCopy;

    // 获取礼物数量数组
    NSArray *activityGiftNums = [SCDictionaryHelper arrayFromDictionary:giftCountDict forKey:@"activityGiftNum" defaultValue:@[]];
    NSArray *normalGiftNums = [SCDictionaryHelper arrayFromDictionary:giftCountDict forKey:@"normalGiftNum" defaultValue:@[]];

    //遍历giftDicts 并且遍历礼物数量
    for (NSDictionary *giftDict in giftDicts) {
        NSString *giftCode = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:nil];
        if (!giftCode) continue;

        // 检查活动礼物数量
        for (NSDictionary *giftNumDict in activityGiftNums) {
            NSString *numCode = [SCDictionaryHelper stringFromDictionary:giftNumDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:nil];
            if(numCode && [giftCode isEqualToString:numCode]){
                [array addObject:[SCGiftService giftNumDictWithGiftDict:giftDict giftNumDict:giftNumDict]];
                break;
            }
        }

        // 检查普通礼物数量
        for (NSDictionary *giftNumDict in normalGiftNums) {
            NSString *numCode = [SCDictionaryHelper stringFromDictionary:giftNumDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:nil];
            if(numCode && [giftCode isEqualToString:numCode]){
                [array addObject:[SCGiftService giftNumDictWithGiftDict:giftDict giftNumDict:giftNumDict]];
                break;
            }
        }
    }

    //根据 sortNo 降序
    [array sortUsingComparator:^NSComparisonResult(NSDictionary * _Nonnull obj1, NSDictionary * _Nonnull obj2) {
        NSInteger sortNo1 = [SCDictionaryHelper integerFromDictionary:obj1 forKey:SCDictionaryKeys.shared.kSCGiftSortNoKey defaultValue:0];
        NSInteger sortNo2 = [SCDictionaryHelper integerFromDictionary:obj2 forKey:SCDictionaryKeys.shared.kSCGiftSortNoKey defaultValue:0];
        if(sortNo1 > sortNo2){
            return NSOrderedAscending;
        }else if(sortNo1 < sortNo2){
            return NSOrderedDescending;
        }else{
            return NSOrderedSame;
        }
    }];

    return [array copy];
}

// 字典版本的辅助方法：根据礼物字典和礼物数量字典创建礼物数量字典
+(NSDictionary *) giftNumDictWithGiftDict:(NSDictionary *) giftDict giftNumDict:(NSDictionary *)giftNumDict{
    NSMutableDictionary *resultDict = [NSMutableDictionary dictionary];

    // 复制礼物信息
    resultDict[SCDictionaryKeys.shared.kSCGiftCodeKey] = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCodeKey defaultValue:@""];
    resultDict[SCDictionaryKeys.shared.kSCGiftCoinPriceKey] = @([SCDictionaryHelper integerFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCoinPriceKey defaultValue:0]);
    resultDict[SCDictionaryKeys.shared.kSCGiftSortNoKey] = @([SCDictionaryHelper integerFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftSortNoKey defaultValue:0]);
    resultDict[SCDictionaryKeys.shared.kSCGiftDescKey] = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftDescKey defaultValue:@""];
    resultDict[SCDictionaryKeys.shared.kSCGiftIconPathKey] = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftIconPathKey defaultValue:@""];
    resultDict[SCDictionaryKeys.shared.kSCGiftIconThumbPathKey] = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftIconThumbPathKey defaultValue:@""];
    resultDict[SCDictionaryKeys.shared.kSCGiftCountKey] = @([SCDictionaryHelper integerFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftCountKey defaultValue:0]);
    resultDict[SCDictionaryKeys.shared.kSCGiftNameKey] = [SCDictionaryHelper stringFromDictionary:giftDict forKey:SCDictionaryKeys.shared.kSCGiftNameKey defaultValue:@""];

    // 添加礼物数量信息
    resultDict[SCDictionaryKeys.shared.kSCGiftNumKey] = @([SCDictionaryHelper integerFromDictionary:giftNumDict forKey:@"num" defaultValue:0]);

    return [resultDict copy];
}


//发送礼物
-(void) sendMessageWithGiftCode:(NSString *) giftCode userId:(NSString *)userId changelName:(NSString *) changelName success:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    kWeakSelf(self)
    [SCAPIServiceManager requestGiveUserGiftsWithRecipientUserId:userId giftCode:giftCode num:1 channelName:changelName success:^(NSInteger coins) {
        dispatch_async(dispatch_get_main_queue(), ^{
            kSCBlockExeNotNil(success,coins);
        });
        
    } failure:failure];
}

@end
