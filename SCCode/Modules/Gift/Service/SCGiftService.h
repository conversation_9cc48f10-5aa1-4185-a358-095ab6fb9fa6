//
//  SCGiftService.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCBaseAppService.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCGiftService : SCBaseAppService

@property(nonatomic, strong) SCObservable<NSArray<NSDictionary *> *> *giftListObx;
///根据Code获取礼物字典
-(NSDictionary * _Nullable) giftDictWithCode:(NSString *)code;
- (void)remoteGiftListWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *> *giftDicts)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
// 获取用户礼物数量（返回字典数组）
-(void) requestGiftsNumDictsWithUserId:(NSString * _Nullable) userId success:(void (^_Nullable)(NSArray<NSDictionary *> * _Nullable giftNumDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

//发送礼物
-(void) sendMessageWithGiftCode:(NSString *) giftCode userId:(NSString *)userId changelName:(NSString *) changelName success:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
