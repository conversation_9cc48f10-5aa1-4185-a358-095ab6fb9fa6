//
//  SCCategoryAPIManagerGift.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCAPIServiceManager.h"



NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerGift)
///赠送礼物
+(void) requestGiveUserGiftsWithRecipientUserId:(NSString * _Nonnull) recipientUserId giftCode:(NSString * _Nonnull) giftCode num:(NSInteger)num channelName:(NSString * _Nullable)channelName success:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取主播礼物接口
+(void) requesGiftCountsWithUserID:(NSString * _Nonnull) userID success:(void (^_Nullable)(NSDictionary*_Nonnull giftCountDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取礼物
+(void) requesGiftsWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull giftDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

///获取活动礼物
+(void) requesActivityGiftsWithUserID:(NSString *_Nonnull)userId success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull activityGiftDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
