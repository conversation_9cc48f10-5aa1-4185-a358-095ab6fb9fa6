//
//  SCCategoryAPIManagerGift.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/22.
//

#import "SCCategoryAPIManagerGift.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCModelCompatibility.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerGift)

///赠送礼物
+(void) requestGiveUserGiftsWithRecipientUserId:(NSString * _Nonnull) recipientUserId giftCode:(NSString * _Nonnull) giftCode num:(NSInteger)num channelName:(NSString * _Nullable)channelName success:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    
    NSMutableDictionary *parames =@{@"recipientUserId":recipientUserId,@"giftCode":giftCode}.mutableCopy;
    if(num > 0){
        parames[@"num"] = @(num);
    }
    if(!kSCIsStrEmpty(channelName)){
        parames[@"channelName"] = channelName;
    }
    
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGiveUserGifts method:SCNetMethodPOST parameters:parames headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSDictionary class]]){
            kSCBlockExeNotNil(success,[responseObject[@"coins"] integerValue]);
        }else{
            ///如果读取不到余额则返回0
            kSCBlockExeNotNil(success,0);
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///获取主播礼物接口
+(void) requesGiftCountsWithUserID:(NSString * _Nonnull) userID success:(void (^_Nullable)(NSDictionary*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGiftCounts method:SCNetMethodPOST parameters:@{@"userId":userID,@"searchType":@9} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@{}); // 返回空字典作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


+(void) requesGiftsWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGiftList method:SCNetMethodGET parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSArray* result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result && success){
            NSMutableArray *giftDicts = [NSMutableArray array];
            for (NSDictionary *giftDict in result) {
                if ([giftDict isKindOfClass:[NSDictionary class]]) {
                    // 标准化礼物字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedGiftDictionary:giftDict];
                    [giftDicts addObject:standardDict];
                }
            }
            success(giftDicts);
        } else if(success) {
            success(@[]); // 返回空数组作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

+(void) requesActivityGiftsWithUserID:(NSString *_Nonnull)userId success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIActivityGiftList method:SCNetMethodGET parameters:nil headers:nil cachePolicy:SCNetCachePolicyCacheAndRefresh success:^(id responseObject) {
        NSArray* result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result && success){
            NSMutableArray *giftDicts = [NSMutableArray array];
            for (NSDictionary *giftDict in result) {
                if ([giftDict isKindOfClass:[NSDictionary class]]) {
                    // 标准化礼物字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedGiftDictionary:giftDict];
                    [giftDicts addObject:standardDict];
                }
            }
            success(giftDicts);
        } else if(success) {
            success(@[]); // 返回空数组作为默认值
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


@end
