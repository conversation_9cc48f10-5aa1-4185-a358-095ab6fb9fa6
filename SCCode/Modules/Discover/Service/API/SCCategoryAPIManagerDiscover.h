//
//  SCCategoryAPIManagerDiscover.h
//  Supercall
//
//  Created by sumengliu on 2024/12/31.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerDiscover)

/// 查询资源路径
+(void) requestMediasWithParameters:(NSArray<NSString*> *)parameters success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull mediaDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure;

@end

NS_ASSUME_NONNULL_END
