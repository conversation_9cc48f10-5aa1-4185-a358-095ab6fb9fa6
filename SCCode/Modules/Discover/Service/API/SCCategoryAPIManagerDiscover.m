//
//  SCCategoryAPIManagerDiscover.m
//  Supercall
//
//  Created by sumengli<PERSON> on 2024/12/31.
//
#import "SCCategoryAPIManagerDiscover.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCModelCompatibility.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerDiscover)

+(void) requestMediasWithParameters:(NSArray<NSString*> *)parameters success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull mediaDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure {
    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIMediaSearch method:SCNetMethodPOST parameters:@{@"list": parameters} headers:nil success:^(id responseObject) {
        NSArray * result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result) {
            NSMutableArray *mediaDicts = [NSMutableArray array];
            for (NSDictionary *mediaDict in result) {
                if ([mediaDict isKindOfClass:[NSDictionary class]]) {
                    // 标准化媒体字典
                    NSDictionary *standardDict = [SCModelCompatibility standardizedMediaDictionary:mediaDict];
                    [mediaDicts addObject:standardDict];
                }
            }
            kSCBlockExeNotNil(success,mediaDicts);
        } else {
            kSCBlockExeNotNil(success,@[]); // 返回空数组作为默认值
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}


@end
