//
//  SCDiscoverBannerCell.m
//  Supercall
//

#import "SCDiscoverBannerCell.h"
//#import "SCAppConfigModel.h"
#import "Lottie/Lottie-Swift.h"

@interface SCDiscoverBannerCell()

@property (nonatomic, strong) UIImageView *coverImageView;
@property (nonatomic, strong) CompatibleAnimationView *loadingAnimationView;

@end

@implementation SCDiscoverBannerCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.layer.cornerRadius = 15;
        self.layer.masksToBounds = YES;
        
        _coverImageView = [[UIImageView alloc] init];
        _coverImageView.contentMode = UIViewContentModeScaleAspectFill;
        _coverImageView.alpha = 0.0;
        [self.contentView addSubview:_coverImageView];
        
        [_coverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.contentView);
        }];
        
        // 创建 Lottie 动画视图
        NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/banner_loading.json"];
        NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
        
        NSError *error = nil;
        NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
        if (jsonData) {
            _loadingAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
            _loadingAnimationView.loopAnimationCount = -1;
            [self.contentView addSubview:_loadingAnimationView];
            [_loadingAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.edges.equalTo(self.contentView);
            }];
            [_loadingAnimationView play];
        }
    }
    return self;
}

- (void)configureCellWithImageUrl:(nullable NSString *)imageUrl 
                      completion:(nullable void(^)(BOOL success))completion {
    if (imageUrl) {
        kWeakSelf(self)
        [self.coverImageView sc_setImageWithURL:imageUrl completionBlock:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image != nil) {
                [weakself.loadingAnimationView stop];
                [weakself.loadingAnimationView removeFromSuperview];
                [UIView animateWithDuration:0.3f animations:^{
                    weakself.coverImageView.alpha = 1.0;
                }];
                if (completion) {
                    completion(YES);
                }
            } else {
                if (completion) {
                    completion(NO);
                }
            }
        }];
    } else {
        if (completion) {
            completion(NO);
        }
    }
}

@end 
