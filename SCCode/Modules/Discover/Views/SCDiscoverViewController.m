//
//  SCDiscoverViewController.m
//  Supercall
//

#import "SCDiscoverViewController.h"
// #import "SCAppConfigModel.h" // 已迁移到字典
#import "SCAuthManager.h"
#import "SCWebViewController.h"
#import "SCDiscoverBannerCell.h"
#import "SCNavigationBar.h"
// #import "SCMediasModel.h" // 已移除，使用字典替代
#import "SCCategoryAPIManagerDiscover.h"
#import "SCFontManager.h"
#import "SCGameWebViewController.h"
#import "SCTrackingUtils.h"
#import "SCThrottle.h"

static NSInteger kSCBannerGameType = 1;

@interface SCDiscoverViewController () <UICollectionViewDelegate, UICollectionViewDataSource>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) NSArray<NSDictionary *> *banners; // 字典版本Banner数据
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSDictionary *> *mediasDict; // 字典版本媒体数据
@property (nonatomic, strong) NSMutableSet *loadedImagePaths;

@end

@implementation SCDiscoverViewController

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self checkAllImagesLoaded];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.scNavigationBar.backButton.hidden = YES;
    [self setupTitleView];
    [self setupCollectionView];
    [self setupData];
    self.loadedImagePaths = [NSMutableSet new];
}

- (NSMutableDictionary<NSString *,NSDictionary *> *)mediasDict {
    if (!_mediasDict) {
        _mediasDict = [NSMutableDictionary new];
    }
    return _mediasDict;
}

- (void)setupTitleView {
    UILabel *titleLb = [[UILabel alloc] init];
    titleLb.font = [SCFontManager boldItalicFontWithSize:20.0f];
    titleLb.text = [@"Discover" translateString];
    titleLb.textColor = UIColor.scWhite;
    
    SCGradientColors *colors =[SCGradientColors gradientWithColors:@[[UIColor colorWithHexString:@"#FF0000"], [UIColor colorWithHexString:@"#460606"]] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *lineImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(17, 8)];
    UIImageView *lineView = [[UIImageView alloc]initWithImage:kScAuthMar.isLanguageForce ? [lineImage imageWithHorizontallyFlippedOrientation] : lineImage];
    lineView.alpha = 0.9;
    
    UIView *barView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 80, 44)];
    [barView addSubview:lineView];
    [barView addSubview:titleLb];
    [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.top.mas_offset(0);
    }];
    [lineView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_offset(0);
        make.width.equalTo(titleLb.mas_width);
        make.height.equalTo(@8);
        make.top.equalTo(titleLb.mas_bottom).offset(-8);
    }];
    
    [self.scNavigationBar.contentView addSubview:barView];
    [barView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.mas_offset(0);
        make.height.mas_offset(44);
        make.width.mas_offset(80);
    }];
}

- (void)setupCollectionView {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionVertical;
    layout.minimumLineSpacing = 10;
    layout.minimumInteritemSpacing = 0;
    
    CGFloat itemWidth = kSCScreenWidth - 32;  // 左右各留16点边距
    layout.itemSize = CGSizeMake(itemWidth, itemWidth * (200.0 / 343.0));  //宽高比
    layout.sectionInset = UIEdgeInsetsMake(12, 12, 12, 12);
    
    self.collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    self.collectionView.backgroundColor = [UIColor scGlobalBgColor];
    self.collectionView.delegate = self;
    self.collectionView.dataSource = self;
    [self.collectionView registerClass:[SCDiscoverBannerCell class] forCellWithReuseIdentifier:@"BannerCell"];
    
    [self.scContentView addSubview:self.collectionView];
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
}

- (void)setupData {
    // 使用专用方法获取扩展数据中的banner数据
    NSDictionary *appExtData = [SCDictionaryHelper appExtDataFromConfigDict:kScAuthMar.appConfig];
    self.banners = [SCDictionaryHelper arrayFromDictionary:appExtData forKey:@"banners" defaultValue:@[]];
    
    // 如果有banner数据，请求对应的媒体资源
    if (self.banners.count > 0) {
        NSMutableArray *parameters = [NSMutableArray array];
        
        // 构建请求参数
        for (NSDictionary *bannerDict in self.banners) {
            NSString *coverUrl = [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerCoverUrlKey defaultValue:@""];
            if (coverUrl.length > 0) {
                [parameters addObject:@{
                    @"mediaPath": coverUrl,
                    @"mediaType": @"photo"
                }];
            }
        }
        
        // 请求媒体资源
        if (parameters.count > 0) {
            [SCAPIServiceManager requestMediasWithParameters:parameters success:^(NSArray<NSDictionary *> * _Nonnull mediaDicts) {
                // 将返回的媒体数据保存到字典中，以mediaPath为key
                for (NSDictionary *mediaDict in mediaDicts) {
                    NSString *mediaPath = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:SCDictionaryKeys.shared.kSCMediaPathKey defaultValue:@""];
                    if (mediaPath.length > 0) {
                        self.mediasDict[mediaPath] = mediaDict;
                    }
                }
                [self.collectionView reloadData];
            } failure:^(SCXErrorModel * _Nonnull error) {
                // 处理错误情况
                
            }];
        }
    }
    
    [self.collectionView reloadData];
}

- (void)trackEventWithIndex:(NSInteger)index {
    // 从字典中获取用户充值状态
    NSDictionary *userInfoDict = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    BOOL isRecharge = [SCDictionaryHelper boolFromDictionary:userInfoDict forKey:SCDictionaryKeys.shared.kSCUserIsRechargeKey defaultValue:NO];

    NSDictionary *params = @{
        @"BannerID": [NSString stringWithFormat:@"Banner%ld", index+1],
        @"UserType": isRecharge ? @"Paid" : @"Free",
        @"ClickTime":  [NSString stringWithFormat:@"%lld", (long long)[[NSDate date] timeIntervalSince1970]]
    };
    
    [SCTrackingUtils trackEventWithName:@"BannerClick" params:params];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.banners.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCDiscoverBannerCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"BannerCell" forIndexPath:indexPath];
    NSDictionary *bannerDict = self.banners[indexPath.item];
    NSString *coverUrl = [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerCoverUrlKey defaultValue:@""];

    // 查找对应的媒体资源
    NSDictionary *mediaDict = self.mediasDict[coverUrl];
    if (mediaDict) {
        NSString *mediaUrl = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:SCDictionaryKeys.shared.kSCMediaUrlKey defaultValue:@""];
        kWeakSelf(self)
        [cell configureCellWithImageUrl:mediaUrl completion:^(BOOL success) {
            if (success) {
                [weakself.loadedImagePaths addObject:coverUrl];
                [weakself checkAllImagesLoaded];
            }
        }];
    } else {
        [cell configureCellWithImageUrl:nil completion:nil];
    }
    
    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger item = indexPath.item;
    kWeakSelf(self)
    [SCThrottle throttleWithTag:@"kThrottleBannerClick" onAfter:^{
        [weakself trackEventWithIndex:item];
    }];
    NSDictionary *bannerDict = self.banners[item];
    NSInteger type = [SCDictionaryHelper integerFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerTypeKey defaultValue:0];
    NSString *jumpUrl = [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerJumpUrlKey defaultValue:@""];

    if (type == kSCBannerGameType) {
        SCGameWebViewController *gameViewController = [[SCGameWebViewController alloc]init];
        [self.navigationController pushViewController:gameViewController animated:YES];
    } else {
        if (jumpUrl.length > 0) {
            [SCWebViewController showWithFromVC:self title:@"" url:jumpUrl];
        }
    }
}

- (void)checkAllImagesLoaded {
    // 检查所有banner的图片是否都已加载
    for (NSDictionary *bannerDict in self.banners) {
        NSString *coverUrl = [SCDictionaryHelper stringFromDictionary:bannerDict forKey:SCDictionaryKeys.shared.kSCBannerCoverUrlKey defaultValue:@""];
        if (![self.loadedImagePaths containsObject:coverUrl]) {
            return;
        }
    }
    
    // 所有图片都已加载完成，记录埋点
    kWeakSelf(self)
    [SCThrottle throttleWithTag:@"kThrottleBannerShowClick" duration:5 onExecute:^{
        [weakself trackBannerImpressions];
    }];
}

- (void)trackBannerImpressions {
    // 从字典中获取用户充值状态
    NSDictionary *userInfoDict = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    BOOL isRecharge = [SCDictionaryHelper boolFromDictionary:userInfoDict forKey:SCDictionaryKeys.shared.kSCUserIsRechargeKey defaultValue:NO];

    NSDictionary *params = @{
        @"PageID": @"DiscoverTab4_Page",
        @"UserType": isRecharge ? @"Paid" : @"Free",
        @"EnterTime": [NSString stringWithFormat:@"%lld", (long long)[[NSDate date] timeIntervalSince1970]]
    };
    
    
    [SCTrackingUtils trackEventWithName:@"DiscoverPageExposure" params:params];
}

@end 
