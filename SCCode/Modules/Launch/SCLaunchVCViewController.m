//
//  SCLaunchVCViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCLaunchVCViewController.h"

@interface SCLaunchVCViewController ()

@end

@implementation SCLaunchVCViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (UIImageView *)launchImageView {
    if (!_launchImageView) {
        _launchImageView = [[UIImageView alloc]init];
        _launchImageView.contentMode = UIViewContentModeScaleAspectFill;
    }
    return _launchImageView;
}

- (void) setupUI {
    [self.view addSubview:self.launchImageView];
    [self.launchImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
}

@end
