//
//  SCNewUserAwardPopup.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCNewUserAwardPopup.h"
#import "SCCoinsService.h"
#import "SCPopupManager.h"
#import "SCFontManager.h"
#import "SCGradientLabel.h"

@interface SCNewUserAwardPopup ()
@property(nonatomic,weak) UIImageView *contentBgIV;
//标题
@property (nonatomic, strong) SCGradientLabel *gradientTitleLabel;
//金币图标
@property(nonatomic,weak) UIImageView *coinIV;
//金币数量
@property(nonatomic,weak) UILabel *coinL;
//描述
@property(nonatomic,weak) UILabel *descL;
//按钮
@property(nonatomic,weak) UIButton *btn;
//关闭按钮
@property(nonatomic,weak) UIButton *closeBtn;
@property (nonatomic, assign) BOOL isShowing;


@end

@implementation SCNewUserAwardPopup

- (void)viewDidLoad {
    [super viewDidLoad];
  
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    //背景宽高比
    CGFloat whScale = 334.0f/405.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(20.0*2);
    CGFloat hScale = (contentWidth/whScale) / 405.0;
    CGFloat wScale = contentWidth / 334.0f;
    
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"bg_new_user_award_popup"]];
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentBgIV = contentBgIV;
    
    UIButton *closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"] target:self action:@selector(dismiss)];
    [self.contentBgIV addSubview:closeBtn];
    self.closeBtn = closeBtn;
    
    self.gradientTitleLabel = [[SCGradientLabel alloc] init];
    self.gradientTitleLabel.numberOfLines = 0;
    self.gradientTitleLabel.textAlignment = NSTextAlignmentCenter;
    self.gradientTitleLabel.contentInsets = UIEdgeInsetsMake(0, 15, 0, 15);
    self.gradientTitleLabel.font = [SCFontManager boldItalicFontWithSize:26.0f];
    self.gradientTitleLabel.text = @"New User Surprise".translateString;
    [self.gradientTitleLabel setGradientColors:@[
        [UIColor colorWithHexString:@"#FFCB34"],
        [UIColor colorWithHexString:@"#FF0000"]
    ]];
    [self.contentBgIV addSubview:self.gradientTitleLabel];
    
    UIImageView *coinIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_new_user_coins"]];
    [self.contentBgIV addSubview:coinIV];
    self.coinIV = coinIV;
    
    UILabel * coinL = [UILabel labelWithText:@"100" textColor:[UIColor scWhite] font:kScUIFontMedium(50) alignment:NSTextAlignmentLeft];
    [self.contentBgIV addSubview:coinL];
    self.coinL = coinL;
    
    UILabel *descL = [UILabel labelWithText:@"Lucky Rewards for New User".translateString textColor:[UIColor scWhite] font:[SCFontManager regularFontWithSize:14.0f] alignment:NSTextAlignmentCenter];
    [self.contentBgIV addSubview:descL];
    self.descL = descL;
    
    UIButton *btn = [UIButton buttonWithTitle:@"Receive".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(20) image:nil backgroundColor:[UIColor scBlack] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onReceive)];
    [self.contentBgIV addSubview:btn];
    self.btn = btn;
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentBgIV);
        make.top.equalTo(self.contentBgIV).inset(85.0*hScale);
        make.size.mas_equalTo(CGSizeMake(30.0f, 30.f));
    }];
    
    [self.coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentBgIV).inset(88.0*wScale);
        make.top.equalTo(self.contentBgIV).inset(190.0*hScale);
        make.size.mas_equalTo(CGSizeMake(56.0*hScale, 44.0*hScale));
    }];
    
    [self.coinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.coinIV.mas_right).offset(20.0*wScale);
        make.centerY.equalTo(self.coinIV);
        make.right.equalTo(self.contentBgIV).inset(0);
    }];
    
    [self.gradientTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentBgIV);
        make.top.equalTo(self.coinIV.mas_bottom).offset(19.0f*hScale);
        make.height.mas_equalTo(35*hScale);
    }];
    
    [self.descL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentBgIV);
        make.top.equalTo(self.gradientTitleLabel.mas_bottom).offset(6.0*hScale);
//        make.height.mas_equalTo(21.0*hScale);
    }];
    
    [self.btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentBgIV).inset(25.0*hScale);
        make.height.mas_equalTo(52.0*hScale);
        make.width.mas_equalTo(204.0*wScale);
        make.centerX.equalTo(self.contentBgIV);
    }];
    
}

-(void)onReceive{
    [self dismiss];
}
///是否已经展示过
+(BOOL) isHaseShow{
    
    // 从字典中获取当前用户ID
    NSString *currentUserID = kSCCurrentUserID;
    return [[NSUserDefaults standardUserDefaults] boolForKey:[NSString stringWithFormat:@"%@_is_hase_show_key",currentUserID]];
}

- (void)showInViewController:(UIViewController *)viewController {
    if (self.isShowing) {
        return;
    }
    
    self.isShowing = YES;
    ///记录是否已经显示
    // 从字典中获取当前用户ID
    NSString *currentUserID = kSCCurrentUserID;
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:[NSString stringWithFormat:@"%@_is_hase_show_key",currentUserID]];
    [[NSUserDefaults standardUserDefaults] synchronize];
    
    [[SCPopupManager shared] showPopup:self inViewController:viewController animationStyle:SCPopupAnimationStyleCenter];
}

- (void)dismiss {
    if (!self.isShowing) {
        return;
    }
    
    [[SCPopupManager shared] dismissPopup:self];
    self.isShowing = NO;
}

@end
