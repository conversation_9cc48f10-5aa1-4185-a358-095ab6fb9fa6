//
//  SCNewUserPromotionPopup.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCNewUserPromotionPopup.h"
#import "SCPromotionDisplayModel.h"
#import "SCCoinsService.h"
#import "SCPayService.h"
#import "SCThirdPartyPayPopup.h"
//#import "SCThirdPayCannelGroupModel.h"
#import "SCPopupManager.h"
#import "SCLocalVideoPlayer.h"
#import "SCFontManager.h"
#import "SCCircularCountdownView.h"
#import "SCDictionaryHelper.h"

@interface SCNewUserPromotionPopup ()

@property(nonatomic,weak) UIImageView *contentBgIV;
//折扣
@property(nonatomic,weak) UILabel *discountL;
//金币图标
@property(nonatomic,weak) UIImageView *coinIV;
//金币数量
@property(nonatomic,weak) UILabel *coinL;

//按钮
@property(nonatomic,weak) UIButton *btn;
//关闭按钮
@property(nonatomic,weak) UIButton *closeBtn;

//描述
@property(nonatomic,weak) UILabel *countdownL;

//进度条
@property (nonatomic, weak) SCCircularCountdownView *countdownView;
//圆形背景色
@property (nonatomic, weak) UIView *countdownContainView;

//促销数据
@property(nonatomic,strong) SCPromotionDisplayModel *promotionDisplayModel;

@property (nonatomic, assign) BOOL isShowing;

@property (nonatomic, strong) SCLocalVideoPlayer *videoPlayer;

@property (nonatomic, weak) UIView *videoContain;

@end

@implementation SCNewUserPromotionPopup

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 添加前后台切换通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationWillResignActive:)
                                               name:UIApplicationWillResignActiveNotification
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationDidBecomeActive:)
                                               name:UIApplicationDidBecomeActiveNotification
                                             object:nil];
}

- (void)initData{
    [super initData];
    _promotionDisplayModel = kScAuthMar.coinsService.promotionObx.value;
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    //背景宽高比
    CGFloat whScale = 330.0f/500.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(22.0*2);
    CGFloat hScale = (contentWidth/whScale) / 500.0f;
    CGFloat wScale = contentWidth / 330.0f;
    CGFloat videoWhScale = 330.0f/401.0f;
    
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[UIImage imageWithColor:[UIColor scWhite]]];
    contentBgIV.layer.cornerRadius = kSCBigCornerRadius;
    contentBgIV.layer.masksToBounds = YES;
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentBgIV = contentBgIV;
    
    // 创建视频容器视图
    UIView *videoContain = [[UIView alloc] init];
    self.videoContain = videoContain;
    [self.contentBgIV addSubview:videoContain];
    
    // 创建视频播放器
    self.videoPlayer = [[SCLocalVideoPlayer alloc] initWithFrame:CGRectZero
                                                      videoName:@"new_user_promotion"
                                                          type:@"mp4"];
    [self.videoContain addSubview:self.videoPlayer];
    // 开始播放
    [self.videoPlayer play];
    
    UILabel *discountL = [UILabel labelWithText:([self.promotionDisplayModel discount] == 0 ? @"" : [NSString stringWithFormat:@"%0.0f%% Off",[self.promotionDisplayModel discount]*100]) textColor:[UIColor colorWithHexString:@"#FFF100"] font:[SCFontManager semiBoldFontWithSize:14.0f] alignment:NSTextAlignmentLeft];
    [self.contentBgIV addSubview:discountL];
    self.discountL = discountL;
    
    UIImageView *coinIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_new_user_coins"]];
    [self.contentBgIV addSubview:coinIV];
    self.coinIV = coinIV;
    
    UILabel * coinL = [UILabel labelWithText:[NSString stringWithFormat:@"%ld",[self.promotionDisplayModel exchangeCoin]] textColor:[UIColor scWhite] font:kScUIFontMedium(50) alignment:NSTextAlignmentLeft];
    [self.contentBgIV addSubview:coinL];
    self.coinL = coinL;
    

    UIButton *btn = [UIButton buttonWithTitle:nil titleColor:[UIColor scWhite] font:kScUIFontSemibold(22) image:nil backgroundColor:[UIColor scTheme] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onPirce)];
    
    
    NSString *pirce = [NSString stringWithFormat:@"$%0.2f",[self.promotionDisplayModel price]];
    NSString *originalPrice =[NSString stringWithFormat:@" $%0.2f",[self.promotionDisplayModel originalPrice]];

    UIFont *pirceFont = kScUIFontSemibold(18);
    UIFont *originalPriceFont = kScUIFontRegular(13);

    NSDictionary *pirceAttributes = @{NSFontAttributeName: pirceFont,NSForegroundColorAttributeName:[UIColor scWhite]};
    NSDictionary *originalAttributes = @{NSFontAttributeName: originalPriceFont,NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#D5D5D5"],NSStrikethroughStyleAttributeName:@(NSUnderlineStyleSingle)};

    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@%@",pirce,originalPrice]];

    [attributedString setAttributes:pirceAttributes range:NSMakeRange(0, [pirce length])];
    [attributedString setAttributes:originalAttributes range:NSMakeRange([pirce length], [originalPrice length])];
    
    [btn setAttributedTitle:attributedString forState:UIControlStateNormal];
    [btn sc_setThemeGradientBackground];
    [self.contentBgIV addSubview:btn];
    self.btn = btn;
    
    UIView *countDownContainView = [[UIView alloc]init];
    countDownContainView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5f];
    countDownContainView.layer.cornerRadius = 64.0f / 2.0f;
    countDownContainView.layer.masksToBounds = YES;
    [self.contentBgIV addSubview:countDownContainView];
    self.countdownContainView = countDownContainView;
    
    // 创建视图
    SCCircularCountdownView *circularView = [[SCCircularCountdownView alloc] initWithFrame:CGRectMake(0, 0, 58.0f, 58.0f)];
    [self.contentBgIV addSubview:circularView];
    self.countdownView = circularView;
    // 设置总时长
    [circularView setTotalDuration:12 * 3600];
    
    UILabel *countdownL = [UILabel labelWithText:@"00:00:00" textColor:[UIColor colorWithHexString:@"#FFDF09"] font:kScUIFontRegular(15) alignment:NSTextAlignmentCenter];
    countdownL.font = [UIFont monospacedDigitSystemFontOfSize:10 weight:UIFontWeightMedium];
    [self.contentBgIV addSubview:countdownL];
    self.countdownL = countdownL;
    
    UIButton *closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"] target:self action:@selector(onClose)];
    [self.contentBgIV addSubview:closeBtn];
    self.closeBtn = closeBtn;
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    // 设置约束
    [self.videoContain mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.contentBgIV);
        make.height.mas_equalTo(contentWidth/videoWhScale);
    }];
    
    [self.videoPlayer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.videoContain);
    }];
    
    [self.countdownL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBgIV).offset(30.0f*hScale);
        make.leading.equalTo(self.contentBgIV).offset(16.0f*wScale);
        make.height.mas_equalTo(21.0f*hScale);
    }];
    
    [self.countdownContainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.countdownL);
        make.size.mas_equalTo(CGSizeMake(64.0f, 64.0f));
    }];
    
    [self.countdownView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.countdownContainView);
        make.size.mas_equalTo(CGSizeMake(60.0f, 60.0f));
    }];
    
    [self.btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentBgIV.mas_bottom).offset(-22.0f*hScale);
        make.centerX.equalTo(self.contentBgIV);
        make.size.mas_equalTo(CGSizeMake(240.0f*wScale, 52.0f*hScale));
    }];
    
    [self.coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.btn.mas_top).offset(-38.0f*hScale);
        make.left.equalTo(self.contentBgIV).inset(55.0*wScale);
        make.size.mas_equalTo(CGSizeMake(56.0*hScale, 44.0*hScale));
    }];
    
    [self.coinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.coinIV);
        make.left.equalTo(self.coinIV.mas_right).offset(10.0f*wScale);
    }];
    
    [self.discountL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.coinL.mas_right).offset(10.0f*wScale);
        make.centerY.equalTo(self.coinL);
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBgIV).offset(11.0f);
        make.trailing.equalTo(self.contentBgIV).offset(-7.0f);
        make.size.mas_equalTo(CGSizeMake(30.0f, 30.f));
    }];
    
    kWeakSelf(self);
    [kScAuthMar.coinsService.promotionCountDownObx subscribe:^(NSNumber * _Nonnull value) {
        if([value integerValue] == 0){
            ///活动结束关闭弹框
            [weakself dismiss];
        }else{
            weakself.countdownL.text = [value toHHMMSS];
            // 更新进度
            [weakself.countdownView updateWithRemainingTime:value.integerValue];
        }
    } error:^(SCXErrorModel * _Nonnull error) {
        ///关闭弹框
        [weakself dismiss];
    } disposeBag:self.disposeBag];
    
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.videoPlayer play];
}

-(void)onClose{
    [self dismiss];
}

-(void)onPirce{
    kWeakSelf(self)
    void (^payBlock) (NSDictionary * _Nonnull channelDict)  = ^(NSDictionary * _Nonnull channelDict){
        NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelDict forKey:@"channelList" defaultValue:@[]];
        if([channelList count] > 1){
        [weakself.view hiddenLoading];
            [SCThirdPartyPayPopup showWithFromVC:weakself coinsDict:weakself.promotionDisplayModel.promotionDict source:[weakself.promotionDisplayModel invitationId] entry:@"pop_promotion" callBack:^(BOOL success) {
                if(success){
                    [weakself dismiss];
                }
            }];
        }else{
            
            [kScAuthMar.payService iAPWithProductCode:[weakself.promotionDisplayModel code] source:[weakself.promotionDisplayModel invitationId] entry:@"pop_promotion" purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
                [weakself.view hiddenLoading];
                if(error != nil){
                    //渠道接口获取失败 那么直接发起支付
                    [weakself.view toast:@"Pay Fail".translateString];
                }else{
                    [weakself dismiss];
                }
            }];
        }
    };
    [weakself.view showLoading];
    if(SCAuthManager.instance.payService.thirdPartCannelDictObx.value == nil){
        //发起三方支付请求（使用字典版本）
        [SCAuthManager.instance.payService remoteThirdPayCannelWithDictSuccess:^(NSDictionary * _Nonnull channelDict) {
            payBlock(channelDict);
            [weakself sc_blank_empty];
        } failure:^(SCXErrorModel * _Nonnull error) {
            //渠道接口获取失败 那么直接发起支付
            [weakself.view hiddenLoading];
            [weakself.view toast:@"Pay Fail".translateString];
        }];
    }else{
        // 使用字典观察者的值
        payBlock(SCAuthManager.instance.payService.thirdPartCannelDictObx.value);
    }
}

- (void)sc_blank_empty{
    
}


- (void)showInViewController:(UIViewController *)viewController {
    if (self.isShowing) {
        return;
    }
    
    self.isShowing = YES;
    [[SCPopupManager shared] showPopup:self inViewController:viewController level:SCPopupLevelHigh animationStyle:SCPopupAnimationStyleCenter];
}

- (void)dismiss {
    if (!self.isShowing) {
        return;
    }
    
    [[SCPopupManager shared] dismissPopup:self];
    self.isShowing = NO;
}

// 添加处理方法
- (void)applicationWillResignActive:(NSNotification *)notification {
    [self.videoPlayer pause];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    if (self.isShowing) {
        [self.videoPlayer play];
    }
}

// 在视图销毁时移除通知
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
@end
