//
//  SCPromotionFloatingView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCPromotionFloatingView.h"
#import "SCCoinsService.h"
#import "SCPromotionDisplayModel.h"

@implementation SCPromotionFloatingView

- (void)initData{
    [super initData];
    self.bgImg = [SCResourceManager loadImageWithName:@"btn_promotion_floating"];
    self.title = [@(kScAuthMar.coinsService.promotionObx.value.countDownNum) toHHMMSS];
}


@end
