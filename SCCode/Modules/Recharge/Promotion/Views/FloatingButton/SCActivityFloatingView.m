//
//  SCActivityFloatingView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCActivityFloatingView.h"
#import "SCCoinsService.h"
#import "SCActivityPromotionDisplayModel.h"

@implementation SCActivityFloatingView


- (void)initData{
    [super initData];
    self.bgImg = [SCResourceManager loadImageWithName:@"btn_activity_floating"];
    self.title = [@(kScAuthMar.coinsService.activityPromotionObx.value.countDownNum) toHHMMSS];
}


@end
