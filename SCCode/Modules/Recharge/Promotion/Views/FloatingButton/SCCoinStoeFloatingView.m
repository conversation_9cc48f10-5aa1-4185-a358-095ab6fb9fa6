//
//  SCCoinStoeFloatingView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCCoinStoeFloatingView.h"

@implementation SCCoinStoeFloatingView

- (void)initData{
    [super initData];
    self.bgImg = [SCResourceManager loadImageWithName:@"btn_coins_floating"];
    self.title = @"Store".translateString;
}

-(void) initUI{
    [super initUI];
    self.titleLabel.textColor = kSCColorWithHexStr(@"#FFE400");
}

@end
