//
//  SCFloatingLayoutView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/15.
//

#import "SCFloatingLayoutView.h"
#import "SCActivityFloatingView.h"
#import "SCPromotionFloatingView.h"
#import "SCCoinStoeFloatingView.h"

#import "SCCoinsService.h"
#import "SCActivityPromotionDisplayModel.h"
#import "SCPromotionDisplayModel.h"

#import "SCNewUserAwardPopup.h"
#import "SCNewUserPromotionPopup.h"
#import "SCActivityPromotionPopup.h"
#import "SCCoinsPopupViewController.h"

@interface SCFloatingLayoutView()

@property(nonatomic,weak) SCActivityFloatingView * activityFloatingView;
@property(nonatomic,weak) SCPromotionFloatingView * promotionFloatingView;
@property(nonatomic,weak) SCCoinStoeFloatingView * coinStoreFloatingView;

@property(nonatomic,strong) SCDisposeBag * disposeBag;

@end
@implementation SCFloatingLayoutView

- (void)dealloc
{
    [_disposeBag dispose];
    _disposeBag = nil;
}

- (void)initUI{
    [super initUI];
    _disposeBag = [[SCDisposeBag alloc] init];
    
    SCCoinStoeFloatingView * coinStoreFloatingView = [[SCCoinStoeFloatingView alloc] init];
    coinStoreFloatingView.userInteractionEnabled = YES;
    kSCAddTapGesture(coinStoreFloatingView, self, onTagCoinStore);
    [self addSubview:coinStoreFloatingView];
    self.coinStoreFloatingView = coinStoreFloatingView;
    
    
    kWeakSelf(self)
    [kScAuthMar.coinsService.activityPromotionObx subscribe:^(SCActivityPromotionDisplayModel * _Nonnull value) {
        if([value isShow]){
            [weakself _buildActivity];
        }else{
            [weakself.activityFloatingView removeFromSuperview];
            weakself.activityFloatingView = nil;
            [weakself updateLayout];
        }
    } error:^(SCXErrorModel * _Nonnull error) {
        [weakself.activityFloatingView removeFromSuperview];
        weakself.activityFloatingView = nil;
        [weakself updateLayout];
    } disposeBag:_disposeBag];
    
    [kScAuthMar.coinsService.promotionObx subscribe:^(SCPromotionDisplayModel * _Nonnull value) {
        if([value isShow]){
            [weakself _buildPromotion];
        }else{
            [weakself.promotionFloatingView removeFromSuperview];
            weakself.promotionFloatingView = nil;
            [weakself updateLayout];
        }
        
    } error:^(SCXErrorModel * _Nonnull error) {
        [weakself.promotionFloatingView removeFromSuperview];
        weakself.promotionFloatingView = nil;
        [weakself updateLayout];
    } disposeBag:_disposeBag];
    
    
    
}

//创建活动悬浮按钮
-(void) _buildActivity{
    if(self.activityFloatingView == nil){
        SCActivityFloatingView * activityFloatingView = [[SCActivityFloatingView alloc] init];
        activityFloatingView.titleXOffset = 10.0f;
        activityFloatingView.titleYOffset = 4.0f;
        activityFloatingView.userInteractionEnabled = YES;
        kSCAddTapGesture(activityFloatingView, self, onTagActivity);
        [self addSubview:activityFloatingView];
        self.activityFloatingView = activityFloatingView;
        
        kWeakSelf(self);
        [kScAuthMar.coinsService.activityCountDownObx subscribe:^(NSNumber * _Nonnull value) {
            if([value integerValue] == 0){
                [weakself.activityFloatingView removeFromSuperview];
                weakself.activityFloatingView = nil;
                [weakself updateLayout];
            }else{
                weakself.activityFloatingView.title = [value toHHMMSS];
            }
            
        } error:^(SCXErrorModel * _Nonnull error) {
            [weakself.activityFloatingView removeFromSuperview];
            weakself.activityFloatingView = nil;
            [weakself updateLayout];
        } disposeBag:_disposeBag];
    }
    [self updateLayout];
}
//创建新用户促销
-(void) _buildPromotion{
    if(self.promotionFloatingView == nil){
        SCPromotionFloatingView * promotionFloatingView = [[SCPromotionFloatingView alloc] init];
        promotionFloatingView.titleXOffset = 2.0f;
        promotionFloatingView.titleYOffset = 1.0f;
        promotionFloatingView.userInteractionEnabled = YES;
        kSCAddTapGesture(promotionFloatingView, self, onTagPromotion);
        [self addSubview:promotionFloatingView];
        self.promotionFloatingView = promotionFloatingView;
        
        kWeakSelf(self);
        [kScAuthMar.coinsService.promotionCountDownObx subscribe:^(NSNumber * _Nonnull value) {
            if([value integerValue] == 0){
                [weakself.promotionFloatingView removeFromSuperview];
                weakself.promotionFloatingView = nil;
                [weakself updateLayout];
            }else{
                weakself.promotionFloatingView.title = [value toHHMMSS];
            }
            
        } error:^(SCXErrorModel * _Nonnull error) {
            [weakself.promotionFloatingView removeFromSuperview];
            weakself.promotionFloatingView = nil;
            [weakself updateLayout];
        } disposeBag:_disposeBag];
    }
    [self updateLayout];
}

//更新布局
- (void) updateLayout{
    UIView * lastView = nil;
    [self.coinStoreFloatingView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.mas_bottom).offset(0);
        make.trailing.equalTo(self).offset(kScAuthMar.isLanguageForce ? -16.0f : 0);
        make.size.mas_equalTo(CGSizeMake(55, 63));
    }];
    lastView = self.coinStoreFloatingView;
    
    if(self.promotionFloatingView != nil){
        [self.promotionFloatingView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(lastView.mas_top).offset(-10);
            make.trailing.equalTo(self).offset(kScAuthMar.isLanguageForce ? -16.0f : 0);
            make.size.mas_equalTo(CGSizeMake(60, 67));
        }];
        lastView = self.promotionFloatingView;
    }
    
    if(self.activityFloatingView != nil){
        [self.activityFloatingView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.bottom.mas_equalTo(lastView.mas_top).offset(-10);
            make.trailing.equalTo(self);
            make.size.mas_equalTo(CGSizeMake(75, 63));
        }];
        lastView = self.activityFloatingView;
    }
    
    [lastView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
    }];
}

#pragma mark - Action

-(void) onTagCoinStore{
    
    [SCCoinsPopupViewController showWithFromVC:[self viewController] entry:SCPayEntry.shared.kPayEntrySourceMatchFloat];
}


- (void)onTagActivity {
    if (self.activityAction) {
        self.activityAction();
    }
}

- (void)onTagPromotion {
    if (self.promotionAction) {
        self.promotionAction();
    }
}

@end
