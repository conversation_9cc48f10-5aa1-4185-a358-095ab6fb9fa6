//
//  SCFloatingView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCFloatingView.h"
@interface SCFloatingView()

@property(nonatomic,assign,readonly) CGFloat imageW;
@property(nonatomic,assign,readonly) CGFloat imageH;

@end

@implementation SCFloatingView

- (void)initUI{
    [super initUI];
    
    
    
    UIImageView * bgImageView = [[UIImageView alloc] initWithImage:self.bgImg];
    [self addSubview:bgImageView];
    _bgImageView = bgImageView;
    
    UILabel * titleLabel = [UILabel labelWithText:_title textColor:[UIColor scWhite] font:kScUIFontRegular(10) alignment:NSTextAlignmentCenter];
    titleLabel.font = [UIFont monospacedDigitSystemFontOfSize:10 weight:UIFontWeightMedium];
    [titleLabel setAdjustsFontSizeToFitWidth:YES];
    [self addSubview:titleLabel];
    _titleLabel = titleLabel;
}

- (void)setBgImg:(UIImage *)bgImg{
    _bgImg = bgImg;
    
    _imageH = _bgImg.size.height;
    _imageW = _bgImg.size.width;
}


- (void)setTitle:(NSString *)title{
    _title = title;
    
    self.titleLabel.text = title;
}

- (void)layoutSubviews{
    [super layoutSubviews];
    
    //图片宽高比
    CGFloat whImgScale = self.imageW/self.imageH;
    CGFloat imageH = self.bounds.size.width / whImgScale;
    self.bgImageView.frame = CGRectMake(0, self.bounds.size.height - imageH, self.bounds.size.width, imageH);
    
    CGFloat labelHeight = self.bounds.size.width * 18.0f/57.0f;
    self.titleLabel.frame = CGRectMake(0 + self.titleXOffset, self.bounds.size.height - labelHeight + self.titleYOffset, self.frame.size.width, labelHeight);
}


@end
