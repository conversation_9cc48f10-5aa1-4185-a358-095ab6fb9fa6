//
//  SCActivityPromotionPopup.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/15.
//

#import "SCActivityPromotionPopup.h"
#import "SCActivityPromotionDisplayModel.h"
#import "SCCoinsService.h"
#import "SCPayService.h"
#import "SCThirdPartyPayPopup.h"
//#import "SCThirdPayCannelGroupModel.h"
#import "SCPopupManager.h"
#import "SCLocalVideoPlayer.h"
#import "SCFontManager.h"
#import "SCCircularCountdownView.h"
#import "SCDictionaryHelper.h"

@interface SCActivityPromotionPopup ()

@property(nonatomic,weak) UIImageView *contentBgIV;

//赠送金币
@property(nonatomic,weak) UILabel *giveCoinL;
//赠送金币背景
@property(nonatomic,weak) UIImageView *giveCoinBGV;
//可购买次数
@property(nonatomic,weak) UILabel *buyCountL;
//可购买次数背景
@property(nonatomic,weak) UIImageView *buyCountBGV;

//金币图标
@property(nonatomic,weak) UIImageView *coinIV;
//金币数量
@property(nonatomic,weak) UILabel *coinL;

//按钮
@property(nonatomic,weak) UIButton *btn;
//关闭按钮
@property(nonatomic,weak) UIButton *closeBtn;

//倒计时
@property(nonatomic,weak) UILabel *countdownL;
//进度条
@property (nonatomic, weak) SCCircularCountdownView *countdownView;
//圆形背景色
@property (nonatomic, weak) UIView *countdownContainView;

//促销数据
@property(nonatomic,strong) SCActivityPromotionDisplayModel *promotionDisplayModel;

@property (nonatomic, assign) BOOL isShowing;

@property (nonatomic, strong) SCLocalVideoPlayer *videoPlayer;
@property (nonatomic, weak) UIView *videoContain;

@end

@implementation SCActivityPromotionPopup

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 添加前后台切换通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationWillResignActive:)
                                               name:UIApplicationWillResignActiveNotification
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationDidBecomeActive:)
                                               name:UIApplicationDidBecomeActiveNotification
                                             object:nil];
    
    [SCAuthManager.instance.payService remoteThirdPayCannelWithDictSuccess:nil failure:nil];
  
}
- (void)initData{
    [super initData];
    _promotionDisplayModel = kScAuthMar.coinsService.activityPromotionObx.value;
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    //背景宽高比
    CGFloat whScale = 330.0f/500.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(22.0*2);
    CGFloat hScale = (contentWidth/whScale) / 500.0f;
    CGFloat wScale = contentWidth / 330.0f;
    CGFloat videoWhScale = 330.0f/401.0f;
    
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[UIImage imageWithColor:[UIColor scWhite]]];
    contentBgIV.layer.cornerRadius = kSCBigCornerRadius;
    contentBgIV.layer.masksToBounds = YES;
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentBgIV = contentBgIV;
    
    // 创建视频容器视图
    UIView *videoContain = [[UIView alloc] init];
    self.videoContain = videoContain;
    [self.contentBgIV addSubview:videoContain];
    
    UIImageView *coinIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_new_user_coins"]];
    [self.contentBgIV addSubview:coinIV];
    self.coinIV = coinIV;
    
    UILabel * coinL = [UILabel labelWithText:[NSString stringWithFormat:@"%ld",[self.promotionDisplayModel exchangeCoin]] textColor:[UIColor scWhite] font:kScUIFontSemibold(49) alignment:NSTextAlignmentLeft];
    [self.contentBgIV addSubview:coinL];
    self.coinL = coinL;
    
    
    UIImageView *giveCoinBGV = [[UIImageView alloc] initWithImage:[[SCResourceManager loadImageWithName:@"bg_activity_promotion_extra_num"] resizableImageWithCapInsets:UIEdgeInsetsMake(15, 6, 6, 6) resizingMode:UIImageResizingModeStretch]];
    [self.contentBgIV addSubview:giveCoinBGV];
    self.giveCoinBGV = giveCoinBGV;
    UILabel *giveCoinL = [UILabel labelWithText:[NSString stringWithFormat:@"+%ld",self.promotionDisplayModel.extraCoinNum] textColor:[UIColor scWhite] font:[SCFontManager semiBoldFontWithSize:14.0f] alignment:NSTextAlignmentCenter];
    [self.giveCoinBGV addSubview:giveCoinL];
    self.giveCoinL = giveCoinL;
    
    if(self.promotionDisplayModel.extraCoinNum <= 0){
        self.giveCoinBGV.hidden = YES;
        self.giveCoinL.hidden = YES;
    }
    

    UIButton *btn = [UIButton buttonWithTitle:nil titleColor:[UIColor scWhite] font:kScUIFontSemibold(22) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(onPirce)];
    [btn sc_setThemeGradientBackground];
    
    NSString *pirce = [NSString stringWithFormat:@"$%0.2f",[self.promotionDisplayModel price]];
    NSString *originalPrice =[NSString stringWithFormat:@" $%0.2f",[self.promotionDisplayModel originalPrice]];

    if ([self.promotionDisplayModel price] != [self.promotionDisplayModel originalPrice]) {
        UIFont *pirceFont = kScUIFontSemibold(18);
        UIFont *originalPriceFont = kScUIFontRegular(13);

        NSDictionary *pirceAttributes = @{NSFontAttributeName: pirceFont,NSForegroundColorAttributeName:[UIColor scWhite]};
        NSDictionary *originalAttributes = @{NSFontAttributeName: originalPriceFont,NSForegroundColorAttributeName:[UIColor colorWithHexString:@"#D5D5D5"] ,NSStrikethroughStyleAttributeName:@(NSUnderlineStyleSingle)};

        NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"%@%@",pirce,originalPrice]];

        [attributedString setAttributes:pirceAttributes range:NSMakeRange(0, [pirce length])];
        [attributedString setAttributes:originalAttributes range:NSMakeRange([pirce length], [originalPrice length])];
        
        [btn setAttributedTitle:attributedString forState:UIControlStateNormal];
    } else {
        [btn setTitle:pirce forState:UIControlStateNormal];
    }
    
    [self.contentBgIV addSubview:btn];
    self.btn = btn;
    
    if (self.promotionDisplayModel.remainBuyNum > 1) {
        UIImageView *buyCountBGV = [[UIImageView alloc] initWithImage:[[SCResourceManager loadImageWithName:@"bg_activity_promotion_remain_num"] resizableImageWithCapInsets:UIEdgeInsetsMake(8, 8, 8, 8) resizingMode:UIImageResizingModeStretch]];
        [self.contentBgIV addSubview:buyCountBGV];
        self.buyCountBGV = buyCountBGV;
        
        UILabel *buyCountL = [UILabel labelWithText:[NSString stringWithFormat:@"x%ld",self.promotionDisplayModel.remainBuyNum] textColor:[UIColor scWhite] font:[SCFontManager boldFontWithSize:10.0f] alignment:NSTextAlignmentCenter];
        [self.buyCountBGV addSubview:buyCountL];
        self.buyCountL = buyCountL;
    }
    
    UIView *countDownContainView = [[UIView alloc]init];
    countDownContainView.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.5f];
    countDownContainView.layer.cornerRadius = 64.0f / 2.0f;
    countDownContainView.layer.masksToBounds = YES;
    [self.contentBgIV addSubview:countDownContainView];
    self.countdownContainView = countDownContainView;
    
    // 创建视图
    SCCircularCountdownView *circularView = [[SCCircularCountdownView alloc] initWithFrame:CGRectMake(0, 0, 60.0f, 60.0f)];
    [self.countdownContainView addSubview:circularView];
    self.countdownView = circularView;
    // 设置总时长
    [circularView setTotalDuration:12 * 3600];
    
    UILabel *countdownL = [UILabel labelWithText:[@(self.promotionDisplayModel.countDownNum) toHHMMSS] textColor:[UIColor colorWithHexString:@"#FFDF09"] font:kScUIFontRegular(10) alignment:NSTextAlignmentCenter];
    countdownL.font = [UIFont monospacedDigitSystemFontOfSize:10 weight:UIFontWeightMedium];
    [self.contentBgIV addSubview:countdownL];
    self.countdownL = countdownL;
    
    UIButton *closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"] target:self action:@selector(onClose)];
    [self.contentBgIV addSubview:closeBtn];
    self.closeBtn = closeBtn;
    
    // 创建视频播放器
    self.videoPlayer = [[SCLocalVideoPlayer alloc] initWithFrame:CGRectZero
                                                      videoName:@"activity_promotion" // 替换为实际视频名称
                                                          type:@"mp4"];
    [self.videoContain addSubview:self.videoPlayer];
    // 开始播放
    [self.videoPlayer play];
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    // 设置视频容器视图的约束
    [self.videoContain mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.contentBgIV);
        make.height.mas_equalTo(contentWidth/videoWhScale); // 根据需要调整高度
    }];
    
    // 设置视频播放器的约束
    [self.videoPlayer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.videoContain);
    }];
    
    [self.countdownL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBgIV).offset(30.0f*hScale);
        make.leading.equalTo(self.contentBgIV).offset(16.0f*wScale);
        make.height.mas_equalTo(21.0f*hScale);
    }];
    
    [self.countdownContainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.countdownL);
        make.size.mas_equalTo(CGSizeMake(64.0f, 64.0f));
    }];
    
    [self.countdownView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.countdownContainView);
        make.size.mas_equalTo(CGSizeMake(60.0f, 60.0f));
    }];
    
    [self.btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentBgIV.mas_bottom).offset(-22.0f*hScale);
        make.centerX.equalTo(self.contentBgIV);
        make.size.mas_equalTo(CGSizeMake(240.0f*wScale, 52.0f*hScale));
    }];
    
    [self.coinIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.btn.mas_top).offset(-38.0f*hScale);
        make.left.equalTo(self.contentBgIV).inset(55.0*wScale);
        make.size.mas_equalTo(CGSizeMake(56.0*hScale, 44.0*hScale));
    }];
    
    [self.coinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.coinIV);
        make.height.mas_equalTo(68.0f*hScale);
        make.left.equalTo(self.coinIV.mas_right).offset(10.0f*wScale);
    }];
    
    [self.giveCoinBGV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.coinL);
        make.height.mas_equalTo(19.0f*hScale);
        make.left.equalTo(self.coinL.mas_right).offset(9.0f);
    }];
    
    [self.giveCoinL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.giveCoinBGV).inset(9.0f);
        make.right.equalTo(self.giveCoinBGV).inset(6.0f);
        make.centerY.equalTo(self.giveCoinBGV);
    }];
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentBgIV).offset(11.0f);
        make.trailing.equalTo(self.contentBgIV).offset(-7.0f);
        make.size.mas_equalTo(CGSizeMake(30.0f, 30.f));
    }];
    
    if (self.promotionDisplayModel.remainBuyNum > 1) {
        [self.buyCountBGV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.btn).offset(-10.0f);
            make.right.equalTo(self.btn.mas_right);
            make.height.mas_equalTo(20.0f);
        }];
        
        [self.buyCountL mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.buyCountBGV).inset(8.0f);
            make.centerY.equalTo(self.buyCountBGV);
        }];
    }
    
    kWeakSelf(self);
    [kScAuthMar.coinsService.activityCountDownObx subscribe:^(NSNumber * _Nonnull value) {
        if([value integerValue] == 0){
            ///活动结束关闭弹框
            [weakself dismiss];
        }else{
            weakself.countdownL.text = [value toHHMMSS];
            // 更新进度
            [weakself.countdownView updateWithRemainingTime:value.integerValue];
        }
    } error:^(SCXErrorModel * _Nonnull error) {
        ///关闭弹框
        [weakself dismiss];
    } disposeBag:self.disposeBag];
    
    //获取到三方渠道后，更新额外赠送的金币数量（使用字典版本观察者）
    [kScAuthMar.payService.thirdPartCannelDictObx subscribe:^(NSDictionary * _Nullable channelDict) {
        [weakself updateExtraCoinLabel];
    } error:nil disposeBag:self.disposeBag];
    
}

// 在 viewDidAppear 中确保视频播放
- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.videoPlayer play];
}

-(void)onClose{
    [self dismiss];
}

-(void)onPirce{
    kWeakSelf(self)
    void (^payBlock) (NSDictionary * _Nonnull channelDict)  = ^(NSDictionary * _Nonnull channelDict){
        NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelDict forKey:@"channelList" defaultValue:@[]];
        if([channelList count] > 1){
        [weakself.view hiddenLoading];
            [SCThirdPartyPayPopup showWithFromVC:weakself coinsDict:weakself.promotionDisplayModel.promotionDict source:[weakself.promotionDisplayModel invitationId] entry:SCPayEntry.shared.kPayEntrySourcePushSpecialOffer callBack:^(BOOL success) {
                if(success){
                    // 更新活动促销
                    [kScAuthMar.coinsService remoteActivityPromotionWithSuccess:nil failure:nil];
                    [weakself dismiss];
                }
            }];
        }else{
            [kScAuthMar.payService iAPWithProductCode:[weakself.promotionDisplayModel code] source:[weakself.promotionDisplayModel invitationId] entry:SCPayEntry.shared.kPayEntrySourcePushSpecialOffer purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
                [weakself.view hiddenLoading];
                if(error != nil){
                    //渠道接口获取失败 那么直接发起支付
                    [weakself.view toast:@"Pay Fail".translateString];
                }else{
                    [kSCKeyWindow toast:@"payment successful".translateString];
                    [weakself dismiss];
                }
            }];
        }
    };
    [weakself.view showLoading];
    if(SCAuthManager.instance.payService.thirdPartCannelDictObx.value == nil){
        //发起三方支付请求（使用字典版本）
        [SCAuthManager.instance.payService remoteThirdPayCannelWithDictSuccess:^(NSDictionary * _Nonnull channelDict) {
            payBlock(channelDict);

        } failure:^(SCXErrorModel * _Nonnull error) {
            //渠道接口获取失败 那么直接发起支付
            [weakself.view hiddenLoading];
            [weakself.view toast:@"Pay Fail".translateString];
        }];
    }else{
        // 使用字典观察者的值
        payBlock(SCAuthManager.instance.payService.thirdPartCannelDictObx.value);
    }
}

- (void)showInViewController:(UIViewController *)viewController {
    if (self.isShowing) {
        return;
    }
    
    self.isShowing = YES;
    [[SCPopupManager shared] showPopup:self inViewController:viewController level:SCPopupLevelNormal animationStyle:SCPopupAnimationStyleCenter];
}

- (void)dismiss {
    if (!self.isShowing) {
        return;
    }
    
    [[SCPopupManager shared] dismissPopup:self];
    self.isShowing = NO;
}

// 更新对比第三方渠道后的赠送数量。
- (void)updateExtraCoinLabel {
    NSInteger extraCoin = [self calculateMaxBonusWithOffer];
    self.giveCoinL.text = [NSString stringWithFormat:@"+%ld",extraCoin];
    self.giveCoinBGV.hidden = extraCoin <= 0;
    self.giveCoinL.hidden = extraCoin <= 0;
}

- (NSInteger)calculateMaxBonusWithOffer {
    NSInteger baseCoins = [self.promotionDisplayModel exchangeCoin];
    NSInteger maxExtraCoins = 0;

    // 如果 thirdpartyCoinPercent 不为空，使用百分比
    NSInteger thirdpartyCoinPercent = [self.promotionDisplayModel thirdpartyCoinPercent];
    if (thirdpartyCoinPercent > 0) {
        maxExtraCoins = (NSInteger)(baseCoins * (thirdpartyCoinPercent / 100.0));
    } else {
        // 如果 thirdpartyCoinPercent 为空，使用 extraCoin
        maxExtraCoins = [self.promotionDisplayModel extraCoin];
    }
    
    // 比较所有三方渠道的赠送比例（使用字典版本）
    NSDictionary *channelGroupDict = kScAuthMar.payService.thirdPartCannelDictObx.value;
    NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelGroupDict forKey:@"channelList" defaultValue:@[]];

    for (NSDictionary *channelDict in channelList) {
        if ([channelDict isKindOfClass:[NSDictionary class]]) {
            NSInteger promotionPresentCoinRatio = [SCDictionaryHelper integerFromDictionary:channelDict forKey:@"promotionPresentCoinRatio" defaultValue:0];
            if (promotionPresentCoinRatio > 0) {
                NSInteger channelExtraCoins = (NSInteger)(baseCoins * (promotionPresentCoinRatio / 100.0));
                maxExtraCoins = MAX(maxExtraCoins, channelExtraCoins);
            }
        }
    }
    
    return maxExtraCoins;
}

// 添加处理方法
- (void)applicationWillResignActive:(NSNotification *)notification {
    [self.videoPlayer pause];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    if (self.isShowing) {
        [self.videoPlayer play];
    }
}

// 在视图销毁时移除通知
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end
