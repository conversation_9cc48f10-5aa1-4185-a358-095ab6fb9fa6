//
//  SCCoinsService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCCoinsService.h"
#import "SCCategoryAPIManagerCoins.h"
#import "SCPromotionDisplayModel.h"
#import "SCActivityPromotionDisplayModel.h"
#import "SCTimer.h"
#import "SCPayService.h"
#import "SCCoinsPopupViewController.h"
#import "SCThrottle.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCCoinsService()
@property(nonatomic,strong) SCDisposeBag * disposeBag;

@property(nonatomic,strong) SCTimer *promotionTimer;
@property(nonatomic,strong) SCTimer *activityTimer;
@end
@implementation SCCoinsService

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        _disposeBag = [[SCDisposeBag alloc]init];
        _promotionCountDownObx = [[SCObservable<NSNumber *> alloc] initWithValue:@(0)];
        _promotionObx = [[SCObservable<SCPromotionDisplayModel *> alloc] initWithValue:nil];
        _activityCountDownObx = [[SCObservable<NSNumber *> alloc] initWithValue:@(0)];
        _activityPromotionObx = [[SCObservable<SCActivityPromotionDisplayModel *> alloc] initWithValue:nil];
        _registerRewardObx = [[SCObservable<NSDictionary *> alloc] initWithValue:nil];
        _inviteRechargeLinkCache = [NSMutableDictionary dictionary];
        kWeakSelf(self);
        
        _promotionTimer = [[SCTimer alloc] initWithInterval:1];
        _promotionTimer.timerBlock = ^{
            if(weakself.promotionObx.value.isShow){
                weakself.promotionCountDownObx.value = @(weakself.promotionObx.value.countDownNum);
            }else{
                weakself.promotionCountDownObx.value = @(0);
                [weakself.promotionTimer stopTimer];
            }
            
        };
        _activityTimer = [[SCTimer alloc] initWithInterval:1];
        _activityTimer.timerBlock = ^{
            if(weakself.activityPromotionObx.value.isShow){
                weakself.activityCountDownObx.value = @(weakself.activityPromotionObx.value.countDownNum);
            }else{
                weakself.activityCountDownObx.value = @(0);
                [weakself.activityTimer stopTimer];
            }
            
        };
        
        [_promotionObx subscribe:^(SCPromotionDisplayModel * _Nonnull value) {
            if([value isShow]){
                [weakself.promotionTimer startTimer];
            }else{
                [weakself.promotionTimer stopTimer];
            }
            
        } error:^(SCXErrorModel * _Nonnull error) {
            [weakself.promotionTimer stopTimer];
        } disposeBag:_disposeBag];
        
        [_activityPromotionObx subscribe:^(SCActivityPromotionDisplayModel * _Nonnull value) {
            if([value isShow]){
                [weakself.activityTimer startTimer];
            }else{
                [weakself.activityTimer stopTimer];
            }
        } error:^(SCXErrorModel * _Nonnull error) {
            [weakself.activityTimer stopTimer];
        } disposeBag:_disposeBag];
        
    }
    return self;
}
- (void)destroyService{
    [super destroyService];
    [_inviteRechargeLinkCache removeAllObjects];
    _inviteRechargeLinkCache = nil;
    [_disposeBag dispose];
    _disposeBag = nil;
}

#pragma mark - 金币购买
- (void)remoteCoinsListWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *> * _Nonnull))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestCoinsWithSuccess:^(NSArray<NSDictionary *> * _Nonnull coinsDicts) {
        __strong SCCoinsService * strongSelf = weakself;
        if(strongSelf){
            strongSelf -> _coinsListModel = coinsDicts;
            NSMutableSet<NSString *> *set = [NSMutableSet new];
            for (NSDictionary *item in coinsDicts) {
                NSString *code = [SCDictionaryHelper stringFromDictionary:item forKey:@"code" defaultValue:nil];
                if (code.length > 0) {
                    [set addObject:code];
                }
            }
            [kScAuthMar.payService refreshProducts:set] ;
        }
        kSCBlockExeNotNil(success,coinsDicts);
    } failure:failure];
}


///刷新可用金币数量
- (void)remoteAvailableCoinsWithSuccess:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull))failure{
    [SCAPIServiceManager requestAvailableCoinsWithSuccess:^(NSInteger coins) {
        [kScAuthMar updateLocalAvailableCoins:coins];
    } failure:failure];
}

-(BOOL)checkEnoughCoinsWithModel:(NSInteger)price entry:(NSString *)entry {
    //判断余额是否足够
    if(kScAuthMar.availableCoinsObx.value.integerValue < price){
        //转跳充值页面，传 nil 是在 Window 上显示，遮挡呼叫弹窗，防止多次点击。
        [SCThrottle throttleWithTag:@"call_not_conin" onAfter:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [SCCoinsPopupViewController showWithFromVC:nil entry:entry];
                //延迟
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [kSCKeyWindow toast:@"Coins not enough".translateString];
                });
            });
        }];
        
        return NO;
    }
    return YES;
}

#pragma mark - 新用户促销


-(void) remotePromotionWithSuccess:(void (^_Nullable)(SCPromotionDisplayModel * _Nullable))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestPromotionWithSuccess:^(NSDictionary * _Nonnull promotionDict) {
        SCPromotionDisplayModel * m;
        __strong SCCoinsService * strongSelf = weakself;
        if(promotionDict != nil && [promotionDict isKindOfClass:[NSDictionary class]]){
            m = [SCPromotionDisplayModel displayModelWithPromotionDict:promotionDict];

            if(strongSelf){
                strongSelf.promotionObx.value = m;
            }
        }else{
            if(strongSelf){
                strongSelf.promotionObx.value = nil;
            }
        }
        kSCBlockExeNotNil(success,m);

    } failure:^(SCXErrorModel * _Nonnull error) {
        weakself.promotionObx.error = error;
        kSCBlockExeNotNil(failure,error);
    }];
}
#pragma mark - 活动促销
-(void) remoteActivityPromotionWithSuccess:(void (^_Nullable)(SCActivityPromotionDisplayModel * _Nonnull))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestActivityPromotionWithSuccess:^(NSDictionary * _Nonnull promotionDict) {
        SCActivityPromotionDisplayModel *display = [SCActivityPromotionDisplayModel displayModelWithPromotionDict:promotionDict];

        __strong SCCoinsService * strongSelf = weakself;
        if(strongSelf){
            strongSelf.activityPromotionObx.value = display;
        }
        kSCBlockExeNotNil(success,display);
    } failure:failure];
}

#pragma mark - 注册奖励

- (void)checkRegisterReward{
    kWeakSelf(self)
    [SCAPIServiceManager requestRegisterRewardWithSuccess:^(NSDictionary * _Nullable rewardDict) {
        weakself.registerRewardObx.value = rewardDict;
    } failure:^(SCXErrorModel * _Nonnull error) {
        weakself.registerRewardObx.error = error;
    }];
}

#pragma mark - 邀请充值
- (void) getInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    if(self.inviteRechargeLinkCache[invitationId] && [self.inviteRechargeLinkCache[invitationId] intValue] == 0){
        //该邀请ID正在请求中
        kSCBlockExeNotNil(failure,[[SCXErrorModel alloc] initWitMsg:@""]);
        return;
    }
    self.inviteRechargeLinkCache[invitationId] = @(0);
    kWeakSelf(self)
    [self remoteInviteRechargeLinkIsValidWithInvitationId:invitationId success:^(BOOL isValid) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(success,isValid);
    } failure:^(SCXErrorModel * _Nonnull error) {
        kSCBlockExeNotNil(failure,error);
        [weakself sc_blank_empty];
    }];
}

- (void)sc_blank_empty{
    
}

- (void) remoteInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestInviteRechargeLinkIsValidWithInvitationId:invitationId success:^(BOOL isValid) {
        weakself.inviteRechargeLinkCache[invitationId] = isValid ? @(1):@(2);
        kSCBlockExeNotNil(success,isValid);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.inviteRechargeLinkCache removeObjectForKey:invitationId];
        kSCBlockExeNotNil(failure,error);
    }];
}
@end
