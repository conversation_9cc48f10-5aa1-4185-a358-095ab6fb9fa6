//
//  SCCoinsService.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/11.
//

#import <Foundation/Foundation.h>
#import "SCObservable.h"
#import "SCBaseAppService.h"
@class SCPromotionDisplayModel,SCActivityPromotionDisplayModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCCoinsService : SCBaseAppService

#pragma mark - 金币购买
///金币商品列表
@property(nonatomic,nullable,strong,readonly) NSArray<NSDictionary *> *coinsListModel;
///拉取远程数据
- (void) remoteCoinsListWithSuccess:(void(^_Nullable)(NSArray<NSDictionary *> *models)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
///筛选可用金币数量
- (void)remoteAvailableCoinsWithSuccess:(void (^_Nullable)(NSInteger coins))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull))failure;
///判断余额不足并且弹窗余额不足弹窗
-(BOOL)checkEnoughCoinsWithModel:(NSInteger)price entry:(NSString *)entry;
#pragma mark - 新用户促销
///新用户促销倒计时
@property(nonatomic,nullable,strong) SCObservable<NSNumber *> *promotionCountDownObx;
///新用户促销
@property(nonatomic,nullable,strong,readonly) SCObservable<SCPromotionDisplayModel *> *promotionObx;
///拉取新用户促销
- (void) remotePromotionWithSuccess:(void(^_Nullable)(SCPromotionDisplayModel *model)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
#pragma mark - 活动促销

///活动促销倒计时
@property(nonatomic,nullable,strong) SCObservable<NSNumber *> *activityCountDownObx;
///活动促销
@property(nonatomic,nullable,strong,readonly) SCObservable<SCActivityPromotionDisplayModel *> *activityPromotionObx;
///拉取活动数据
- (void) remoteActivityPromotionWithSuccess:(void(^_Nullable)(SCActivityPromotionDisplayModel *model)) success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;


#pragma mark - 注册奖励
///注册奖励
@property(nonatomic,nullable,strong,readonly) SCObservable<NSDictionary *> *registerRewardObx;
///检测注册奖励【链接Socket成功后调用】
- (void) checkRegisterReward;

#pragma mark - 邀请充值
///邀请充值链接状态缓存  0:未知 1:有效 2:无效
@property(nonatomic,nullable,strong,readonly) NSMutableDictionary<NSString *,NSNumber *> *inviteRechargeLinkCache;
///邀请充值链接是否有效
- (void) remoteInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;
- (void) getInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel * _Nonnull error))failure;

@end

NS_ASSUME_NONNULL_END
