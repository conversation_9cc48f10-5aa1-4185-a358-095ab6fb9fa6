//
//  SCCategoryAPIManagerCoins.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//
#import "SCCategoryAPIManagerCoins.h"
#import "SCDataConverter.h"
#import "SCDictionaryHelper.h"
#import "SCModelCompatibility.h"

@implementation SCAPIServiceManager (SCCategoryAPIManagerCoins)
//金币列表
+(void) requestCoinsWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull coinsDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIGoodsList method:SCNetMethodPOST parameters:@{@"isIncludeSubscription":@(false),@"payChannel":@"IAP"} headers:nil success:^(id responseObject) {
        NSArray * result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@[]); // 返回空数组作为默认值
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

//邀请充值金币链接是否有效
+(void) requestInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIInviteRechargeLinkIsValid method:SCNetMethodGET parameters:@{@"invitationId":invitationId} headers:nil success:^(id responseObject) {
        BOOL result = [responseObject boolValue];
        kSCBlockExeNotNil(success,result);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

//邀请充值金币列表
+(void) requestCoinsWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull coinsDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIInviteRechargeLinkList method:SCNetMethodPOST parameters:@{@"invitationId":invitationId,@"payChannel":@"IAP"} headers:nil cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(id responseObject) {
        NSArray * result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result) {
            kSCBlockExeNotNil(success,result);
        } else {
            kSCBlockExeNotNil(success,@[]); // 返回空数组作为默认值
        }
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///新用户促销
+(void) requestPromotionWithSuccess:(void (^_Nullable)(NSDictionary*_Nullable promotionDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIPromotion method:SCNetMethodPOST parameters:@{@"isIncludeSubscription":@(NO),@"payChannel":@"IAP"} headers:nil success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        kSCBlockExeNotNil(success,result);
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
///活动促销
+(void) requestActivityPromotionWithSuccess:(void (^_Nullable)(NSDictionary * _Nullable promotionDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIActivityPromotion method:SCNetMethodPOST parameters:@{@"isIncludeSubscription":@(NO),@"payChannel":@"IAP"} headers:nil success:^(id responseObject) {
        NSArray * result = [SCDataConverter safeArrayFromResponseObject:responseObject];
        if(result && [result count] > 0){
            NSDictionary *firstPromotion = result[0];
            if ([firstPromotion isKindOfClass:[NSDictionary class]]) {
                kSCBlockExeNotNil(success,firstPromotion);
            } else {
                kSCBlockExeNotNil(success,nil);
            }
        }else{
            kSCBlockExeNotNil(success,nil);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}

///注册奖励
+(void) requestRegisterRewardWithSuccess:(void (^_Nullable)(NSDictionary * _Nullable rewardDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIRegisterReward method:SCNetMethodGET parameters:nil headers:nil success:^(id responseObject) {
        NSDictionary * result = [SCDataConverter safeDictionaryFromResponseObject:responseObject];
        if(result){
            kSCBlockExeNotNil(success,result);
        }else{
            kSCBlockExeNotNil(success,nil);
        }

    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
//查询可用金币数量
+(void) requestAvailableCoinsWithSuccess:(void (^_Nullable)(NSInteger  coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{

    [SCNetworkManager requestWithURL:[SCAPIManage shared].kSCAPIMyCoinsNum method:SCNetMethodGET parameters:nil headers:nil success:^(id responseObject) {
        if([responseObject isKindOfClass:[NSDictionary class]]){
            NSDictionary *dic = (NSDictionary *)responseObject;
            kSCBlockExeNotNil(success, [[dic objectForKey:@"availableCoins"] integerValue]);
        }else{
            kSCBlockExeNotNil(success,0);
        }
       
        
    } failure:^(SCXErrorModel *error) {
        kSCBlockExeNotNil(failure,error);
    }];
}
@end
