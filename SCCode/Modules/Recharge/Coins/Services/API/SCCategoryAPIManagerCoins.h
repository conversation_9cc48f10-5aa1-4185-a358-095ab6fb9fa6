//
//  SCCategoryAPIManagerCoins.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCAPIServiceManager.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAPIServiceManager (SCCategoryAPIManagerCoins)

///金币列表
+(void) requestCoinsWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull coinsDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure;

///新用户促销
+(void) requestPromotionWithSuccess:(void (^_Nullable)(NSDictionary*_Nullable promotionDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

///活动促销
+(void) requestActivityPromotionWithSuccess:(void (^_Nullable)(NSDictionary * _Nullable activityPromotionDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

///注册奖励
+(void) requestRegisterRewardWithSuccess:(void (^_Nullable)(NSDictionary * _Nullable registerRewardDict))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

//查询可用金币数量
+(void) requestAvailableCoinsWithSuccess:(void (^_Nullable)(NSInteger  coins))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;

//邀请充值金币链接是否有效
+(void) requestInviteRechargeLinkIsValidWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(BOOL isValid))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure;
//邀请充值金币列表
+(void) requestCoinsWithInvitationId:(NSString *)invitationId success:(void (^_Nullable)(NSArray<NSDictionary *>*_Nonnull coinsDicts))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error) )failure;
@end

NS_ASSUME_NONNULL_END
