//
//  SCContinueRechargePopup.h
//  Supercall
//
//  Created by sumengliu on 2024/12/12.
//

#import "SCBaseViewController.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCContinueRechargePopup : SCBaseViewController
- (void)showInViewController:(UIViewController *)viewController 
                  closeBlock:(nullable void(^)(void))closeBlock 
               rechargeBlock:(nullable void(^)(void))rechargeBlock;
- (void)dismiss;
@end

NS_ASSUME_NONNULL_END
