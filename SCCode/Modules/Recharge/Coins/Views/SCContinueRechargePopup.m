//
//  SCContinueRechargePopup.m
//  Supercall
//
//  Created by sumengli<PERSON> on 2024/12/12.
//

#import "SCContinueRechargePopup.h"
#import "SCPopupManager.h"
#import "SCLocalVideoPlayer.h"
#import "SCFontManager.h"

@interface SCContinueRechargePopup ()

@property(nonatomic,weak) UIImageView *contentBgIV;
//按钮
@property(nonatomic,weak) UIButton *reChargebtn;
//关闭按钮
@property(nonatomic,weak) UIButton *nextTimeBtn;
//描述
@property(nonatomic,weak) UILabel *desLabel;

@property (nonatomic, assign) BOOL isShowing;

@property (nonatomic, strong) SCLocalVideoPlayer *videoPlayer;

@property (nonatomic, weak) UIView *videoContain;

@property (nonatomic, copy) void(^closeBlock)(void);
@property (nonatomic, copy) void(^rechargeBlock)(void);

@end

@implementation SCContinueRechargePopup

- (void)viewDidLoad {
    [super viewDidLoad];
    
    // 添加前后台切换通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationWillResignActive:)
                                               name:UIApplicationWillResignActiveNotification
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationDidBecomeActive:)
                                               name:UIApplicationDidBecomeActiveNotification
                                             object:nil];
}

- (void)initData{
    [super initData];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    //背景宽高比
    CGFloat whScale = 350.0f/500.0f;
    CGFloat contentWidth = kSCScreenWidth - kSCScaleWidth(12.0*2);
    CGFloat hScale = (contentWidth/whScale) / 500.0f;
    CGFloat wScale = contentWidth / 350.0f;
    CGFloat videoWhScale = 350.0f/369.0f;
    
    UIImageView *contentBgIV = [[UIImageView alloc] initWithImage:[UIImage imageWithColor:[UIColor colorWithHexString:@"#282828"]]];
    contentBgIV.layer.cornerRadius = kSCBigCornerRadius;
    contentBgIV.layer.masksToBounds = YES;
    contentBgIV.userInteractionEnabled = YES;
    [self.view addSubview:contentBgIV];
    self.contentBgIV = contentBgIV;
    
    // 创建视频容器视图
    UIView *videoContain = [[UIView alloc] init];
    self.videoContain = videoContain;
    [self.contentBgIV addSubview:videoContain];
    
    // 创建视频播放器
    self.videoPlayer = [[SCLocalVideoPlayer alloc] initWithFrame:CGRectZero
                                                      videoName:@"exit_recharge"
                                                          type:@"mp4"];
    [self.videoContain addSubview:self.videoPlayer];
    // 开始播放
    [self.videoPlayer play];

    UIView *transparentView = [[UIView alloc]init];
    transparentView.backgroundColor = [UIColor scTranBlackBGColor];
    [self.videoPlayer addSubview:transparentView];

    UILabel *desLabel = [UILabel labelWithText:@"Many girls are waiting for your reply. Recharge now and enjoy happy time with her now~".translateString textColor:[UIColor colorWithHexString:@"#D0D0D0"] font:[UIFont scRegular:14.0f]];
    desLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentBgIV addSubview:desLabel];
    self.desLabel = desLabel;
    
    UIButton *continueRechargeButton = [UIButton buttonWithTitle:@"Continue to recharge".translateString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16.0f) image:nil backgroundColor:nil cornerRadius:kSCNormalCornerRadius target:self action:@selector(onRecharge)];
    [continueRechargeButton sc_setThemeGradientBackground];
    [self.contentBgIV addSubview:continueRechargeButton];
    self.reChargebtn = continueRechargeButton;
    
    UIButton *nextTimeBtn = [UIButton buttonWithTitle:@"Next Time".translateString titleColor:[UIColor scWhite] font:kScUIFontSemibold(16.0f) image:nil backgroundColor:[UIColor colorWithHexString:@"#4B4B4B"] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onClose)];
    [self.contentBgIV addSubview:nextTimeBtn];
    self.nextTimeBtn = nextTimeBtn;
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.size.mas_equalTo(CGSizeMake(contentWidth, contentWidth/whScale));
    }];
    
    // 设置约束
    [self.videoContain mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.contentBgIV);
        make.height.mas_equalTo(contentWidth/videoWhScale);
    }];
    
    [self.videoPlayer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.videoContain);
    }];
    
    [transparentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.videoPlayer);
    }];
    
    [self.desLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.videoContain.mas_bottom).offset(-7*hScale);
        make.leading.equalTo(self.contentBgIV).offset(22.0f*wScale);
        make.trailing.equalTo(self.contentBgIV).offset(-22.0f*wScale);
    }];
    
    [self.reChargebtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.videoContain.mas_bottom).offset(10.0f*hScale);
        make.centerX.equalTo(self.contentBgIV);
        make.size.mas_equalTo(CGSizeMake(238.0f*wScale, 46.0f*hScale));
    }];
    
    [self.nextTimeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.reChargebtn.mas_bottom).offset(10.0f*hScale);
        make.centerX.equalTo(self.contentBgIV);
        make.size.mas_equalTo(CGSizeMake(238.0f*wScale, 46.0f*hScale));
    }];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.videoPlayer play];
}

-(void)onClose {
    if (self.closeBlock) {
        self.closeBlock();
    }
    [self dismiss];
}

-(void)onRecharge {
    if (self.rechargeBlock) {
        self.rechargeBlock();
    }
    [self dismiss];
}

- (void)showInViewController:(UIViewController *)viewController
                  closeBlock:(nullable void(^)(void))closeBlock
               rechargeBlock:(nullable void(^)(void))rechargeBlock {
    if (self.isShowing) {
        return;
    }
    
    self.closeBlock = closeBlock;
    self.rechargeBlock = rechargeBlock;
    self.isShowing = YES;
    [[SCPopupManager shared] showPopup:self inViewController:viewController level:SCPopupLevelAlert animationStyle:SCPopupAnimationStyleCenter];
}

- (void)dismiss {
    if (!self.isShowing) {
        return;
    }
    
    [[SCPopupManager shared] dismissPopup:self];
    self.isShowing = NO;
}

// 添加处理方法
- (void)applicationWillResignActive:(NSNotification *)notification {
    [self.videoPlayer pause];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    if (self.isShowing) {
        [self.videoPlayer play];
    }
}

// 在视图销毁时移除通知
- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
@end
