//
//  SCCoinsPopupViewController.h
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/15.
//

#import "SCBaseViewController.h"
#import "SCCoinsListView.h"
NS_ASSUME_NONNULL_BEGIN

@interface SCCoinsPopupViewController : SCBaseViewController

@property(nonatomic,nullable,strong,readonly) SCCoinsStoreViewModel * viewModel;
@property(nonatomic,nullable,weak,readonly) SCCoinsListView * listView;
@property(nonatomic,nullable,weak,readonly) UIImageView * contentIV;;
@property(nonatomic,nullable,weak,readonly) UIButton * closeBtn;
@property(nonatomic,nullable,weak,readonly) UIButton *serviceButton;
@property(nonatomic,nullable,weak,readonly) UIStackView *stackView;
@property(nonatomic,assign) SCCoinsListViewStyle style;
@property (nonatomic, strong) NSString *entry;

+(void)showWithFromVC:(nullable UIViewController *)fromVC entry:(nonnull NSString *)entry;
+(void)showWithFromVC:(UIViewController *)fromVC hideService:(BOOL)hideService entry:(NSString *)entry;
-(void) onClose;
- (void)onServiceClick;
@end

NS_ASSUME_NONNULL_END
