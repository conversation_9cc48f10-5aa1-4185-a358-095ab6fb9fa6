//
//  SCCoinsCollectionViewCell.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/9.
//

#import "SCCoinsCollectionViewCell.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCCoinsCollectionViewCell ()

//标签
@property (nonatomic, strong) UILabel * tagLabel;
@property (nonatomic, strong) UIImageView * tagBG;
//金币图标
@property (nonatomic, strong) UIImageView * coinsIcon;
//价格
@property (nonatomic, strong) UILabel * priceLabel;

@end
@implementation SCCoinsCollectionViewCell



- (void)initUI{
    [super initUI];    
    UIImageView * tagBG = [[UIImageView alloc] init];
    [self.contentView addSubview:tagBG];
    self.tagBG = tagBG;
   
    UILabel * tagLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontMedium(0) alignment:NSTextAlignmentCenter];
    [self.contentView addSubview:tagLabel];
    self.tagLabel = tagLabel;
    
    UIImageView * coinsIcon = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_new_user_coins"]];
    [self.contentView addSubview:coinsIcon];
    self.coinsIcon = coinsIcon;
    
    
    UILabel * coinsLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontMedium(0) alignment:NSTextAlignmentCenter];
    [self.contentView addSubview:coinsLabel];
    self.coinsLabel = coinsLabel;
    
    
    UILabel * discountLabel = [UILabel labelWithText:@"" textColor:[UIColor colorWithHexString:@"#FFE500"] font:kScUIFontMedium(0) alignment:NSTextAlignmentCenter];
    [self.contentView addSubview:discountLabel];
    self.discountLabel = discountLabel;
    
    
    UILabel * priceLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:kScUIFontMedium(0) alignment:NSTextAlignmentCenter];
    priceLabel.backgroundColor = [UIColor scTheme];
    [self.contentView addSubview:priceLabel];
    self.priceLabel = priceLabel;
    
    [self updateLayouts];
   
}

- (void)setStyle:(SCCoinsCollectionViewCellStyle)style{
    _style = style;
}
- (void) updateLayouts{
    CGFloat radius = 10.0f;
    //边框
    self.contentView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    self.contentView.layer.cornerRadius = radius;
    self.contentView.layer.masksToBounds = true;
    
    CGFloat tagSize = _style == SCCoinsCollectionViewCellStyleBig ? 13.0f:11.0f;
    self.tagLabel.font = [SCFontManager boldFontWithSize:tagSize];
    
    CGFloat coinsSize = _style == SCCoinsCollectionViewCellStyleBig ? 24.0f:16.0f;
    self.coinsLabel.font = kScUIFontMedium(coinsSize);
    
    CGFloat discountSize = _style == SCCoinsCollectionViewCellStyleBig ? 12.0f:12.0f;
    self.discountLabel.font = kScUIFontRegular(discountSize);
    
    CGFloat priceSize = _style == SCCoinsCollectionViewCellStyleBig ? 15.0f:16.0f;
    self.priceLabel.font = kScUIFontSemibold(priceSize);
    
    // ======== 约束 ==========
    [self.tagBG mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(59.0f, 40.0f));
    }];
    [self.tagLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(-6);
        make.width.mas_equalTo(56.0f);
        make.top.equalTo(self.contentView).offset(8.0f);
    }];
    
    CGSize coinsIconSize = _style == SCCoinsCollectionViewCellStyleBig ? CGSizeMake(74.0f, 70.0f):CGSizeMake(36.0f, 28.0f);
    CGFloat coinsTop = _style == SCCoinsCollectionViewCellStyleBig ? 24.0f:24.0f;
    [self.coinsIcon mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(coinsTop);
        make.centerX.equalTo(self.contentView);
        make.size.mas_equalTo(coinsIconSize);
    }];
    
    CGFloat coinsLabelTop = _style == SCCoinsCollectionViewCellStyleBig ? -6.0f:3.0f;
    CGFloat coinsLabelHeight = _style == SCCoinsCollectionViewCellStyleBig ? 34.0f:22.0f;
    [self.coinsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.coinsIcon.mas_bottom).offset(coinsLabelTop);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(coinsLabelHeight);
    }];
    
    CGFloat discountLabelTop = _style == SCCoinsCollectionViewCellStyleBig ? 5.0f:0.0f;
    CGFloat discountLabelHeight = _style == SCCoinsCollectionViewCellStyleBig ? 16.0f:12.0f;
    [self.discountLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.coinsLabel.mas_bottom).offset(discountLabelTop);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(discountLabelHeight);
    }];
    
    CGFloat priceHeight = _style == SCCoinsCollectionViewCellStyleBig ? 35.0f:44.0f;
    [self.priceLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(priceHeight);
    }];
    
    // 添加渐变背景
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[[UIColor scGradientBtnStartColor],
                                           [UIColor scGradientBtnEndColor]] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *gradientImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(self.contentView.bounds.size.width, priceHeight)];
    self.priceLabel.backgroundColor = [UIColor colorWithPatternImage:gradientImage];
}

// 配置方法
- (void)configureWithDict:(NSDictionary *)coinsDict{
    NSInteger exchangeCoin = kSCCoinsExchangeCoinFromDict(coinsDict);
    self.coinsLabel.text = [NSString stringWithFormat:@"%ld", exchangeCoin];

    CGFloat discount = kSCCoinsDiscountFromDict(coinsDict);
    self.discountLabel.text = (discount == 0 ? @"" : [NSString stringWithFormat:@"%0.2f%%", discount*100]);

    CGFloat price = kSCCoinsPriceFromDict(coinsDict);
    self.priceLabel.text = [NSString stringWithFormat:@"$%0.2f", price];
}

- (void)configureTagWithPromotionText:(NSString *)promotionText coinsDict:(NSDictionary *)coinsDict {
    BOOL isPromotion = kSCCoinsIsPromotionFromDict(coinsDict);
    NSString *tags = kSCCoinsTagsFromDict(coinsDict);

    if (isPromotion && !kSCIsStrEmpty(promotionText)) {
        self.tagLabel.text = promotionText;
        CGAffineTransform transform = CGAffineTransformMakeRotation(-0.556);
        self.tagLabel.transform = transform;
        self.tagBG.hidden = NO;
        self.tagBG.image = [SCResourceManager loadImageWithName:@"bg_coin_store_time"];
    } else if (!kSCIsStrEmpty(tags)) {
        self.tagLabel.text = tags;
        CGAffineTransform transform = CGAffineTransformMakeRotation(-0.556);
        self.tagLabel.transform = transform;
        self.tagBG.hidden = NO;
        self.tagBG.image = [SCResourceManager loadImageWithName:@"bg_coin_store_tag"];
    } else {
        self.tagLabel.text = @"";
        self.tagBG.hidden = YES;
    }
}

@end
