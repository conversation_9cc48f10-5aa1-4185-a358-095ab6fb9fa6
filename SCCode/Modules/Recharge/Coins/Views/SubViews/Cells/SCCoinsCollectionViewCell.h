//
//  SCCoinsCollectionViewCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/9.
//

#import "SCBaseCollectionCell.h"
// #import "SCCoinsModel.h" // 已移除，使用字典替代

typedef enum : NSUInteger {
    SCCoinsCollectionViewCellStyleDefault,
    SCCoinsCollectionViewCellStyleBig,
} SCCoinsCollectionViewCellStyle;
NS_ASSUME_NONNULL_BEGIN

@interface SCCoinsCollectionViewCell : SCBaseCollectionCell
@property(nonatomic,assign) SCCoinsCollectionViewCellStyle style;

//金币数量
@property (nonatomic, strong) UILabel * coinsLabel;
//折扣
@property (nonatomic, strong) UILabel * discountLabel;

- (void) updateLayouts;
- (void)configureTagWithPromotionText:(NSString *)promotionText coinsDict:(NSDictionary *)coinsDict;

// 配置方法
- (void)configureWithDict:(NSDictionary *)coinsDict;

@end

NS_ASSUME_NONNULL_END
