//
//  SCCoinsListView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/12.
//

#import "SCCoinsListView.h"
#import "SCCoinsStoreViewModel.h"
#import "SCCoinsFullScreenHeaderView.h"
#import "SCCoinsBigCollectionViewCell.h"
#import "SCBannerService.h"
#import "SCCoinsService.h"
#import "SCTrackingUtils.h"

@interface SCCoinsListView()

@property(nonatomic,nullable,weak) UICollectionView * collectionView;
//我的金币页面
@property(nonatomic,nullable,weak) SCCoinsFullScreenHeaderView * headerView;
///不强持有，ViewModel只能被Controller持有。所以需要使用weak
@property(nonatomic,nullable,weak) SCCoinsStoreViewModel * viewModel;
///样式
@property(nonatomic,assign) SCCoinsListViewStyle style;
///用于关闭订阅
@property(nonatomic,strong) SCDisposeBag * disposeBag;

@property (nonatomic, copy) NSString *countDownText;

@end


@implementation SCCoinsListView

- (instancetype)initWithFrame:(CGRect)frame style:(SCCoinsListViewStyle)style bindViewModel:(SCCoinsStoreViewModel *)viewModel
{
    self = [super initWithFrame:frame];
    if (self) {
        _viewModel = viewModel;
        _style = style;
        _disposeBag = [[SCDisposeBag alloc] init];

        // 金币点击事件初始化
        _onClickCoinsDictObx = [[SCObservable<NSDictionary *> alloc] initWithValue:nil];

        [self _initUI];
    }
    return self;
}

- (void) _initUI{
    self.backgroundColor = [UIColor colorWithHexString:@"#370000"];
    self.layer.cornerRadius = 38.0f;
    self.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    
    switch (_style) {
        case SCCoinsListViewStyleHalfScreen:
            [self _buildHeaderView];
            [self _buildCollectionViewWithRowNum:3 cellClass:[SCCoinsCollectionViewCell class] forCellReuseIdentifier:[SCCoinsCollectionViewCell cellIdentifier]];
            break;
        case SCCoinsListViewStyleAnchorGuide:
            
            [self _buildCollectionViewWithRowNum:3 cellClass:[SCCoinsCollectionViewCell class] forCellReuseIdentifier:[SCCoinsCollectionViewCell cellIdentifier]];
            break;
        default:
            [self _buildHeaderView];
            [self _buildCollectionViewWithRowNum:3 cellClass:[SCCoinsCollectionViewCell class] forCellReuseIdentifier:[SCCoinsCollectionViewCell cellIdentifier]];
            break;
    }
    
    if(_headerView){
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.equalTo(self);
            make.top.equalTo(self.headerView.mas_bottom).offset(6.0f);
        }];
    }else{
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.equalTo(self);
            make.top.equalTo(self).offset(12);
        }];
    }
    
    kWeakSelf(self)
    // 监听字典版本的金币数据变化
    [self.viewModel.coinsDictsObs subscribe:^(NSArray<NSDictionary *> * _Nonnull value) {
        [weakself.collectionView reloadData];
    } error:^(SCXErrorModel * _Nonnull error) {
        [weakself.collectionView reloadData];
    } disposeBag:_disposeBag];
    
    [kScAuthMar.coinsService.promotionCountDownObx subscribe:^(NSNumber * _Nonnull value) {
        if([value integerValue] == 0){
            //活动结束
            weakself.countDownText = @"";
        }else{
            weakself.countDownText = [value toHHMMSS];
        }
        [weakself.collectionView reloadData];
    } error:^(SCXErrorModel * _Nonnull error) {
        weakself.countDownText = @"";
        [weakself.collectionView reloadData];
    } disposeBag:_disposeBag];
    
    if(self.headerView){
        [self.viewModel.bannerObs subscribe:^(NSArray<NSDictionary *> * _Nonnull value) {
            weakself.headerView.banners = value;
        } error:^(SCXErrorModel * _Nonnull error) {
            weakself.headerView.banners = nil;
        } disposeBag:_disposeBag];
        [self.viewModel.availableCoins subscribe:^(NSNumber * _Nonnull value) {
            [weakself.headerView setAvailableCoins:[value integerValue]];
        } error:nil disposeBag:_disposeBag];
    }
    
}
-(void) _buildHeaderView{
    SCCoinsFullScreenHeaderView * headerView = [[SCCoinsFullScreenHeaderView alloc] init];
    headerView.style = self.style;
    [self addSubview:headerView];
    self.headerView = headerView;
    [headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self).offset(0);
    }];
}
-(void) _buildCollectionViewWithRowNum:(int) rowNum cellClass:(nullable Class)cellClass forCellReuseIdentifier:(NSString *_Nullable)identifier{
    
    UICollectionViewFlowLayout * layout = [[UICollectionViewFlowLayout alloc] init];
    layout.minimumLineSpacing = 15;
    layout.minimumInteritemSpacing = 6;
    CGFloat itemWidth = (kSCScreenWidth - 30 - (6*(rowNum-1))) / ((CGFloat)rowNum);
    if(rowNum == 2){
        layout.itemSize = CGSizeMake(itemWidth, 191);
    }else{
        CGFloat width = round(kSCScaleWidth(110.0f));
        layout.itemSize = CGSizeMake(width, round(width * (135.0/110.0)));
    }
    
    UICollectionView * collectionView = [UICollectionView collectionViewWithFrame:self.bounds layout:layout delegate:self dataSource:self cellClass:cellClass forCellReuseIdentifier:identifier];
    //左右间距
    collectionView.backgroundColor = [UIColor colorWithHexString:@"#370000"];
    collectionView.contentInset = UIEdgeInsetsMake(0, 15, 0, 15);
    [self addSubview:collectionView];
    self.collectionView = collectionView;
}


- (void)dealloc{
    [_disposeBag dispose];
    _disposeBag = nil;
}

@end
@implementation SCCoinsListView (CollectionView)
- (nonnull __kindof UICollectionViewCell *)collectionView:(nonnull UICollectionView *)collectionView cellForItemAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCCoinsCollectionViewCell *cell = [SCCoinsCollectionViewCell initWithFormCollectionView:collectionView forIndexPath:indexPath];

    NSInteger row = indexPath.row;

    if (self.viewModel.coinsDictsObs.value.count > 0 && row < self.viewModel.coinsDictsObs.value.count) {
        NSDictionary *coinsDict = self.viewModel.coinsDictsObs.value[row];
        [cell configureWithDict:coinsDict];
        [cell configureTagWithPromotionText:self.countDownText coinsDict:coinsDict];
    } else {
        [cell configureTagWithPromotionText:self.countDownText coinsDict:@{}];
    }

    return cell;
}

- (NSInteger)collectionView:(nonnull UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.viewModel.coinsDictsObs.value.count;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    NSInteger row = indexPath.row;

    if (self.viewModel.coinsDictsObs.value.count > 0 && row < self.viewModel.coinsDictsObs.value.count) {
        NSDictionary *coinsDict = self.viewModel.coinsDictsObs.value[row];
        self.onClickCoinsDictObx.value = coinsDict;

        NSString *goodsId = kSCCoinsGoodsIdFromDict(coinsDict);
        double price = kSCCoinsPriceFromDict(coinsDict);
        double originalPrice = kSCCoinsOriginalPriceFromDict(coinsDict);
        [SCTrackingUtils trackProductClickWithProductId:goodsId price:price originPrice:originalPrice];
    }
}
@end
