//
//  SCMyCoinsView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/9.
//

#import "SCMyCoinsView.h"
#import <Masonry/Masonry.h>
#import "SCFontManager.h"

@interface SCMyCoinsView ()

@end
@implementation SCMyCoinsView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self _initUI];
    }
    return self;
}

- (void)_initUI {
    UIImageView *bgImageView = [[UIImageView alloc] init];
    bgImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self addSubview:bgImageView];
    _bgImageView = bgImageView;
    
    UILabel *titleLabel = [UILabel labelWithText:@"My Diamonds:".translateString textColor:[UIColor scWhite] font:[SCFontManager boldItalicFontWithSize:22.0f]];
    [self addSubview:titleLabel];
    _titleLabel = titleLabel;
    
    UILabel *subTitleLabel = [UILabel labelWithText:@"0" textColor:[UIColor scWhite] font:kScUIFontSemibold(20.0f)];
    [self addSubview:subTitleLabel];
    _subTitleLabel = subTitleLabel;
    
    // Masonry constraints
    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(20.0f);
        make.leading.equalTo(self).offset(15.0f);
        make.trailing.equalTo(self).offset(-15.0f);
    }];
    
    [subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5.0f);
        make.leading.equalTo(self).offset(15.0f);
        make.trailing.equalTo(self).offset(-15.0f);
    }];
}

- (void)setStyle:(SCCoinsListViewStyle)style {
    _style = style;
    self.bgImageView.image = [SCResourceManager loadImageWithName:self.style == SCCoinsListViewStyleFullScreen ? @"bg_coin_store_fullscreen" : @"bg_mycoin_popup"];
    if (self.style == SCCoinsListViewStyleFullScreen) {
        self.titleLabel.hidden = YES;
        self.subTitleLabel.font = [SCFontManager boldItalicFontWithSize:30.0f];
        CGFloat offset = (132.0f / 305.0f) * kSCScaleHeight(305.0f);
        
        [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self).offset(offset);
            make.centerX.equalTo(self);
        }];
    } else {
        self.titleLabel.hidden = NO;
        self.subTitleLabel.font = kScUIFontSemibold(20.0f);
        [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.titleLabel.mas_bottom).offset(5.0f);
            make.leading.equalTo(self).offset(15.0f);
            make.trailing.equalTo(self).offset(-15.0f);
        }];
    }
}

@end
