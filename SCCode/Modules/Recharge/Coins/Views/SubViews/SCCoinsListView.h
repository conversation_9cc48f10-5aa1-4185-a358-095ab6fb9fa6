//
//  SCCoinsListView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/12.
//

#import <UIKit/UIKit.h>
@class SCCoinsStoreViewModel;
NS_ASSUME_NONNULL_BEGIN
typedef enum : NSUInteger {
    ///全屏的样式
    SCCoinsListViewStyleFullScreen = 0,
    ///半屏的样式
    SCCoinsListViewStyleHalfScreen = 1,
    ///主播引导的样式
    SCCoinsListViewStyleAnchorGuide = 2,
} SCCoinsListViewStyle;


@interface SCCoinsListView : UIView

// 金币点击事件
@property(nonatomic,strong) SCObservable<NSDictionary *> *onClickCoinsDictObx;

- (instancetype)initWithFrame:(CGRect)frame style:(SCCoinsListViewStyle)style bindViewModel:(SCCoinsStoreViewModel *)viewModel;
@end

@interface SCCoinsListView (CollectionView)<UICollectionViewDelegate,UICollectionViewDataSource>

@end
NS_ASSUME_NONNULL_END
