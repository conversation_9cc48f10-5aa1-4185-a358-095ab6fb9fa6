//
//  SCCoinsFullScreenHeaderView.m
//  Supercall
//
//  Created by guanweihong on 2024/1/9.
//

#import "SCCoinsFullScreenHeaderView.h"
//第三方库
#import <Masonry/Masonry.h>
//View
#import "SCMyCoinsView.h"
#import "SCBannerView.h"

// Banner顶部圆角图
static CGFloat kCornerIVTopFullScreenOffset = 75.0f;
static CGFloat kCornerIVTopHalfScreenOffset = 23.0f;
static CGFloat kCornerIVHeight = 23.0f;

@interface SCCoinsFullScreenHeaderView ()

@property(nonatomic,nullable,weak) SCMyCoinsView * myCoinsView;
//Banner顶部圆角图
@property(nonatomic,nullable,weak) UIImageView *cornerView;

@property(nonatomic,nullable,weak) SCBannerView * bannerView;

@property(nonatomic,nullable,weak) UIView *bannerContainView;

@end

@implementation SCCoinsFullScreenHeaderView

- (void)initUI{
    [super initUI];
    SCMyCoinsView * myCoinsView = [[SCMyCoinsView alloc] init];
    [self addSubview:myCoinsView];
    self.myCoinsView = myCoinsView;
    kSCAddTapGesture(myCoinsView, self, onTapMyCoins);
    [myCoinsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.leading.trailing.equalTo(self);
        make.height.mas_equalTo(self.style == SCCoinsListViewStyleFullScreen ? kSCScaleHeight(305) : 124);
    }];
    
    UIImage *bgImage = [[[UIImage imageWithColor:[UIColor colorWithHexString:@"#370000"] imageSize:CGSizeMake(kSCBigCornerRadius*3, kSCBigCornerRadius*3)] imageWithRoundedCorners:SCCornerRadiusMake(kSCBigCornerRadius, kSCBigCornerRadius, 0, 0)] resizableImageWithCapInsets:UIEdgeInsetsMake(kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius) resizingMode:UIImageResizingModeStretch];
    UIImageView * cornerIV = [[UIImageView alloc] initWithImage:bgImage];
    [self addSubview:cornerIV];
    self.cornerView = cornerIV;
    [cornerIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.myCoinsView.mas_bottom).offset(- (self.style == SCCoinsListViewStyleFullScreen ? kCornerIVTopFullScreenOffset : kCornerIVTopHalfScreenOffset));
        make.leading.trailing.equalTo(self);
        make.height.mas_equalTo(kCornerIVHeight);
        make.bottom.equalTo(self).offset(-12);
    }];
}

- (void)setStyle:(SCCoinsListViewStyle)style {
    _style = style;
    if (self.myCoinsView != nil) {
        self.myCoinsView.style = style;
        [self.myCoinsView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.mas_equalTo(self.style == SCCoinsListViewStyleFullScreen ? kSCScaleHeight(305) : 124);
        }];
        [self.cornerView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.myCoinsView.mas_bottom).offset(- (self.style == SCCoinsListViewStyleFullScreen ? kCornerIVTopFullScreenOffset : kCornerIVTopHalfScreenOffset));
        }];
    }
   
}

- (void)setBanners:(NSArray<NSDictionary *> *)banners{
    _banners = banners;
    if(_banners == nil || _banners.count == 0){
        [self removeBannerView];
        return ;
    }
    if(_bannerView == nil){
        UIView *containView = [[UIView alloc]init];
        containView.backgroundColor = [UIColor colorWithHexString:@"#370000"];
        [self addSubview:containView];
        self.bannerContainView = containView;
        
        SCBannerView * bannerView = [[SCBannerView alloc] init];
        bannerView.layer.cornerRadius = kSCNormalCornerRadius;
        bannerView.layer.masksToBounds = true;
        [self.bannerContainView addSubview:bannerView];
        self.bannerView = bannerView;
        
        [self.cornerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.myCoinsView.mas_bottom).offset(- (self.style == SCCoinsListViewStyleFullScreen ? kCornerIVTopFullScreenOffset : kCornerIVTopHalfScreenOffset));
            make.leading.trailing.equalTo(self);
            make.height.mas_equalTo(kCornerIVHeight);
        }];
        
        [containView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.equalTo(self);
            make.top.equalTo(self.cornerView.mas_bottom);
            make.bottom.equalTo(self).offset(-12);
        }];
        
        [bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.bannerContainView).offset(15);
            make.trailing.equalTo(self.bannerContainView).offset(-15);
            make.top.bottom.equalTo(self.bannerContainView);
            make.height.mas_equalTo(108);
        }];
    }
    self.bannerView.banners = _banners;
    [self layoutIfNeeded];
    [self setNeedsLayout];
}
- (void)setBannerIndex:(NSInteger)bannerIndex{
    [self.bannerView showIndex:bannerIndex];
}

- (void)setAvailableCoins:(NSInteger)availableCoins{
    _availableCoins = availableCoins;
    _myCoinsView.subTitleLabel.text = [NSString stringWithFormat:@"%ld",availableCoins];
    [_myCoinsView setNeedsLayout];
}

-(void) removeBannerView{
    if(_bannerView != nil){
        [_bannerContainView removeFromSuperview];
        [_bannerView removeFromSuperview];
        self.bannerContainView = nil;
        self.bannerView = nil;
        [self.cornerView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.myCoinsView.mas_bottom).offset(- (self.style == SCCoinsListViewStyleFullScreen ? kCornerIVTopFullScreenOffset : kCornerIVTopHalfScreenOffset));
            make.leading.trailing.equalTo(self);
            make.height.mas_equalTo(kCornerIVHeight);
            make.bottom.equalTo(self).offset(-12);
        }];
    };
}

#pragma mark - onTapMyCoins
- (void) onTapMyCoins{
    [self eventCallBackWithInfo:@(SCCoinsFullScreenHeaderViewActionMyCoins)];
}

@end
