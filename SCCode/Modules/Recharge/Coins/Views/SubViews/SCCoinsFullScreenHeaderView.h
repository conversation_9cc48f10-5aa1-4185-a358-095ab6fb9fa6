//
//  SCCoinsFullScreenHeaderView.h
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/9.
//

#import <UIKit/UIKit.h>
#import "SCBaseView.h"
#import "SCBannerView.h"
#import "SCCoinsListView.h"
NS_ASSUME_NONNULL_BEGIN

typedef enum : NSUInteger {
    //点击我的金币
    SCCoinsFullScreenHeaderViewActionMyCoins,
} SCCoinsFullScreenHeaderViewAction;

@interface SCCoinsFullScreenHeaderView : SCBaseView
@property(nonatomic,strong) NSArray<NSDictionary *> * _Nullable banners; // 字典版本Banner数据
@property(nonatomic,assign) NSInteger availableCoins;
@property(nonatomic,assign) NSInteger bannerIndex;
@property(nonatomic,assign) SCCoinsListViewStyle style;

@end

NS_ASSUME_NONNULL_END
