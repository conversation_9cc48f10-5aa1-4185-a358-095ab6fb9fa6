//
//  SCAnchorCoinsViewController.m
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/15.
//

#import "SCAnchorCoinsViewController.h"
#import "SCBaseNavigationController.h"
#import "SCRobotCustomerServiceViewController.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCPopupManager.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorCoinsViewController ()

@property(nonatomic,weak)UIImageView * avatarIV;
@property(nonatomic,weak)UILabel *nickNameLabel;
@property(nonatomic,weak)UILabel * titleLabel;
@property(nonatomic,weak)UILabel * subTitleLabel;
@property(nonatomic,weak)UIImageView *infoBgImageView;
@property(nonatomic,weak)UIImageView *cornerImageView;
// 字典版本的主播信息
@property(nonatomic,strong) NSDictionary * anchorDict;

@end

@implementation SCAnchorCoinsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
}

- (void)initData{
    [super initData];
    self.style = SCCoinsListViewStyleAnchorGuide;
}

- (void)initUI{
    [super initUI];
    self.view.backgroundColor = [UIColor clearColor];
    self.listView.layer.cornerRadius = 0.0f;
    
    UIImageView *infobgImageView = [[UIImageView alloc]initWithImage:[SCResourceManager loadImageWithName:@"bg_anchor_coins_info"]];
    [self.contentIV addSubview:infobgImageView];
    _infoBgImageView = infobgImageView;
    
    UIImage *bgImage = [[[UIImage imageWithColor:[UIColor colorWithHexString:@"#370000"] imageSize:CGSizeMake(kSCBigCornerRadius*3, kSCBigCornerRadius*3)] imageWithRoundedCorners:SCCornerRadiusMake(kSCBigCornerRadius, kSCBigCornerRadius, 0, 0)] resizableImageWithCapInsets:UIEdgeInsetsMake(kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius, kSCBigCornerRadius) resizingMode:UIImageResizingModeStretch];
    UIImageView * cornerIV = [[UIImageView alloc] initWithImage:bgImage];
    [_infoBgImageView addSubview:cornerIV];
    
    [self.contentIV bringSubviewToFront:self.closeBtn];
    [self.contentIV bringSubviewToFront:self.stackView];
    [self.closeBtn setImage:[SCResourceManager loadImageWithName:@"btn_close_white"] forState:UIControlStateNormal];
    
    CGFloat avatarIVWH = 70.0f;
    UIImageView * avatarIV = [[UIImageView alloc] init];
    avatarIV.layer.cornerRadius = avatarIVWH/2.0f;
    avatarIV.layer.masksToBounds = YES;
    avatarIV.layer.borderWidth = 2.0f;
    avatarIV.layer.borderColor = UIColor.scWhite.CGColor;
    avatarIV.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentIV addSubview:avatarIV];
    _avatarIV = avatarIV;
    // 使用字典数据
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:self.anchorDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:self.anchorDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];

    [self.avatarIV sc_setImageWithURL:avatarUrl];

    UILabel *nickNameLabel = [UILabel labelWithTextColor:UIColor.scWhite font:[SCFontManager semiBoldFontWithSize:16.0f]];
    nickNameLabel.text = nickname;
    nickNameLabel.numberOfLines = 1;
    [self.contentIV addSubview:nickNameLabel];
    _nickNameLabel = nickNameLabel;

    NSString *titleString = [@"Do you like ###?".translateString stringByReplacingOccurrencesOfString:@"###" withString:nickname.replaceMoreThan10];
    UILabel * titleLabel = [UILabel labelWithText:titleString textColor:[UIColor scWhite] font:kScUIFontRegular(12) alignment:NSTextAlignmentLeft];
    [self.contentIV addSubview:titleLabel];
    _titleLabel = titleLabel;
    
    UILabel * subTitleLabel = [UILabel labelWithText:@"Recharge to call back!".translateString textColor:[UIColor scWhite] font:kScUIFontRegular(12) alignment:NSTextAlignmentLeft];
    [self.contentIV addSubview:subTitleLabel];
    _subTitleLabel = subTitleLabel;
    
    [self.closeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentIV.mas_top).offset(35.0f);
    }];
    
    [self.infoBgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentIV);
        make.leading.trailing.equalTo(self.contentIV);
        make.height.mas_equalTo(124.0f);
    }];
    
    [cornerIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.bottom.trailing.equalTo(self.infoBgImageView);
        make.height.mas_equalTo(25.0f);
    }];
    
    [self.avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentIV).offset(16.0f);
        make.leading.equalTo(self.contentIV).offset(24.0f);
        make.size.mas_equalTo(CGSizeMake(avatarIVWH, avatarIVWH));
    }];
    
    [self.nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentIV).offset(23.0f);
        make.leading.equalTo(self.avatarIV.mas_trailing).offset(13.0f);
        make.trailing.equalTo(self.closeBtn.mas_leading).offset(-10.0f);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nickNameLabel.mas_bottom).offset(3.5f);
        make.height.mas_equalTo(18.0f);
        make.leading.equalTo(self.nickNameLabel);
        make.trailing.equalTo(self.nickNameLabel);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(3.5f);
        make.height.mas_equalTo(18.0f);
        make.leading.equalTo(self.nickNameLabel);
        make.trailing.equalTo(self.nickNameLabel);
    }];
    
    [self.listView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(350.0f);
    }];
    
    [self.stackView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentIV).offset(124.0f);
    }];
}

-(void) onClose {
    [[SCPopupManager shared] dismissPopup:self];
}

-(void)onServiceClick {
    SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

// 字典版本的显示方法实现
+(void)showWithFromVC:(nullable UIViewController *)fromVC anchorDict:(NSDictionary *) anchorDict{
    SCAnchorCoinsViewController *vc = [[SCAnchorCoinsViewController alloc] init];
    vc.anchorDict = anchorDict;
    vc.entry = SCPayEntry.shared.kPayEntrySourceAfterFreeCall;
    [[SCPopupManager shared] showPopup:vc inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
}

@end
