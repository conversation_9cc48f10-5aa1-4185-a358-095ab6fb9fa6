//
//  SCCoinsPopupViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/15.
//

#import "SCCoinsPopupViewController.h"
#import "SCRobotCustomerServiceViewController.h"
#import "SCCoinsStoreViewModel.h"
#import "SCThirdPartyPayPopup.h"
#import "SCPopupManager.h"

#import "SCPayService.h"
#import "SCSocketService.h"


@interface SCCoinsPopupViewController ()

@property (nonatomic, assign) BOOL hideService;

@end


@implementation SCCoinsPopupViewController

- (void)initData{
    [super initData];
    _viewModel = [[SCCoinsStoreViewModel alloc] init];
    _style = SCCoinsListViewStyleHalfScreen;
}

- (void)setHideService:(BOOL)hideService {
    _hideService = hideService;
    [self updateUI];
}

- (void)initUI{
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.backgroundColor = [UIColor clearColor];
    
    UIImageView * contentIV = [[UIImageView alloc] init];
    contentIV.backgroundColor = [UIColor colorWithHexString:@"290000"];
    contentIV.layer.cornerRadius = 44.0f;
    contentIV.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self.view addSubview:contentIV];
    [contentIV setUserInteractionEnabled:YES];
    _contentIV = contentIV;
    
    UIButton * closeBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"]];
    kSCAddTapGesture(closeBtn, self, onClose);
    [self.contentIV addSubview:closeBtn];
    _closeBtn = closeBtn;
    
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(28, 28));
        make.bottom.equalTo(self.contentIV.mas_top).offset(-7.0f);
        make.trailing.equalTo(self.contentIV).offset(-15.0f);
    }];
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.spacing = 16;
    [self.contentIV addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentIV);
        make.leading.trailing.equalTo(self.contentIV);
        make.bottom.equalTo(self.contentIV).offset(-MAX(kSCSafeAreaBottomHeight, 20));
    }];
    _stackView = stackView;
    
    SCCoinsListView * listView = [[SCCoinsListView alloc] initWithFrame:self.self.contentIV.bounds style:_style bindViewModel:self.viewModel];
    [stackView addArrangedSubview:listView];
    _listView = listView;
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(555.0f);
    }];
    
    UIButton *serviceBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_robot_customer"]];
    [serviceBtn setTitle:@"Customer Service".translateString forState:UIControlStateNormal];
    [serviceBtn setTitleColor:UIColor.scWhite forState:UIControlStateNormal];
    serviceBtn.titleLabel.font = kScUIFontMedium(12);
    serviceBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [serviceBtn addTarget:self action:@selector(onServiceClick) forControlEvents:UIControlEventTouchUpInside];
    [stackView addArrangedSubview:serviceBtn];
    _serviceButton = serviceBtn;
    [self.serviceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(20);
    }];
    
    [self.contentIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
    }];
    
    [self updateUI];
    
    [self.viewModel requestData];
    kWeakSelf(self)
    // 使用字典版本的金币点击事件
    [self.listView.onClickCoinsDictObx subscribe:^(NSDictionary * _Nonnull coinsDict) {
        __strong SCCoinsPopupViewController *strongSelf = weakself;
        [strongSelf viewModelToPayWithCoinsDict:coinsDict];
    } error:nil disposeBag:self.disposeBag];
    
    //监听订单遍变化，服务端已更新商店数据
    [kScAuthMar.payService.orderChangeObx afterSubscribe:^(SCOrderResultModel * _Nonnull value) {
        // 刷新商店数据 - 使用字典版本
        [weakself.viewModel requestCoinsDicts];
    } error:nil disposeBag:self.disposeBag];

    //监听socket订单编号，服务端已更新商店数据 - 使用字典处理
    [kSCAuthSocketService.orderEventObs afterSubscribe:^(NSDictionary * _Nonnull orderDict) {
        // 刷新商店数据 - 使用字典版本
        [weakself.viewModel requestCoinsDicts];
    } error:nil disposeBag:self.disposeBag];
    
}

// 支付金币
- (void)viewModelToPayWithCoinsDict:(NSDictionary * _Nonnull)coinsDict {
    kWeakSelf(self)
    [self.viewModel toPayWithCoinsDict:coinsDict entry:self.entry fromVC:self callBack:^(BOOL success) {
        if(success){//如果充值成功则关闭页面
            [kSCKeyWindow toast:@"payment successful".translateString];
            [weakself onClose];
        }
    }];
}

- (void)updateUI {
    if (self.serviceButton) {
        self.serviceButton.hidden = self.hideService;
    }
}

- (void)viewWillLayoutSubviews{
    [super viewWillLayoutSubviews];
    
}

#pragma mark - Action
-(void) onClose{
    [[SCPopupManager shared] dismissPopup:self];
}

- (void)onServiceClick {
    SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

+(void)showWithFromVC:(nullable UIViewController *)fromVC entry:(nonnull NSString *)entry {
    SCCoinsPopupViewController *vc = [[SCCoinsPopupViewController alloc] init];
    vc.entry = entry;
    [[SCPopupManager shared] showPopup:vc inViewController:fromVC level:SCPopupLevelNormal animationStyle:SCPopupAnimationStyleBottom];
}

+ (void)showWithFromVC:(UIViewController *)fromVC hideService:(BOOL)hideService entry:(nonnull NSString *)entry {
    SCCoinsPopupViewController *vc = [[SCCoinsPopupViewController alloc] init];
    vc.hideService = hideService;
    vc.entry = entry;
    [[SCPopupManager shared] showPopup:vc inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
}

@end

