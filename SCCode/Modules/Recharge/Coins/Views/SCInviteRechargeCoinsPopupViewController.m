//
//  SCInviteRechargeCoinsPopupViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/7.
//

#import "SCInviteRechargeCoinsPopupViewController.h"
#import "SCRobotCustomerServiceViewController.h"
#import "SCCoinsCollectionViewCell.h"
#import "SCCategoryAPIManagerCoins.h"
#import "SCPayService.h"
//#import "SCThirdPayCannelGroupModel.h"
#import "SCThirdPartyPayPopup.h"
#import "SCBaseNavigationController.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCFontManager.h"
#import "SCPopupManager.h"

@interface SCInviteRechargeCoinsPopupViewController ()

// 内容布局
@property(nonatomic, nullable, weak) UIView *contentView;
// 顶部内容布局
@property(nonatomic, nullable, weak) UIView *topContentView;
// 顶部图标
@property(nonatomic, nullable, weak) UIImageView *topIconIV;
// 顶部背景图
@property(nonatomic, nullable, weak) UIImageView *topBgIV;
// 标题
@property(nonatomic, nullable, weak) UILabel *titleLabel;
// 描述
@property(nonatomic, nullable, weak) UILabel *descLabel;
// 右上角关闭按钮
@property(nonatomic, nullable, weak) UIButton *closeBtn;
// 金币列表
@property(nonatomic, nullable, weak) UICollectionView *coinsCollectionView;
// 机器人客服
@property (nonatomic, nullable, weak) UIButton *serviceButton;

@property(nonatomic, strong) NSArray<NSDictionary *> *coins;
///邀请ID
@property(nonatomic, copy) NSString *inviteId;

@end

@interface SCInviteRechargeCoinsPopupViewController(SCInviteRechargeCoinsCollectionView)<UICollectionViewDelegate, UICollectionViewDataSource>

@end

@implementation SCInviteRechargeCoinsPopupViewController

- (void)initUI {
    [super initUI];
    [self setIsHiddenSCNavigationBar:YES];
    self.view.setBackgroundColor(UIColor.clearColor);
    
    _topContentView = [UIView new].setBackgroundColor(UIColor.clearColor).setCornerRadius(28.0f).addSuperView(self.view);
    _topContentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    _topBgIV = [UIImageView new].setImage([SCResourceManager loadImageWithName:@"bg_invite_recharge_coins_top"]).addSuperView(self.topContentView);
    _topIconIV = [UIImageView new].setImage([SCResourceManager loadImageWithName:@"ic_invite_recharge_coins_top"]).addSuperView(self.topContentView);
    _titleLabel = [UILabel new].setText(@"Getting discounts".translateString).setTextColor(UIColor.scWhite).addSuperView(self.topContentView);
    _titleLabel.font = [SCFontManager semiBoldFontWithSize:16.0f];
    _descLabel = [UILabel new].setText(@"Get more 10% discount".translateString).setFontRegularSize(12).setTextColor(UIColor.scWhite).addSuperView(self.topContentView);
    
    _contentView = [[UIView alloc] init].setBackgroundColor([UIColor colorWithHexString:@"#370000"]).setCornerRadius(kSCBigCornerRadius).addSuperView(self.view);
    _contentView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    
    _closeBtn = [UIButton buttonWithImageName:@"btn_circle_close_white" target:self action:@selector(onClose)].addSuperView(self.view);
    
    UICollectionViewFlowLayout * layout = [[UICollectionViewFlowLayout alloc] init];
    layout.minimumLineSpacing = 15;
    layout.minimumInteritemSpacing = 6;
    CGFloat width = round(kSCScaleWidth(110.0f));
    layout.itemSize = CGSizeMake(width, round(width * (135.0/110.0)));
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.spacing = 16;
    [self.contentView addSubview:stackView];
    
    UICollectionView *collectionView = [UICollectionView collectionViewWithFrame:self.contentView.bounds layout:layout delegate:self dataSource:self cellClass:[SCCoinsCollectionViewCell class] forCellReuseIdentifier:[SCCoinsCollectionViewCell cellIdentifier]];
    collectionView.backgroundColor = [UIColor clearColor];
    collectionView.contentInset = UIEdgeInsetsMake(0, 10, 0, 10);
    [stackView addArrangedSubview:collectionView];
    _coinsCollectionView = collectionView;
    
    UIButton *serviceBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_robot_customer"]];
    [serviceBtn setTitle:@"Customer Service".translateString forState:UIControlStateNormal];
    [serviceBtn setTitleColor:[UIColor scWhite] forState:UIControlStateNormal];
    serviceBtn.titleLabel.font = kScUIFontMedium(12);
    serviceBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [serviceBtn addTarget:self action:@selector(onServiceClick) forControlEvents:UIControlEventTouchUpInside];
    [stackView addArrangedSubview:serviceBtn];
    _serviceButton = serviceBtn;
    [self.serviceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(20);
    }];
    
    [self.topContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.contentView.mas_top).offset(25.0f);
        make.height.mas_equalTo(124.0f);
    }];
    
    [self.topIconIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.topContentView).offset(13.0f);
        make.top.equalTo(self.topContentView).offset(3.0f);
        make.size.mas_equalTo(CGSizeMake(91, 95));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.topIconIV.mas_trailing).offset(3.0f);
        make.top.equalTo(self.topContentView).offset(34);
        make.height.mas_equalTo(20.0f);
    }];
    
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(2);
        make.height.mas_equalTo(17.0f);
    }];
    
    [self.topBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.topContentView);
    }];
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.leading.bottom.equalTo(self.view);
    }];
    
    //关闭按钮
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.topContentView.mas_top).offset(-3);
        make.trailing.equalTo(self.view).offset(-10);
        make.size.mas_equalTo(CGSizeMake(40, 40));
    }];
    
    [self.coinsCollectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(315.0f);
    }];
    
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(30);
        make.leading.trailing.equalTo(self.contentView);
        make.bottom.equalTo(self.contentView).offset(-MAX(kSCSafeAreaBottomHeight, 20));
    }];
    
    kWeakSelf(self);
    [SCAPIServiceManager requestCoinsWithInvitationId:self.inviteId success:^(NSArray<NSDictionary *> * _Nonnull coinsDicts) {
        weakself.coins = coinsDicts;
        [weakself.coinsCollectionView reloadData];
    } failure:nil];
}

#pragma mark - Action

- (void)onClose {
    [[SCPopupManager shared] dismissPopup:self.navigationController];
}

- (void)onServiceClick {
    SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
    [[UIViewController currentViewController].navigationController pushViewController:vc animated:YES];
}

+(void) showFromVC:(UIViewController *) fromVC inviteId:(NSString *)inviteId{
    SCInviteRechargeCoinsPopupViewController *vc = [[SCInviteRechargeCoinsPopupViewController alloc] init];
    vc.inviteId = inviteId;
    SCBaseNavigationController *nav = [[SCBaseNavigationController alloc]initWithRootViewController:vc];
    [[SCPopupManager shared] showPopup:nav inViewController:fromVC animationStyle:SCPopupAnimationStyleBottom];
}

- (nonnull __kindof UICollectionViewCell *)collectionView:(nonnull UICollectionView *)collectionView cellForItemAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCCoinsCollectionViewCell * cell = [SCCoinsCollectionViewCell initWithFormCollectionView:collectionView forIndexPath:indexPath];
    NSDictionary *coinsDict = self.coins[indexPath.row];
    [cell configureWithDict:coinsDict];

    NSInteger extraCoinPercent = kSCCoinsExtraCoinPercentFromDict(coinsDict);
    cell.discountLabel.text = (extraCoinPercent == 0 ? @"" : [NSString stringWithFormat:@"%ld%%off", extraCoinPercent]);

    NSInteger exchangeCoin = kSCCoinsExchangeCoinFromDict(coinsDict);
    NSInteger extra = (NSInteger)(((double)extraCoinPercent / 100.0) * (double)exchangeCoin);
    cell.coinsLabel.text = extra > 0 ? [NSString stringWithFormat:@"%ld+%ld", exchangeCoin, extra] : [NSString stringWithFormat:@"%ld", exchangeCoin];
    [cell configureTagWithPromotionText:@"" coinsDict:coinsDict];
    return cell;
}

- (NSInteger)collectionView:(nonnull UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.coins.count;
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{

    NSDictionary *coinsDict = self.coins[indexPath.row];
    kWeakSelf(self);
    [self toPayWithCoinsDict:coinsDict callBack:^(BOOL success) {
        if(success){//如果充值成功则关闭页面
            [kSCKeyWindow toast:@"payment successful".translateString];
            [weakself  onClose];
        }
    }];
}
-(void) toPayWithCoinsDict:(NSDictionary * _Nonnull)coinsDict  callBack:(void(^ _Nullable)(BOOL success)) callback{
    kWeakSelf(self);
    void (^payBlock) (NSDictionary * _Nonnull channelDict)  = ^(NSDictionary * _Nonnull channelDict){
        NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelDict forKey:@"channelList" defaultValue:@[]];
        if([channelList count] > 1){
        [weakself.view hiddenLoading];
            [SCThirdPartyPayPopup showWithFromVC:weakself coinsDict:coinsDict source:self.inviteId entry:SCPayEntry.shared.kPayEntrySourceRechargeLink callBack:callback];
        }else{
            NSString *code = kSCCoinsCodeFromDict(coinsDict);
            [kScAuthMar.payService iAPWithProductCode:code source:self.inviteId entry:SCPayEntry.shared.kPayEntrySourceRechargeLink purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
                [weakself.view hiddenLoading];
                if(error != nil){
                    //渠道接口获取失败 那么直接发起支付
                    [weakself.view toast:@"Pay Fail".translateString];
                    callback(NO);
                }else{
                    callback(YES);
                }
            }];
        }
    };
    [self.view showLoading];
    if(SCAuthManager.instance.payService.thirdPartCannelDictObx.value == nil){
        //发起三方支付请求（使用字典版本）
        [SCAuthManager.instance.payService remoteThirdPayCannelWithDictSuccess:^(NSDictionary * _Nonnull channelDict) {
            payBlock(channelDict);
            [weakself sc_blank_empty];
        } failure:^(SCXErrorModel * _Nonnull error) {
            //渠道接口获取失败 那么直接发起支付
            [weakself.view hiddenLoading];
            [weakself.view toast:@"Pay Fail".translateString];
        }];
    }else{
        // 使用字典观察者的值
        payBlock(SCAuthManager.instance.payService.thirdPartCannelDictObx.value);
    }
}

- (void)sc_blank_empty{
    
}
@end
