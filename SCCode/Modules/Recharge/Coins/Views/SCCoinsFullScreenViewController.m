//
//  SCCoinsFullScreenViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/9.
//

#import "SCCoinsFullScreenViewController.h"
#import "SCRobotCustomerServiceViewController.h"
//第三方库
#import <Masonry/Masonry.h>
//View
#import "SCCoinsFullScreenHeaderView.h"
#import "SCCoinsBigCollectionViewCell.h"
#import "SCCoinsListView.h"
#import "SCNavigationBar.h"
//ViewModel
#import "SCCoinsStoreViewModel.h"

#import "SCPayService.h"
#import "SCSocketService.h"


@interface SCCoinsFullScreenViewController ()

@property(nonatomic,nullable,strong) SCCoinsStoreViewModel * viewModel;
@property(nonatomic,nullable,weak) SCCoinsListView * listView;
@property(nonatomic,nullable,weak) UIButton *serviceButton;
@property(nonatomic,nullable,weak) UIStackView *stackView;

@end


@implementation SCCoinsFullScreenViewController

- (void)initData{
    [super initData];
    _viewModel = [[SCCoinsStoreViewModel alloc] init];
    
}


- (void)initUI{
    [super initUI];
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#290000"];
    self.title = @"Diamonds Store".translateString;
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.spacing = 16;
    [self.scContentView addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scContentView).offset(-(kSCSafeAreaTopHeight + kSCStatusBarHeight));
        make.leading.trailing.equalTo(self.scContentView);
        make.bottom.equalTo(self.scContentView).offset(-MAX(kSCSafeAreaBottomHeight, 20));
    }];
    _stackView = stackView;
    
    SCCoinsListView * listView = [[SCCoinsListView alloc] initWithFrame:self.scContentView.bounds style:SCCoinsListViewStyleFullScreen bindViewModel:self.viewModel];
    [self.stackView addArrangedSubview:listView];
    self.listView = listView;
    
    UIButton *serviceBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_robot_customer"]];
    [serviceBtn setTitle:@"Customer Service".translateString forState:UIControlStateNormal];
    [serviceBtn setTitleColor:UIColor.scWhite forState:UIControlStateNormal];
    serviceBtn.titleLabel.font = kScUIFontMedium(12);
    serviceBtn.imageView.contentMode = UIViewContentModeScaleAspectFit;
    [serviceBtn addTarget:self action:@selector(onServiceClick) forControlEvents:UIControlEventTouchUpInside];
    [self.stackView addArrangedSubview:serviceBtn];
    _serviceButton = serviceBtn;
    [self.serviceButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(20);
    }];
    
    [self.viewModel requestData];
    
    kWeakSelf(self)
    // 使用字典版本的金币点击事件
    [self.listView.onClickCoinsDictObx subscribe:^(NSDictionary * _Nonnull coinsDict) {
        __strong SCCoinsFullScreenViewController *strongSelf = weakself;
        [strongSelf toPayWithCoinsDict:coinsDict];
    } error:^(SCXErrorModel * _Nonnull error) {

    } disposeBag:self.disposeBag];
    
    //监听订单遍变化，服务端已更新商店数据
    [kScAuthMar.payService.orderChangeObx afterSubscribe:^(SCOrderResultModel * _Nonnull value) {
        // 刷新商店数据 - 使用字典版本
        [weakself.viewModel requestCoinsDicts];
    } error:nil disposeBag:self.disposeBag];

    //监听socket订单编号，服务端已更新商店数据 - 使用字典处理
    [kSCAuthSocketService.orderEventObs afterSubscribe:^(NSDictionary * _Nonnull orderDict) {
        // 刷新商店数据 - 使用字典版本
        [weakself.viewModel requestCoinsDicts];
    } error:nil disposeBag:self.disposeBag];
}

// 支付金币
- (void)toPayWithCoinsDict:(NSDictionary * _Nonnull)coinsDict{

    [self.viewModel toPayWithCoinsDict:coinsDict entry:SCPayEntry.shared.kPayEntrySourceUserCenter fromVC:self callBack:^(BOOL success) {
        if(success){
            [kSCKeyWindow toast:@"payment successful".translateString];
        }
    }];
}

- (void)onServiceClick {
    SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}

@end

