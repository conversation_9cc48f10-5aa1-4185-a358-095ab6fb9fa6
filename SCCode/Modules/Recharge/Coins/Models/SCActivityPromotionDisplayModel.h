//
//  SCActivityPromotionDisplayModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON>hong on 2024/1/15.
//

#import "SCBaseViewController.h"
// #import "SCActivityPromotionModel.h" // 已移除，使用字典替代
NS_ASSUME_NONNULL_BEGIN

@interface SCActivityPromotionDisplayModel : NSObject
@property(nonatomic,nullable,strong) NSDictionary * promotionDict;
///获取数据的时间（服务器返回数据那一刻记录的时间，用于倒计时）
@property(nonatomic,assign,readonly) NSTimeInterval timeInterval;
///倒计时
@property(nonatomic,assign,readonly) NSInteger countDownNum;
//获取额外赠送的数量
@property(nonatomic,assign,readonly) NSInteger extraCoinNum;
//剩余次数 Number of purchases remaining
@property(nonatomic,assign,readonly) NSInteger remainBuyNum;
//是否显示
@property(nonatomic,assign,readonly) BOOL isShow;

// 字典支持方法
+ (instancetype)displayModelWithPromotionDict:(NSDictionary *)promotionDict;
- (void)configWithPromotionDict:(NSDictionary *)promotionDict;

// 便捷访问方法
- (NSInteger)exchangeCoin;
- (CGFloat)price;
- (CGFloat)originalPrice;
- (NSString *)code;
- (NSString *)invitationId;
- (NSInteger)extraCoin;
- (NSInteger)thirdpartyCoinPercent;
@end

NS_ASSUME_NONNULL_END
