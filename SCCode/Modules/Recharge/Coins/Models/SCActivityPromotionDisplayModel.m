//
//  SCActivityPromotionDisplayModel.m
//  Supercall
//
//  Created by guan<PERSON>hong on 2024/1/15.
//

#import "SCActivityPromotionDisplayModel.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCActivityPromotionDisplayModel ()

@end

@implementation SCActivityPromotionDisplayModel

- (NSInteger)countDownNum{
    NSString *remainMilliseconds = [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionRemainMillisecondsKey defaultValue:@"0"];

    NSInteger downNum = (NSInteger)([remainMilliseconds integerValue]/1000.0 - ([[NSDate new] timeIntervalSince1970] - _timeInterval));
    if(downNum < 0 || downNum >= INT_MAX){
        downNum = 0;
    }
    return downNum;
}

//获取额外赠送的数量
- (NSInteger)extraCoinNum{
    NSInteger thirdpartyCoinPercent = [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionThirdpartyCoinPercentKey defaultValue:0];
    NSInteger exchangeCoin = [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionExchangeCoinKey defaultValue:0];
    NSInteger extraCoin = [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionExtraCoinKey defaultValue:0];

    if(thirdpartyCoinPercent > 0){
        return exchangeCoin * thirdpartyCoinPercent/100.0f;
    }else{
        return extraCoin;
    }
}

- (NSInteger)remainBuyNum{
    NSInteger capableRechargeNum = [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionCapableRechargeNumKey defaultValue:0];
    NSInteger rechargeNum = [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionRechargeNumKey defaultValue:0];

    return capableRechargeNum - rechargeNum;
}

- (BOOL)isShow{
    if(self.countDownNum > 0){
        return true;
        
    }
    
    return  false;
}

#pragma mark - 便捷访问方法

- (NSInteger)exchangeCoin {
    return [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionExchangeCoinKey defaultValue:0];
}

- (CGFloat)price {
    return [SCDictionaryHelper floatFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsPriceKey defaultValue:0.0];
}

- (CGFloat)originalPrice {
    return [SCDictionaryHelper floatFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsOriginalPriceKey defaultValue:0.0];
}

- (NSString *)code {
    return [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsCodeKey defaultValue:@""];
}

- (NSString *)invitationId {
    return [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsInvitationIdKey defaultValue:@""];
}

- (NSInteger)extraCoin {
    return [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionExtraCoinKey defaultValue:0];
}

- (NSInteger)thirdpartyCoinPercent {
    return [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionThirdpartyCoinPercentKey defaultValue:0];
}

#pragma mark - 字典支持方法

+ (instancetype)displayModelWithPromotionDict:(NSDictionary *)promotionDict {
    SCActivityPromotionDisplayModel *model = [[SCActivityPromotionDisplayModel alloc] init];
    [model configWithPromotionDict:promotionDict];
    return model;
}

- (void)configWithPromotionDict:(NSDictionary *)promotionDict {
    self.promotionDict = promotionDict;
    //获取当前时间戳
    _timeInterval = [[NSDate now] timeIntervalSince1970];
}

@end
