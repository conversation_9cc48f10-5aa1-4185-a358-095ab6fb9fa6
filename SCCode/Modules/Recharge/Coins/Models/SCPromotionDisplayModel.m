//
//  SCPromotionDisplayModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCPromotionDisplayModel.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCPromotionDisplayModel

// 已移除setPromotionModel方法，使用configWithPromotionDict替代

- (NSInteger)countDownNum{
    NSString *surplusMillisecond = [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionSurplusMillisecondKey defaultValue:@"0"];

    NSInteger downNum = (NSInteger)([surplusMillisecond integerValue]/1000.0 - ([[NSDate new] timeIntervalSince1970] - _timeInterval));
    if(downNum < 0 || downNum >= INT_MAX){
        downNum = 0;
    }
    return downNum;
}

- (BOOL)isShow {
    // 判断是否已经充值过，前端本地保存避免接口存在时间差
    NSString *code = nil;
    if (self.promotionDict) {
        code = [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCPromotionCodeKey defaultValue:nil];
    }

    if (code == nil) {
        return NO;
    }

    // 是否已经购买过
    NSDictionary *userInfo = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    NSString *userId = [SCDictionaryHelper stringFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    NSString *key = [NSString stringWithFormat:@"%@%@", userId, code];
    if ([[NSUserDefaults standardUserDefaults] boolForKey:key]) {
        // 如果已经购买过就不显示
        return NO;
    }

    // 判断本地金币是否已经超过150也不显示
    NSInteger availableCoins = [SCDictionaryHelper integerFromDictionary:userInfo forKey:SCDictionaryKeys.shared.kSCUserAvailableCoinsKey defaultValue:0];
    if (availableCoins >= 150) {
        // 如果已经购买过就不显示
        return NO;
    }
    
    if (self.countDownNum <= 0) {
        // 如果倒计时结束那么也不显示
        return NO;
    }
    
    return YES;
}

#pragma mark - 便捷访问方法

- (CGFloat)discount {
    return [SCDictionaryHelper floatFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsDiscountKey defaultValue:0.0];
}

- (NSInteger)exchangeCoin {
    return [SCDictionaryHelper integerFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsExchangeCoinKey defaultValue:0];
}

- (CGFloat)price {
    return [SCDictionaryHelper floatFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsPriceKey defaultValue:0.0];
}

- (CGFloat)originalPrice {
    return [SCDictionaryHelper floatFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsOriginalPriceKey defaultValue:0.0];
}

- (NSString *)code {
    return [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsCodeKey defaultValue:@""];
}

- (NSString *)invitationId {
    return [SCDictionaryHelper stringFromDictionary:self.promotionDict forKey:SCDictionaryKeys.shared.kSCCoinsInvitationIdKey defaultValue:@""];
}

#pragma mark - 字典支持方法

+ (instancetype)displayModelWithPromotionDict:(NSDictionary *)promotionDict {
    SCPromotionDisplayModel *model = [[SCPromotionDisplayModel alloc] init];
    [model configWithPromotionDict:promotionDict];
    return model;
}

- (void)configWithPromotionDict:(NSDictionary *)promotionDict {
    self.promotionDict = promotionDict;
    //获取当前时间戳
    _timeInterval = [[NSDate now] timeIntervalSince1970];
}

@end
