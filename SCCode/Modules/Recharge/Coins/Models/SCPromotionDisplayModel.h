//
//  SCPromotionDisplayModel.h
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/11.
//

#import <Foundation/Foundation.h>
// 已移除SCPromotionModel，使用字典替代
NS_ASSUME_NONNULL_BEGIN

@interface SCPromotionDisplayModel : NSObject
// 已移除promotionModel属性，使用promotionDict替代
@property(nonatomic,nullable,strong) NSDictionary * promotionDict;
///获取数据的时间（服务器返回数据那一刻记录的时间，用于倒计时）
@property(nonatomic,assign,readonly) NSTimeInterval timeInterval;
///倒计时
@property(nonatomic,assign,readonly) NSInteger countDownNum;
@property(nonatomic,assign,readonly) BOOL isShow;

// 字典支持方法
+ (instancetype)displayModelWithPromotionDict:(NSDictionary *)promotionDict;
- (void)configWithPromotionDict:(NSDictionary *)promotionDict;

// 便捷访问方法
- (CGFloat)discount;
- (NSInteger)exchangeCoin;
- (CGFloat)price;
- (CGFloat)originalPrice;
- (NSString *)code;
- (NSString *)invitationId;
@end

NS_ASSUME_NONNULL_END
