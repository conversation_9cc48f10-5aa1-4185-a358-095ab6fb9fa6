//
//  SCCoinsStoreViewModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import <UIKit/UIKit.h>
#import "SCObservable.h"

@class SCPromotionDisplayModel;

NS_ASSUME_NONNULL_BEGIN

@interface SCCoinsStoreViewModel : NSObject

@property(nonatomic, strong,readonly) SCObservable<NSArray<NSDictionary *> *> *bannerObs; // 字典版本Banner数据
@property(nonatomic, strong,readonly) SCObservable<SCPromotionDisplayModel*> *promoteObs;
@property(nonatomic, strong,readonly) SCObservable<NSNumber *> *availableCoins;

// 金币数据
@property(nonatomic, strong,readonly) SCObservable<NSArray<NSDictionary *> *> *coinsDictsObs;

-(void) requestData;

// 请求金币数据
- (void) requestCoinsDicts;
- (void) toPayWithCoinsDict:(NSDictionary *)coinsDict entry:(NSString *)entry fromVC:(UIViewController *)fromVC callBack:(void (^)(BOOL success))callback;
@end

NS_ASSUME_NONNULL_END
