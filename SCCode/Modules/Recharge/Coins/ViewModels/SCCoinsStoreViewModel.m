//
//  SCCoinsStoreViewModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/11.
//

#import "SCCoinsStoreViewModel.h"
#import "SCCategoryAPIManagerCoins.h"
#import "SCBannerService.h"
#import "SCCoinsService.h"
#import "SCPayService.h"
//#import "SCThirdPayCannelGroupModel.h"
#import "SCThirdPartyPayPopup.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCCoinsStoreViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        
        _bannerObs = [[SCObservable alloc] initWithValue:kScAuthMar.bannerService.bannersObs.value];
        _promoteObs = kScAuthMar.coinsService.promotionObx;
        _availableCoins = kScAuthMar.availableCoinsObx;

        // 金币数据初始化
        _coinsDictsObs = [[SCObservable alloc] initWithValue:kScAuthMar.coinsService.coinsListModel];
    }
    return self;
}

-(void) requestData{
    //请求可以余额
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];

    kWeakSelf(self)
    [kScAuthMar.bannerService remoteBannerWithSuccess:^(NSArray<NSDictionary *> * _Nonnull banners) {
        [weakself.bannerObs setValue:banners];
    } failure:nil];
    // 使用字典版本请求金币数据
    [self requestCoinsDicts];
}
// 请求金币数据
- (void) requestCoinsDicts{
    kWeakSelf(self)
    [kScAuthMar.coinsService remoteCoinsListWithSuccess:^(NSArray<NSDictionary *> * _Nonnull coinsDicts) {
        if (coinsDicts.count > 0) {
            [weakself.coinsDictsObs setValue:coinsDicts];
        }
    } failure:nil];

    if(kScAuthMar.coinsService.promotionObx.value == nil){
        [kScAuthMar.coinsService remotePromotionWithSuccess:^(SCPromotionDisplayModel * _Nonnull model) {
            weakself.promoteObs.value = model;
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself.promoteObs setError:error];
        }];
    }
    [self.availableCoins postValue:kScAuthMar.availableCoinsObx.value];
    ///获取三方支付渠道
    [kScAuthMar.payService remoteThirdPayCannelWithDictSuccess:nil failure:nil];
}


// 支付金币
- (void) toPayWithCoinsDict:(NSDictionary *)coinsDict entry:(NSString *)entry fromVC:(UIViewController *)fromVC callBack:(void (^)(BOOL success))callback{

    void (^payBlock) (NSDictionary * _Nonnull channelDict)  = ^(NSDictionary * _Nonnull channelDict){
        NSArray *channelList = [SCDictionaryHelper arrayFromDictionary:channelDict forKey:@"channelList" defaultValue:@[]];
        if([channelList count] > 1){
        [fromVC.view hiddenLoading];
            // 使用字典版本的支付弹窗
            [SCThirdPartyPayPopup showWithFromVC:fromVC coinsDict:coinsDict source:@"" entry:entry callBack:^(BOOL success) {
                if (success) {
                    // 补充刷新商店
                    [self requestCoinsDicts];
                }
                callback(success);
            }];
        }else{
            kWeakSelf(self)
            NSString *code = kSCCoinsCodeFromDict(coinsDict);
            [kScAuthMar.payService iAPWithProductCode:code source:@"" entry:entry purchaseBlock:^(NSDictionary * _Nonnull orderDict, SCXErrorModel * _Nonnull error) {
                [fromVC.view hiddenLoading];
                [weakself sc_blank_empty];
                if(error != nil){
                    //渠道接口获取失败 那么直接发起支付
                    [fromVC.view toast:@"Pay Fail".translateString];
                    callback(NO);
                }else{
                    [self requestCoinsDicts];
                    callback(YES);
                }
            }];
        }
    };
    [fromVC.view showLoading];
    kWeakSelf(self)
    if(kScAuthMar.payService.thirdPartCannelDictObx.value == nil){
        //发起三方支付请求（使用字典版本）
        [kScAuthMar.payService remoteThirdPayCannelWithDictSuccess:^(NSDictionary * _Nonnull channelDict) {
            payBlock(channelDict);
            [weakself sc_blank_empty];
        } failure:^(SCXErrorModel * _Nonnull error) {
            //渠道接口获取失败 那么直接发起支付
            [weakself sc_blank_empty];
            [fromVC.view hiddenLoading];
            [fromVC.view toast:@"Pay Fail".translateString];
        }];
    }else{
        // 使用字典观察者的值
        payBlock(kScAuthMar.payService.thirdPartCannelDictObx.value);
    }
}

- (void)sc_blank_empty{

}

@end
