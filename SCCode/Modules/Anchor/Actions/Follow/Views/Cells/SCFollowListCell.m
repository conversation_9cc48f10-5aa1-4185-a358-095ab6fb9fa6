//
//  SCFollowListCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCFollowListCell.h"
#import "SCIMUIConfig.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCCountryService.h"

@interface SCFollowListCell()

//头像
@property(nonatomic,weak) UIImageView *avatarImageView;
//昵称
@property(nonatomic,weak) UILabel *nickNameLabel;
//content
@property(nonatomic,weak) UILabel *contentLabel;

@end

@implementation SCFollowListCell

- (void)initUI{
    [super initUI];
    
    self.contentView.backgroundColor = [UIColor scGlobalBgColor];
    
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.layer.cornerRadius = kSCConversationAvatarWH/2.0f;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;
    
    UILabel *nickNameLabel = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontSemibold(14)];
    nickNameLabel.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
    [self.contentView addSubview:nickNameLabel];
    self.nickNameLabel = nickNameLabel;
    
    UILabel *contentLabel = [UILabel labelWithText:@"" textColor:UIColor.scWhite font:kScUIFontRegular(12) alignment:NSTextAlignmentLeft];
    [self.contentView addSubview:contentLabel];
    self.contentLabel = contentLabel;
    
    
    UIView *onlineStatusView = [[UIView alloc] init];
    onlineStatusView.layer.cornerRadius = 6.0f;
    onlineStatusView.layer.masksToBounds = YES;
    [self.contentView addSubview:onlineStatusView];
    self.onlineStatusView = onlineStatusView;
    
    //底部线条
    UIView * lineV = [UIView new].setBackgroundColor([UIColor.scWhite colorWithAlphaComponent:0.1]);
    [self.contentView addSubview:lineV];
    
    [avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16.0f);
        make.centerY.equalTo(self.contentView);
        make.width.height.mas_equalTo(kSCConversationAvatarWH);
    }];
    
    [nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(avatarImageView.mas_trailing).offset(6);
        make.trailing.equalTo(self.contentView).offset(-6);
        make.centerY.equalTo(avatarImageView).offset(-10);
        make.height.mas_equalTo(21);
    }];
    
    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(nickNameLabel);
        make.top.equalTo(nickNameLabel.mas_bottom).offset(0);
        make.height.mas_equalTo(19);
    }];
    
    [self.onlineStatusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(12.0f, 12.0f));
        make.bottom.equalTo(self.avatarImageView);
        make.trailing.equalTo(self.avatarImageView).inset(4.0f);
    }];
    
    [lineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.contentView);
        make.leading.equalTo(self.contentView).offset(74.0f);
        make.trailing.equalTo(self.contentView).offset(-15.0f);
        make.height.mas_equalTo(1.0f);
    }];
}

- (void)configWithAvatarUrl:(NSString *)avatarUrl nickName:(NSString *)nickName age:(NSInteger)age countryCode:(NSString *)countryCode onLineStatus:(SCAnchorStatus)onLineStatus{
    [self.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
    self.nickNameLabel.text = nickName;
    self.contentLabel.text = [NSString stringWithFormat:@"%@ %ld",countryCode, age];
    if(onLineStatus != AnchorStatusUnknown){
        self.onlineStatusView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:onLineStatus];
        self.onlineStatusView.hidden = NO;
    }else{
        self.onlineStatusView.hidden = YES;
    }
}

-(void)configWithUserDict:(NSDictionary *)userDict {
    // 使用字典数据配置UI
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
    NSString *statusStr = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];

    // 格式化国家信息
    NSString *countryCode = [NSString stringWithFormat:@"%@ %@", [SCCountryService emojiForCountry:country], country];

    // 解析在线状态
    SCAnchorStatus onLineStatus = [SCDictionaryHelper anchorStatusFromString:statusStr];

    // 调用原有的配置方法
    [self configWithAvatarUrl:avatarUrl nickName:nickname age:age countryCode:countryCode onLineStatus:onLineStatus];
}

@end
