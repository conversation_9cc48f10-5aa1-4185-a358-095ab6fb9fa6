//
//  SCFollowListViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCFollowListViewController.h"
#import "SCFollowListCell.h"
#import "MJRefresh.h"
#import "SCAnchorInfoViewController.h"
#import "SCActionService.h"
#import "SCUserBoolChangeModel.h"
#import "SCOnlineStatesService.h"
#import "UIScrollView+EmptyDataSet.h"
#import "SCCountryService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCFollowListViewController ()<DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>{
    int _page;
    int _pageSize;
    
}
//TableView
@property(nonatomic,weak) UITableView *tableView;
//关注好友列表（字典版本）
@property(nonatomic,strong) NSMutableArray<NSDictionary *> * followDictList;
///当前VC是否正在显示
@property(nonatomic,assign) BOOL isActivity;
///等待删除的用户ID , 用于页面转跳 取消关注过后回来需要移除显示
@property(nonatomic,strong) NSMutableSet<NSString *> *waitRemoveUserId;

@property(nonatomic,strong) SCOnlineStatusSubscribe * onlineStatusSub;

@end

@implementation SCFollowListViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    if([self.followDictList count] == 0){
        [self.tableView.mj_header beginRefreshing];
    }
    self.isActivity = YES;
    [self removeUnFollowUser];
}
- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.isActivity = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    //隐藏自定义的导航栏
    [self setIsHiddenSCNavigationBar:YES];
}
- (void)initData{
    [super initData];
    _followDictList = @[].mutableCopy;
    _page = 1;
    _pageSize = 20;
    _waitRemoveUserId = [[NSMutableSet alloc] init];
}

- (void)initUI{
    [super initUI];
    UITableView *tableView = [UITableView tableViewWithFrame:self.view.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    tableView.backgroundColor = [UIColor scGlobalBgColor];
    [self.view addSubview:tableView];
    self.tableView = tableView;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 创建下拉刷新控件
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(onHeaderRefresh)];
    header.stateLabel.textColor = UIColor.scWhite;
    [header setTitle:@"Pull down to refresh".translateString forState:MJRefreshStateIdle];
    [header setTitle:@"Release to refresh".translateString forState:MJRefreshStatePulling];
    [header setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    header.lastUpdatedTimeLabel.hidden = YES;
    self.tableView.mj_header = header;
    
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(onLoadMore)];
    footer.stateLabel.textColor = UIColor.scWhite;
    [footer setTitle:@"Pull up to load more".translateString forState:MJRefreshStateIdle];
    [footer setTitle:@"Release and load more".translateString forState:MJRefreshStatePulling];
    [footer setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    [footer setTitle:@"".translateString forState:MJRefreshStateNoMoreData];
    footer.triggerAutomaticallyRefreshPercent = -20;
    self.tableView.mj_footer = footer;
    
    self.tableView.emptyDataSetSource = self;
    self.tableView.emptyDataSetDelegate = self;
    
    kWeakSelf(self)
    [kSCAuthActionService.followChageObx afterSubscribe:^(SCUserBoolChangeModel * _Nonnull value) {
        
        if(value.value){
            //关注
            if([weakself.waitRemoveUserId containsObject:value.userId]){
                [weakself.waitRemoveUserId removeObject:value.userId];
            }
        }else{
            //取消关注
            [weakself.waitRemoveUserId addObject:value.userId];
        }
        [weakself removeUnFollowUser];
    } error:^(SCXErrorModel * _Nonnull error) {
        
    } disposeBag:self.disposeBag];
    
    
    //开启在线状态监听的初始化
    _onlineStatusSub = [[SCOnlineStatusSubscribe alloc] init];
    //默认关闭
    [_onlineStatusSub pause];
    [_onlineStatusSub.changeObs subscribe:^(NSArray<NSString *> * _Nullable value) {
        [weakself onChangeOnlineStatusWithIds:value];
    } error:nil disposeBag:self.disposeBag];
    [kSCAuthOnlineStatesService add:self.onlineStatusSub dispose:self.disposeBag];
}

-(void) onChangeOnlineStatusWithIds:(NSArray<NSString *> *) ids{
    for (UITableViewCell *cell in self.tableView.visibleCells) {
        if ([cell isKindOfClass:[SCFollowListCell class]]) {
            SCFollowListCell *followCell = (SCFollowListCell *)cell;
            NSIndexPath *indexPath = [self.tableView indexPathForCell:cell];
            if (indexPath.row < self.followDictList.count) {
                NSDictionary *userDict = [self.followDictList objectAtIndex:indexPath.row];
                NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
                NSDictionary *statusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:userID];
                if (statusDict) {
                    SCAnchorStatus newStatus = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:statusDict forKey:@"status" defaultValue:AnchorStatusOffline];
                    if (newStatus != AnchorStatusUnknown) {
                        followCell.onlineStatusView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:newStatus];
                        followCell.onlineStatusView.hidden = NO;
                    } else {
                        followCell.onlineStatusView.hidden = YES;
                    }
                } else {
                    followCell.onlineStatusView.hidden = YES;
                }                
            }
        }
    }
}

-(void) removeUnFollowUser{
    if(self.isActivity && [self.waitRemoveUserId count] > 0){
        for (NSDictionary *userDict in [self.followDictList copy]) {
            NSString *userId = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
            if([self.waitRemoveUserId containsObject:userId]){
                [self.followDictList removeObject:userDict];
                [self.waitRemoveUserId removeObject:userId];
                if(self.waitRemoveUserId.count == 0){
                    break;
                }
            }
        }
        [self.tableView reloadData];
        //刷新缓存
        [kSCAuthActionService requestFollowListWithPage:1 pageSize:_pageSize success:nil failure:nil];
    }
}

#pragma mark - JXCategoryListContentViewDelegate
- (UIView *)listView {
    return self.view;
}
- (void)listWillAppear{
    if(self.onlineStatusSub){
        [self onChangeOnlineStatusWithIds:nil];
        [self.onlineStatusSub start];
        //进入页面立刻刷新一次
        [kSCAuthOnlineStatesService refresh];
    }
}

- (void)listWillDisappear{
    if(self.onlineStatusSub){
        //标记暂停
        [self.onlineStatusSub pause];
    }
}
#pragma mark - UITableViewDelegate UITableViewDataSource

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSDictionary * userDict = self.followDictList[indexPath.row];
    kWeakSelf(self);
    UIContextualAction *action = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"UnFollow".translateString handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        kStrongSelf;
        [strongSelf.followDictList removeObjectAtIndex:indexPath.row];
        // 更新UITableView的显示
        [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        completionHandler(true);
        //刷新接口
        [weakself requestUnFollowWithUserDict:userDict];
    }];
    action.backgroundColor = kSCColorWithHexStr(@"#666666");
    UISwipeActionsConfiguration *config = [UISwipeActionsConfiguration configurationWithActions:@[action]];
    config.performsFirstActionWithFullSwipe = false;
    return config;
}

- (void)requestUnFollowWithUserDict:(NSDictionary *)userDict {
    NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    [kSCAuthActionService requestUnFollowWithUserId:userID success:^{
        // 字典版本不需要修改isFriend属性，因为数据会重新加载
    } failure:nil];
}

- (void)sc_blank_empty{
    
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCFollowListCell *cell = [SCFollowListCell initWithFormTableView:tableView];
    
    // 使用字典数据源
    if (indexPath.row < self.followDictList.count) {
        NSDictionary *userDict = self.followDictList[indexPath.row];
        [cell configWithUserDict:userDict];
        
        NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        [self.onlineStatusSub addWithUserID:userID];
        return cell;
    }
    
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    // 使用字典数据源
    return [self.followDictList count];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 83.0f;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    // 优先使用字典数据源
    if (self.followDictList.count > 0 && indexPath.row < self.followDictList.count) {
        NSDictionary *userDict = self.followDictList[indexPath.row];
        NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        if (userID.length == 0) {
            return;
        }
        [SCAnchorInfoViewController openWithUserDict:userDict from:self];
        // 移除Model版本的回退逻辑，只使用字典版本
    }
}

#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [SCResourceManager loadImageWithName:@"ic_list_empty"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"No Data".translateString;
    NSDictionary *attributes = @{NSFontAttributeName: kScUIFontMedium(24),
                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B7010D"]};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView
{
    return YES;
}

#pragma mark - 网络请求
-(void)onHeaderRefresh{
    _page = 1;
    [self requestFolloeList];
}
-(void)onLoadMore{
    _page ++;
    [self requestFolloeList];
}

-(void) requestFolloeList{
    // 直接使用字典版本的API
    __block int page = _page;
    kWeakSelf(self)
    [kSCAuthActionService requestFollowListWithPage:_page pageSize:_pageSize success:^(NSArray<NSDictionary *> * _Nonnull dictList) {
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        if(page == 1){
            //刷新
            [weakself.followDictList removeAllObjects];
            [weakself.followDictList addObjectsFromArray:dictList];
            [weakself.tableView.mj_header endRefreshing];
        }else{
            //加载更多
            [weakself.followDictList addObjectsFromArray:dictList];
        }
        if([dictList count] >= strongSelf -> _pageSize){
            [weakself.tableView.mj_footer resetNoMoreData];
        }else{
            [weakself.tableView.mj_footer endRefreshingWithNoMoreData];
        }
        [weakself.tableView reloadData];
    } failure:^(SCXErrorModel * _Nonnull error) {
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        if(page == 1){
            [weakself.tableView.mj_header endRefreshing];
        }else{
            [weakself.tableView.mj_footer endRefreshing];
        }
        //[SCToast showToast:error.message];
    }];
}


@end
