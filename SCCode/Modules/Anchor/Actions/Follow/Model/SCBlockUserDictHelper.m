//
//  SCBlockUserDictHelper.m
//  Supercall
//
//  Created by AI Assistant on 2025/7/29.
//  拉黑用户字典访问工具类实现
//

#import "SCBlockUserDictHelper.h"
#import "SCDictionaryKeys.h"
#import "SCDictionaryHelper.h"

@implementation SCBlockUserDictHelper

#pragma mark - 字典访问方法

+ (NSString *)nicknameFromDict:(NSDictionary *)blockUserDict {
    return [SCDictionaryHelper stringFromDictionary:blockUserDict 
                                             forKey:@"nickName"
                                       defaultValue:@""];
}

+ (NSString *)avatarFromDict:(NSDictionary *)blockUserDict {
    return [SCDictionaryHelper stringFromDictionary:blockUserDict 
                                             forKey:@"avatar"
                                       defaultValue:@""];
}

+ (NSInteger)ageFromDict:(NSDictionary *)blockUserDict {
    return [SCDictionaryHelper integerFromDictionary:blockUserDict 
                                              forKey:SCDictionaryKeys.shared.kSCUserAgeKey 
                                        defaultValue:0];
}

+ (NSString *)registerCountryFromDict:(NSDictionary *)blockUserDict {
    return [SCDictionaryHelper stringFromDictionary:blockUserDict 
                                             forKey:SCDictionaryKeys.shared.kSCUserRegisterCountryKey 
                                       defaultValue:@""];
}

+ (NSString *)broadcasterIdFromDict:(NSDictionary *)blockUserDict {
    return [SCDictionaryHelper stringFromDictionary:blockUserDict 
                                             forKey:@"broadcasterId"
                                       defaultValue:@""];
}

@end
