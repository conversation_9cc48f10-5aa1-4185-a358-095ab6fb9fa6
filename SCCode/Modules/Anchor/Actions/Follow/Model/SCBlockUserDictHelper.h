//
//  SCBlockUserDictHelper.h
//  Supercall
//
//  Created by AI Assistant on 2025/7/29.
//  拉黑用户字典访问工具类
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/// 拉黑用户字典访问工具类
/// 提供安全的字典访问方法，用于访问拉黑用户相关数据
/// 设计模式参考SCUserRankModel的实现
@interface SCBlockUserDictHelper : NSObject

#pragma mark - 字典访问方法

/// 从字典获取用户昵称
/// @param blockUserDict 拉黑用户字典
/// @return 用户昵称，默认返回空字符串
+ (NSString *)nicknameFromDict:(NSDictionary *)blockUserDict;

/// 从字典获取用户头像URL
/// @param blockUserDict 拉黑用户字典
/// @return 头像URL，默认返回空字符串
+ (NSString *)avatarFromDict:(NSDictionary *)blockUserDict;

/// 从字典获取用户年龄
/// @param blockUserDict 拉黑用户字典
/// @return 用户年龄，默认返回0
+ (NSInteger)ageFromDict:(NSDictionary *)blockUserDict;

/// 从字典获取用户注册国家
/// @param blockUserDict 拉黑用户字典
/// @return 注册国家代码，默认返回空字符串
+ (NSString *)registerCountryFromDict:(NSDictionary *)blockUserDict;

/// 从字典获取主播ID
/// @param blockUserDict 拉黑用户字典
/// @return 主播ID，默认返回空字符串
+ (NSString *)broadcasterIdFromDict:(NSDictionary *)blockUserDict;

@end

NS_ASSUME_NONNULL_END
