//
//  SCActionService.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCActionService.h"
#import "SCUserBoolChangeModel.h"
#import "SCAPIServiceManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCActionService

- (instancetype)initWithUserId:(NSString *)userId{
    self = [super initWithUserId:userId];
    if (self) {
        _followChageObx = [[SCObservable<SCUserBoolChangeModel *> alloc] initWithValue:nil];
        _blockChageObx = [[SCObservable<SCUserBoolChangeModel *> alloc] initWithValue:nil];
    }
    return self;
}
///取消关注
- (void)requestUnFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestUnFollowWithUserId:followUserId success:^{
        kStrongSelf;
        kSCBlockExeNotNil(success);
        strongSelf.followChageObx.value = [[SCUserBoolChangeModel alloc] initWithUserId:followUserId value:NO];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}

- (void)sc_blank_empty{
    
}
///关注
-(void)requestFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestFollowWithUserId:followUserId success:^{
        kStrongSelf;
        kSCBlockExeNotNil(success);
        strongSelf.followChageObx.value = [[SCUserBoolChangeModel alloc] initWithUserId:followUserId value:YES];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}

///取消拉黑
-(void) requestUnBlockWithUserId:(NSString * _Nonnull) blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestUnBlockWithUserId:blockUserId success:^{
        kStrongSelf;
        kSCBlockExeNotNil(success);
        strongSelf.blockChageObx.value = [[SCUserBoolChangeModel alloc] initWithUserId:blockUserId value:NO];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}
///拉黑
-(void) requestBlockWithUserId:(NSString * _Nonnull)blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    kWeakSelf(self);
    [SCAPIServiceManager requestBlockAndReportWithUserId:blockUserId complainCategory:@"Block" complainSub:nil success:^{
        kStrongSelf;
        kSCBlockExeNotNil(success);
        strongSelf.blockChageObx.value = [[SCUserBoolChangeModel alloc] initWithUserId:blockUserId value:YES];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
   
}

///举报
-(void) requestReportWithUserId:(NSString * _Nonnull)blockUserId complainSub:(NSString *)complainSub success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure{
    kWeakSelf(self)
    [SCAPIServiceManager requestBlockAndReportWithUserId:blockUserId complainCategory:@"Report" complainSub:complainSub success:^{
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(success);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
   
}

///关注列表（字典版本）
-(void) requestFollowListWithPage:(int) page pageSize:(int) pageSize success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    //缓存添加逻辑只有在第一页才缓存， page是从1开始
    kWeakSelf(self)
    [SCAPIServiceManager requestFollowListWithPage:page pageSize:pageSize cachePolicy:page == 1 ? SCNetCachePolicyCacheAndRefreshCallback:SCNetCachePolicyNotCache success:^(NSArray<NSDictionary *> * _Nonnull userDicts) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(success,userDicts);
    } failure:failure];
}

///拉黑列表
- (void) requestBlockListWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    [SCAPIServiceManager requestBlockListWithSuccess:success failure:failure];
}


@end
