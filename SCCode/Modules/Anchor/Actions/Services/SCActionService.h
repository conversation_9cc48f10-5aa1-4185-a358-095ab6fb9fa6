//
//  SCActionService.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCBaseAppService.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCActionService : SCBaseAppService
///关注状态发送变化
@property(nonatomic,strong,nonnull)SCObservable<SCUserBoolChangeModel *>* followChageObx;
///拉黑状态发送变化
@property(nonatomic,strong,nonnull)SCObservable<SCUserBoolChangeModel *>* blockChageObx;

///取消关注
- (void)requestUnFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///关注
-(void)requestFollowWithUserId:(NSString * _Nonnull) followUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///取消拉黑
-(void) requestUnBlockWithUserId:(NSString * _Nonnull) blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///拉黑
-(void) requestBlockWithUserId:(NSString * _Nonnull)blockUserId success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///举报
-(void) requestReportWithUserId:(NSString * _Nonnull)blockUserId complainSub:(NSString *)complainSub success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull error))failure;
///关注列表
// 关注列表API（字典版本）
-(void) requestFollowListWithPage:(int) page pageSize:(int) pageSize success:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
///拉黑列表
- (void) requestBlockListWithSuccess:(void (^_Nullable)(NSArray<NSDictionary *> *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
