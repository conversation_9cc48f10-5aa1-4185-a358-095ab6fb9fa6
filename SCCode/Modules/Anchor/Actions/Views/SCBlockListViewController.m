//
//  SCBlockListViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCBlockListViewController.h"
#import "SCFollowListCell.h"
#import "MJRefresh.h"
#import "SCAnchorInfoViewController.h"
#import "SCActionService.h"
#import "SCUserBoolChangeModel.h"
#import "SCBlockUserDictHelper.h"
#import "SCOnlineStatesService.h"
#import "UIScrollView+EmptyDataSet.h"
#import "SCNavigationBar.h"
#import "SCCountryService.h"
#import <RongIMLib/RongIMLib.h>
@interface SCBlockListViewController ()<UITableViewDelegate,UITableViewDataSource, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate>
//TableView
@property(nonatomic,weak) UITableView *tableView;
//拉黑用户列表
@property(nonatomic,strong) NSMutableArray<NSDictionary *> * blockList;
///当前VC是否正在显示
@property(nonatomic,assign) BOOL isActivity;
///等待删除的用户ID , 用于页面转跳 取消关注过后回来需要移除显示
@property(nonatomic,strong) NSMutableSet<NSString *> *waitRemoveUserId;

@end

@implementation SCBlockListViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    
    if([self.blockList count] == 0){
        [self.tableView.mj_header beginRefreshing];
    }
    self.isActivity = YES;
    [self removeUnFollowUser];
}
- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.isActivity = NO;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"Blocked List".translateString;
}

- (void)initData{
    [super initData];
    _blockList = @[].mutableCopy;
    _waitRemoveUserId = [[NSMutableSet alloc] init];
}

- (void)initUI{
    [super initUI];
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];

    UITableView *tableView = [UITableView tableViewWithFrame:self.view.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    tableView.backgroundColor = [UIColor scGlobalBgColor];
    [self.scContentView addSubview:tableView];
    self.tableView = tableView;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
    
    // 创建下拉刷新控件
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(onHeaderRefresh)];
    header.stateLabel.textColor = UIColor.scWhite;
    [header setTitle:@"Pull down to refresh".translateString forState:MJRefreshStateIdle];
    [header setTitle:@"Release to refresh".translateString forState:MJRefreshStatePulling];
    [header setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    header.lastUpdatedTimeLabel.hidden = YES;
    self.tableView.mj_header = header;

    self.tableView.emptyDataSetSource = self;
    self.tableView.emptyDataSetDelegate = self;
    
    kWeakSelf(self)
    [kSCAuthActionService.blockChageObx afterSubscribe:^(SCUserBoolChangeModel * _Nonnull value) {
        
        if(value.value){
            //拉黑
            if([weakself.waitRemoveUserId containsObject:value.userId]){
                [weakself.waitRemoveUserId removeObject:value.userId];
            }
        }else{
            //取消拉黑
            [weakself.waitRemoveUserId addObject:value.userId];
        }
        [weakself removeUnFollowUser];
    } error:nil disposeBag:self.disposeBag];
}
-(void) removeUnFollowUser{
    if(self.isActivity && [self.waitRemoveUserId count] > 0){
        for (NSDictionary *item in [self.blockList copy]) {
            NSString *userId = [SCBlockUserDictHelper broadcasterIdFromDict:item];
            if([self.waitRemoveUserId containsObject:userId]){
                [self.blockList removeObject:item];
                [self.waitRemoveUserId removeObject:userId];
                if(self.waitRemoveUserId.count == 0){
                    break;
                }
            }
        }
        [self.tableView reloadData];
        //刷新缓存
        [kSCAuthActionService requestBlockListWithSuccess:nil failure:nil];
    }
}


#pragma mark - UITableViewDelegate UITableViewDataSource

- (UISwipeActionsConfiguration *)tableView:(UITableView *)tableView trailingSwipeActionsConfigurationForRowAtIndexPath:(NSIndexPath *)indexPath{
    NSDictionary * userDict = self.blockList[indexPath.row];
    kWeakSelf(self);
    UIContextualAction *action = [UIContextualAction contextualActionWithStyle:UIContextualActionStyleDestructive title:@"UnBlock".translateString handler:^(UIContextualAction * _Nonnull action, __kindof UIView * _Nonnull sourceView, void (^ _Nonnull completionHandler)(BOOL)) {
        kStrongSelf;
        [strongSelf.blockList removeObjectAtIndex:indexPath.row];
        // 更新UITableView的显示
        [tableView deleteRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationAutomatic];
        completionHandler(true);
        //刷新接口
        [weakself requestUnBlockWithUserId:[SCBlockUserDictHelper broadcasterIdFromDict:userDict]];
    }];
    action.backgroundColor = kSCColorWithHexStr(@"#666666");
    UISwipeActionsConfiguration *config = [UISwipeActionsConfiguration configurationWithActions:@[action]];
    config.performsFirstActionWithFullSwipe = false;
    return config;
}

- (void)requestUnBlockWithUserId:(NSString * _Nonnull) blockUserId {
    [kSCAuthActionService requestUnBlockWithUserId:blockUserId success:^{
        [[RCCoreClient sharedCoreClient] removeFromBlacklist:blockUserId success:nil error:nil];
    }  failure:nil];
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCFollowListCell *cell = [SCFollowListCell initWithFormTableView:tableView];
    NSDictionary * userDict = self.blockList[indexPath.row];
    [cell configWithAvatarUrl:[SCBlockUserDictHelper avatarFromDict:userDict]
                     nickName:[SCBlockUserDictHelper nicknameFromDict:userDict]
                          age:[SCBlockUserDictHelper ageFromDict:userDict]
                  countryCode:[SCBlockUserDictHelper registerCountryFromDict:userDict]
                 onLineStatus:AnchorStatusUnknown];
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.blockList count];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 93.0f;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    NSDictionary * userDict = self.blockList[indexPath.row];
    [SCAnchorInfoViewController openWith:[SCBlockUserDictHelper broadcasterIdFromDict:userDict] from:self];
}

#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [SCResourceManager loadImageWithName:@"ic_list_empty"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"No Data".translateString;
    NSDictionary *attributes = @{NSFontAttributeName: kScUIFontMedium(24),
                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B7010D"]};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

- (CGFloat)verticalOffsetForEmptyDataSet:(UIScrollView *)scrollView
{
    return -100;
}

#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView
{
    return YES;
}


#pragma mark - 网络请求
-(void)onHeaderRefresh{
    [self requestFolloeList];
}
-(void) requestFolloeList{
    kWeakSelf(self)
    [kSCAuthActionService requestBlockListWithSuccess:^(NSArray<NSDictionary *> * _Nonnull dictList) {
        kStrongSelf;
        if(strongSelf == nil){
            return;
        }
        [strongSelf.blockList removeAllObjects];
        [strongSelf.blockList addObjectsFromArray:dictList];

        [weakself.tableView.mj_header endRefreshing];
        [weakself.tableView reloadData];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.tableView.mj_header endRefreshing];
    }];


}

@end
