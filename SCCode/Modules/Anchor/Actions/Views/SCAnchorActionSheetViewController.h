//
//  SCAnchorMoreActionSheetViewController.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCActionSheetViewController.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCAnchorActionSheetViewController : SCActionSheetViewController

// 已废弃，使用字典版本
// +(void) showMoreActionSheetWithFromVC:(UIViewController *)fromVC userInfo:(SCUserInfoModel *)userInfo actionBlock:(void (^_Nullable)(NSInteger))actionBlock;
+(void) showMoreActionSheetWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actionBlock:(void (^_Nullable)(NSInteger))actionBlock;

// 已废弃，使用字典版本
// +(SCAnchorActionSheetViewController *_Nonnull)showWithFromVC:(UIViewController *)fromVC userInfo:(SCUserInfoModel *)userInfo actions:(NSArray<UserMorePopoupAction *> * _Nullable)actions cancel:(UserMorePopoupAction * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock;
+(SCAnchorActionSheetViewController *_Nonnull)showWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actions:(NSArray<UserMorePopoupAction *> * _Nullable)actions cancel:(UserMorePopoupAction * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock;

@end

NS_ASSUME_NONNULL_END
