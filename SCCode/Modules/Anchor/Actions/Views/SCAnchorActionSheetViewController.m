//
//  SCAnchorMoreActionSheetViewController.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/23.
//

#import "SCAnchorActionSheetViewController.h"
// #import "SCUserInfoModel.h" // 已迁移到字典
#import <RongIMLib/RongIMLib.h>
#import "SCActionService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorActionSheetViewController ()
// @property(nonatomic,strong) SCUserInfoModel *userInfo; // 已废弃，使用字典版本
@property(nonatomic,strong) NSMutableDictionary *userDict;
@end

@implementation SCAnchorActionSheetViewController

- (void)viewDidLoad {
    [super viewDidLoad];
}

// 已废弃的 Model 版本方法
// - (void)setUserInfo:(SCUserInfoModel *)userInfo{
//     _userInfo = userInfo;
// }

+ (void)requestReportWith:(NSInteger)index userId:(NSString * _Nonnull)blockUserId complainSub:(NSString *)complainSub actionBlock:(void (^)(NSInteger))actionBlock reportSuccessBlock:(void(^)(void)) reportSuccessBlock{
    [kSCAuthActionService requestReportWithUserId:blockUserId complainSub:complainSub success:^{
        [kSCKeyWindow toast:@"Report Successfully".translateString];
        kSCBlockExeNotNil(reportSuccessBlock);
        kSCBlockExeNotNil(actionBlock, index);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [kSCKeyWindow toast:@"Report Failure".translateString];
    }];
}

///举报
+(void) showReportActionSheetWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actionBlock:(void (^)(NSInteger))actionBlock reportSuccessBlock:(void(^)(void)) reportSuccessBlock{
    
    NSArray<NSString *> * actions = @[@"Pornographic".translateString,@"Fdlse gender".translateString,@"Fraud".translateString,@"Political sensitive".translateString,@"Other".translateString];
    NSArray<NSString *> * keys = @[@"Pornographic",@"Fdlse gender",@"Fraud",@"Political sensitive",@"Other"];
    __block SCAnchorActionSheetViewController * moreVC;
    moreVC = [self showWithFromVC:fromVC userDict:userDict actionStrs:actions cancelStr:@"Cancel".translateString isAuthClose:NO actionBlock:^(NSInteger index) {
        NSString * complainSub = keys[index];
        NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        [SCAnchorActionSheetViewController requestReportWith:index userId:userID complainSub:complainSub actionBlock:actionBlock reportSuccessBlock:reportSuccessBlock];
    } cancelBlock:^{
        [moreVC dismissViewControllerAnimated:YES completion:nil];
    }];
}

#pragma mark - 字典支持方法

+(void) showMoreActionSheetWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actionBlock:(void (^_Nullable)(NSInteger))actionBlock{
    __block SCAnchorActionSheetViewController * moreVC;

    // 从字典中获取用户信息
    BOOL isFriend = [SCDictionaryHelper boolFromDictionary:userDict forKey:@"isFriend" defaultValue:NO];
    BOOL isBlock = [SCDictionaryHelper boolFromDictionary:userDict forKey:@"isBlock" defaultValue:NO];

    moreVC = [self showWithFromVC:fromVC userDict:userDict actionStrs:@[ isFriend ? @"UnFollow".translateString:@"Follow".translateString, isBlock ? @"UnBlock".translateString:@"Block".translateString,@"Report".translateString] cancelStr:@"Cancel".translateString isAuthClose:NO actionBlock:^(NSInteger index) {
        [SCAnchorActionSheetViewController showWithFromVCActionBlockDict:index actionBlock:actionBlock userDict:userDict vc:moreVC];
    } cancelBlock:^{
        [moreVC dismissViewControllerAnimated:YES completion:nil];
    }];
}

+(SCAnchorActionSheetViewController *_Nonnull)showWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actions:(NSArray<UserMorePopoupAction *> * _Nullable)actions cancel:(UserMorePopoupAction * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void(^_Nullable)(NSInteger)) actionBlock cancelBlock:(void(^_Nullable)(void)) cancelBlock {
    SCAnchorActionSheetViewController *vc = [SCAnchorActionSheetViewController showWithFromVC:fromVC actions:actions cancel:cancel isAuthClose:isAuthClose actionBlock:actionBlock cancelBlock:cancelBlock];
    vc.userDict = userDict;
    return vc;
}

+ (SCAnchorActionSheetViewController *)showWithFromVC:(UIViewController *)fromVC userDict:(NSMutableDictionary *)userDict actionStrs:(NSArray<NSString *> * _Nullable)actions cancelStr:(NSString * _Nullable)cancel isAuthClose:(BOOL)isAuthClose actionBlock:(void (^)(NSInteger))actionBlock cancelBlock:(void (^)(void))cancelBlock{
    SCAnchorActionSheetViewController *vc = [SCAnchorActionSheetViewController showWithFromVC:fromVC actionStrs:actions cancelStr:cancel isAuthClose:isAuthClose actionBlock:actionBlock cancelBlock:cancelBlock];
    vc.userDict = userDict;
    return vc;
}

+ (void)showWithFromVCActionBlockDict:(NSInteger)index actionBlock:(void (^_Nullable)(NSInteger))actionBlock userDict:(NSMutableDictionary *)userDict vc:(UIViewController *)moreVC{
    NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    BOOL isFriend = [SCDictionaryHelper boolFromDictionary:userDict forKey:@"isFriend" defaultValue:NO];
    BOOL isBlock = [SCDictionaryHelper boolFromDictionary:userDict forKey:@"isBlock" defaultValue:NO];

    if(index == 0){
        ///关注 / 取消关注
        if(isFriend){
            [kSCAuthActionService requestUnFollowWithUserId:userID success:^{
                // 修改userDict中isFriend值为NO
                userDict[@"isFriend"] = @(NO);
                [kSCKeyWindow toast:@"UnFollow Successfully".translateString];
                kSCBlockExeNotNil(actionBlock, index);
            } failure:^(SCXErrorModel * _Nonnull error) {
                [kSCKeyWindow toast:@"UnFollow Failure".translateString];
            }];
        }else{
            [kSCAuthActionService requestFollowWithUserId:userID success:^{
                // 修改userDict中isFriend值为YES
                userDict[@"isFriend"] = @(YES);
                [kSCKeyWindow toast:@"Follow Successfully".translateString];
                kSCBlockExeNotNil(actionBlock, index);
            } failure:^(SCXErrorModel * _Nonnull error) {
                [kSCKeyWindow toast:@"Follow Failure".translateString];
            }];
        }

        [moreVC dismissViewControllerAnimated:YES completion:nil];
    }else if(index == 1){
        ///拉黑 / 取消拉黑
        if(isBlock){
            [kSCAuthActionService requestUnBlockWithUserId:userID success:^{
                //融云取消拉黑
                // 修改userDict中isBlock值为NO（取消拉黑后用户不再被拉黑）
                userDict[@"isBlock"] = @(NO);
                [[RCCoreClient sharedCoreClient] removeFromBlacklist:userID success:nil error:nil];
                [kSCKeyWindow toast:@"UnBlock Successfully".translateString];
                kSCBlockExeNotNil(actionBlock, index);
            } failure:^(SCXErrorModel * _Nonnull error) {
                [kSCKeyWindow toast:@"UnBlock Failure".translateString];
            }];
        }else{
            [kSCAuthActionService requestBlockWithUserId:userID success:^{
                //融云拉黑
                // 修改userDict中isBlock值为YES（拉黑后用户被拉黑）
                userDict[@"isBlock"] = @(YES);
                [[RCCoreClient sharedCoreClient] addToBlacklist:userID success:nil error:nil];
                [kSCKeyWindow toast:@"Block Successfully".translateString];
                kSCBlockExeNotNil(actionBlock, index);
            } failure:^(SCXErrorModel * _Nonnull error) {
                [kSCKeyWindow toast:@"Block Failure".translateString];
            }];
        }
        [moreVC dismissViewControllerAnimated:YES completion:nil];
    }else if(index == 2){
        ///举报
        [SCAnchorActionSheetViewController showReportActionSheetWithFromVC:moreVC userDict:userDict actionBlock:^(NSInteger index) {
            [moreVC dismissPresentedViewControllersWithCompletion:^{
                [moreVC dismissViewControllerAnimated:YES completion:nil];
            }];
        } reportSuccessBlock:^{
            kSCBlockExeNotNil(actionBlock, index);
        }];
    }
}

@end
