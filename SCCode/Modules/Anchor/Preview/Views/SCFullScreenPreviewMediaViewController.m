//
//  SCFullScreenPreviewMediaViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCFullScreenPreviewMediaViewController.h"
//视图
#import "SCMediaItemView.h"
#import "SCCustomPageControl.h"
#import "SCUserItemView.h"
#import "SCAnchorInfoBottomView.h"
#import "SCConversationInfoViewController.h"
#import "SCBaseNavigationController.h"
#import "SCAnchorActionSheetViewController.h"
#import "SCScrollView.h"
//数据模型
// #import "SCMediaListModel.h" // 已移除，使用字典替代
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCNavigationBar.h"
//呼叫服务
#import "SCActionService.h"
#import "SCCallService.h"
#import "SCAPIServiceManager.h"

@interface SCFullScreenPreviewMediaViewController ()<UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
// 字典版本的属性
@property (nonatomic, strong) NSDictionary *userDict;
@property (nonatomic, strong) NSArray<NSDictionary *> *mediaDicts;
@property (nonatomic, strong) NSMutableArray<SCMediaItemView *> *imageViews;
@property (nonatomic, strong) SCCustomPageControl *pageControl;
@property (nonatomic, assign) SCFullScreenPreviewMediaType type;
@property (nonatomic, weak) SCMediaItemView *currentItemView;
@property (nonatomic, weak) SCUserItemView *userItemView;

//底部视图
@property(nonatomic,weak) SCAnchorInfoBottomView * bottomView;

@property (nonatomic,nullable, weak) UIButton *closeBtn;
@property (nonatomic,nullable, weak) UIButton *reportBtn;
@property (nonatomic,nullable, weak) UIButton *followBtn;

@property (nonatomic, assign) NSInteger index;
///是否显示拨打按钮
@property (nonatomic, assign) BOOL isShowCall;
@property (nonatomic, copy) void(^actionCallBack)(int type);

//是否正在显示中
@property (nonatomic, assign) BOOL isShowing;

@end

@implementation SCFullScreenPreviewMediaViewController

#pragma mark - 便捷访问方法

// 获取用户ID
- (NSString *)getUserID {
    return [SCDictionaryHelper stringFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
}

// 获取用户状态
- (NSString *)getUserStatus {
    return [SCDictionaryHelper stringFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];
}

// 获取是否关注状态
- (BOOL)getIsFriend {
    return [SCDictionaryHelper boolFromDictionary:self.userDict forKey:@"isFriend" defaultValue:NO];
}

// 设置关注状态
- (void)setIsFriend:(BOOL)isFriend {
    if (self.userDict) {
        NSMutableDictionary *mutableUserDict = [self.userDict mutableCopy];
        mutableUserDict[@"isFriend"] = @(isFriend);
        self.userDict = [mutableUserDict copy];
    }
}

// 获取媒体数量
- (NSInteger)getMediaCount {
    return self.mediaDicts ? self.mediaDicts.count : 0;
}

// 获取指定索引的媒体类型
- (NSString *)getMediaTypeAtIndex:(NSInteger)index {
    if (self.mediaDicts && index < self.mediaDicts.count) {
        return [SCDictionaryHelper stringFromDictionary:self.mediaDicts[index] forKey:@"mediaType" defaultValue:@""];
    }
    return @"";
}

// 字典版本的初始化方法
- (instancetype)initWithMediaDicts:(NSArray<NSDictionary *> * _Nonnull)mediaDicts userDict:(NSDictionary * _Nullable)userDict type:(SCFullScreenPreviewMediaType) type isShowCall:(BOOL) isShowCall
{
    self = [super init];
    if (self) {
        _mediaDicts = mediaDicts;
        _userDict = userDict;
        _type = type;
        _isShowCall = isShowCall;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.isShowing = YES;
    if(self.currentItemView){
        [self.currentItemView play];
    }
    if (self.followBtn) {
        self.followBtn.selected = [self getIsFriend];
        [self requestInfo];
    }

}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    self.isShowing = NO;
    [self stopAllVideo];
}

- (void)requestInfo{
    kWeakSelf(self);
    NSString *userID = [self getUserID];
    if (userID.length == 0) return;

    // 使用字典版本的API调用
    [SCAPIServiceManager requestUserInfoWithUserId:userID cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(NSDictionary * _Nonnull userDict) {
        // 更新字典数据
        if (weakself.userDict) {
            NSMutableDictionary *mutableUserDict = [weakself.userDict mutableCopy];
            mutableUserDict[@"isFriend"] = @([SCDictionaryHelper boolFromDictionary:userDict forKey:@"isFriend" defaultValue:NO]);
            mutableUserDict[@"isBlock"] = @([SCDictionaryHelper boolFromDictionary:userDict forKey:@"isBlock" defaultValue:NO]);
            weakself.userDict = [mutableUserDict copy];
        }
        if (weakself.followBtn) {
            weakself.followBtn.selected = [weakself getIsFriend];
        }

    } failure:nil];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setIsHiddenSCNavigationBar:YES];
    
    self.view.backgroundColor = [UIColor scBlack];
    
    _scrollView = [[SCScrollView alloc] initWithFrame:self.view.bounds];
    _scrollView.delegate = self;
    _scrollView.pagingEnabled = YES;
    _scrollView.showsHorizontalScrollIndicator = NO;
    _scrollView.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
    if (kScAuthMar.isLanguageForce) {
        _scrollView.transform = CGAffineTransformMakeScale(-1, 1);
    }
    [self.view addSubview:_scrollView];
    
    _pageControl = [[SCCustomPageControl alloc] init];
    CGFloat pageControlHeight = kSCSafeAreaBottomHeight > 0 ? 50 : 25;
    _pageControl.frame = CGRectMake(0, self.view.bounds.size.height - pageControlHeight, self.view.bounds.size.width, pageControlHeight);
    _pageControl.numberOfPages = [self getMediaCount];
    _pageControl.currentPage = 0;
    _pageControl.userInteractionEnabled = NO;
    _pageControl.activePageColor = UIColor.scWhite;
    _pageControl.inactivePageColor = [UIColor.scWhite colorWithAlphaComponent:0.5];;
    _pageControl.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
    if (kScAuthMar.isLanguageForce) {
        _pageControl.transform = CGAffineTransformMakeScale(-1, 1);
    }
    [self.view addSubview:_pageControl];
    UIImage *closeImage = [SCResourceManager loadImageWithName:@"ic_white_arr" isAutoForce:YES];
    UIButton *closeBtn = [UIButton buttonWithImage:closeImage target:self action:@selector(onClose)];
    closeBtn.backgroundColor = [UIColor sc50TranBlackBGColor];
    closeBtn.layer.cornerRadius = 10.0f;
    closeBtn.layer.masksToBounds = YES;
    [self.view addSubview:closeBtn];
    self.closeBtn = closeBtn;
    [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(15.0f);
        make.top.mas_equalTo(kSCSafeAreaTopHeight);
        make.size.mas_equalTo(CGSizeMake(36, 36));
    }];
    
    if(_type == SCFullScreenPreviewMediaTypeVideo){
      
        SCUserItemView * userItemView = [[SCUserItemView alloc] init];
        [self.view addSubview:userItemView];
        [userItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.closeBtn.mas_trailing).offset(10.0f);
            make.centerY.equalTo(self.closeBtn);
            make.height.mas_equalTo(36.0f);
        }];
        self.userItemView = userItemView;
        // 配置用户信息，使用字典
        [userItemView configWithUserDict:self.userDict];

        UIButton *reportBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"ic_nav_white_more"] target:self action:@selector(onReport)].addSuperView(self.view);
        reportBtn.backgroundColor = [UIColor sc50TranBlackBGColor];
        reportBtn.layer.cornerRadius = 10.0f;
        reportBtn.layer.masksToBounds = YES;
        reportBtn.selected = [self getIsFriend];
        self.reportBtn = reportBtn;
        [reportBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.userItemView.mas_trailing).offset(10);
            make.centerY.equalTo(self.closeBtn);
            make.size.mas_equalTo(CGSizeMake(36.0f, 36.0f));
        }];
        
        UIButton *followBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_follow"] forState:UIControlStateNormal];
        [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_followed"] forState:UIControlStateSelected];
        [followBtn addTarget:self action:@selector(onFollow) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:followBtn];
        [followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.view).offset(-15);
            make.centerY.equalTo(self.closeBtn);
            make.size.mas_equalTo(CGSizeMake(36.0f, 36.0f));
        }];
        self.followBtn = followBtn;
        self.followBtn.selected = [self getIsFriend];
    }
    
    //底部视图
    SCAnchorInfoBottomView * bottomView = [[SCAnchorInfoBottomView alloc] init];
    [bottomView setHidden: !self.isShowCall];
    bottomView.nextAnimationView.hidden = YES;
    kSCAddTapGesture(bottomView.callAnimationView, self, onCall);
    [bottomView.msgBtn addTarget:self action:@selector(onMessage) forControlEvents:UIControlEventTouchUpInside];
    [bottomView.callBtn addTarget:self action:@selector(onCall) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:bottomView];
    self.bottomView = bottomView;
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.view).offset(kSCSafeAreaBottomHeight > 0 ? 0 : -20);
    }];
    
    _imageViews = [NSMutableArray array];
    
    CGFloat maxHeight = self.view.bounds.size.height;
    
    CGFloat hwScale = 16.0 / 9.0f;
    CGFloat width = self.view.bounds.size.width;
    CGFloat height = width * hwScale;
    height = MIN(height, maxHeight);
    
    //垂直居中
    CGFloat y = (maxHeight - height) / 2;
    
    NSInteger mediaCount = [self getMediaCount];
    for (int i = 0; i < mediaCount; i++) {
        SCMediaItemView *itemView = [[SCMediaItemView alloc] initWithFrame:CGRectMake(i * self.view.bounds.size.width, y, self.view.bounds.size.width, height)];

        // 使用字典数据
        if (i < self.mediaDicts.count) {
            itemView.model = self.mediaDicts[i];
        }

        [_scrollView addSubview:itemView];
        [_imageViews addObject:itemView];
    }

    _scrollView.contentSize = CGSizeMake(self.view.bounds.size.width * mediaCount, height);
    
    [self selectToIndex:self.index];
    
    // 使用便捷方法获取状态并转换
    NSString *statusString = [self getUserStatus];
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:statusString];
    //只有在线才可以呼叫
    if (status == AnchorStatusOnline) {
        self.bottomView.callBtn.hidden = YES;
        self.bottomView.callAnimationView.hidden = NO;
        [self.bottomView.callAnimationView play];
    } else {
        self.bottomView.callBtn.hidden = NO;
        self.bottomView.callAnimationView.hidden = YES;
        [self.bottomView.callAnimationView stop];
    }
    
    kWeakSelf(self)
    [kSCAuthCallService.callingShowObx subscribe:^(NSNumber * _Nullable value) {
        kStrongSelf
        UIViewController *topViewController = [UIViewController currentViewController];
        if (value.boolValue) {
            // 暂停视频播放
            [strongSelf stopAllVideo];
        } else if (strongSelf.currentItemView && [[[strongSelf getMediaTypeAtIndex:strongSelf.index] lowercaseString] isEqualToString:@"video"] && [topViewController isKindOfClass:[SCFullScreenPreviewMediaViewController class]] && strongSelf.isShowing) {
            [strongSelf.currentItemView play];
        }
    } error:nil disposeBag:self.disposeBag];
}
-(void) selectToIndex:(NSInteger) index{
    _index = index;
    CGFloat pageWidth = CGRectGetWidth(self.scrollView.frame);
    CGFloat newOffsetX = pageWidth * index;
    [self.scrollView setContentOffset:CGPointMake(newOffsetX, 0) animated:YES];
//    if(_index == 0){
    if(self.imageViews != nil){
        if(_index < [self.imageViews count] ){
            self.currentItemView = self.imageViews[_index];
            [self.currentItemView play];
        }
    }
        
//    }
    
}
- (void)dealloc{
    [self stopAllVideo];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
#pragma mark - UIScrollViewDelegate

//- (nullable UIView *)viewForZoomingInScrollView:(UIScrollView *)scrollView {
//    return self.imageViews[self.pageControl.currentPage];
//}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat pageWidth = scrollView.frame.size.width;
    int index = (scrollView.contentOffset.x + pageWidth / 2) / pageWidth;
    self.pageControl.currentPage = index;
    _scrollView.scrollEnabled = YES;
    
}
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    CGFloat pageWidth = scrollView.frame.size.width;
    int index = (scrollView.contentOffset.x + pageWidth / 2) / pageWidth;
    SCMediaItemView *currentItemView = self.imageViews[index];
    if(self.currentItemView != currentItemView){
        // 暂停当前视频
        NSString *currentMediaType = [self getMediaTypeAtIndex:self.index];
        if([[currentMediaType lowercaseString] isEqualToString:@"video"]){
            [self.currentItemView pause];
        }

        self.currentItemView = currentItemView;
        // 播放新的视频
        NSString *newMediaType = [self getMediaTypeAtIndex:index];
        if([[newMediaType lowercaseString] isEqualToString:@"video"]){
            [self.currentItemView play];
        }
    }
    _index = index;
}

- (void)stopAllVideo {
    NSString *currentMediaType = [self getMediaTypeAtIndex:self.index];
    if (self.currentItemView && [[currentMediaType lowercaseString] isEqualToString:@"video"]) {
        for (SCMediaItemView *itemView in self.imageViews) {
            [itemView pause];
        }
    }
}

#pragma mark - Action
- (void) onClose{
    [self.navigationController popViewControllerAnimated:YES];
}
-(void)onReport{
    kWeakSelf(self)
    // 使用字典版本的方法
    NSMutableDictionary *mutableUserDict = [self.userDict mutableCopy];
    [SCAnchorActionSheetViewController showMoreActionSheetWithFromVC:self userDict:mutableUserDict actionBlock:^(NSInteger index) {
        // 将更新后的字典同步回userDict
        weakself.userDict = [mutableUserDict copy];
        // 更新用户信息显示
        if (weakself.userItemView) {
            [weakself.userItemView configWithUserDict:weakself.userDict];
        }
        weakself.followBtn.selected = [weakself getIsFriend];
    }];
}
-(void) onMessage{
    [self stopAllVideo];
    NSString *userID = [self getUserID];
    // 使用字典版本的方法
    [SCConversationInfoViewController showWithFromVC:self tager:userID userDict:self.userDict];
}
-(void) onCall{
    [self stopAllVideo];
    NSString *currentMediaType = [self getMediaTypeAtIndex:self.index];

    // 使用字典版本的方法
    if ([[currentMediaType lowercaseString] isEqualToString:@"video"]) {
        [kSCAuthCallService startCallingWithUserDict:self.userDict callSource:SCCallSourceDetailVideoPage];
    } else {
        [kSCAuthCallService startCallingWithUserDict:self.userDict callSource:SCCallSourceDetailPhotoPage];
    }
}

-(void)onFollow{
    kWeakSelf(self);
    NSString *userID = [self getUserID];
    BOOL isFriend = [self getIsFriend];

    if(isFriend){
        //取消关注
        [kSCAuthActionService requestUnFollowWithUserId:userID success:^{
            [weakself setIsFriend:NO];
            // 更新UI
            if (weakself.userItemView) {
                [weakself.userItemView configWithUserDict:weakself.userDict];
            }
            weakself.followBtn.selected = [weakself getIsFriend];
            [weakself requestUserInfoWithUserId:userID];
            [kSCKeyWindow toast:@"UnFollow Successfully".translateString];
        } failure:nil];
    }else{
        //关注
        [kSCAuthActionService requestFollowWithUserId:userID success:^{
            [weakself setIsFriend:YES];
            // 更新UI
            if (weakself.userItemView) {
                [weakself.userItemView configWithUserDict:weakself.userDict];
            }
            weakself.followBtn.selected = [weakself getIsFriend];
            [weakself requestUserInfoWithUserId:userID];
            [kSCKeyWindow toast:@"Follow Successfully".translateString];
        } failure:nil];
    }
}

- (void)requestUserInfoWithUserId:(NSString *) userId {
    // 使用字典版本的API调用进行缓存刷新
    [SCAPIServiceManager requestUserInfoWithUserId:userId cachePolicy:SCNetCachePolicyCacheAndRefresh success:nil failure:nil];
}

#pragma mark - 显示入口
// 字典版本的显示入口
+ (void) showWithFromVC:(UIViewController *)fromVC media:(NSArray<NSDictionary *> *)mediaDicts userDict:(NSDictionary *)userDict type:(SCFullScreenPreviewMediaType) type index:(NSInteger)index isShowCall:(BOOL) isShowCall{

    SCFullScreenPreviewMediaViewController *vc = [[SCFullScreenPreviewMediaViewController alloc] initWithMediaDicts:mediaDicts userDict:userDict type:type isShowCall:isShowCall];
    vc.index = index;
    [fromVC.navigationController pushViewController:vc animated:YES];
}

@end
