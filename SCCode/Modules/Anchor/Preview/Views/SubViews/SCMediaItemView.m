//
//  SCMediaItemView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCMediaItemView.h"
//系统库
//视频播放
#import <AVFoundation/AVFoundation.h>
//Session
#import "SCAVAudioSessionUtils.h"

//数据模型
// #import "SCMediaListModel.h" // 已移除，使用字典替代
//Service
#import "SCCallService.h"

@interface SCMediaItemView()<UIGestureRecognizerDelegate>{
    AVPlayer *_player;
    AVPlayerLayer *_playerLayer;
    AVPlayerItem *_playerItem;

}

@property (nonatomic, strong) UIImageView *imageView;
@property (nonatomic, strong) UIButton *playButton;
@property (nonatomic, assign) CGFloat currentScale;

@end

@implementation SCMediaItemView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _imageView = [[UIImageView alloc] initWithFrame:self.bounds];
        _imageView.contentMode = UIViewContentModeScaleAspectFit;
        [self addSubview:_imageView];
        
        _playButton = [UIButton buttonWithType:UIButtonTypeCustom];
        UIImage *playImage = [SCResourceManager loadImageWithName:@"btn_video_play_large"];
        [_playButton setImage:playImage forState:UIControlStateNormal];
        [_playButton addTarget:self action:@selector(handlePlayAndPause) forControlEvents:UIControlEventTouchUpInside];
        _playButton.hidden = YES;
        [self addSubview:_playButton];
        [_playButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self);
            make.size.mas_equalTo(playImage.size);
        }];
        
        self.userInteractionEnabled = YES;
        UIPinchGestureRecognizer *pinchGesture = [[UIPinchGestureRecognizer alloc] initWithTarget:self action:@selector(handlePinchGesture:)];
        pinchGesture.delegate = self;
        [self addGestureRecognizer:pinchGesture];
        
        _currentScale = 1.0;

        // 添加点击手势
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture:)];
        [self addGestureRecognizer:tapGesture];
    }
    return self;
}

- (void)setModel:(NSDictionary *)model{
    _model = model;
    if([[[_model objectForKey:@"mediaType"] lowercaseString] isEqualToString:@"video"]){
        if (kScAuthMar.isLanguageForce) {
            self.imageView.transform = CGAffineTransformMakeScale(-1, 1);
        }
//
        [self.imageView sc_setImageWithURL:[_model objectForKey:@"middleThumbUrl"]];
        // 设置音频会话，使声音不受静音影响
        NSError *error = nil;
        [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayback withMode:AVAudioSessionModeMoviePlayback  error:&error];
        
        // 初始化AVPlayerItem
        AVURLAsset *asset = [AVURLAsset URLAssetWithURL:[NSURL URLWithString:[_model objectForKey:@"mediaUrl"]] options:nil];
        _playerItem = [AVPlayerItem playerItemWithAsset:asset];

        // 初始化AVPlayer
        _player = [AVPlayer playerWithPlayerItem:_playerItem];
        //循环播放
        _player.actionAtItemEnd = AVPlayerActionAtItemEndNone;
        _playerLayer = [AVPlayerLayer playerLayerWithPlayer:_player];
        _playerLayer.frame = self.imageView.bounds;
        [self.imageView.layer addSublayer:_playerLayer];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(playerItemDidReachEnd:) name:AVPlayerItemDidPlayToEndTimeNotification object:_player.currentItem];

        // 添加KVO观察
        [_playerItem addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];
        [_playerItem addObserver:self forKeyPath:@"loadedTimeRanges" options:NSKeyValueObservingOptionNew context:nil];
        [self showLoading];
    }else{
        [self hiddenLoading];
        kWeakSelf(self)
        [self.imageView sc_setImageWithURL:[_model objectForKey:@"mediaUrl"] completionBlock:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (kScAuthMar.isLanguageForce) {
                weakself.imageView.image = [[UIImage alloc]initWithCGImage:image.CGImage scale:image.scale orientation:UIImageOrientationUpMirrored];
            }
        }];
    }
    
    
}
- (void)play{
    if(kSCAuthCallService.isCalling){
        //当前正在被呼叫无法播放
        return;
    }
    [SCAVAudioSessionUtils configAVAudioSessionWithCategory:AVAudioSessionCategoryPlayback withMode:AVAudioSessionModeMoviePlayback  error:NULL];
    [_player play];
    _playButton.hidden = YES;
}
- (void)pause{
    //判断当前是否正在被呼叫
    
    [_player pause];
    _playButton.hidden = NO;
}

- (void)playerItemDidReachEnd:(NSNotification *)notification {
//    AVPlayerItem *playerItem = [notification object];
    [_player seekToTime:kCMTimeZero];
    [_player play];
    _playButton.hidden = YES;
}
- (void)dealloc{
    if (_playerItem != nil) {
        [_playerItem removeObserver:self forKeyPath:@"status"];
        [_playerItem removeObserver:self forKeyPath:@"loadedTimeRanges"];
    }
    
    if(_player != nil){
        [_player pause];
        [_player seekToTime:kCMTimeZero];
        [[NSNotificationCenter defaultCenter] removeObserver:self name:AVPlayerItemDidPlayToEndTimeNotification object:_player.currentItem];
        
        _player = nil;
    }
    
    if(_playerLayer != nil) {
        [_playerLayer removeFromSuperlayer];
    }
}

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    if (object == _playerItem) {
        if ([keyPath isEqualToString:@"status"]) {
            // 观察到status属性变化
            AVPlayerItemStatus status = _playerItem.status;
            if (status == AVPlayerItemStatusReadyToPlay) {
                // 视频缓存完成，可以播放
                [self hiddenLoading];
            } else if (status == AVPlayerItemStatusFailed) {
                // 视频缓存失败
                
                [self hiddenLoading];
            }
        } else if ([keyPath isEqualToString:@"loadedTimeRanges"]) {
            // 观察到loadedTimeRanges属性变化
            NSArray *timeRanges = _playerItem.loadedTimeRanges;
            if (timeRanges.count > 0) {
                // 获取缓冲的时间范围
                CMTimeRange timeRange = [[timeRanges firstObject] CMTimeRangeValue];
                CGFloat bufferedTime = CMTimeGetSeconds(CMTimeAdd(timeRange.start, timeRange.duration));
                CGFloat totalDuration = CMTimeGetSeconds(_playerItem.duration);
                CGFloat bufferProgress = bufferedTime / totalDuration;
                
            }
        }
    }
}


- (void)handlePinchGesture:(UIPinchGestureRecognizer *)gesture {
//    CGFloat minW = self.frame.size.width * 0.5;
//    CGFloat maxW = self.frame.size.width * 2;
//    CGFloat minH = self.frame.size.height * 0.5;
//    CGFloat maxH = self.frame.size.height * 2;
//    
//    if (gesture.state == UIGestureRecognizerStateChanged) {
//        CGFloat scale = gesture.scale;
//                
//                CGPoint pinchCenter = [gesture locationInView:self.imageView];
//                
//                CGRect currentFrame = self.imageView.frame;
//                CGRect newFrame = CGRectApplyAffineTransform(currentFrame, CGAffineTransformMakeScale(scale, scale));
//                
//                CGFloat offsetX = (pinchCenter.x - CGRectGetMidX(currentFrame)) * (1 - scale);
//                CGFloat offsetY = (pinchCenter.y - CGRectGetMidY(currentFrame)) * (1 - scale);
//                
//                newFrame.origin.x -= offsetX;
//                newFrame.origin.y -= offsetY;
//                
//        if(newFrame.size.width < minW || newFrame.size.width > maxW){
//            newFrame = currentFrame;
//        }else if(newFrame.size.height < minH || newFrame.size.height > maxH){
//            newFrame = currentFrame;
//        }
//        
//        
//                self.imageView.frame = newFrame;
//                
//                gesture.scale = 1.0;
//    } else if (gesture.state == UIGestureRecognizerStateEnded) {
//        CGFloat scale = gesture.scale;
//        self.currentScale *= scale;
//        gesture.scale = 1.0;
//        
//        if(self.imageView.frame.size.width < self.frame.size.width && self.imageView.frame.size.height < self.frame.size.height){
//            [UIView animateWithDuration:0.2 animations:^{
//                self.imageView.center = self.center;
//            }];
//        }else if(self.imageView.frame.size.width < self.frame.size.width){
//            [UIView animateWithDuration:0.2 animations:^{
//                self.imageView.center = CGPointMake(self.center.x, self.imageView.center.y);
//            }];
//        }else if(self.imageView.frame.size.height < self.frame.size.height){
//            [UIView animateWithDuration:0.2 animations:^{
//                self.imageView.center = CGPointMake(self.imageView.center.x, self.center.y);
//            }];
//            
//        }else{
//            if(self.imageView.frame.origin.x < 0 || self.imageView.frame.origin.y < 0){
//                [UIView animateWithDuration:0.2 animations:^{
//                    CGRect frame = self.imageView.frame;
//                    if(frame.origin.x < 0){
//                        frame.origin.x = 0;
//                    }
//                    if(frame.origin.y < 0){
//                        frame.origin.y = 0;
//                    }
//                    self.imageView.frame = frame;
//                }];
//            }
//        }
//    }
}

//- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
//    if ([gestureRecognizer isKindOfClass:[UIPinchGestureRecognizer class]]) {
//        return YES;
//    }
//    return NO;
//}

- (void)handleTapGesture:(UITapGestureRecognizer *)gesture {
    [self handlePlayAndPause];
}


- (void)handlePlayAndPause {
    if ([[[_model objectForKey:@"mediaType"] lowercaseString] isEqualToString:@"video"]) {
        if (_player.rate == 0) {  // 当前是暂停状态
            [self play];
        } else {  // 当前是播放状态
            [self pause];
        }
    }
}

@end
