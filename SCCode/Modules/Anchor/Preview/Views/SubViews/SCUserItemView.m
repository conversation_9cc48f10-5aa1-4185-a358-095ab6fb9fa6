//
//  SCUserItemView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCUserItemView.h"
#import <Masonry/Masonry.h>
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCRoundedLabelView.h"
#import "SCFontManager.h"
#import "SCCountryService.h"

@interface SCUserItemView ()
///头像
@property (nonatomic, weak) UIImageView *avatarImageView;
///昵称
@property (nonatomic, weak) UILabel *nickNameLabel;
///国家
@property(nonatomic,weak,nullable) UILabel * countryLabel;

@end

@implementation SCUserItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initView];
    }
    return self;
}
CGFloat const kAvatarImageViewWidth = 32.0f;
CGFloat const kItemViewHeight = 36.0f;

-(void) _initView{
    UIImageView *avatarImageView = [[UIImageView alloc] init];
    avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    avatarImageView.layer.masksToBounds = YES;
    avatarImageView.layer.cornerRadius = kAvatarImageViewWidth/2.0f;
    [self addSubview:avatarImageView];
    _avatarImageView = avatarImageView;
    
    UILabel *nickNameLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:[SCFontManager semiBoldFontWithSize:12.0f] alignment:kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft];
    [self addSubview:nickNameLabel];
    _nickNameLabel = nickNameLabel;
    
    //国家
    UILabel * countryLabel = [[UILabel alloc] init];
    [countryLabel setFont:kScUIFontRegular(10)];
    countryLabel.textColor = UIColor.scWhite;
    [self addSubview:countryLabel];
    _countryLabel = countryLabel;
    
    [avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).inset(5);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(kAvatarImageViewWidth, kAvatarImageViewWidth));
    }];
    
    [nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(avatarImageView.mas_trailing).offset(10);
        make.trailing.equalTo(self).inset(7);
        make.top.equalTo(avatarImageView).inset(2);
        make.height.mas_equalTo(15);
    }];
    
    [countryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(nickNameLabel);
        make.top.equalTo(nickNameLabel.mas_bottom).offset(2);
        make.height.mas_equalTo(14);
        make.trailing.equalTo(self).inset(7);
    }];
    
    //背景
    self.backgroundColor = [UIColor sc50TranBlackBGColor];
    self.layer.masksToBounds = YES;
    self.layer.cornerRadius = 10.0f;
}

-(void) configWithUserDict:(NSDictionary *)userDict{
    [self.avatarImageView sc_setImageWithURL:[userDict objectForKey:@"avatarUrl"] placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
    self.nickNameLabel.text = [[userDict objectForKey:@"nickname"] replaceMoreThan10];
    NSString *country = [userDict objectForKey:@"country"];
    [self.countryLabel setText:[NSString stringWithFormat:@"%@ %@", [SCCountryService emojiForCountry:country], country.replaceMoreThan10]];

}

@end
