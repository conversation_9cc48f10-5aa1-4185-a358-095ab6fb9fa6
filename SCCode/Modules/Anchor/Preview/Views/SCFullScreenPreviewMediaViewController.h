//
//  SCFullScreenPreviewMediaViewController.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCBaseViewController.h"
// 已移除Model依赖，使用字典替代
NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, SCFullScreenPreviewMediaType) {
    SCFullScreenPreviewMediaTypePhoto = 0,
    SCFullScreenPreviewMediaTypeVideo = 1,
};

@interface SCFullScreenPreviewMediaViewController : SCBaseViewController

#pragma mark - 显示入口
// 字典版本的显示入口
+ (void) showWithFromVC:(UIViewController *)fromVC media:(NSArray<NSDictionary *> *)mediaDicts userDict:(NSDictionary *)userDict type:(SCFullScreenPreviewMediaType) type index:(NSInteger)index isShowCall:(BOOL) isShowCall;

@end

NS_ASSUME_NONNULL_END
