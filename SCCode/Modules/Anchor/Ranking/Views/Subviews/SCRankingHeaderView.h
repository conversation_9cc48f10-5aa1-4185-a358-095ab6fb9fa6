//
//  SCRankingHeaderView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import <UIKit/UIKit.h>
@class SCRankingHeaderItemView;
NS_ASSUME_NONNULL_BEGIN
//排行榜数据封装， 榜一，榜二，榜三
@interface SCRankingHeaderView : UIView
@property(nonatomic,nonnull,strong) UIView * subNavView;
@property(nonatomic,nonnull,strong) UIButton * subTitleBtn;
@property(nonnull,nonatomic,strong) UIView *contentView;
//榜一
@property(nonatomic,strong) SCRankingHeaderItemView * ranking1;
@property(nonatomic,strong) SCRankingHeaderItemView * ranking2;
@property(nonatomic,strong) SCRankingHeaderItemView * ranking3;

//點擊事件
@property(nonatomic,copy)void (^clickCallback)(int index);

@end


//榜一的头像布局
@interface SCRankingHeaderItemView : UIView

//圆形头像
@property(nonatomic,strong)UIImageView *avatarImageView;
//头像下面的名字
@property(nonatomic,strong)UILabel *nameLabel;
//名字下的排名
@property(nonatomic,strong)UILabel *rankingLabel;
//覆盖到头像上面的头像框
@property(nonatomic,strong)UIImageView *avatarCoverImageView;
//排名
@property(nonatomic,assign,readonly) int ranking;


- (instancetype)initWith:(int) ranking;

-(void)configWithAvatar:(NSString *)avatar name:(NSString *)name;
@end

NS_ASSUME_NONNULL_END
