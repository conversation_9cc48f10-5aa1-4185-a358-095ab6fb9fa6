//
//  SCRankingTitleView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingTitleView.h"
#import "SCAnchorSubJXCategoryTitleCell.h"
#import "SCAnchorSubTitleJXCategoryTitleCellModel.h"

@implementation SCRankingTitleView

- (void)initializeData {
    [super initializeData];

    self.cellWidthIncrement = 0;
    self.cellWidth = 86.0f;
    self.normalBackgroundColor = [UIColor scWhite];
    self.normalBorderColor = [UIColor scTheme];
    self.selectedBackgroundColor = [UIColor scTheme];
    self.selectedBorderColor = [UIColor scTheme];
    self.borderLineWidth = 0.0f;
    self.backgroundCornerRadius = 4.0f;
    self.backgroundWidth = JXCategoryViewAutomaticDimension;
    self.backgroundHeight = 29;
    self.titleSelectedColor = [UIColor scWhite];
    self.titleColor = [UIColor scBlack];
    self.titleFont = kScUIFontMedium(15);
    self.titleSelectedFont = kScUIFontSemibold(20);
    self.cellSpacing = 10;
    self.backgroundColor = [UIColor scWhite];
}

//返回自定义的cell class
- (Class)preferredCellClass {
    return [SCAnchorSubJXCategoryTitleCell class];
}

- (void)refreshDataSource {
    NSMutableArray *tempArray = [NSMutableArray array];
    for (int i = 0; i < self.titles.count; i++) {
        SCAnchorSubTitleJXCategoryTitleCellModel *cellModel = [[SCAnchorSubTitleJXCategoryTitleCellModel alloc] init];
        [tempArray addObject:cellModel];
    }
    self.dataSource = tempArray;
}

- (void)refreshCellModel:(JXCategoryBaseCellModel *)cellModel index:(NSInteger)index {
    [super refreshCellModel:cellModel index:index];

    SCAnchorSubTitleJXCategoryTitleCellModel *myModel = (SCAnchorSubTitleJXCategoryTitleCellModel *)cellModel;
    myModel.normalBackgroundColor = self.normalBackgroundColor;
    myModel.normalBorderColor = self.normalBorderColor;
    myModel.selectedBackgroundColor = self.selectedBackgroundColor;
    myModel.selectedBorderColor = self.selectedBorderColor;
    myModel.borderLineWidth = self.borderLineWidth;
    myModel.backgroundCornerRadius = self.backgroundCornerRadius;
    myModel.backgroundWidth = self.backgroundWidth;
    myModel.backgroundHeight = self.backgroundHeight;
}

@end
