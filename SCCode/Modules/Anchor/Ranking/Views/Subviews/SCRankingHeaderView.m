//
//  SCRankingHeaderView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingHeaderView.h"
#import <Masonry/Masonry.h>
#import <SDWebImage/SDWebImage.h>
#import "SCFontManager.h"

@interface SCRankingHeaderView()

//背景图
@property(nonatomic,strong) UIImageView * bgIV;
@property(nonatomic,strong) UIImageView *bottomMaskIV;

@end

@implementation SCRankingHeaderView

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self _init];
    }
    return self;
}

-(void) _init{
    self.subNavView = [[UIView alloc] init];
    self.subNavView.backgroundColor = [UIColor clearColor];
    [self addSubview:self.subNavView];
    [_subNavView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(0);
        make.leading.trailing.equalTo(self).offset(0);
        make.height.mas_equalTo(58);
    }];
    
    _subTitleBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    _subTitleBtn.userInteractionEnabled = NO;
    [_subTitleBtn setTitleColor:[UIColor scWhite] forState:UIControlStateNormal];
    _subTitleBtn.titleLabel.font = [SCFontManager boldFontWithSize:16.0f];
    _subTitleBtn.layer.cornerRadius = 10.0f;
    _subTitleBtn.contentEdgeInsets = UIEdgeInsetsMake(0, 11, 0, 11);    
    NSArray *colors = @[[UIColor colorWithHexString:@"#FF2E29"], [UIColor colorWithHexString:@"#8C0033"]];
    NSArray *locations = @[@(0), @(1.0f)];
    CGPoint startPoint = kScAuthMar.isLanguageForce ? CGPointMake(1, 0.5) : CGPointMake(0, 0.5);
    CGPoint endPoint = kScAuthMar.isLanguageForce ? CGPointMake(0, 0.5) : CGPointMake(1, 0.5);
    [_subTitleBtn sc_setGradientBackgroundWithColors:colors gradientLocations:locations startPoint:startPoint endPoint:endPoint cornerRadius:10.0f];
    [self.subNavView addSubview:_subTitleBtn];
    [_subTitleBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(34.0f);
        make.width.mas_greaterThanOrEqualTo(100.0f);
        make.center.equalTo(self.subNavView);
    }];
    
    _contentView = [[UIView alloc] init];
    [self addSubview:_contentView];
    [_contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self);
        make.top.equalTo(self.subNavView.mas_bottom);
        make.bottom.equalTo(self);
    }];
    
    _bgIV = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"bg_ranking_header"]];
    [_contentView addSubview:_bgIV];
    
    _ranking1 = [[SCRankingHeaderItemView alloc] initWith:1];
    _ranking2 = [[SCRankingHeaderItemView alloc] initWith:2];
    _ranking3 = [[SCRankingHeaderItemView alloc] initWith:3];
    kSCAddTapGesture(_ranking1, self, onClickAvatar:);
    kSCAddTapGesture(_ranking2, self, onClickAvatar:);
    kSCAddTapGesture(_ranking3, self, onClickAvatar:);
    
    [_contentView addSubview:_ranking1];
    [_contentView addSubview:_ranking2];
    [_contentView addSubview:_ranking3];
    
    _bottomMaskIV = [[UIImageView alloc]initWithImage:[SCResourceManager loadImageWithName:@"mask_ranking_bottom"]];
    [_contentView addSubview:_bottomMaskIV];
    
    [_bgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(65.0f);
        make.leading.equalTo(self.contentView).offset(17.0f);
        make.trailing.equalTo(self.contentView).offset(-17.0f);
        make.height.equalTo(self.bgIV.mas_width).multipliedBy(177.0f / 341.0f);
        make.bottom.equalTo(self.contentView).offset(-24.0f);
    }];
    
    [_ranking1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(0);
        make.centerX.equalTo(self.contentView).offset(0);
        make.width.mas_equalTo(138);
    }];
    [_ranking2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(72);
        make.left.equalTo(self.contentView).offset(0);
        make.width.mas_equalTo(138);
    }];
    [_ranking3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(72);
        make.right.equalTo(self.contentView).offset(0);
        make.width.mas_equalTo(138);
    }];
    
    [_bottomMaskIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.bottom.trailing.equalTo(self.contentView);
//        make.height.mas_equalTo(54.0f);
        make.height.equalTo(self.bottomMaskIV.mas_width).multipliedBy(54.0f / 375.0f);
    }];
    
}
-(void) onClickAvatar:(UITapGestureRecognizer *)tap{
    if(tap.view == _ranking1){
        kSCBlockExeNotNil(self.clickCallback,0);
    }else if(tap.view == _ranking2){
        kSCBlockExeNotNil(self.clickCallback,1);
    }else if(tap.view == _ranking3){
        kSCBlockExeNotNil(self.clickCallback,2);
    }
}

@end

@implementation SCRankingHeaderItemView

- (instancetype)initWith:(int) ranking
{
    self = [super init];
    if (self) {
        _ranking = ranking;
        [self _initView];
    }
    return self;
}


-(void) _initView{
    
    _avatarImageView = [[UIImageView alloc] init];
    _avatarImageView.clipsToBounds = YES;
    [self addSubview:_avatarImageView];
    
    _nameLabel = [[UILabel alloc] init];
    _nameLabel.font = [SCFontManager boldFontWithSize:14.0f];
    _nameLabel.textColor = [UIColor scWhite];
    _nameLabel.textAlignment = NSTextAlignmentCenter;
    [self addSubview:_nameLabel];
    
    _rankingLabel = [[UILabel alloc] init];
    _rankingLabel.font = kScUIFontSemibold(70);
    _rankingLabel.textColor = [UIColor scWhite];
    _rankingLabel.textAlignment = NSTextAlignmentCenter;
    [self addSubview:_rankingLabel];
    
    _avatarCoverImageView = [[UIImageView alloc] init];
    [self addSubview:_avatarCoverImageView];
    
    
    [_nameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.leading.equalTo(self).offset(16);
        make.trailing.equalTo(self).offset(-16);
        make.top.equalTo(self.avatarImageView.mas_bottom).offset(self.ranking == 1 ? 10:0);
        make.height.mas_equalTo(21.0);
    }];
    [_rankingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.nameLabel.mas_bottom).offset(0);
        make.width.equalTo(self);
        make.bottom.equalTo(self).offset(-10);
    }];
//    [_rankingLabel setText:[NSString stringWithFormat:@"%d",_ranking]];
    if(_ranking == 1){
        //第一名
        _avatarImageView.layer.cornerRadius = 78/2.0;
        
        [_avatarCoverImageView setImage:[SCResourceManager loadImageWithName:@"ic_ranking_1"]];
        [_avatarCoverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(_avatarCoverImageView.image.size);
            make.top.equalTo(self).offset(0);
            make.centerX.equalTo(self);
        }];
        [_avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(78);
            make.center.equalTo(self.avatarCoverImageView);
        }];
        
        
    }else if(_ranking == 2){
        //第二名
        _avatarImageView.layer.cornerRadius = 54/2.0;

        [_avatarCoverImageView setImage:[SCResourceManager loadImageWithName:@"ic_ranking_2"]];
        [_avatarCoverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(_avatarCoverImageView.image.size);
            make.top.equalTo(self).offset(0);
            make.centerX.equalTo(self);
        }];
        [_avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(54);
            make.center.equalTo(self.avatarCoverImageView);
        }];
    }else if(_ranking == 3){
        //第三名
        _avatarImageView.layer.cornerRadius = 46/2.0;
        [_avatarCoverImageView setImage:[SCResourceManager loadImageWithName:@"ic_ranking_3"]];
        [_avatarCoverImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(_avatarCoverImageView.image.size);
            make.top.equalTo(self).offset(0);
            make.centerX.equalTo(self);
        }];
        [_avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(46);
            make.center.equalTo(self.avatarCoverImageView);
        }];
    }
    
}
- (void)configWithAvatar:(NSString *)avatar name:(NSString *)name{
    [_avatarImageView sc_setImageWithURL:avatar];
    [_nameLabel setText:[name replaceMoreThan10]];
}

@end
