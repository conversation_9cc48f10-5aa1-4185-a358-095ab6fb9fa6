//
//  SCRankingItemView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/25.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCRankingItemView : UIView
// 渐变背景,只用作样式
@property (nonatomic, strong)UIButton *bgButon;
//排名
@property(nonatomic,strong)UILabel * rankingLabel;
//头像
@property(nonatomic,strong)UIImageView * avatarIV;
//昵称
@property(nonatomic,strong)UILabel * nickNameLabel;
//contentView
@property(nonatomic,strong)UIView * contentView;

// 字典版本配置方法（推荐使用）
-(void) configWithDict:(NSDictionary *)itemDict;
-(void) configAnchorWithDict:(NSDictionary *)itemDict;
@end

NS_ASSUME_NONNULL_END
