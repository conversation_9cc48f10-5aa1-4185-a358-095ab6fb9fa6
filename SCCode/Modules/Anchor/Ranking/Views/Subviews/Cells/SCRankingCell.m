//
//  SCRankingCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingCell.h"
#import <Masonry/Masonry.h>
#import "SCUserRankModel.h"
#import "SCRankingItemView.h"

@interface SCRankingCell()

@property(nonatomic,strong) SCRankingItemView * itemView;

@end

@implementation SCRankingCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if(self){
        [self _initUI];
    }
    return self;
}

- (void) _initUI{
    [self setSelectionStyle:UITableViewCellSelectionStyleNone];
    [self setBackgroundColor:[UIColor colorWithHexString:@"#390C0C"]];
    self.itemView = [[SCRankingItemView alloc] initWithFrame:self.bounds];
    self.itemView.layer.cornerRadius = kSCNormalCornerRadius;
    self.itemView.layer.masksToBounds = YES;
    [self addSubview:self.itemView];
    [self.itemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(15);
        make.trailing.equalTo(self).offset(-15);
        make.top.equalTo(self);
        make.bottom.equalTo(self).offset(-10);
    }];
}
#pragma mark - 字典版本配置方法

-(void)configWithDict:(NSDictionary *) itemDict{
    [self.itemView configWithDict:itemDict];
}

-(void) configAnchorWithDict:(NSDictionary *)itemDict{
    [self.itemView configAnchorWithDict:itemDict];
}

@end
