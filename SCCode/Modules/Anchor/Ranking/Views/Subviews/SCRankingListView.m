//
//  SCRankingListView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingListView.h"
#import "MJRefresh.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCRankingHeaderView.h"
#import "SCRankingCell.h"
#import "SCUserRankModel.h"
#import "SCAPIServiceManager.h"
#import <Masonry/Masonry.h>
#import "SCRankingItemView.h"
// #import "SCTokenModel.h" // 已移除，使用字典替代
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCAnchorInfoViewController.h"

@interface SCRankingListView ()<UITableViewDataSource, UITableViewDelegate>

@property(nonatomic,strong)NSMutableArray<NSDictionary *> *users; // 改为使用字典替代SCUserInfoModel
@property(nonatomic,strong) SCRankingHeaderView * headerView;

@property(nonatomic,strong) SCRankingTypeModel * type;
@property(nonatomic,strong) NSDictionary * rankDict; // 改为使用字典替代SCUserRankModel
@property(nonatomic,strong) NSDictionary * anchorRankDict; // 改为使用字典替代SCAnchorRankModel
///当前用户排名
@property(nonatomic,strong) SCRankingItemView *myItem;
@property(nonatomic,strong) UIView *footerView;

@end
@implementation SCRankingListView

- (instancetype)initWithType:(SCRankingTypeModel *) type
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        _type = type;
        [self _initView];
    }
    return self;
}

- (void)dealloc
{
    self.scrollCallback = nil;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initView];
        
    }
    return self;
}
-(void) _initView{
    
    self.backgroundColor = [UIColor clearColor];
    
    _footerView = [[UIView alloc]init];
    _footerView.backgroundColor = [UIColor colorWithHexString:@"#290000"];
    
    _myItem = [[SCRankingItemView alloc] init];
    _myItem.layer.cornerRadius = kSCNormalCornerRadius;
    _myItem.rankingLabel.textColor = [UIColor whiteColor];
    _myItem.nickNameLabel.textColor = [UIColor whiteColor];
    [_myItem setBackgroundColor:[UIColor scTheme]];
    [_myItem.contentView setBackgroundColor:[UIColor clearColor]];
    _myItem.bgButon.hidden = NO;
    [_footerView addSubview:_myItem];
    [_myItem mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.equalTo(self.footerView).offset(15.0f);
        make.trailing.equalTo(self.footerView).offset(-15.0f);
        make.height.mas_equalTo(72.0f);
    }];
    
    _headerView = [[SCRankingHeaderView alloc] init];
    _headerView.frame = CGRectMake(0, 0, kSCScreenWidth, 244+58);
    kWeakSelf(self)
    _headerView.clickCallback = ^(int index) {
        kStrongSelf
        [strongSelf actionClickWithIndex:index];
    };
    _tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width, self.frame.size.height) style:UITableViewStylePlain];
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    [self.tableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
    [self.tableView registerClass:[SCRankingCell class] forCellReuseIdentifier:cellId];
    [self addSubview:self.tableView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    
    self.tableView.tableHeaderView = _headerView;

    __weak typeof(self) weakSelf = self;
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [weakSelf requestRankData];
    }];
    header.stateLabel.textColor = UIColor.scWhite;
    [header setTitle:@"Pull down to refresh".translateString forState:MJRefreshStateIdle];
    [header setTitle:@"Release to refresh".translateString forState:MJRefreshStatePulling];
    [header setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    header.lastUpdatedTimeLabel.hidden = YES;
    
    self.tableView.mj_header = header;
//    _tableView.frame = CGRectMake(0, 0, kSCScreenWidth, self.frame.size.height);
}
- (void)layoutSubviews{
    [super layoutSubviews];
//    _tableView.frame = CGRectMake(0, 0, self.frame.size.width, self.frame.size.height);
}

static int offSetNum = 3;
#pragma mark - UITableViewDataSource, UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (self.rankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCUserRankModel rankDataFromDict:self.rankDict];
        if([rankDataArray count] > offSetNum){
            return [rankDataArray count] - offSetNum;
        }
        return 0;
    }else if (self.anchorRankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCAnchorRankModel rankDataFromDict:self.anchorRankDict];
        if([rankDataArray count] > offSetNum){
            return [rankDataArray count] - offSetNum;
        }
        return 0;
    }
    return 0;
}


- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section{
    if(_type.rankType == SCRankingTypeRich){
        return 15.0f * 2 + kSCSafeAreaBottomHeight + 72.0f;
    }
    return 0;
}
- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section{
    return self.footerView;
}

static NSString * cellId = @"SCRankingCell_key";
- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    SCRankingCell *cell = [tableView dequeueReusableCellWithIdentifier:cellId forIndexPath:indexPath];
    NSInteger index = indexPath.row + offSetNum;
    if (self.rankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCUserRankModel rankDataFromDict:self.rankDict];
        if(index < [rankDataArray count]){
            NSDictionary *itemDict = rankDataArray[index];
            [cell configWithDict:itemDict];
        }
    }else if (self.anchorRankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCAnchorRankModel rankDataFromDict:self.anchorRankDict];
        if(index < [rankDataArray count]){
            NSDictionary *itemDict = rankDataArray[index];
            [cell configAnchorWithDict:itemDict];
        }
    }

    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 82;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger index = indexPath.row + offSetNum;
    [self actionClickWithIndex:index];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}

#pragma mark - JXCategoryListContentViewDelegate

- (UIView *)listView {
    return self;
}

#pragma mark - 点击事件

-(void) actionClickWithIndex:(NSInteger) index{
    if (self.rankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCUserRankModel rankDataFromDict:self.rankDict];
        if(index < [rankDataArray count]){
            NSDictionary *itemDict = rankDataArray[index];
            NSString *userID = [SCUserRankItemModel userIDFromDict:itemDict];
            ///点击事件
            if(self.type.rankType == SCRankingTypeRegion){
                //转跳详情页
                [SCAnchorInfoViewController openWith:userID from:[UIViewController currentViewController]];
            }
        }
    }else if (self.anchorRankDict != nil) {
        NSArray<NSDictionary *> *rankDataArray = [SCAnchorRankModel rankDataFromDict:self.anchorRankDict];
        if(index < [rankDataArray count]){
            NSDictionary *itemDict = rankDataArray[index];
            NSString *userID = [SCAnchorRankItemModel userIDFromDict:itemDict];
            ///点击事件
            if(self.type.rankType == SCRankingTypeRegion){
                //转跳详情页
                [SCAnchorInfoViewController openWith:userID from:[UIViewController currentViewController]];
            }
        }
    }

}

#pragma mark - 网络请求
- (void)beginFirstRefresh {
    if (self.rankDict == nil) {
        [self.tableView.mj_header beginRefreshing];
    }
}
-(void) configWithDict:(NSDictionary *) rankDict{
    _rankDict = rankDict;
    NSArray<NSDictionary *> *rankDataArray = [SCUserRankModel rankDataFromDict:rankDict];
    if([rankDataArray count] >= offSetNum){
        NSDictionary *itemDict = rankDataArray[0];
        NSString *avatar = [SCUserRankItemModel avatarFromDict:itemDict];
        NSString *nickname = [SCUserRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking1 configWithAvatar:avatar name:nickname];

        itemDict = rankDataArray[1];
        avatar = [SCUserRankItemModel avatarFromDict:itemDict];
        nickname = [SCUserRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking2 configWithAvatar:avatar name:nickname];

        itemDict = rankDataArray[2];
        avatar = [SCUserRankItemModel avatarFromDict:itemDict];
        nickname = [SCUserRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking3 configWithAvatar:avatar name:nickname];
    }
    NSString *monthName = [SCUserRankModel monthNameFromDict:rankDict];
    [_headerView.subTitleBtn setTitle:monthName forState:UIControlStateNormal];
    

    if(_type.rankType == SCRankingTypeRich){
        __block NSInteger mySort = 100000;
        NSArray<NSDictionary *> *rankDataArray = [SCUserRankModel rankDataFromDict:rankDict];
        [rankDataArray enumerateObjectsUsingBlock:^(NSDictionary * _Nonnull itemDict, NSUInteger idx, BOOL * _Nonnull stop) {
            NSString *userID = [SCUserRankItemModel userIDFromDict:itemDict];
            if([userID isEqualToString:kSCCurrentUserID]){
                mySort = [SCUserRankItemModel sortFromDict:itemDict];
            }
        }];

        // 创建当前用户的字典数据
        NSDictionary *myDict = @{
            SCDictionaryKeys.shared.kSCRankItemUserIDKey: kSCCurrentUserID,
            SCDictionaryKeys.shared.kSCRankItemAvatarKey: kSCCurrentUserAvatarUrl,
            SCDictionaryKeys.shared.kSCRankItemAvatarMapPathKey: [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:@"avatarMapPath" defaultValue:@""],
            SCDictionaryKeys.shared.kSCRankItemNicknameKey: kSCCurrentUserNickname,
            SCDictionaryKeys.shared.kSCRankItemSortKey: @(mySort)
        };
        [self.myItem configWithDict:myDict];
    }
    
    [self adjustTableHeaderViewHeight:_headerView];
    [self.tableView reloadData];
    
}

-(void) configAnchorWithDict:(NSDictionary *) rankDict{
    _anchorRankDict = rankDict;
    NSArray<NSDictionary *> *rankDataArray = [SCAnchorRankModel rankDataFromDict:rankDict];
    if([rankDataArray count] >= offSetNum){
        NSDictionary *itemDict = rankDataArray[0];
        NSString *avatar = [SCAnchorRankItemModel avatarFromDict:itemDict];
        NSString *nickname = [SCAnchorRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking1 configWithAvatar:avatar name:nickname];

        itemDict = rankDataArray[1];
        avatar = [SCAnchorRankItemModel avatarFromDict:itemDict];
        nickname = [SCAnchorRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking2 configWithAvatar:avatar name:nickname];

        itemDict = rankDataArray[2];
        avatar = [SCAnchorRankItemModel avatarFromDict:itemDict];
        nickname = [SCAnchorRankItemModel nicknameFromDict:itemDict];
        [_headerView.ranking3 configWithAvatar:avatar name:nickname];
    }
    NSString *monthName = [SCAnchorRankModel monthNameFromDict:rankDict];
    [_headerView.subTitleBtn setTitle:monthName forState:UIControlStateNormal];
    
    if(_type.rankType == SCRankingTypeRich){
        __block NSInteger mySort = 100000;
        NSArray<NSDictionary *> *rankDataArray = [SCAnchorRankModel rankDataFromDict:rankDict];
        [rankDataArray enumerateObjectsUsingBlock:^(NSDictionary * _Nonnull itemDict, NSUInteger idx, BOOL * _Nonnull stop) {
            NSString *userID = [SCAnchorRankItemModel userIDFromDict:itemDict];
            if([userID isEqualToString:kSCCurrentUserID]){
                mySort = [SCAnchorRankItemModel sortFromDict:itemDict];
            }
        }];

        // 创建当前用户的字典数据
        NSDictionary *myDict = @{
            SCDictionaryKeys.shared.kSCRankItemUserIDKey: kSCCurrentUserID,
            SCDictionaryKeys.shared.kSCRankItemAvatarKey: kSCCurrentUserAvatarUrl,
            SCDictionaryKeys.shared.kSCRankItemAvatarMapPathKey: [SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:@"avatarMapPath" defaultValue:@""],
            SCDictionaryKeys.shared.kSCRankItemNicknameKey: kSCCurrentUserNickname,
            SCDictionaryKeys.shared.kSCRankItemSortKey: @(mySort)
        };
        [self.myItem configWithDict:myDict];
    }
    
    [self adjustTableHeaderViewHeight:_headerView];
    [self.tableView reloadData];
    
}

- (void)adjustTableHeaderViewHeight:(UIView *)headerView {
    [headerView setNeedsLayout];
    [headerView layoutIfNeeded];
    
    // 根据内容计算适合的高度
    CGFloat height = [headerView systemLayoutSizeFittingSize:UILayoutFittingCompressedSize].height;
    CGRect frame = headerView.frame;
    frame.size.height = height;
    headerView.frame = frame;
    
    self.tableView.tableHeaderView = headerView; // 重新赋值以更新高度
}

- (void)requestRankData {
    kWeakSelf(self)
    if(_type.rankType == SCRankingTypeRegion){
        [SCAPIServiceManager requestUserRankList:50 success:^(NSDictionary * _Nonnull rankDict) {
            [weakself.tableView.mj_header endRefreshing];
            [weakself configWithDict:rankDict];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself.tableView.mj_header endRefreshing];
        }];
    }else if(_type.rankType == SCRankingTypeRich){
        [SCAPIServiceManager requestAnchorRankList:50 success:^(NSDictionary * _Nonnull rankDict) {
            [weakself.tableView.mj_header endRefreshing];
            [weakself configAnchorWithDict:rankDict];

        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself.tableView.mj_header endRefreshing];
        }];
    }
}
@end
