//
//  SCRankingItemView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingItemView.h"
#import <Masonry/Masonry.h>
#import "SCUserRankModel.h"
#import "SCFontManager.h"

@implementation SCRankingItemView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}

- (void) _initUI{
    
    [self setBackgroundColor:[UIColor clearColor]];
    self.contentView = [[UIView alloc] init];
    [self.contentView setBackgroundColor: [UIColor colorWithHexString:@"#500E0E"]];
    [self addSubview:self.contentView];
    
    _bgButon = [UIButton buttonWithType:UIButtonTypeCustom];
    _bgButon.userInteractionEnabled = NO;
    _bgButon.hidden = YES;
    NSArray *colors = @[[UIColor colorWithHexString:@"#FF423E"], [UIColor colorWithHexString:@"#740A31"]];
    NSArray *locations = @[@(0), @(1.0f)];
    CGPoint startPoint = kScAuthMar.isLanguageForce ? CGPointMake(1, 0.64) : CGPointMake(0, 0.28);
    CGPoint endPoint = kScAuthMar.isLanguageForce ? CGPointMake(0, 0.28) : CGPointMake(1, 0.64);
    [_bgButon sc_setGradientBackgroundWithColors:colors gradientLocations:locations startPoint:startPoint endPoint:endPoint cornerRadius:15.0f];
    [self.contentView addSubview:_bgButon];
    
    _rankingLabel = [[UILabel alloc] init];
    _rankingLabel.font = [SCFontManager boldItalicFontWithSize:24.0f];
    _rankingLabel.textColor = [UIColor colorWithHexString:@"#FF4141"];
    [self.contentView addSubview:_rankingLabel];
    
    _avatarIV = [[UIImageView alloc] init].setContentMode(UIViewContentModeScaleAspectFill).setCornerRadius(50/2.0f).addSuperView(_avatarIV);
    [self.contentView addSubview:_avatarIV];
    
    _nickNameLabel = [[UILabel alloc] init];
    _nickNameLabel.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft;
    _nickNameLabel.font = kScUIFontMedium(14);
    _nickNameLabel.textColor = [UIColor scWhite];
    [self.contentView addSubview:_nickNameLabel];
    
    [_bgButon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [_rankingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.leading.equalTo(self.contentView).offset(18);
        
    }];
    [_avatarIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.leading.equalTo(self.rankingLabel.mas_trailing).offset(10);
        make.size.mas_equalTo(CGSizeMake(50, 50));
    }];
    
    [_nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.leading.equalTo(self.avatarIV.mas_trailing).offset(12);
    }];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self);
        make.bottom.equalTo(self).offset(0);
    }];
}
#pragma mark - 字典版本配置方法

-(void) configWithDict:(NSDictionary *)itemDict{
    NSInteger sort = [SCUserRankItemModel sortFromDict:itemDict];
    if(sort > 50){
        _rankingLabel.text = @"50+";
        _rankingLabel.textColor = [UIColor scWhite];
    }else{
        _rankingLabel.text = [NSString stringWithFormat:@"%ld",sort];
        _rankingLabel.textColor = [UIColor colorWithHexString:@"#FF4141"];
    }

    NSString *avatar = [SCUserRankItemModel avatarFromDict:itemDict];
    [_avatarIV sc_setImageWithURL:avatar placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    NSString *nickname = [SCUserRankItemModel nicknameFromDict:itemDict];
    [_nickNameLabel setText:[nickname replaceMoreThan10]];
}

-(void) configAnchorWithDict:(NSDictionary *)itemDict{
    NSInteger sort = [SCAnchorRankItemModel sortFromDict:itemDict];
    if(sort > 50){
        _rankingLabel.text = @"50+";
        _rankingLabel.textColor = [UIColor scWhite];
    }else{
        _rankingLabel.text = [NSString stringWithFormat:@"%ld",sort];
        _rankingLabel.textColor = [UIColor colorWithHexString:@"#FF4141"];
    }

    NSString *avatar = [SCAnchorRankItemModel avatarFromDict:itemDict];
    [_avatarIV sc_setImageWithURL:avatar placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    NSString *nickname = [SCAnchorRankItemModel nicknameFromDict:itemDict];
    [_nickNameLabel setText:[nickname replaceMoreThan10]];
}

@end
