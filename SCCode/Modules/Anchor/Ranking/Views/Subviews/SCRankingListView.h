//
//  SCRankingListView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/25.
//

#import <UIKit/UIKit.h>
#import <JXCategoryView/JXCategoryView.h>
@class SCRankingTypeModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCRankingListView : UIView<JXCategoryListContentViewDelegate>
@property (nullable,nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, strong) UITableView *tableView;

- (instancetype)initWithType:(SCRankingTypeModel *) type;
- (void)beginFirstRefresh;
@end

NS_ASSUME_NONNULL_END
