//
//  SCRankingViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCRankingViewController.h"
#import "SCRankingTitleView.h"
#import "SCNavigationBar.h"
#import <Masonry/Masonry.h>
#import "SCRankingListView.h"
#import "SCUserRankModel.h"
#import "SCGradientLineView.h"
#import "SCFontManager.h"

@interface SCRankingViewController ()<JXCategoryViewDelegate,JXCategoryListContainerViewDelegate>

@property (nonatomic, strong) UIScrollView *currentListView;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;
//titleView
@property(nonatomic,nonnull,strong) JXCategoryTitleView * scTitleView;
@property(nonatomic,nonnull,strong) NSArray<SCRankingTypeModel *> * rankingTypes;
@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);


@end

@implementation SCRankingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor clearColor];
    self.scContentView.backgroundColor = [UIColor clearColor];
    UIImageView *bgImageView = [[UIImageView alloc]initWithImage:[SCResourceManager loadImageWithName:@"bg_rank"]];
    [self.view addSubview:bgImageView];
    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];

    [self.view bringSubviewToFront:self.scNavigationBar];
    [self.view bringSubviewToFront:self.scContentView];
    
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    
    _rankingTypes = @[[[SCRankingTypeModel alloc] initWithType:SCRankingTypeRegion title:@"Charm".translateString],[[SCRankingTypeModel alloc] initWithType:SCRankingTypeRich title:@"Rich".translateString]];
    
    NSMutableArray * _titles = @[].mutableCopy;
    [_rankingTypes enumerateObjectsUsingBlock:^(SCRankingTypeModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [_titles addObject:obj.title];
    }];
    _scTitleView = [[JXCategoryTitleView alloc] initWithFrame:CGRectMake(0, 0, [_titles count] * (70), 50)];
    
    self.scTitleView.titles = _titles;
    self.scTitleView.backgroundColor = [UIColor clearColor];
    self.scTitleView.delegate = self;
    self.scTitleView.titleSelectedColor = [UIColor scWhite];
    self.scTitleView.titleColor = [UIColor scWhite];
    self.scTitleView.titleColorGradientEnabled = YES;
    self.scTitleView.titleFont = [SCFontManager boldItalicFontWithSize:16];
    self.scTitleView.titleSelectedFont = [SCFontManager boldItalicFontWithSize:20];
    self.scTitleView.cellSpacing = 16;
    [self.scNavigationBar addSubview:self.scTitleView];
    
    //标题指示器
    SCGradientLineView *lineView = [[SCGradientLineView alloc] init];
    lineView.indicatorColor = [UIColor clearColor];
    lineView.indicatorWidthIncrement = 10;
    lineView.indicatorHeight = 8;
    lineView.verticalMargin = 8;
    self.scTitleView.indicators = @[lineView];
    
    [self.scTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo([[self rankingTypes] count] * (70));
        make.height.equalTo(self.scNavigationBar.contentView).offset(0);
        make.center.equalTo(self.scNavigationBar.contentView).offset(0);
    }];
    
    self.listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    [self.scContentView addSubview:self.listContainerView];

    self.scTitleView.listContainer = self.listContainerView;
    
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.top.equalTo(self.scContentView).offset(0);
    }];
}

#pragma mark - JXCategoryListContainerViewDelegate

- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return [self.rankingTypes count];
}

- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {

    SCRankingListView *partnerListView = [[SCRankingListView alloc] initWithType:self.rankingTypes[index]];
    self.currentListView = partnerListView.tableView;
    [partnerListView beginFirstRefresh];
    return partnerListView;
    
}



#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    //根据选中的下标，实时更新currentListView
    SCRankingListView *list = (SCRankingListView *)self.listContainerView.validListDict[@(index)];
    self.currentListView = list.tableView;
}


@end

