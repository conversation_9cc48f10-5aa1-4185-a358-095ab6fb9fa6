//
//  SCAnchorViewModel.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorViewModel.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代

@implementation SCAnchorViewModel

- (instancetype)init{
    self = [super init];
    if(self){
        [self _init];
    }
    return self;
}

-(void) _init{
    //获取首页标签 - 从策略字典中获取主播墙标签列表
    _tags = [SCDictionaryHelper arrayFromDictionary:kScAuthMar.strategyObs.value forKey:@"broadcasterWallTagList" defaultValue:@[]];
    _selectCountryCode = [[SCObservable<NSString *> alloc] initWithValue:nil];
    _isSelectFirstItem = [[SCObservable<NSNumber *> alloc] initWithValue:@(NO)];
}

@end
