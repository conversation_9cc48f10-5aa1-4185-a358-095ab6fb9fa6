//
//  SCAnchorRankModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/25.
//

#import "SCUserRankModel.h"
#import "SCDictionaryKeys.h"
#import "SCDictionaryHelper.h"

@implementation SCUserRankModel

#pragma mark - 字典访问方法

+ (NSString *)monthNameFromDict:(NSDictionary *)rankDict {
    return [SCDictionaryHelper stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankMonthNameKey defaultValue:@""];
}

+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict {
    NSArray *rawArray = [SCDictionaryHelper arrayFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankDataKey defaultValue:@[]];
    NSMutableArray *result = [NSMutableArray array];

    for (id item in rawArray) {
        if ([item isKindOfClass:[NSDictionary class]]) {
            [result addObject:(NSDictionary *)item];
        }
    }

    return [result copy];
}

+ (NSString *)sortNoFromDict:(NSDictionary *)rankDict {
    return [SCDictionaryHelper stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankSortNoKey defaultValue:@""];
}

@end
@implementation SCUserRankItemModel

#pragma mark - 字典访问方法

+ (NSString *)avatarFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemAvatarKey defaultValue:@""];
}

+ (NSString *)avatarMapPathFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemAvatarMapPathKey defaultValue:@""];
}

+ (NSString *)nicknameFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemNicknameKey defaultValue:@""];
}

+ (NSInteger)sortFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemSortKey defaultValue:0];
}

+ (NSString *)userIDFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemUserIDKey defaultValue:@""];
}

@end

@implementation SCAnchorRankModel

#pragma mark - 字典访问方法

+ (NSString *)monthNameFromDict:(NSDictionary *)rankDict {
    return [SCDictionaryHelper stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankMonthNameKey defaultValue:@""];
}

+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict {
    NSArray *rawArray = [SCDictionaryHelper arrayFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankDataKey defaultValue:@[]];
    NSMutableArray *result = [NSMutableArray array];

    for (id item in rawArray) {
        if ([item isKindOfClass:[NSDictionary class]]) {
            [result addObject:(NSDictionary *)item];
        }
    }

    return [result copy];
}

+ (NSString *)sortNoFromDict:(NSDictionary *)rankDict {
    return [SCDictionaryHelper stringFromDictionary:rankDict forKey:SCDictionaryKeys.shared.kSCRankSortNoKey defaultValue:@""];
}

@end

@implementation SCAnchorRankItemModel

#pragma mark - 字典访问方法

// 主播特有属性访问方法
+ (NSInteger)broadcasterIdFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankBroadcasterIdKey defaultValue:0];
}

+ (NSString *)broadcasterNameFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankBroadcasterNameKey defaultValue:@""];
}

+ (NSInteger)broadcasterOnlineTimeFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankOnlineTimeKey defaultValue:0];
}

+ (NSInteger)guildIdFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankGuildIdKey defaultValue:0];
}

+ (NSString *)guildNameFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankGuildNameKey defaultValue:@""];
}

+ (NSInteger)totalIncomeCoinsFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCAnchorRankTotalIncomeKey defaultValue:0];
}

// 通用属性访问方法
+ (NSString *)avatarFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemAvatarKey defaultValue:@""];
}

+ (NSString *)avatarMapPathFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemAvatarMapPathKey defaultValue:@""];
}

+ (NSString *)nicknameFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemNicknameKey defaultValue:@""];
}

+ (NSInteger)sortFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper integerFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemSortKey defaultValue:0];
}

+ (NSString *)userIDFromDict:(NSDictionary *)itemDict {
    return [SCDictionaryHelper stringFromDictionary:itemDict forKey:SCDictionaryKeys.shared.kSCRankItemUserIDKey defaultValue:@""];
}

@end

@implementation SCRankingTypeModel

- (instancetype)initWithType:(SCRankingType)rankType title:(NSString *)title{
    self = [super init];
    if(self){
        _rankType = rankType;
        _title = title;
    }
    return self;
}

#pragma mark - 字典访问方法

+ (SCRankingType)rankTypeFromDict:(NSDictionary *)typeDict {
    NSInteger typeValue = [SCDictionaryHelper integerFromDictionary:typeDict forKey:SCDictionaryKeys.shared.kSCRankingTypeKey defaultValue:0];
    return (SCRankingType)typeValue;
}

+ (NSString *)titleFromDict:(NSDictionary *)typeDict {
    return [SCDictionaryHelper stringFromDictionary:typeDict forKey:SCDictionaryKeys.shared.kSCRankingTitleKey defaultValue:@""];
}

+ (NSDictionary *)dictFromRankType:(SCRankingType)rankType title:(NSString *)title {
    return @{
        SCDictionaryKeys.shared.kSCRankingTypeKey: @(rankType),
        SCDictionaryKeys.shared.kSCRankingTitleKey: title ?: @""
    };
}

@end
