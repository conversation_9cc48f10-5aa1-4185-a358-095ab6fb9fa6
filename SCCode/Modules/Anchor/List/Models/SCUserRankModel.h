//
//  SCAnchorRankModel.h
//  Supercall
//
//  Created by guanweihong on 2023/12/25.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

#pragma mark - 排行榜字典访问工具类

/// 用户排行榜字典访问工具类
@interface SCUserRankModel : NSObject

#pragma mark - 字典访问方法
/// 从字典获取月份名称
+ (NSString *)monthNameFromDict:(NSDictionary *)rankDict;
/// 从字典获取排行数据数组（字典数组）
+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict;
/// 从字典获取排序号
+ (NSString *)sortNoFromDict:(NSDictionary *)rankDict;

@end

/// 用户排行榜项字典访问工具类
@interface SCUserRankItemModel : NSObject

#pragma mark - 字典访问方法
/// 从字典获取头像URL
+ (NSString *)avatarFromDict:(NSDictionary *)itemDict;
/// 从字典获取头像映射路径
+ (NSString *)avatarMapPathFromDict:(NSDictionary *)itemDict;
/// 从字典获取昵称
+ (NSString *)nicknameFromDict:(NSDictionary *)itemDict;
/// 从字典获取排序
+ (NSInteger)sortFromDict:(NSDictionary *)itemDict;
/// 从字典获取用户ID
+ (NSString *)userIDFromDict:(NSDictionary *)itemDict;

@end

/// 主播排行榜字典访问工具类
@interface SCAnchorRankModel : NSObject

#pragma mark - 字典访问方法
/// 从字典获取月份名称
+ (NSString *)monthNameFromDict:(NSDictionary *)rankDict;
/// 从字典获取主播排行数据数组（字典数组）
+ (NSArray<NSDictionary *> *)rankDataFromDict:(NSDictionary *)rankDict;
/// 从字典获取排序号
+ (NSString *)sortNoFromDict:(NSDictionary *)rankDict;

@end

/// 主播排行榜项字典访问工具类
@interface SCAnchorRankItemModel : NSObject

#pragma mark - 字典访问方法
// 主播特有属性访问方法
/// 从字典获取主播ID
+ (NSInteger)broadcasterIdFromDict:(NSDictionary *)itemDict;
/// 从字典获取主播名称
+ (NSString *)broadcasterNameFromDict:(NSDictionary *)itemDict;
/// 从字典获取主播在线时间
+ (NSInteger)broadcasterOnlineTimeFromDict:(NSDictionary *)itemDict;
/// 从字典获取公会ID
+ (NSInteger)guildIdFromDict:(NSDictionary *)itemDict;
/// 从字典获取公会名称
+ (NSString *)guildNameFromDict:(NSDictionary *)itemDict;
/// 从字典获取总收入金币
+ (NSInteger)totalIncomeCoinsFromDict:(NSDictionary *)itemDict;

// 通用属性访问方法
/// 从字典获取头像URL
+ (NSString *)avatarFromDict:(NSDictionary *)itemDict;
/// 从字典获取头像映射路径
+ (NSString *)avatarMapPathFromDict:(NSDictionary *)itemDict;
/// 从字典获取昵称
+ (NSString *)nicknameFromDict:(NSDictionary *)itemDict;
/// 从字典获取排序
+ (NSInteger)sortFromDict:(NSDictionary *)itemDict;
/// 从字典获取用户ID
+ (NSString *)userIDFromDict:(NSDictionary *)itemDict;

@end

//排行榜类型 枚举
typedef NS_ENUM(NSInteger, SCRankingType) {
    SCRankingTypeRegion = 0,
    SCRankingTypeRich = 1,
};
@interface SCRankingTypeModel : NSObject
@property (nonatomic, assign) SCRankingType rankType;
@property (nonatomic,copy) NSString * title;

///构造方法
-(instancetype) initWithType:(SCRankingType)rankType title:(NSString *) title;

#pragma mark - 字典访问方法
/// 从字典获取排行榜类型
+ (SCRankingType)rankTypeFromDict:(NSDictionary *)typeDict;
/// 从字典获取标题
+ (NSString *)titleFromDict:(NSDictionary *)typeDict;
/// 将排行榜类型转换为字典
+ (NSDictionary *)dictFromRankType:(SCRankingType)rankType title:(NSString *)title;

@end



NS_ASSUME_NONNULL_END

