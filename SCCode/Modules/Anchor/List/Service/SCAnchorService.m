//
//  SCAnchorService.h
//  Supercall
//
//  Created by sumengliu on 2024/12/9.
//

#import "SCAnchorService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorService()

@end

@implementation SCAnchorService

- (instancetype)initWithUserId:(NSString *)userId
{
    self = [super initWithUserId:userId];
    if (self) {
        
    }
    return self;
}

- (void)destroyService{
    [super destroyService];
    _anchorListModel = nil;
}

@end
