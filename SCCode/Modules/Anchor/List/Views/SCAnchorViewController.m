//
//  AnchorViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCAnchorViewController.h"
#import <JXCategoryView/JXCategoryTitleView.h>
#import <JXCategoryView/JXCategoryIndicatorLineView.h>
#import "SCAnchorSubTagViewController.h"
#import <Masonry/Masonry.h>
#import "SCAnchorViewModel.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCRankingViewController.h"
#import "SCNavigationBar.h"
#import "SCGradientLineView.h"
#import "SCCountrySelectPopoupView.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorViewController()<JXCategoryViewDelegate,JXCategoryListContainerViewDelegate>
//一级标签
@property(nonnull,strong,nonatomic) JXCategoryTitleView * tagView;
//标签数据
@property(nonnull,strong,nonatomic) SCAnchorViewModel * viewModel;
@property (nonatomic, weak) UIButton *filterCountryBtn;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;

//标题背景
@property(nonatomic,weak) UIImageView * headerBgIV;

- (void)_rankingBtnClick:(UIButton *)sender;
- (void) _onClickFilterCountry;

@end



@implementation SCAnchorViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initData];
    [self _initUI];
    
}
-(void) _initData{
    _viewModel = [[SCAnchorViewModel alloc] init];
}
-(void) _initUI{
    //隐藏父类导航栏
    [self.scNavigationBar setHidden:true];
    
    _tagView = [[JXCategoryTitleView alloc] initWithFrame:CGRectMake(0, kSCSafeAreaBottomHeight, [self.viewModel.tags count] * 83, 40)];
    //viewModel.tags转字符串数组
    NSMutableArray<NSString *> *titles = @[].mutableCopy;
    [self.viewModel.tags enumerateObjectsUsingBlock:^(NSDictionary * _Nonnull tagDict, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *tagName = [SCDictionaryHelper stringFromDictionary:tagDict forKey:SCDictionaryKeys.shared.kSCTagNameKey defaultValue:@""];
        [titles addObject:tagName];
    }];
    
    self.tagView.titles = titles;
    self.tagView.backgroundColor = [UIColor clearColor];
    self.tagView.delegate = self;
    self.tagView.titleSelectedColor = [UIColor scWhite];
    self.tagView.titleColor = [UIColor scWhite];
    self.tagView.titleColorGradientEnabled = YES;
    self.tagView.titleFont = [SCFontManager boldItalicFontWithSize:16];
    self.tagView.titleSelectedFont = [SCFontManager boldItalicFontWithSize:20];
    self.tagView.cellSpacing = 16;
    [self.view addSubview:self.tagView];
    
    //标题指示器
    SCGradientLineView *lineView = [[SCGradientLineView alloc] init];
    lineView.indicatorColor = [UIColor clearColor];
    lineView.indicatorWidthIncrement = 10;
    lineView.indicatorHeight = 8;
    lineView.verticalMargin = 6;
    self.tagView.indicators = @[lineView];
    
    UIButton *rankingBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [rankingBtn setImage:[SCResourceManager loadImageWithName:@"ic_nav_ranking"] forState:UIControlStateNormal];
    [self.view addSubview:rankingBtn];
    [rankingBtn addTarget:self action:@selector(_rankingBtnClick:) forControlEvents:UIControlEventTouchUpInside];
    [rankingBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(47);
        make.trailing.equalTo(self.view).offset(0);
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight);
    }];
    
    UIButton *filterCountryBtn = [UIButton buttonWithImageName:@"ic_anchor_filter_country" target:self action:@selector(_onClickFilterCountry)].addSuperView(self.view);
    [filterCountryBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(47);
        make.trailing.equalTo(rankingBtn.mas_leading).offset(0);
        make.top.equalTo(rankingBtn);
    }];
    self.filterCountryBtn = filterCountryBtn;
    
    self.listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    self.listContainerView.backgroundColor = [UIColor clearColor];
    self.listContainerView.listCellBackgroundColor = [UIColor clearColor];
    self.listContainerView.scrollView.backgroundColor = [UIColor clearColor];
    
    [self.view addSubview:self.listContainerView];
    
    self.tagView.listContainer = self.listContainerView;
    
    //导航栏左滑关闭 只有在第一个生效
    self.navigationController.interactivePopGestureRecognizer.enabled = (self.tagView.selectedIndex == 0);
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kSCSafeAreaTopHeight+5);
        make.leading.equalTo(self.view);
        make.width.mas_equalTo([self.viewModel.tags count] * 83);
        make.height.mas_equalTo(40);
    }];
    
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tagView.mas_bottom);
        make.leading.trailing.equalTo(self.view);
        make.bottom.equalTo(self.view).offset(0);
    }];
    [self.viewModel.isSelectFirstItem subscribe:^(NSNumber * _Nullable value) {
        filterCountryBtn.hidden = !value.boolValue;
    } error:nil disposeBag:self.disposeBag];
}

#pragma mark - JXCategoryViewDelegate


//为什么会把选中代理分为三个，因为有时候只关心点击选中的，有时候只关心滚动选中的，有时候只关心选中。所以具体情况，使用对应方法。
/**
 点击选中或者滚动选中都会调用该方法。适用于只关心选中事件，不关心具体是点击还是滚动选中的。

 @param categoryView categoryView对象
 @param index 选中的index
 */
- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index{
    
}

/**
 点击选中的情况才会调用该方法

 @param categoryView categoryView对象
 @param index 选中的index
 */
- (void)categoryView:(JXCategoryBaseView *)categoryView didClickSelectedItemAtIndex:(NSInteger)index{
    
}

/**
 滚动选中的情况才会调用该方法

 @param categoryView categoryView对象
 @param index 选中的index
 */
- (void)categoryView:(JXCategoryBaseView *)categoryView didScrollSelectedItemAtIndex:(NSInteger)index{
    
}

/**
 控制能否点击Item

 @param categoryView categoryView对象
 @param index 准备点击的index
 @return 能否点击
 */
- (BOOL)categoryView:(JXCategoryBaseView *)categoryView canClickItemAtIndex:(NSInteger)index{
    
    return YES;
}

/**
 正在滚动中的回调

 @param categoryView categoryView对象
 @param leftIndex 正在滚动中，相对位置处于左边的index
 @param rightIndex 正在滚动中，相对位置处于右边的index
 @param ratio 从左往右计算的百分比
 */
- (void)categoryView:(JXCategoryBaseView *)categoryView scrollingFromLeftIndex:(NSInteger)leftIndex toRightIndex:(NSInteger)rightIndex ratio:(CGFloat)ratio{
    
}

#pragma mark - JXCategoryListContainerViewDelegate


- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    return self.viewModel.tags.count;
}

- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    SCAnchorSubTagViewController * subTagVC = [[SCAnchorSubTagViewController alloc] init];
    subTagVC.tag = self.viewModel.tags[index];
    subTagVC.tagIndex = index;
    subTagVC.viewModel = self.viewModel;
    return subTagVC;
}


#pragma mark - JXPagerMainTableViewGestureDelegate

#pragma mark - Action 点击事件
- (void)_rankingBtnClick:(UIButton *)sender{
    //转跳到排行榜
    SCRankingViewController * rankVC = [[SCRankingViewController alloc] init];
    [self.navigationController pushViewController:rankVC animated:YES];
}
//点击筛选国家
- (void) _onClickFilterCountry{
    kWeakSelf(self);
    [SCCountrySelectPopoupView showFromPoint:CGPointMake(15, 44+kSCSafeAreaTopHeight) superView:self.view defaultCountry:self.viewModel.selectCountryCode.value selectBlock:^(SCCountryModel * _Nonnull country) {
        weakself.viewModel.selectCountryCode.value = country.code;
        
        if (country.code) {
            [weakself.filterCountryBtn setTitle:country.emoji forState:UIControlStateNormal];
            [weakself.filterCountryBtn setImage:nil forState:UIControlStateNormal];
        } else {
            [weakself.filterCountryBtn setTitle:@"" forState:UIControlStateNormal];
            [weakself.filterCountryBtn setImage:[SCResourceManager loadImageWithName:@"ic_anchor_filter_country"] forState:UIControlStateNormal];
        }        
    }];
}
@end
