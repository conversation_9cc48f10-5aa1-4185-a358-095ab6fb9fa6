//
//  SCAnchorListViewController.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCBaseViewController.h"
#import "SCAnchorViewModel.h"
#import <JXCategoryView/JXCategoryView.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCAnchorSubTagViewController : UIViewController <JXCategoryListContentViewDelegate>
@property (nonatomic, strong) UIScrollView *contentScrollView;
@property (nonatomic, strong) NSDictionary * tag; // 字典版本标签数据
@property (nonatomic, assign) NSInteger tagIndex;
@property (nonatomic, assign) SCAnchorViewModel *viewModel;
@end

NS_ASSUME_NONNULL_END
