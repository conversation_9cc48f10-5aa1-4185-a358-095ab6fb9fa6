//
//  SCRoundedLabelView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCRoundedLabelView.h"
#import <Masonry/Masonry.h>

@implementation SCRoundedLabelView

- (instancetype)init {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        // 设置背景颜色和圆角
        self.backgroundColor = [UIColor blueColor];
        self.layer.cornerRadius = 10.0;
        
        // 添加标签
        self.label = [[UILabel alloc] init];
        self.label.textColor = [UIColor whiteColor];
        self.label.font = kScUIFontRegular(10);
        [self addSubview:self.label];
        
        // 使用 Auto Layout 进行布局
        [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self).offset(4);
            make.top.equalTo(self).offset(0);
            make.trailing.equalTo(self).offset(-4);
            make.bottom.equalTo(self).offset(0);
            make.width.equalTo(@0);
        }];
    }
    return self;
}

- (void)updateLeftRight:(CGFloat)padding {
    [self.label mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(padding);
        make.trailing.equalTo(self).offset(-padding);
    }];
}

- (void)setText:(NSString *)text {
    self.label.text = text;
    
    // 计算文本的尺寸
    CGSize labelSize = [self.label sizeThatFits:CGSizeMake(CGFLOAT_MAX, 0.0)];
    
    // 调整 RoundedLabelView 的宽度
    [self.label mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(labelSize.width)); // 16 为左右内边距
    }];
}
@end
