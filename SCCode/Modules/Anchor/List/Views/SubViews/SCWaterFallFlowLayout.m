//
//  SCWaterFallFlowLayout.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/22.
//

#import "SCWaterFallFlowLayout.h"

@implementation SCWaterFallFlowLayout

- (instancetype)init {
    self = [super init];
    if (self) {
        self.cols = 2;
        self.layoutAttributeArray = [NSMutableArray array];
        self.yArray = [NSMutableArray arrayWithCapacity:self.cols];
        for (NSInteger i = 0; i < self.cols; i++) {
            [self.yArray addObject:@(self.sectionInset.top+self.offSetTop)];
        }
        self.maxHeight = 0;
    }
    return self;
}
- (void)prepareLayout {
    [super prepareLayout];
    
    CGFloat itemWidth = (self.collectionView.bounds.size.width - self.sectionInset.left - self.sectionInset.right - self.minimumInteritemSpacing * (CGFloat)(self.cols - 1)) / (CGFloat)self.cols;
    NSInteger itemCount = [self.collectionView numberOfItemsInSection:0];
    
    NSInteger minHeightIndex = 0;
    
    if (itemCount > self.layoutAttributeArray.count) {
        for (NSInteger i = self.layoutAttributeArray.count; i < itemCount; i++) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:i inSection:0];
            UICollectionViewLayoutAttributes *attr = [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:indexPath];
            
            CGFloat itemHeight = [self.delegate waterFlowLayout:self itemHeightForIndexPath:indexPath];
            
            minHeightIndex = [self.yArray[0] intValue] > [self.yArray[1] intValue] ? 1 : 0;
            
            CGFloat itemY = [self.yArray[minHeightIndex] floatValue];
            if (i >= self.cols) {
                itemY += self.minimumInteritemSpacing;
            }
            
            CGFloat itemX = self.sectionInset.left + (itemWidth + self.minimumInteritemSpacing) * (CGFloat)minHeightIndex;
            attr.frame = CGRectMake(itemX, itemY, itemWidth, itemHeight);
            
            [self.layoutAttributeArray addObject:attr];
            self.yArray[minHeightIndex] = @(CGRectGetMaxY(attr.frame));
        }
    }
    int index = self.yArray[0] < self.yArray[1] ? 1 : 0;
    CGFloat maxValue = [self.yArray[index] floatValue];
    self.maxHeight = maxValue + self.sectionInset.bottom;
}

- (void)resetLayout {
    [self.layoutAttributeArray removeAllObjects];
    self.yArray = [NSMutableArray arrayWithCapacity:self.cols];
    for (NSInteger i = 0; i < self.cols; i++) {
        [self.yArray addObject:@(self.sectionInset.top+self.offSetTop)];
    }
    [self prepareLayout];
    [self.collectionView invalidateIntrinsicContentSize];
    [self.collectionView reloadData];
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    return [self.layoutAttributeArray filteredArrayUsingPredicate:[NSPredicate predicateWithBlock:^BOOL(UICollectionViewLayoutAttributes * _Nullable evaluatedObject, NSDictionary<NSString *,id> * _Nullable bindings) {
        return CGRectIntersectsRect(evaluatedObject.frame, rect);
    }]];
}

- (CGSize)collectionViewContentSize {
    return CGSizeMake(self.collectionView.bounds.size.width, self.maxHeight);
}
@end
