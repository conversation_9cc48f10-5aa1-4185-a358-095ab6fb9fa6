//
//  SCGradientLineView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/18.
//

#import "SCGradientLineView.h"

@implementation SCGradientLineView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        // 创建渐变色
        self.gradientLayer = [CAGradientLayer layer];
        self.gradientLayer.colors = @[(__bridge id)[UIColor colorWithHexString:@"#FF0000"].CGColor, (__bridge id)[UIColor colorWithHexString:@"#460606"].CGColor];
        // 设置渐变的方向（可根据需要调整）
        self.gradientLayer.startPoint = CGPointMake(0, 0);
        self.gradientLayer.endPoint = CGPointMake(1, 0);
        [self.layer addSublayer:self.gradientLayer];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];
    
    // 设置渐变色的大小和位置
    self.gradientLayer.frame = self.bounds;
}

@end
