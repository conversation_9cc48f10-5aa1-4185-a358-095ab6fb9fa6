//
//  SCAnchorCollectionView.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorCollectionView.h"
#import "MJRefresh.h"
#import "SCWaterFallFlowLayout.h"
#import <Masonry/Masonry.h>
#import "SCAnchorCell.h"
#import "MJRefresh.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCAPIServiceManager.h"
#import "SCBannerService.h"
#import "SCAnchorService.h"
#import "SCCoinsFullScreenHeaderView.h"
#import "SCOnlineStatesService.h"
#import "UIScrollView+EmptyDataSet.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

static CGFloat kColletionMarginLR = 10.0f;
static CGFloat kCollectionItemSpacing = 8.0f;

@interface SCAnchorCollectionView()<UICollectionViewDelegate, UICollectionViewDataSource,SCWaterFallFlowLayoutDelegate, DZNEmptyDataSetSource, DZNEmptyDataSetDelegate,UICollectionViewDelegateFlowLayout>

@property (nonatomic, assign) int page;
@property (nonatomic, strong) NSMutableArray<NSDictionary *> *anchorDicts;
@property (nonatomic, assign) int pageSize;

//是否第一项
@property (nonatomic, assign) BOOL isFirstItem;

@property(nonatomic,copy,nonnull) NSString * tagStr;
@property(nonatomic,copy,nonnull) NSString * subTagStr;
@property(nonatomic,strong) SCDisposeBag * disposeBag;
@property(nonatomic,nullable,weak) SCBannerView * bannerView;
@property(nonatomic,strong) SCOnlineStatusSubscribe * onlineStatusSub;

// 是否已经请求过
@property (nonatomic, assign) BOOL hasRequestData;

@end



@implementation SCAnchorCollectionView



- (instancetype)initWithTag:(NSString * _Nonnull) tagStr subTag:(NSString *_Nonnull) subTagStr tagIndex:(NSInteger) tagIndex subIndex:(NSInteger) subIndex viewModel:(SCAnchorViewModel * _Nonnull)viewModel{
    self = [super init];
    if (self) {
        _disposeBag = [[SCDisposeBag alloc] init];
        _tagStr = tagStr;
        _subTagStr = subTagStr;
        _pageSize = 20;
        _anchorDicts = [NSMutableArray new];
        _isFirstItem = (tagIndex == 0 && subIndex == 0);
        _viewModel = viewModel;
        
        self.backgroundColor = [UIColor clearColor];
        
        // 设置 collectionview
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc]init];
        layout.minimumLineSpacing = 9.0f;
        layout.minimumInteritemSpacing = kCollectionItemSpacing;
        layout.sectionInset = UIEdgeInsetsMake(0, kColletionMarginLR, 0, kColletionMarginLR);
        
        self.collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:layout];
        // 注册 Cell
        [self.collectionView registerClass:[SCAnchorCell class] forCellWithReuseIdentifier:reuseIdentifier];
        self.collectionView.backgroundColor = [UIColor clearColor];
        self.collectionView.dataSource = self;
        self.collectionView.delegate = self;
        self.collectionView.emptyDataSetSource = self;
        self.collectionView.emptyDataSetDelegate = self;
        
        [self addSubview:self.collectionView];
        
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        [self setupRefreshControl];
        if(self.isFirstItem){//只有第一个选项 的all才有Banner
            kWeakSelf(self);
            [kScAuthMar.bannerService.bannersObs subscribe:^(NSArray<NSDictionary *> * _Nonnull value) {
                if([value count] > 0){
                    [weakself _buildHeader];
                }else{
                    [weakself removeBanner];
                }
                weakself.bannerView.banners = value;
            } error:^(SCXErrorModel * _Nonnull error) {
                [weakself removeBanner];
            } disposeBag:_disposeBag];
            //只有第一个选项才需要使用国家筛选
            [self.viewModel.selectCountryCode afterSubscribe:^(NSString * _Nullable value) {
                [weakself.collectionView.mj_header beginRefreshing];
            } error:nil disposeBag:self.disposeBag];
        }
        
        //开启在线状态监听的初始化
        _onlineStatusSub = [[SCOnlineStatusSubscribe alloc] init];
        //默认关闭
        [_onlineStatusSub pause];
        kWeakSelf(self);
        [_onlineStatusSub.changeObs subscribe:^(NSArray<NSString *> * _Nullable value) {
            [weakself onChangeOnlineStatusWithIds:value];
        } error:nil disposeBag:self.disposeBag];
        [kSCAuthOnlineStatesService add:self.onlineStatusSub dispose:self.disposeBag];
        
        
    }
    return self;
}
-(void) _buildHeader{
    if(self.bannerView == nil){
        SCBannerView * bannerView = [[SCBannerView alloc] init];
        bannerView.layer.cornerRadius = 10.0f;
        bannerView.layer.masksToBounds = true;
        [self.collectionView addSubview:bannerView];
        self.bannerView = bannerView;
        self.bannerView.frame = CGRectMake(9, 0, kSCScreenWidth - (9*2), 105);
    }
}

-(void) removeBanner{
    [self.bannerView removeFromSuperview];
    self.bannerView = nil;
}

- (void)dealloc
{
    self.scrollCallback = nil;
    [_disposeBag dispose];
    _disposeBag = nil;
}

-(void) onChangeOnlineStatusWithIds:(NSArray<NSString *> *)ids{
    for (UICollectionViewCell *cell in self.collectionView.visibleCells) {
        if ([cell isKindOfClass:[SCAnchorCell class]]) {
            SCAnchorCell *historyCell = (SCAnchorCell *)cell;
            NSIndexPath *indexPath = [self.collectionView indexPathForCell:cell];
            if (indexPath.row < self.anchorDicts.count) {
                NSDictionary *userDict = self.anchorDicts[indexPath.row];
                NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];

                NSDictionary *onlineStatusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:userID];
                if(onlineStatusDict){
                    SCAnchorStatus newStatus = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:onlineStatusDict forKey:@"status" defaultValue:AnchorStatusOffline];
                    NSString *currentStatus = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];
                    NSString *newStatusStr = [SCDictionaryHelper stringFromAnchorStatus:newStatus];

                    if(newStatus != AnchorStatusUnknown && ![currentStatus isEqualToString:newStatusStr]){
                        // 创建更新后的用户字典
                        NSMutableDictionary *updatedUserDict = [userDict mutableCopy];
                        updatedUserDict[SCDictionaryKeys.shared.kSCUserStatusKey] = newStatusStr;

                        // 更新数据源
                        self.anchorDicts[indexPath.row] = [updatedUserDict copy];
                        userDict = updatedUserDict;
                    }
                }
                [historyCell configureWithDict:userDict];
            }
        }
    }
}

- (void)layoutSubviews {
    [super layoutSubviews];
}

#pragma mark - 下拉刷新，上拉更多
- (void)setupRefreshControl {
    // 创建下拉刷新控件
    MJRefreshNormalHeader *header = [MJRefreshNormalHeader headerWithRefreshingTarget:self refreshingAction:@selector(headerRefresh)];
    header.stateLabel.textColor = UIColor.scWhite;
    [header setTitle:@"Pull down to refresh".translateString forState:MJRefreshStateIdle];
    [header setTitle:@"Release to refresh".translateString forState:MJRefreshStatePulling];
    [header setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    header.lastUpdatedTimeLabel.hidden = YES;
    self.collectionView.mj_header = header;
    
    MJRefreshAutoNormalFooter *footer = [MJRefreshAutoNormalFooter footerWithRefreshingTarget:self refreshingAction:@selector(loadMore)];
    footer.stateLabel.textColor = UIColor.scWhite;
    [footer setTitle:@"Pull up to load more".translateString forState:MJRefreshStateIdle];
    [footer setTitle:@"Release and load more".translateString forState:MJRefreshStatePulling];
    [footer setTitle:@"Loading".translateString forState:MJRefreshStateRefreshing];
    [footer setTitle:@"" forState:MJRefreshStateNoMoreData];
    footer.triggerAutomaticallyRefreshPercent = -20;
    self.collectionView.mj_footer = footer;
    
}
- (void)beginFirstRefresh {
    if (self.anchorDicts.count == 0) {
        [self.collectionView.mj_header beginRefreshing];
    }
}
- (void)headerRefresh {
    [self requestData:NO count:3];
}
- (void)loadMore {
    [self requestData:YES count:0];
}


- (void)requestData:(BOOL)isMore count:(NSInteger)count{
    
    self.hasRequestData = YES;
    
    if (isMore) {
        self.page += 1;
    } else {
        self.page = 1;
    }
    
    kWeakSelf(self)
    
    [SCAPIServiceManager requestAnchorList:self.tagStr tag:self.subTagStr region: self.isFirstItem ? self.viewModel.selectCountryCode.value:nil page:self.page pageSize:self.pageSize success:^(NSArray<NSDictionary *> * _Nonnull anchorDicts) {
        [weakself _handleSuccessWithDicts:anchorDicts isMore:isMore];
    } failure:^(SCXErrorModel * _Nonnull error) {
        if (weakself.isFirstItem && count > 0) {
            [weakself requestData:isMore count:count -1];
            return;
        }
        if (weakself.page > 1) {
            weakself.page -= 1;
        }
        
        dispatch_async(dispatch_get_main_queue(), ^{
            [weakself.collectionView.mj_header endRefreshing];
            if (weakself.anchorDicts.count > 0) {
                [weakself.collectionView.mj_footer endRefreshing];
            } else {
                [weakself.collectionView.mj_footer endRefreshingWithNoMoreData];
            }
            [weakself.collectionView reloadEmptyDataSet];
        });
    }];
}
-(void) _handleSuccessWithDicts:(NSArray<NSDictionary *> * _Nonnull) anchorDicts isMore:(BOOL)isMore{

    //遍历加入状态监听
    for (NSInteger i = ([anchorDicts count] - 1); i >= 0; i--) {
        //倒序加入，因为后加入的优先级最高。可以先被加载刷新
        NSString *userID = [SCDictionaryHelper stringFromDictionary:anchorDicts[i] forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        if (userID.length > 0) {
            [self.onlineStatusSub addWithUserID:userID];
        }
    }

    if (isMore) {
        [self.anchorDicts addObjectsFromArray:anchorDicts];

    } else {
        [self.anchorDicts removeAllObjects];
        [self.anchorDicts addObjectsFromArray:anchorDicts];
    }

    [self updateAnchorList];

    dispatch_async(dispatch_get_main_queue(), ^{
        [self.collectionView.mj_header endRefreshing];
        if ([anchorDicts count] > 0) {
            [self.collectionView.mj_footer endRefreshing];
        } else {
            [self.collectionView.mj_footer endRefreshingWithNoMoreData];
        }

        [self.collectionView reloadData];
    });
}

/// 更新共享的主播列表数据
- (void)updateAnchorList {
    // 共享数据给主播详情页，用在切换下一个主播功能
    kScAuthMar.anchorService.anchorListModel = self.anchorDicts ?: @[];
}

#pragma mark -  UICollectionViewDataSource
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    !self.scrollCallback ?: self.scrollCallback(scrollView);
}
#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView
numberOfItemsInSection:(NSInteger)section
{
    return [self.anchorDicts count];
}


static NSString * const reuseIdentifier = @"Cell";
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView
cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    SCAnchorCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:reuseIdentifier forIndexPath:indexPath];

    if(indexPath.row <  [self.anchorDicts count]){
        NSDictionary *userDict = self.anchorDicts[indexPath.row];
        NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];

        // 获取在线状态并更新字典
        NSDictionary *onlineStatusDict = [kSCAuthOnlineStatesService.userStatusDic objectForKey:userID];
        if(onlineStatusDict){
            SCAnchorStatus status = (SCAnchorStatus)[SCDictionaryHelper integerFromDictionary:onlineStatusDict forKey:@"status" defaultValue:AnchorStatusOffline];
            if(status != AnchorStatusUnknown){
                // 创建更新后的用户字典
                NSMutableDictionary *updatedUserDict = [userDict mutableCopy];
                updatedUserDict[SCDictionaryKeys.shared.kSCUserStatusKey] = [SCDictionaryHelper stringFromAnchorStatus:status];
                userDict = [updatedUserDict copy];
            }
        }
        //就算已经存在也需要重新插入，这会更新排序
        [self.onlineStatusSub addWithUserID:userID];

        [cell configureWithDict:userDict];
    }


    return cell;
}
- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    if(indexPath.row <  [self.anchorDicts count]){
        [self updateAnchorList];
        NSDictionary *userDict = self.anchorDicts[indexPath.row];
        
        // 优先使用新的字典delegate方法
        if(_delegate && [_delegate respondsToSelector:@selector(scAnchorCollectionView:selectUserDict:)]){
            [_delegate scAnchorCollectionView:self selectUserDict:userDict];
            // 移除Model版本的delegate回调，只保留字典版本
        }
        
    }
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    // 这里只提升高度，没有将 banner 加到 header 中。
    if (self.isFirstItem && self.bannerView.banners > 0) {
        return CGSizeMake(kSCScreenWidth - (9*2), 105+10);
    } else {
        return CGSizeZero;
    }
}

#pragma mark - DZNEmptyDataSetSource
- (UIImage *)imageForEmptyDataSet:(UIScrollView *)scrollView {
    return [SCResourceManager loadImageWithName:@"ic_list_empty"];
}

- (NSAttributedString *)titleForEmptyDataSet:(UIScrollView *)scrollView {
    NSString *text = @"No Data".translateString;
    NSDictionary *attributes = @{NSFontAttributeName: kScUIFontMedium(24),
                                 NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#B7010D"]};
    return [[NSAttributedString alloc] initWithString:text attributes:attributes];
}

#pragma mark - DZNEmptyDataSetDelegate
- (BOOL)emptyDataSetShouldDisplay:(UIScrollView *)scrollView
{
    return self.hasRequestData && self.anchorDicts.count <= 0;
}

- (BOOL)emptyDataSetShouldAllowScroll:(UIScrollView *)scrollView
{
    return YES;
}

#pragma mark - JXCategoryListContentViewDelegate

- (UIView *)listView {
    return self;
}


#pragma mark - SCWaterFallFlowLayoutDelegate
- (CGFloat)waterFlowLayout:(SCWaterFallFlowLayout *)layout itemHeightForIndexPath:(NSIndexPath *)indexPath {
    return kSCScaleWidth(200.0f);
}

#pragma mark - UICollectionViewDelegateFlowLayout
- (CGSize)collectionView:(UICollectionView *)collectionView
                  layout:(UICollectionViewLayout *)collectionViewLayout
  sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat totalWidth = collectionView.bounds.size.width;
    CGFloat numberOfItemsPerRow = 2.0;

    // 可用宽度计算
    CGFloat availableWidth = totalWidth - (2*kColletionMarginLR) - kCollectionItemSpacing;
    CGFloat itemWidth = availableWidth / numberOfItemsPerRow;

    // 返回单元格尺寸，稍微增加高度以适应名称标签
    return CGSizeMake(itemWidth, itemWidth * 1.4);
}


- (void)listWillAppear{
    if(self.onlineStatusSub){
        [self onChangeOnlineStatusWithIds:nil];
        [self.onlineStatusSub start];
        //进入页面立刻刷新一次
        [kSCAuthOnlineStatesService refresh];
    }
    self.viewModel.isSelectFirstItem.value = @(self.isFirstItem);
    [self updateAnchorList];
}

- (void)listWillDisappear{
    if(self.onlineStatusSub){
        //标记暂停
        [self.onlineStatusSub pause];
    }
}
@end
