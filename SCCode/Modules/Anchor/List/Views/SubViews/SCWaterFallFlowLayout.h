//
//  SCWaterFallFlowLayout.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <UIKit/UIKit.h>



@class SCWaterFallFlowLayout;

@protocol SCWaterFallFlowLayoutDelegate <NSObject>

- (CGFloat)waterFlowLayout:(SCWaterFallFlowLayout *)layout itemHeightForIndexPath:(NSIndexPath *)indexPath;

@end

@interface SCWaterFallFlowLayout : UICollectionViewFlowLayout

@property (nonatomic, weak) id<SCWaterFallFlowLayoutDelegate> delegate;

@property (nonatomic, assign) NSInteger cols;
@property (nonatomic, strong) NSMutableArray<UICollectionViewLayoutAttributes *> *layoutAttributeArray;
@property (nonatomic, strong) NSMutableArray<NSNumber *> *yArray;
@property (nonatomic, assign) CGFloat maxHeight;
@property (nonatomic, assign) CGFloat offSetTop;

- (void)resetLayout;

@end
