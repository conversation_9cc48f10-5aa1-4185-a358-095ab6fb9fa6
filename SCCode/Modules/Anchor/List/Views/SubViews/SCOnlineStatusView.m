//
//  SCOnlineStatusView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import "SCOnlineStatusView.h"
#import <Masonry/Masonry.h>

@implementation SCOnlineStatusView


- (instancetype)initWithType:(SCOnlineStatusViewType) type {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.dotView = [[UIView alloc] init];
        self.dotView.backgroundColor = [UIColor greenColor];
        self.dotView.layer.cornerRadius = type == SCOnlineStatusViewTypeSmall ? 4.0f:(10/2.0f);
        [self addSubview:self.dotView];
        
        // 使用 Auto Layout 进行布局
        [self.dotView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self);
            make.width.equalTo(type == SCOnlineStatusViewTypeSmall ? @8:@10);
            make.height.equalTo(type == SCOnlineStatusViewTypeSmall ? @8:@10);
        }];
    }
    return self;
}

- (void)setText:(NSString *)text {

}
@end
