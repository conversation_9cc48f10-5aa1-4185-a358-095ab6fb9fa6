//
//  SCAnchorSubTitleJXCategoryTitleCellModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <JXCategoryView/JXCategoryView.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCAnchorSubTitleJXCategoryTitleCellModel : JXCategoryTitleCellModel
@property (nonatomic, strong) UIColor *normalBackgroundColor;
@property (nonatomic, strong) UIColor *normalBorderColor;
@property (nonatomic, strong) UIColor *selectedBackgroundColor;
@property (nonatomic, strong) UIColor *selectedBorderColor;
@property (nonatomic, assign) CGFloat borderLineWidth;
@property (nonatomic, assign) CGFloat backgroundCornerRadius;
@property (nonatomic, assign) CGFloat backgroundWidth;
@property (nonatomic, assign) CGFloat backgroundHeight;
@property (nonatomic, strong) NSArray *normalGradientColors;      // 正常状态渐变色数组
@property (nonatomic, strong) NSArray *selectedGradientColors;    // 选中状态渐变色数组
@property (nonatomic, strong) NSArray *gradientLocations;         // 渐变位置数组
@property (nonatomic, assign) CGPoint gradientStartPoint;         // 渐变开始点
@property (nonatomic, assign) CGPoint gradientEndPoint;           // 渐变结束点
@end

NS_ASSUME_NONNULL_END
