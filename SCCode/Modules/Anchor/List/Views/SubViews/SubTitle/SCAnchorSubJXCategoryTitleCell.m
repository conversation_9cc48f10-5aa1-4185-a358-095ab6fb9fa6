//
//  SCAnchorSubJXCategoryTitleCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorSubJXCategoryTitleCell.h"
#import "SCAnchorSubTitleJXCategoryTitleCellModel.h"


@interface SCAnchorSubJXCategoryTitleCell()
@property (nonatomic, strong) CAGradientLayer *bgLayer;
@end

@implementation SCAnchorSubJXCategoryTitleCell

- (void)initializeViews {
    [super initializeViews];

    self.bgLayer = [CAGradientLayer layer];
    [self.contentView.layer insertSublayer:self.bgLayer atIndex:0];
}

- (void)layoutSubviews {
    [super layoutSubviews];

    SCAnchorSubTitleJXCategoryTitleCellModel *myCellModel = (SCAnchorSubTitleJXCategoryTitleCellModel *)self.cellModel;
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    CGFloat bgWidth = self.contentView.bounds.size.width;
    if (myCellModel.backgroundWidth != JXCategoryViewAutomaticDimension) {
        bgWidth = myCellModel.backgroundWidth;
    }
    CGFloat bgHeight = self.contentView.bounds.size.height;
    if (myCellModel.backgroundHeight != JXCategoryViewAutomaticDimension) {
        bgHeight = myCellModel.backgroundHeight;
    }
    self.bgLayer.bounds = CGRectMake(0, 0, bgWidth, bgHeight);
    self.bgLayer.position = self.contentView.center;
    [CATransaction commit];
}

- (void)reloadData:(JXCategoryBaseCellModel *)cellModel {
    [super reloadData:cellModel];

    SCAnchorSubTitleJXCategoryTitleCellModel *myCellModel = (SCAnchorSubTitleJXCategoryTitleCellModel *)cellModel;
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    
    self.bgLayer.borderWidth = myCellModel.borderLineWidth;
    self.bgLayer.cornerRadius = myCellModel.backgroundCornerRadius;
    
    if (myCellModel.isSelected) {
        if (myCellModel.selectedGradientColors) {
            self.bgLayer.colors = myCellModel.selectedGradientColors;
            self.bgLayer.locations = myCellModel.gradientLocations;
            self.bgLayer.startPoint = myCellModel.gradientStartPoint;
            self.bgLayer.endPoint = myCellModel.gradientEndPoint;
        } else {
            self.bgLayer.colors = @[(id)myCellModel.selectedBackgroundColor.CGColor,
                                  (id)myCellModel.selectedBackgroundColor.CGColor];
        }
        self.bgLayer.borderColor = myCellModel.selectedBorderColor.CGColor;
    } else {
        if (myCellModel.normalGradientColors) {
            self.bgLayer.colors = myCellModel.normalGradientColors;
            self.bgLayer.locations = myCellModel.gradientLocations;
            self.bgLayer.startPoint = myCellModel.gradientStartPoint;
            self.bgLayer.endPoint = myCellModel.gradientEndPoint;
        } else {
            self.bgLayer.colors = @[(id)myCellModel.normalBackgroundColor.CGColor,
                                  (id)myCellModel.normalBackgroundColor.CGColor];
        }
        self.bgLayer.borderColor = myCellModel.normalBorderColor.CGColor;
    }
    
    [CATransaction commit];
}

@end
