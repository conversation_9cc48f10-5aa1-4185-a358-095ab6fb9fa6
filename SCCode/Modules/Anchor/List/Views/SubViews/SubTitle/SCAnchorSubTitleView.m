//
//  SCAnchorSubTitleView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorSubTitleView.h"
#import "SCAnchorSubJXCategoryTitleCell.h"
#import "SCAnchorSubTitleJXCategoryTitleCellModel.h"

@implementation SCAnchorSubTitleView


- (void)initializeData {
    [super initializeData];

    self.cellWidthIncrement = 18;
//    self.cellWidth = 55;
    self.normalBackgroundColor = [UIColor clearColor];
    self.normalBorderColor = [UIColor clearColor];
    self.selectedBackgroundColor = [UIColor scWhite];
    self.selectedBorderColor = [UIColor scWhite];
//    self.borderLineWidth = 0.5;
    self.backgroundCornerRadius = 7.0f;
    self.backgroundWidth = JXCategoryViewAutomaticDimension;
    self.backgroundHeight = 22;
    self.titleSelectedColor = [UIColor scBlack];
    self.titleColor = [UIColor scGray];
    self.titleFont = kScUIFontMedium(12);
    self.titleSelectedFont = kScUIFontMedium(12);
    [self setContentEdgeInsetLeft:15];
    self.cellSpacing = 0;
    self.backgroundColor = [UIColor clearColor];
    self.averageCellSpacingEnabled = NO;
    self.contentScrollViewClickTransitionAnimationEnabled = NO;
    
    // 初始化渐变色相关属性
    self.gradientStartPoint = CGPointMake(0, 0.5);  // 默认从左到右
    self.gradientEndPoint = CGPointMake(1, 0.5);
    self.gradientLocations = @[@0.0, @1.0];
}

//返回自定义的cell class
- (Class)preferredCellClass {
    return [SCAnchorSubJXCategoryTitleCell class];
}

- (void)refreshDataSource {
    NSMutableArray *tempArray = [NSMutableArray array];
    for (int i = 0; i < self.titles.count; i++) {
        SCAnchorSubTitleJXCategoryTitleCellModel *cellModel = [[SCAnchorSubTitleJXCategoryTitleCellModel alloc] init];
        [tempArray addObject:cellModel];
    }
    self.dataSource = tempArray;
}

- (void)refreshCellModel:(JXCategoryBaseCellModel *)cellModel index:(NSInteger)index {
    [super refreshCellModel:cellModel index:index];

    SCAnchorSubTitleJXCategoryTitleCellModel *myModel = (SCAnchorSubTitleJXCategoryTitleCellModel *)cellModel;
    
    // 设置基础属性
    myModel.normalBackgroundColor = self.normalBackgroundColor;
    myModel.normalBorderColor = self.normalBorderColor;
    myModel.selectedBackgroundColor = self.selectedBackgroundColor;
    myModel.selectedBorderColor = self.selectedBorderColor;
    myModel.borderLineWidth = self.borderLineWidth;
    myModel.backgroundCornerRadius = self.backgroundCornerRadius;
    myModel.backgroundWidth = self.backgroundWidth;
    myModel.backgroundHeight = self.backgroundHeight;
    
    // 设置渐变色相关属性
    myModel.normalGradientColors = self.normalGradientColors;
    myModel.selectedGradientColors = self.selectedGradientColors;
    myModel.gradientLocations = self.gradientLocations;
    myModel.gradientStartPoint = self.gradientStartPoint;
    myModel.gradientEndPoint = self.gradientEndPoint;
}


@end
