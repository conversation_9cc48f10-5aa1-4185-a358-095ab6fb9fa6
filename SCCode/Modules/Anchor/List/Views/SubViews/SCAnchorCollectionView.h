//
//  SCAnchorCollectionView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <UIKit/UIKit.h>
#import <JXCategoryView/JXCategoryView.h>
#import "SCAnchorViewModel.h"
@class SCAnchorCollectionView;
NS_ASSUME_NONNULL_BEGIN

@protocol SCAnchorCollectionViewDelegate <NSObject>

///点击事件回调
@optional
// 已废弃，使用字典版本
// 移除Model版本的delegate方法声明
- (void) scAnchorCollectionView:(SCAnchorCollectionView *) view selectUserDict:(NSDictionary *)userDict;

@end

@interface SCAnchorCollectionView : UIView <JXCategoryListContentViewDelegate>

@property (nullable,nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, strong) UICollectionView *collectionView;
@property(nonatomic,weak) id<SCAnchorCollectionViewDelegate> delegate;
@property (nonatomic, assign) SCAnchorViewModel *viewModel;

- (instancetype)initWithTag:(NSString * _Nonnull) tagStr subTag:(NSString *_Nonnull) subTagStr tagIndex:(NSInteger) tagIndex subIndex:(NSInteger) subIndex viewModel:(SCAnchorViewModel * _Nonnull)viewModel;

- (void)beginFirstRefresh;
@end

NS_ASSUME_NONNULL_END
