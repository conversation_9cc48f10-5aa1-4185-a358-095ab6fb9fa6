//
//  SCAnchorCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorCell.h"
#import <Masonry/Masonry.h>
#import <SDWebImage/SDWebImage.h>
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCCategoryUIButtonCode.h"
#import "SCCategoryNSStringCode.h"
#import "SCRoundedLabelView.h"
#import "SCOnlineStatusView.h"
#import "SCCallService.h"
#import "SCCoinsPopupViewController.h"
#import "Lottie/Lottie-Swift.h"
#import "SCCountryService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@implementation SCAnchorCell {
    UIImageView *_gradientContainerView;
    CompatibleAnimationView *_callAnimationView;
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self initView];
    }
    return self;
}

- (void)initView {
    self.iv = [[UIImageView alloc] init];
    self.iv.contentMode = UIViewContentModeScaleAspectFill;
    self.iv.layer.cornerRadius = 10;
    self.iv.clipsToBounds = YES;
    [self.contentView addSubview:self.iv];
    [self.iv mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[
        [UIColor blackColor],
        [UIColor clearColor]
    ] orientation:SCGradientColorsOrientationVertical];
    
    UIImage *gradientImage = [[UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(168, 56)] resizableImageWithCapInsets:UIEdgeInsetsMake(10, 10, 10, 10) resizingMode:UIImageResizingModeStretch];
   
    // 创建渐变层的容器视图
    _gradientContainerView = [[UIImageView alloc] init];
    _gradientContainerView.image = gradientImage;
    _gradientContainerView.clipsToBounds = YES;
    [self.contentView addSubview:_gradientContainerView];
    [_gradientContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.contentView);
        make.height.equalTo(@56);
    }];
    
    // 设置圆角
    _gradientContainerView.layer.cornerRadius = 10;
    _gradientContainerView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    
    self.activityTagImgView = [[UIImageView alloc]init];
    self.activityTagImgView.contentMode = UIViewContentModeScaleAspectFit;
    self.activityTagImgView.hidden = YES;
    [self.contentView addSubview:self.activityTagImgView];
    [self.activityTagImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView);
        make.top.equalTo(self.contentView).offset(10.0f);
        make.height.mas_equalTo(19.0f);
        make.width.mas_equalTo(0.0f);
    }];
    
    self.onlineStatusView = [[SCOnlineStatusView alloc] initWithType:SCOnlineStatusViewTypeSmall];
    [self.contentView addSubview:self.onlineStatusView];
    [self.onlineStatusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(14);
        make.left.equalTo(self.contentView).offset(8);
        make.height.width.equalTo(@8);
    }];
    
    self.nameL = [[UILabel alloc] init];
    self.nameL.font = kScUIFontSemibold(14);
    self.nameL.textColor = [UIColor whiteColor];
    [self.contentView addSubview:self.nameL];
    [self.nameL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.onlineStatusView);
        make.left.equalTo(self.onlineStatusView.mas_right).offset(6);
        make.right.lessThanOrEqualTo(self.activityTagImgView.mas_left).offset(-6);
    }];
    
    self.country = [[UILabel alloc] init];
    self.country.font = kScUIFontRegular(10);
    self.country.textColor = [UIColor whiteColor];
    [self.contentView addSubview:self.country];
    [self.country mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(6);
        make.top.equalTo(self.nameL.mas_bottom).offset(2);
        make.height.equalTo(@12);
    }];
    
    self.age = [[UILabel alloc] init];
    self.age.font = kScUIFontRegular(10);
    self.age.textColor = [UIColor whiteColor];
    [self.contentView addSubview:self.age];
    [self.age mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.country.mas_right).offset(4);
        make.right.lessThanOrEqualTo(self.contentView).offset(-6);
        make.bottom.equalTo(self.country);
        make.height.equalTo(@12);
    }];
    
    self.call = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_video_call"] target:self action:@selector(onCall:)];
    [self.contentView addSubview:self.call];
    [self.call mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.centerX.equalTo(self.contentView);
        make.bottom.equalTo(self.contentView).offset(-6);
    }];
    
    // 创建 Lottie 动画视图
    NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/call_animation.json"];
    NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
    
    NSError *error = nil;
    NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
    if (jsonData) {
        _callAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
        _callAnimationView.loopAnimationCount = -1;
        _callAnimationView.hidden = YES;
        kSCAddTapGesture(_callAnimationView, self, onCall:)
        [self.contentView addSubview:_callAnimationView];
        [_callAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(80, 80));
            make.centerX.equalTo(self.contentView);
            make.bottom.equalTo(self.contentView).offset(12);
        }];
    }
    
    self.priceLabl = [[UILabel alloc]init];
    self.priceLabl.textColor = [UIColor scWhite];
    self.priceLabl.font = kScUIFontSemibold(10);
    self.priceLabl.text = @"100   /Min";
    self.priceLabl.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:self.priceLabl];
    [self.priceLabl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.bottom.equalTo(self.call.mas_top).offset(-2);
    }];
}

// 移除Model版本的configure方法，只保留字典版本

- (void)onCall:(id)sender {
    if (self.userDict) {
        // 使用字典数据进行通话
        NSMutableDictionary *callUserDict = [self.userDict mutableCopy];
        NSInteger callCoins = [SCDictionaryHelper integerFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserCallCoinsKey defaultValue:0];
        callUserDict[SCDictionaryKeys.shared.kSCUserUnitPriceKey] = @(callCoins); // 设置单价为通话费用
        [kSCAuthCallService startCallingWithUserDict:callUserDict callSource:SCCallSourcePopularWall];
    }
}

- (void)configureWithDict:(NSDictionary *)userDict {
    self.userDict = userDict;

    // 刷新UI - 使用字典数据

    NSString *avatar = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"avatar" defaultValue:@""];

    [self.iv sc_setImageWithURL:avatar];

    NSString *activityTagUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"activityTagUrl" defaultValue:@""];
    if (!kSCIsStrEmpty(activityTagUrl)) {
        self.activityTagImgView.hidden = NO;
        kWeakSelf(self)
        [self.activityTagImgView sd_setImageWithURL:[NSURL URLWithString:activityTagUrl] completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
            if (image != nil) {
                CGFloat whScale = image.size.width / image.size.height;
                CGFloat width = 19.0f * whScale;
                [weakself.activityTagImgView mas_updateConstraints:^(MASConstraintMaker *make) {
                    make.width.mas_equalTo(width);
                }];
            }
        }];
    } else {
        self.activityTagImgView.hidden = YES;
    }

    NSString *statusStr = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:statusStr];
    self.onlineStatusView.dotView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:status];
    if (status == AnchorStatusOnline) {
        _callAnimationView.hidden = NO;
        [_callAnimationView play];
        [self.call setImage:nil forState:UIControlStateNormal];
    } else {
        _callAnimationView.hidden = YES;
        [_callAnimationView stop];
        [self.call setImage:[SCResourceManager loadImageWithName:@"ic_anchor_call_disabled"] forState:UIControlStateNormal];
    }

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    self.nameL.text = nickname;

    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    [self.age setText:[NSString stringWithFormat:@"| %ld", age]];

    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
    [self.country setText: [NSString stringWithFormat:@"%@ %@", [SCCountryService emojiForCountry:country], country]];

    NSInteger callCoins = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCallCoinsKey defaultValue:0];
    self.priceLabl.attributedText = [self priceAttributeWithImage:[SCResourceManager loadImageWithName:@"ic_coins_small"] text:[NSString stringWithFormat:@"%ld ", callCoins] after:@"/Min".translateString];
}

- (void)dealloc {
    [_callAnimationView stop];
}

-(NSMutableAttributedString *) priceAttributeWithImage:(UIImage *)image text:(NSString *)text after:(NSString *)after{
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] init];
    NSTextAttachment *textAttachment = [[NSTextAttachment alloc] init];
    CGSize imageSize = CGSizeMake(10, 8);
    textAttachment.bounds = CGRectMake(0, 0, imageSize.width, imageSize.height);
    textAttachment.image = image;
    NSAttributedString *imageString = [NSAttributedString attributedStringWithAttachment:textAttachment];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:text attributes:@{NSBaselineOffsetAttributeName:@(1.5)}]];
    [attributedString appendAttributedString:imageString];
    [attributedString appendAttributedString:[[NSAttributedString alloc] initWithString:after attributes:@{NSBaselineOffsetAttributeName:@(1.5)}]];
    return attributedString;
}

@end
