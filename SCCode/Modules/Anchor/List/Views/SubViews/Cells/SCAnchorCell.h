//
//  SCAnchorCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import <UIKit/UIKit.h>
@class SCOnlineStatusView,SCRoundedLabelView,SCAnchorCell;
NS_ASSUME_NONNULL_BEGIN



@interface SCAnchorCell : UICollectionViewCell

@property (nonatomic, strong) SCOnlineStatusView *onlineStatusView;
@property (nonatomic, strong) UIImageView *iv;
@property (nonatomic, strong) UIButton *call;
@property (nonatomic, strong) UILabel *nameL;
@property (nonatomic, strong) UILabel *age;
@property (nonatomic, strong) UILabel *country;
@property (nonatomic, strong) UIImageView *activityTagImgView;
@property (nonatomic, strong) UILabel *priceLabl;

@property (nonatomic, strong) NSDictionary *userDict;

- (void)configureWithDict:(NSDictionary *)userDict;

@end


NS_ASSUME_NONNULL_END

