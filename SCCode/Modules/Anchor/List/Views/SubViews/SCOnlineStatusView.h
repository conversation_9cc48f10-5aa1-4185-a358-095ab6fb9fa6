//
//  SCOnlineStatusView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

//定义枚举 大小两种类型
typedef NS_ENUM(NSInteger, SCOnlineStatusViewType) {
    SCOnlineStatusViewTypeSmall,
    SCOnlineStatusViewTypeBig,
};

@interface SCOnlineStatusView : UIView

@property (nonatomic, strong) UIView *dotView;

- (instancetype)initWithType:(SCOnlineStatusViewType) type;
@end

NS_ASSUME_NONNULL_END
