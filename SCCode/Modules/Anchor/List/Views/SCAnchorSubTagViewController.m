//
//  SCAnchorListViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/22.
//

#import "SCAnchorSubTagViewController.h"
#import <JXCategoryView/JXCategoryView.h>
#import "SCAnchorCollectionView.h"
#import "SCAnchorSubTitleView.h"
// #import "SCStrategyModel.h" // 已移除，使用字典替代
#import "SCAnchorInfoViewController.h"
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorSubTagViewController ()<JXCategoryViewDelegate,JXCategoryListContainerViewDelegate,SCAnchorCollectionViewDelegate>

@property (nonatomic, strong) SCAnchorSubTitleView *categoryView;
@property (nonatomic, copy) void(^scrollCallback)(UIScrollView *scrollView);
@property (nonatomic, strong) UIScrollView *currentListView;
@property (nonatomic, strong) JXCategoryListContainerView *listContainerView;
@end

@implementation SCAnchorSubTagViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor clearColor];
    
    _categoryView = [[SCAnchorSubTitleView alloc] init];
    _categoryView.backgroundColor = [UIColor clearColor];
    self.categoryView.titleFont = [SCFontManager italicFontWithSize:16];
    self.categoryView.titleSelectedFont = [SCFontManager italicFontWithSize:16];
    self.categoryView.titleColor = [UIColor scWhite];
    self.categoryView.titleSelectedColor = [UIColor scWhite];
    
    self.categoryView.selectedGradientColors = @[
        (id)[kSCColorWithHexStr(@"#FF423E") CGColor],
        (id)[kSCColorWithHexStr(@"#740A31") CGColor]
    ];
    self.categoryView.gradientStartPoint = CGPointMake(0, 0);
    self.categoryView.gradientEndPoint = CGPointMake(1, 1);
    self.categoryView.gradientLocations = @[@0.0, @1.0];
    
    NSArray<NSString *> *subTagList = [SCDictionaryHelper arrayFromDictionary:self.tag forKey:SCDictionaryKeys.shared.kSCSubTagListKey defaultValue:@[]];
    self.categoryView.titles = subTagList;
    self.categoryView.delegate = self;
    [self.view addSubview:self.categoryView];
    
    [self.categoryView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.view);
        make.height.mas_equalTo(50);
    }];

    self.listContainerView = [[JXCategoryListContainerView alloc] initWithType:JXCategoryListContainerType_ScrollView delegate:self];
    self.listContainerView.backgroundColor = [UIColor clearColor];
    self.listContainerView.listCellBackgroundColor = [UIColor clearColor];
    self.listContainerView.scrollView.backgroundColor = [UIColor clearColor];
    
    [self.view addSubview:self.listContainerView];
    
    [self.listContainerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
        make.top.equalTo(self.categoryView.mas_bottom);
    }];
    
    self.contentScrollView = self.listContainerView.scrollView;

    self.categoryView.listContainer = self.listContainerView;
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    NSArray<NSString *> *subTagList = [SCDictionaryHelper arrayFromDictionary:self.tag forKey:SCDictionaryKeys.shared.kSCSubTagListKey defaultValue:@[]];
    bool isNotShow = [subTagList count] <= 1;
    CGFloat headerHeight = isNotShow ? 0:50;
    [self.categoryView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(headerHeight);
    }];
}

#pragma mark - JXCategoryViewDelegate

- (void)categoryView:(JXCategoryBaseView *)categoryView didSelectedItemAtIndex:(NSInteger)index {
    //根据选中的下标，实时更新currentListView
    SCAnchorCollectionView *list = (SCAnchorCollectionView *)self.listContainerView.validListDict[@(index)];
    self.currentListView = list.collectionView;
}

#pragma mark - JXCategoryListContainerViewDelegate

- (NSInteger)numberOfListsInlistContainerView:(JXCategoryListContainerView *)listContainerView {
    NSArray<NSString *> *subTagList = [SCDictionaryHelper arrayFromDictionary:self.tag forKey:SCDictionaryKeys.shared.kSCSubTagListKey defaultValue:@[]];
    return [subTagList count];
}

- (id<JXCategoryListContentViewDelegate>)listContainerView:(JXCategoryListContainerView *)listContainerView initListForIndex:(NSInteger)index {
    NSString *subTitle = @"";
    NSArray<NSString *> *subTagList = [SCDictionaryHelper arrayFromDictionary:self.tag forKey:SCDictionaryKeys.shared.kSCSubTagListKey defaultValue:@[]];
    NSString *tagName = [SCDictionaryHelper stringFromDictionary:self.tag forKey:SCDictionaryKeys.shared.kSCTagNameKey defaultValue:@""];

    if(index < subTagList.count){
        subTitle = subTagList[index];

    }
    SCAnchorCollectionView *partnerListView = [[SCAnchorCollectionView alloc] initWithTag:tagName subTag:subTitle tagIndex:self.tagIndex subIndex:index viewModel:self.viewModel];
    [partnerListView setDelegate:self];
    self.currentListView = partnerListView.collectionView;
    [partnerListView beginFirstRefresh];
    return partnerListView;
    
}

#pragma mark - JXCategoryListContentViewDelegate

- (UIView *)listView {
    return self.view;
}

#pragma mark - SCAnchorCollectionViewDelegate
// 移除Model版本的delegate方法，只保留字典版本

- (void)scAnchorCollectionView:(SCAnchorCollectionView *)view selectUserDict:(NSDictionary *)userDict{
    [SCAnchorInfoViewController openWithUserDict:userDict from:self];
}

@end
