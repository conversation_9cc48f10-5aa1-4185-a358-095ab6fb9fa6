//
//  SCAnchorInfoViewController.m
//  Supercall
//
//  Created by guanweihong on 2023/12/25.
//

#import "SCAnchorInfoViewController.h"

#import <Masonry/Masonry.h>
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

//View
#import "SCAnchorInfoHeaderView.h"
#import "SCAnchorInfoViewModel.h"
#import "SCNavigationBar.h"
#import "SCAnchorInfoBottomView.h"
#import "SCImageBrowserView.h"
#import "SCVideoHorizontalListView.h"
//数据模型
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
// #import "SCMediaListModel.h" // 已移除，使用字典替代
//其他页面
#import "SCFullScreenPreviewMediaViewController.h"
#import "SCConversationInfoViewController.h"
#import "SCAnchorActionSheetViewController.h"
//服务
#import "SCActionService.h"
#import "SCCallService.h"
#import "SCAnchorService.h"

#import "SCThrottle.h"


#pragma mark - 声明
@interface SCAnchorInfoViewController ()<SCImageBrowserViewDelegate,SCVideoHorizontalListViewDelegate,UIScrollViewDelegate>
@property(nonatomic,strong) SCAnchorInfoViewModel * viewModel;
@property(nonatomic,weak) SCAnchorInfoHeaderView * headerView;
@property(nonatomic,weak) UIScrollView * scrollView;
@property(nonatomic,weak) UIView * scrollContentView;
//底部视图
@property(nonatomic,weak) SCAnchorInfoBottomView * bottomView;
@property(nonatomic,weak) UIButton * moreBtn;

@end

@implementation SCAnchorInfoViewController

#pragma mark - 入口
+ (void) openWith:(NSString *)userID from:(UIViewController *)fromVC{
    SCAnchorInfoViewController * vc = [[SCAnchorInfoViewController alloc] initWith:userID];
    [fromVC.navigationController pushViewController:vc animated:YES];
}

+ (void) openWith:(NSString *)userID status:(NSString *)status from:(UIViewController *)fromVC {
    SCAnchorInfoViewController * vc = [[SCAnchorInfoViewController alloc] initWith:userID];
    vc.viewModel.userStatus = status;
    [fromVC.navigationController pushViewController:vc animated:YES];
}

// 移除Model版本的方法，只保留字典版本

+ (void) openWithUserDict:(NSDictionary *)userDict from:(UIViewController *)fromVC {
    NSString *userID = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    if (userID.length == 0) {
        return; // 无效的用户ID
    }

    SCAnchorInfoViewController * vc = [[SCAnchorInfoViewController alloc] initWith:userID];

    // 设置用户状态
    NSString *status = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];
    vc.viewModel.userStatus = status;

    // 直接设置字典数据到ViewModel
    NSMutableDictionary *mutableUserDict = [userDict mutableCopy];
    mutableUserDict[SCDictionaryKeys.shared.kSCUserIDKey] = userID;
    mutableUserDict[SCDictionaryKeys.shared.kSCUserStatusKey] = status;
    vc.viewModel.userDict = [mutableUserDict copy];

    [fromVC.navigationController pushViewController:vc animated:YES];
}

#pragma mark - 生命周期

- (void)dealloc
{
    
}

- (instancetype)initWith:(NSString *)userID
{
    self = [super init];
    if (self) {
        _viewModel = [[SCAnchorInfoViewModel alloc] initWithUserID:userID];
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    //消息页面的时候刷新下用户信息显示的内容。【其他页面关注拉黑会回来，刷新UI】
    if(self.viewModel.userDict && [self.viewModel.userDict count] > 0)
        [self configUserInfUI];
    
    [self requestUserInfo];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    if (_headerView) {
        kWeakSelf(self)
        [SCThrottle throttleWithTag:kSCThrottleRefreshAnchorInfoKey duration:0.3 onAfter:^{
            [SCThrottle cancelWithTag:kSCThrottleRefreshAnchorInfoKey];
            [weakself.headerView configWithLabelDict:weakself.viewModel.userExtraDict];
            // 从字典中获取视频媒体数据
            NSArray *videoMedias = [SCDictionaryHelper videoMediasFromUserDict:weakself.viewModel.userDict];
            [weakself.headerView.videoListView refreshWithMediaList:videoMedias];
        }];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initUI];
    [self requestUserInfo];
}

-(void) _initUI{
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.scNavigationBar.backButton.layer.cornerRadius = 10.0f;
    self.scNavigationBar.backButton.backgroundColor = [UIColor sc50TranBlackBGColor];
    [self.scNavigationBar.backButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.scNavigationBar.contentView).offset(15.0f);
        make.width.height.mas_equalTo(36);
    }];
    
    //设置更多按钮
    UIButton * moreBtn = [[UIButton alloc] init];
    [moreBtn setImage:[SCResourceManager loadImageWithName:@"ic_nav_white_more"] forState:UIControlStateNormal];
    [moreBtn setTintColor:[UIColor scWhite]];
    moreBtn.layer.cornerRadius = 10.0f;
    moreBtn.backgroundColor = [UIColor sc50TranBlackBGColor];
    [self.scNavigationBar.contentView addSubview:moreBtn];
    self.moreBtn = moreBtn;
    [moreBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.scNavigationBar.contentView).offset(-15.0f);
        make.centerY.equalTo(self.scNavigationBar.contentView);
        make.width.height.mas_equalTo(36);
    }];
    
    [self.scContentView setHidden:YES];
    
    UIScrollView *scrollView = [[UIScrollView alloc] init];
    scrollView.showsVerticalScrollIndicator = NO;
    scrollView.showsHorizontalScrollIndicator = NO;
    scrollView.contentInsetAdjustmentBehavior  = UIScrollViewContentInsetAdjustmentNever;
    scrollView.scrollEnabled = YES;
    scrollView.delegate = self;
    scrollView.contentInset = UIEdgeInsetsMake(0, 0, 70 + kSCSafeAreaBottomHeight, 0);
    scrollView.bounces = NO;
    [self.view insertSubview:scrollView atIndex:0];
    self.scrollView = scrollView;
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.bottom.equalTo(self.view);
    }];
    
    UIView * scrollContentView = [[UIView alloc] init];
    [self.scrollView addSubview:scrollContentView];
    self.scrollContentView = scrollContentView;
    [self.scrollContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    SCAnchorInfoHeaderView * headerView = [[SCAnchorInfoHeaderView alloc] init];
    [self.scrollContentView addSubview:headerView];
    self.headerView = headerView;
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollContentView);
    }];
    
    //底部视图
    SCAnchorInfoBottomView * bottomView = [[SCAnchorInfoBottomView alloc] init];
    bottomView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:bottomView];
    self.bottomView = bottomView;
    [self.bottomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(self.view);
//        make.top.equalTo(self.scrollView.mas_bottom);
    }];
    
    [self addListenOnlineStatusWithUserID:self.viewModel.userID];
    [self configUserStatusUI];
    [self _initAction];
}
- (void) _initAction{
    ///监听相册点击回调
    self.headerView.imageBrowserView.delegate = self;
    self.headerView.videoListView.delegate = self;
    
    kSCAddTapGesture(self.bottomView.callAnimationView, self, onCall);
    kSCAddTapGesture(self.bottomView.nextAnimationView, self, onNextAction);
    [self.bottomView.msgBtn addTarget:self action:@selector(onMessage) forControlEvents:UIControlEventTouchUpInside];
    [self.bottomView.callBtn addTarget:self action:@selector(onCall) forControlEvents:UIControlEventTouchUpInside];
    [self.moreBtn addTarget:self action:@selector(onMore) forControlEvents:UIControlEventTouchUpInside];
    [self.headerView.followBtn addTarget:self action:@selector(onFollow) forControlEvents:UIControlEventTouchUpInside];
}

- (void)viewWillLayoutSubviews{
    [super viewWillLayoutSubviews];
    [self.scrollView setContentSize:CGSizeMake(self.scrollContentView.frame.size.width, self.scrollContentView.frame.size.height)];
}

-(void) configUserInfUI{
    if (_headerView) {
        // 使用字典数据配置UI，photoMediaUrls方法已经支持字典优先
        [_headerView configWithImgs:[self.viewModel photoMediaUrls] userDict:self.viewModel.userDict];
    }
}
-(void) configUserStatusUI{
    [_headerView configWithStatus:self.viewModel.userStatus];
    // 使用SCDictionaryHelper转换状态字符串
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:self.viewModel.userStatus];
    //只有在线才可以呼叫
    if (status == AnchorStatusOnline) {
        self.bottomView.callBtn.hidden = YES;
        self.bottomView.callAnimationView.hidden = NO;
        [self.bottomView.callAnimationView play];
    } else {
        self.bottomView.callBtn.hidden = NO;
        self.bottomView.callAnimationView.hidden = YES;
        [self.bottomView.callAnimationView stop];
    }
}
///更新用户标签
-(void) configUserLabel{
    [_headerView configWithLabelDict:self.viewModel.userExtraDict];
}
///更新用户礼物
-(void) configUserGift{
    // 使用字典版本
    [_headerView configWithGiftDicts:self.viewModel.giftNumDicts];
}

-(void)onMessage{
    // 使用字典版本的方法
    [SCConversationInfoViewController showWithFromVC:self tager:self.viewModel.userID userDict:self.viewModel.userDict];
}
-(void)onCall{
    // 使用字典版本的方法
    [kSCAuthCallService startCallingWithUserDict:self.viewModel.userDict callSource:SCCallSourceDetailPage];
}

-(void)onMore{
    kWeakSelf(self);
    // 使用字典版本的方法
    NSMutableDictionary *mutableUserDict = [self.viewModel.userDict mutableCopy];
    [SCAnchorActionSheetViewController showMoreActionSheetWithFromVC:self userDict:mutableUserDict actionBlock:^(NSInteger index) {
        // 将更新后的字典同步回viewModel
        weakself.viewModel.userDict = [mutableUserDict copy];
        [weakself configUserInfUI];
    }];
}

-(void)onFollow{
    //先修改再请求接口
    if (kSCIsStrEmpty(self.viewModel.userID)) {
        return;
    }

    // 从字典中获取关注状态
    BOOL isFriend = [SCDictionaryHelper boolFromDictionary:self.viewModel.userDict forKey:@"isFriend" defaultValue:NO];
    self.headerView.followBtn.selected = !isFriend;

    kWeakSelf(self)
    if(isFriend){
        //取消关注
        [kSCAuthActionService requestUnFollowWithUserId:self.viewModel.userID success:^{
            // 更新字典中的关注状态
            NSMutableDictionary *mutableUserDict = [weakself.viewModel.userDict mutableCopy];
            mutableUserDict[@"isFriend"] = @(NO);
            weakself.viewModel.userDict = [mutableUserDict copy];
            [kSCKeyWindow toast:@"UnFollow Successfully".translateString];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            [kSCKeyWindow toast:@"UnFollow Failure".translateString];
        }];
    }else{
        //关注
        [kSCAuthActionService requestFollowWithUserId:self.viewModel.userID success:^{
            // 更新字典中的关注状态
            NSMutableDictionary *mutableUserDict = [weakself.viewModel.userDict mutableCopy];
            mutableUserDict[@"isFriend"] = @(YES);
            weakself.viewModel.userDict = [mutableUserDict copy];
            [kSCKeyWindow toast:@"Follow Successfully".translateString];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            [kSCKeyWindow toast:@"Follow Failure".translateString];
        }];
    }

}

//切换主播
- (void)onNextAction {
    NSDictionary *nextOnlineAnchorDict = [self findNextOnlineAnchor];
    if (nextOnlineAnchorDict) {
        [self pushToAnchorInfo:nextOnlineAnchorDict];
    } else {
        [self.view toast:[@"There are no more anchors online" translateString]];
    }
}

- (NSDictionary *)findNextOnlineAnchor {
    NSArray<NSDictionary *> *anchorList = kScAuthMar.anchorService.anchorListModel;
    if (anchorList.count == 0) return nil;

    // 找到当前主播的索引
    NSInteger currentIndex = [anchorList indexOfObjectPassingTest:^BOOL(NSDictionary *anchorDict, NSUInteger idx, BOOL *stop) {
        NSString *userID = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        return [userID isEqualToString:self.viewModel.userID];
    }];

    if (currentIndex == NSNotFound) {
        currentIndex = -1;
    }

    // 从当前位置向后查找
    for (NSInteger i = 0; i < anchorList.count; i++) {
        NSInteger nextIndex = (currentIndex + 1 + i) % anchorList.count;
        NSDictionary *anchorDict = anchorList[nextIndex];

        NSString *userID = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
        if ([self statusWithUserID:userID] == AnchorStatusOnline) {
            return anchorDict;
        }
    }

    return nil;
}

- (void)pushToAnchorInfo:(NSDictionary *)anchorDict {
    NSString *userID = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:SCDictionaryKeys.shared.kSCUserIDKey defaultValue:@""];
    NSString *status = [SCDictionaryHelper stringFromDictionary:anchorDict forKey:SCDictionaryKeys.shared.kSCUserStatusKey defaultValue:@""];

    self.viewModel = [[SCAnchorInfoViewModel alloc] initWithUserID:userID];
    self.viewModel.userStatus = status;
    self.viewModel.userDict = anchorDict; // 直接设置字典数据
    [self addListenOnlineStatusWithUserID:self.viewModel.userID];
    [self requestUserInfo];
    [self configUserStatusUI];

    [self.scrollView setContentOffset:CGPointZero animated:NO];
}

- (void)sc_blank_empty{}
#pragma mark - 网络请求

-(void) requestUserInfo {
    kWeakSelf(self);
    // 使用字典版本的方法
    [self.viewModel requestUserInfoDictWithSuccess:^(NSDictionary * _Nonnull userDict) {
        [weakself configUserInfUI];
    } failure:nil];

    [weakself requestGiftAndLabel];
}

- (void) requestGiftAndLabel {
    kWeakSelf(self);
    // 使用字典版本的礼物数据请求
    [self.viewModel requestGiftsNumDictsWithSuccess:^{
        [weakself configUserGift];
    } failure:nil];
    [self.viewModel requestUserExtraInfoDictWithSuccess:^(NSDictionary * _Nonnull userExtraDict) {
        [weakself configUserLabel];
    } failure:nil];
}
#pragma mark - SCImageBrowserViewDelegate
- (void)scImageBrowserView:(SCImageBrowserView *)browserView index:(NSInteger)index{
    
    // 使用字典数据获取媒体信息
    NSArray<NSDictionary *> *photoMedias = [SCDictionaryHelper photoMediasDefaultAvatarFromUserDict:self.viewModel.userDict];
    [SCFullScreenPreviewMediaViewController showWithFromVC:self media:photoMedias userDict:self.viewModel.userDict type:SCFullScreenPreviewMediaTypePhoto index:index isShowCall:YES];
}
#pragma mark - SCVideoHorizontalListView
- (void)scVideoHorizontalListView:(SCVideoHorizontalListView *)videoListView index:(NSInteger)index{
    // 从字典中获取视频媒体数据
    NSArray *videoMedias = [SCDictionaryHelper videoMediasFromUserDict:self.viewModel.userDict];
    [SCFullScreenPreviewMediaViewController showWithFromVC:self media:videoMedias userDict:self.viewModel.userDict type:SCFullScreenPreviewMediaTypeVideo index:index isShowCall:YES];
}

#pragma mark - UISceneDelegate
-(void)scrollViewDidScroll:(UIScrollView *)scrollView{
    CGFloat offsetY = scrollView.contentOffset.y;
    CGFloat alpha = 0;
    if (offsetY <= 0) {
        alpha = 0;
    }else if(offsetY > 0 && offsetY <= 100){
        alpha = offsetY / 100;
    }else{
        alpha = 1;
    }
    [self.scNavigationBar setBackgroundColor:[UIColor.scGlobalBgColor colorWithAlphaComponent:alpha]];
    self.scNavigationBar.backButton.backgroundColor = [UIColor.scBlack colorWithAlphaComponent:0.5-(alpha/2.0f)];
    self.moreBtn.backgroundColor = [UIColor.scBlack colorWithAlphaComponent:0.5-(alpha/2.0f)];
}
#pragma mark - 更新在线状态
- (void)onChangeOnlineStatusWithIds:(NSArray<NSString *> *)ids{
    self.viewModel.userStatus = [SCDictionaryHelper stringFromAnchorStatus:[self statusWithUserID:self.viewModel.userID]];
    // 更新字典中的状态
    if (self.viewModel.userDict) {
        NSMutableDictionary *mutableUserDict = [self.viewModel.userDict mutableCopy];
        mutableUserDict[SCDictionaryKeys.shared.kSCUserStatusKey] = self.viewModel.userStatus;
        self.viewModel.userDict = [mutableUserDict copy];
    }
    [self configUserStatusUI];
}

@end
