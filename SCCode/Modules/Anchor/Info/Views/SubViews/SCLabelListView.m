//
//  SCLabelListView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/2.
//

#import "SCLabelListView.h"
#import "SCTagCell.h"
#import <Masonry/Masonry.h>
#import "SCAlignedCollectionViewFlowLayout.h"

@interface SCLabelListView () <UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) SCAlignedCollectionViewFlowLayout *layout;
///选中的集合
@property (nonatomic, strong) NSMutableSet<NSString *> *selectSet;


@end

@implementation SCLabelListView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        _selectChangeObx = [[SCObservable<NSArray<NSString *> *> alloc] initWithValue:nil];
        _selectSet = [NSMutableSet new];
        
        _layout = [[SCAlignedCollectionViewFlowLayout alloc] init];
        _layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        _layout.estimatedItemSize = UICollectionViewFlowLayoutAutomaticSize;
        _layout.minimumLineSpacing = 10;
        _layout.minimumInteritemSpacing = 10;
        
        self.collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:_layout];
        self.collectionView.delegate = self;
        self.collectionView.dataSource = self;
        self.collectionView.backgroundColor = [UIColor scGlobalBgColor];
        [self.collectionView registerClass:[SCTagCell class] forCellWithReuseIdentifier:[SCTagCell cellIdentifier]];
        [self addSubview:self.collectionView];
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        self.labelSpacing = 10;
    }
    return self;
}

- (void)setIsCanSelect:(BOOL)isCanSelect {
    _isCanSelect = isCanSelect;
    self.collectionView.backgroundColor = isCanSelect ? [UIColor colorWithHexString:@"#282828"] : [UIColor scGlobalBgColor];
    [self.collectionView reloadData];
}

- (void)setTags:(NSArray<NSString *> *)tags {    
    _tags = tags;
    [self.collectionView reloadData];
    [self.collectionView layoutIfNeeded];
}

- (CGFloat)calculateViewHeight {
    [self.collectionView layoutIfNeeded]; // 确保布局是最新的

    CGFloat totalHeight = 0;
    
    if (self.tags.count > 0) {
        // 使用 collectionViewContentSize 获取实际内容大小
        CGSize contentSize = self.collectionView.collectionViewLayout.collectionViewContentSize;
        totalHeight = contentSize.height;
        
        // 添加内边距
        UIEdgeInsets contentInset = self.collectionView.contentInset;
        totalHeight += contentInset.top + contentInset.bottom;
    }
    // 设置最小高度，以防没有标签时显示空白
    CGFloat minHeight = 36.0; // 或者其他适合您设计的最小高度
    return MAX(totalHeight, minHeight);
}

// 评价时获取高度
- (CGFloat)calculateViewHeightWithData {
    CGFloat totalHeight = self.collectionView.contentInset.top + self.collectionView.contentInset.bottom;
    CGFloat viewWidth = self.collectionView.scWidth - self.collectionView.contentInset.left -  self.collectionView.contentInset.right;
    CGFloat currentWidth = 0;
    CGFloat currentRow =  [self.tags count] > 0 ? 1:0;
    for (NSString *tag in self.tags) {
        CGSize textSize = [tag sizeWithAttributes:@{NSFontAttributeName: kScUIFontMedium(10.0f)}];
        CGFloat textWidth = textSize.width + 30;
        if(currentWidth + textWidth + self.layout.minimumInteritemSpacing  >= viewWidth){
            if(currentWidth == 0){
                //自己已经沾满一行
            }else{
                //累加到下一行
                currentWidth = textWidth;
            }
            currentRow += 1;
        }else{
            //累计
            currentWidth += textWidth + self.layout.minimumInteritemSpacing;
        }
    }
    if(currentRow > 0){
        totalHeight += (30 + self.layout.minimumLineSpacing)*currentRow - self.layout.minimumLineSpacing;
    }
    return totalHeight;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.tags.count;
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCTagCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:[SCTagCell cellIdentifier] forIndexPath:indexPath];
    NSString * titleStr = self.tags[indexPath.item];
    cell.label.text = titleStr;
    if (self.isCanSelect) {
        cell.contentView.layer.cornerRadius = 30.0f/2.0f;
        cell.label.font = kScUIFontMedium(10.0f);
        if([self.selectSet containsObject:titleStr]){
            cell.contentView.layer.borderWidth = 1.0f;
            cell.contentView.layer.borderColor = UIColor.scTheme.CGColor;
            cell.contentView.backgroundColor = [UIColor colorWithHexString:@"#282828"];
            cell.label.textColor = UIColor.scTheme;
        }else{
            cell.contentView.layer.borderWidth = 0.0f;
            cell.contentView.backgroundColor = [UIColor colorWithHexString:@"#454442"];
            cell.label.textColor = UIColor.scWhite;
        }
    } else {
        cell.label.font = kScUIFontMedium(12.0f);
        cell.label.textColor = UIColor.scWhite;
        cell.contentView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
        cell.contentView.layer.cornerRadius = 36.0f/2.0f;
    }
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    NSString * titleStr = self.tags[indexPath.item];
    if(self.isCanSelect){
        if([self.selectSet containsObject:titleStr]){
            [self.selectSet removeObject:titleStr];
        }else{
            if(self.isMust){
                
                if(self.maxSelectNum != 0  && self.maxSelectNum <= [self.selectSet count]){
                    self.selectChangeObx.error = [[SCXErrorModel alloc] initWitMsg:@"Maximum number of selections exceeded".translateString];
                    return;
                }
            }else{
                [self.selectSet removeAllObjects];
            }
            
            
            [self.selectSet addObject:titleStr];
        }
        
        
        [self.collectionView reloadData];
        self.selectChangeObx.value = [self.selectSet allObjects];
    }
}
- (NSArray<NSString *> *)selectList{
    return [self.selectSet allObjects];
}

@end
