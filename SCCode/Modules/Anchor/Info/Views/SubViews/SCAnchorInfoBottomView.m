//
//  SCAnchorInfoBottomView.m
//  Supercall
//
//  Created by guan<PERSON>hong on 2024/1/4.
//

#import "SCAnchorInfoBottomView.h"
#import <Masonry/Masonry.h>

@interface SCAnchorInfoBottomView()
@property(nullable,nonatomic,weak) UIView *contentV;
@end

@implementation SCAnchorInfoBottomView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}

-(void) _initUI{
    self.backgroundColor = [UIColor clearColor];
    //UI初始化
    //内容布局 对齐安全区
    UIView * contentView = [UIView new];
    contentView.backgroundColor = [UIColor clearColor];
    [self addSubview:contentView];
    self.contentV = contentView;
    
    {
        // 创建 Lottie 动画视图
        NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/next_anchor_animation.json"];
        NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
        
        NSError *error = nil;
        NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
        if (jsonData) {
            self.nextAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
            self.nextAnimationView.loopAnimationCount = -1;
            [contentView addSubview:self.nextAnimationView];
            [self.nextAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(contentView).offset(24);
                make.centerY.equalTo(contentView);
                make.size.mas_equalTo(72);
            }];
            [self.nextAnimationView play];
        }
    }
    
    UIButton *msgBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_anchor_info_msg"]];
    [contentView addSubview:msgBtn];
    _msgBtn = msgBtn;
    
    //呼叫按钮
    UIButton *callBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [callBtn setImage:[SCResourceManager loadImageWithName:@"ic_anchor_large_call_disabled"] forState:UIControlStateNormal];
    [contentView addSubview:callBtn];
    _callBtn = callBtn;
    
    {
        // 创建 Lottie 动画视图
        NSString *jsonPath = [[SCResourceManager assterPath] stringByAppendingString:@"/json/call_animation.json"];
        NSURL *jsonURL = [NSURL URLWithString:[@"file://" stringByAppendingString:jsonPath]];
        
        NSError *error = nil;
        NSData *jsonData = [NSData dataWithContentsOfURL:jsonURL options:0 error:&error];
        if (jsonData) {
            self.callAnimationView = [[CompatibleAnimationView alloc] initWithData:jsonData];
            self.callAnimationView.loopAnimationCount = -1;
            self.callAnimationView.hidden = YES;
            [contentView addSubview:self.callAnimationView];
            [self.callAnimationView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.center.equalTo(contentView);
                make.size.mas_equalTo(110);
            }];
        }
    }
    
    //约束
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(70);
        make.leading.trailing.equalTo(self);
        make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
        make.top.equalTo(self);
    }];
    
    [self.msgBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(contentView).offset(-26);
        make.centerY.equalTo(contentView);
        make.height.mas_equalTo(66);
        make.width.mas_equalTo(66);
    }];
    
    [self.callBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(contentView);
        make.size.mas_equalTo(66);
    }];
    
}

@end
