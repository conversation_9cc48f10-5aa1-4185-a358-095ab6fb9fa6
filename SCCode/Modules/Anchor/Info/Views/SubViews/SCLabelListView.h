//
//  SCLabelListView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/2.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface SCLabelListView : UIView

@property (nonatomic, strong) NSArray<NSString *> *tags;
@property (nonatomic, assign) CGFloat labelSpacing; // 标签之间的间距，默认为10
@property (nonatomic, assign) BOOL isCanSelect; //是否可以选择
@property (nonatomic, assign) BOOL isMust; //是否为多选
@property (nonatomic, assign) NSUInteger maxSelectNum; //最多选择数量
@property (nonatomic, strong) SCObservable<NSArray<NSString *> *> *selectChangeObx; //选中变化通知


- (CGFloat)calculateViewHeight;
- (CGFloat)calculateViewHeightWithData;

-(NSArray<NSString *> *) selectList;
@end
NS_ASSUME_NONNULL_END
