//
//  SCImageBrowserView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <UIKit/UIKit.h>
@class SCImageBrowserView;

NS_ASSUME_NONNULL_BEGIN

@protocol SCImageBrowserViewDelegate <NSObject>

- (void) scImageBrowserView:(SCImageBrowserView *)browserView index:(NSInteger) index;

@end


@interface SCImageBrowserView : UIView

- (void)configWithNSArray:(NSArray<NSString *> *)array;
//======  交互属性  ======
@property(nonatomic,weak) id<SCImageBrowserViewDelegate> delegate;

@end
NS_ASSUME_NONNULL_END
