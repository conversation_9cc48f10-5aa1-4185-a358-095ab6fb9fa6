//
//  SCImageBrowserView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <UIKit/UIKit.h>
@class SCImageBrowserView;

NS_ASSUME_NONNULL_BEGIN

@protocol SCImageBrowserViewDelegate <NSObject>

- (void) scImageBrowserView:(SCImageBrowserView *)browserView index:(NSInteger) index;

@end


@interface SCImageBrowserView : UIView

- (void)configWithNSArray:(NSArray<NSString *> *)array;

//======  交互属性  ======
@property(nonatomic,weak) id<SCImageBrowserViewDelegate> delegate;

//======  自动轮播属性  ======
/// 是否启用自动轮播，默认为 YES
@property (nonatomic, assign) BOOL autoScrollEnabled;
/// 自动轮播间隔时间，默认为 3.0 秒
@property (nonatomic, assign) NSTimeInterval autoScrollInterval;

//======  轮播控制方法  ======
/// 开始自动轮播
- (void)startAutoScroll;
/// 停止自动轮播
- (void)stopAutoScroll;

@end
NS_ASSUME_NONNULL_END
