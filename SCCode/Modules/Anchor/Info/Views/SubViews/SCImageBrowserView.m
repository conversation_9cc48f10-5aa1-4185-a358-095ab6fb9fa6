//
//  SCImageBrowserView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import "SCImageBrowserView.h"
//View
#import "SCCustomPageControl.h"

// 自定义图片浏览Cell
@interface SCImageBrowserCell : UICollectionViewCell
@property (nonatomic, strong) UIImageView *imageView;
@end

@implementation SCImageBrowserCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupImageView];
    }
    return self;
}

- (void)setupImageView {
    self.imageView = [[UIImageView alloc] init];
    self.imageView.contentMode = UIViewContentModeScaleAspectFill;
    self.imageView.clipsToBounds = YES;
    [self.contentView addSubview:self.imageView];

    // 设置约束，让imageView填满整个cell
    self.imageView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.imageView.topAnchor constraintEqualToAnchor:self.contentView.topAnchor],
        [self.imageView.leadingAnchor constraintEqualToAnchor:self.contentView.leadingAnchor],
        [self.imageView.trailingAnchor constraintEqualToAnchor:self.contentView.trailingAnchor],
        [self.imageView.bottomAnchor constraintEqualToAnchor:self.contentView.bottomAnchor]
    ]];
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.imageView.image = nil;
    [self.imageView.gestureRecognizers enumerateObjectsUsingBlock:^(__kindof UIGestureRecognizer * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        [self.imageView removeGestureRecognizer:obj];
    }];
}

@end

@interface SCImageBrowserView()<UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout>
@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, strong) UICollectionViewFlowLayout *flowLayout;
@property (nonatomic, strong) SCCustomPageControl *pageControl;

@property (nonatomic, strong) NSArray<NSString *> *imageArray;

// 自动轮播相关属性
@property (nonatomic, strong) NSTimer *autoScrollTimer;
@property (nonatomic, assign) BOOL isUserInteracting;
@property (nonatomic, assign) NSInteger currentIndex;
@end

@implementation SCImageBrowserView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self _initUI];
    }
    return self;
}

- (void) _initUI{
    // 初始化UICollectionView
    [self setupCollectionView];

    self.pageControl = [[SCCustomPageControl alloc] init];
    self.pageControl.activePageColor = UIColor.scWhite;
    self.pageControl.inactivePageColor = [UIColor.scWhite colorWithAlphaComponent:0.5];
    [self addSubview:self.pageControl];

    // 初始化自动轮播属性
    self.autoScrollEnabled = YES;
    self.autoScrollInterval = 3.0;
    self.isUserInteracting = NO;
    self.currentIndex = 0;
}

- (void)setupCollectionView {
    // 创建流式布局
    self.flowLayout = [[UICollectionViewFlowLayout alloc] init];
    self.flowLayout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    self.flowLayout.minimumLineSpacing = 0;
    self.flowLayout.minimumInteritemSpacing = 0;

    // 创建CollectionView
    self.collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:self.flowLayout];
    self.collectionView.dataSource = self;
    self.collectionView.delegate = self;
    self.collectionView.pagingEnabled = YES;
    self.collectionView.showsHorizontalScrollIndicator = NO;
    self.collectionView.showsVerticalScrollIndicator = NO;
    self.collectionView.backgroundColor = [UIColor clearColor];

    // 注册cell
    [self.collectionView registerClass:[SCImageBrowserCell class] forCellWithReuseIdentifier:@"SCImageBrowserCell"];

    [self addSubview:self.collectionView];

    // 设置约束
    self.collectionView.translatesAutoresizingMaskIntoConstraints = NO;
    [NSLayoutConstraint activateConstraints:@[
        [self.collectionView.topAnchor constraintEqualToAnchor:self.topAnchor],
        [self.collectionView.leadingAnchor constraintEqualToAnchor:self.leadingAnchor],
        [self.collectionView.trailingAnchor constraintEqualToAnchor:self.trailingAnchor],
        [self.collectionView.bottomAnchor constraintEqualToAnchor:self.bottomAnchor]
    ]];
}
static int defaultItemTag = 2100;

#pragma mark - 无限循环索引转换方法

- (NSInteger)totalItemCount {
    if (self.imageArray.count == 0) {
        return 0;
    }

    if (self.imageArray.count > 1) {
        // 无限循环时，在前后各添加一个项目
        return self.imageArray.count + 2;
    }

    return self.imageArray.count;
}

- (NSInteger)collectionViewIndexForActualIndex:(NSInteger)actualIndex {
    if (self.imageArray.count <= 1) {
        return actualIndex;
    }

    // 无限循环时，实际索引需要偏移1（因为前面添加了一个项目）
    return actualIndex + 1;
}

- (NSInteger)actualIndexForCollectionViewIndex:(NSInteger)collectionViewIndex {
    if (self.imageArray.count <= 1) {
        return collectionViewIndex;
    }

    if (collectionViewIndex == 0) {
        // 第一个项目是最后一个实际项目的副本
        return self.imageArray.count - 1;
    } else if (collectionViewIndex == [self totalItemCount] - 1) {
        // 最后一个项目是第一个实际项目的副本
        return 0;
    } else {
        // 中间的项目需要减去偏移量
        return collectionViewIndex - 1;
    }
}

#pragma mark - 无限循环边界处理方法

- (void)handleScrollEnd {
    if (self.imageArray.count <= 1) {
        return;
    }

    // 处理无限循环的边界情况
    NSInteger currentCollectionViewIndex = [self getCurrentCollectionViewIndex];

    if (currentCollectionViewIndex == 0) {
        // 滚动到了第一个副本，跳转到最后一个实际项目
        self.currentIndex = self.imageArray.count - 1;
        [self scrollToCurrentIndexWithoutAnimation];
    } else if (currentCollectionViewIndex == [self totalItemCount] - 1) {
        // 滚动到了最后一个副本，跳转到第一个实际项目
        self.currentIndex = 0;
        [self scrollToCurrentIndexWithoutAnimation];
    }
}

- (NSInteger)getCurrentCollectionViewIndex {
    CGFloat pageWidth = self.collectionView.frame.size.width;
    if (pageWidth == 0) {
        return 0;
    }

    CGFloat currentOffsetX = self.collectionView.contentOffset.x;
    return (NSInteger)round(currentOffsetX / pageWidth);
}

- (void)scrollToCurrentIndexWithoutAnimation {
    if (self.imageArray.count == 0) {
        return;
    }

    NSInteger collectionViewIndex = [self collectionViewIndexForActualIndex:self.currentIndex];
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:collectionViewIndex inSection:0];
    [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:NO];
}

- (void)scrollToItemAtIndex:(NSInteger)index animated:(BOOL)animated {
    if (index < 0 || index >= self.imageArray.count) {
        return;
    }

    self.currentIndex = index;
    NSInteger collectionViewIndex = [self collectionViewIndexForActualIndex:index];
    NSIndexPath *indexPath = [NSIndexPath indexPathForItem:collectionViewIndex inSection:0];
    [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:animated];
}
- (void)configWithNSArray:(NSArray<NSString *> *)array{

    // 停止当前的自动轮播
    [self stopAutoScroll];

    _imageArray = array;
    self.pageControl.numberOfPages = self.imageArray.count;
    self.pageControl.currentPage = 0;
    self.currentIndex = 0;

    // 重新加载数据
    [self.collectionView reloadData];
    [self setNeedsLayout];

    // 设置初始滚动位置
    if (self.imageArray.count > 1) {
        // 无限循环模式：滚动到第一张实际图片的位置（索引1）
        dispatch_async(dispatch_get_main_queue(), ^{
            NSIndexPath *indexPath = [NSIndexPath indexPathForItem:1 inSection:0];
            [self.collectionView scrollToItemAtIndexPath:indexPath atScrollPosition:UICollectionViewScrollPositionCenteredHorizontally animated:NO];
        });
    }

    // 配置完成后启动自动轮播
    [self startAutoScrollIfNeeded];
}

- (void)layoutSubviews{
    [super layoutSubviews];

    // 更新item大小
    self.flowLayout.itemSize = self.bounds.size;

    // 如果frame发生变化，需要重新调整当前位置
    if (!CGSizeEqualToSize(self.collectionView.frame.size, self.bounds.size)) {
        [self scrollToCurrentIndexWithoutAnimation];
    }

    self.pageControl.frame = CGRectMake(0, self.frame.size.height - 20, self.frame.size.width, 20);
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return [self totalItemCount];
}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCImageBrowserCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:@"SCImageBrowserCell" forIndexPath:indexPath];

    NSInteger actualIndex = [self actualIndexForCollectionViewIndex:indexPath.item];
    NSString *imageUrl = self.imageArray[actualIndex];

    [cell.imageView sc_setImageWithURL:imageUrl];
    cell.imageView.contentMode = UIViewContentModeScaleAspectFill;
    cell.imageView.clipsToBounds = YES;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onTapImg:)];
    tapGesture.view.tag = defaultItemTag + actualIndex;
    [cell.imageView addGestureRecognizer:tapGesture];

    return cell;
}

#pragma mark - UICollectionViewDelegate

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    NSInteger actualIndex = [self actualIndexForCollectionViewIndex:indexPath.item];
    if(_delegate != nil && [_delegate respondsToSelector:@selector(scImageBrowserView:index:)]){
        [_delegate scImageBrowserView:self index:actualIndex];
    }
}

#pragma mark - UICollectionViewDelegateFlowLayout

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    return self.bounds.size;
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    // 用户开始拖拽时停止自动轮播
    self.isUserInteracting = YES;
    [self stopAutoScroll];
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    // 用户结束拖拽时，如果没有减速则立即处理边界并重新启动自动轮播
    if (!decelerate) {
        self.isUserInteracting = NO;
        [self handleScrollEnd];
        [self startAutoScrollIfNeeded];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    // 滚动结束时处理边界并重新启动自动轮播
    self.isUserInteracting = NO;
    [self handleScrollEnd];
    [self startAutoScrollIfNeeded];
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
    // 自动滚动动画结束时处理边界
    [self handleScrollEnd];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    [self updateCurrentIndex];
    self.pageControl.currentPage = self.currentIndex;
}

- (void)updateCurrentIndex {
    NSInteger collectionViewIndex = [self getCurrentCollectionViewIndex];
    NSInteger newCurrentIndex = [self actualIndexForCollectionViewIndex:collectionViewIndex];

    if (newCurrentIndex != self.currentIndex && newCurrentIndex >= 0 && newCurrentIndex < self.imageArray.count) {
        self.currentIndex = newCurrentIndex;
    }
}

#pragma mark - 自动轮播方法

- (void)startAutoScrollIfNeeded {
    if (self.autoScrollEnabled && self.imageArray.count > 1 && !self.isUserInteracting) {
        [self stopAutoScroll];
        self.autoScrollTimer = [NSTimer scheduledTimerWithTimeInterval:self.autoScrollInterval
                                                                target:self
                                                              selector:@selector(autoScrollAction)
                                                              userInfo:nil
                                                               repeats:YES];
        [[NSRunLoop mainRunLoop] addTimer:self.autoScrollTimer forMode:NSRunLoopCommonModes];
    }
}

- (void)stopAutoScroll {
    if (self.autoScrollTimer) {
        [self.autoScrollTimer invalidate];
        self.autoScrollTimer = nil;
    }
}

- (void)autoScrollAction {
    if (self.imageArray.count <= 1) {
        return;
    }

    NSInteger nextIndex = (self.currentIndex + 1) % self.imageArray.count;
    [self scrollToItemAtIndex:nextIndex animated:YES];
}

- (void)startAutoScroll {
    [self startAutoScrollIfNeeded];
}

#pragma mark - Action

- (void) onTapImg:(UITapGestureRecognizer *)gestureRecognizer{
    if(gestureRecognizer.view.tag >= defaultItemTag){
        if(_delegate != nil && [_delegate respondsToSelector:@selector(scImageBrowserView:index:)]){
            [_delegate scImageBrowserView:self index:gestureRecognizer.view.tag-defaultItemTag];
        }
    }
}

#pragma mark - 内存管理

- (void)dealloc {
    [self stopAutoScroll];
}

@end


