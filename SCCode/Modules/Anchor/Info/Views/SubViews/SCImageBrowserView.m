//
//  SCImageBrowserView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2023/12/26.
//

#import "SCImageBrowserView.h"
//View
#import "SCCustomPageControl.h"

@interface SCImageBrowserView()<UIScrollViewDelegate>
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) SCCustomPageControl *pageControl;
@property (nonatomic, strong) NSMutableArray<UIImageView *> *imageViews;

@property (nonatomic, strong) NSArray<NSString *> *imageArray;
@end

@implementation SCImageBrowserView

- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if(self){
        [self _initUI];
    }
    return self;
}

- (void) _initUI{
    self.scrollView = [[UIScrollView alloc] initWithFrame:self.bounds];
    self.scrollView.delegate = self;
    self.scrollView.pagingEnabled = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.contentSize = CGSizeZero;
    [self addSubview:self.scrollView];
    
    self.pageControl = [[SCCustomPageControl alloc] init];
    self.pageControl.activePageColor = UIColor.scWhite;
    self.pageControl.inactivePageColor = [UIColor.scWhite colorWithAlphaComponent:0.5];
    [self addSubview:self.pageControl];
}
static int defaultItemTag = 2100;
- (void)configWithNSArray:(NSArray<NSString *> *)array{
    
    if(self.imageViews == nil){
        self.imageViews = [NSMutableArray array];
    }else{
        [self.imageViews enumerateObjectsUsingBlock:^(UIImageView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [obj removeFromSuperview];
        }];
        [self.imageViews removeAllObjects];
    }
    _imageArray = array;
    for (NSInteger i = 0; i < self.imageArray.count; i++) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(self.frame.size.width * i, 0, self.frame.size.width, self.frame.size.height)];
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        [imageView sc_setImageWithURL:self.imageArray[i]];
        [imageView setUserInteractionEnabled:true];
        imageView.clipsToBounds = YES;
        [self.scrollView addSubview:imageView];
        imageView.tag = defaultItemTag+i;
        [imageView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onTapImg:)]];
        [self.imageViews addObject:imageView];
    }
    [self.scrollView setContentOffset:CGPointZero];
    self.pageControl.numberOfPages = self.imageArray.count;
    self.pageControl.currentPage = 0;
    [self setNeedsLayout];
}

- (void)layoutSubviews{
    [super layoutSubviews];
    self.scrollView.frame = self.bounds;
    self.pageControl.frame = CGRectMake(0, self.frame.size.height - 20, self.frame.size.width, 20);
    self.scrollView.contentSize = CGSizeMake(self.frame.size.width * self.imageArray.count, self.frame.size.height);
    for (NSInteger i = 0; i < self.imageArray.count; i++) {
        UIImageView *imageView = self.imageViews[i];
        imageView.frame = CGRectMake(self.frame.size.width * i, 0, self.frame.size.width, self.frame.size.height);
    }
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    CGFloat screenWidth = CGRectGetWidth(scrollView.frame);
    NSInteger currentPage = scrollView.contentOffset.x / screenWidth;
    self.pageControl.currentPage = currentPage;
}

#pragma mark - Action

- (void) onTapImg:(UITapGestureRecognizer *)gestureRecognizer{
    if(gestureRecognizer.view.tag >= defaultItemTag){
        if(_delegate != nil && [_delegate respondsToSelector:@selector(scImageBrowserView:index:)]){};
        [_delegate scImageBrowserView:self index:gestureRecognizer.view.tag-defaultItemTag];
    }
}
@end


