//
//  SCCustomPageControl.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCCustomPageControl.h"


@implementation SCCustomPageControl

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setBackgroundColor:[UIColor clearColor]];
        self.hidesForSinglePage = YES;
        _activePageColor = [UIColor whiteColor];
        _inactivePageColor = [UIColor colorWithHexString:@"FFD8D8D8"];
        _activePageSize = CGSizeMake(20, 5);
        _inactivePageSize = CGSizeMake(5, 5);
        _pageSpacing = 3.0;
    }
    return self;
}

- (void)setCurrentPage:(NSInteger)currentPage {
    [super setCurrentPage:currentPage];
    [UIView animateWithDuration:0.2 animations:^{
        [self setNeedsDisplay];
    }];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    for (UIView *subview in self.subviews) {
        subview.hidden = YES;
    }
}
- (void)drawRect:(CGRect)rect {
    if (self.hidesForSinglePage && self.numberOfPages <= 1) {
        return;
    }
    
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSetAllowsAntialiasing(context, true);
    
    CGFloat beginX = (rect.size.width - self.numberOfPages * (self.inactivePageSize.width + self.pageSpacing) - self.pageSpacing + (self.activePageSize.width - self.inactivePageSize.width)) / 2.0;
    
    for (NSInteger i = 0; i < self.numberOfPages; i++) {
        CGFloat x = beginX + i * (self.inactivePageSize.width + self.pageSpacing);
        
        if (i > self.currentPage) {
            x += (self.activePageSize.width - self.inactivePageSize.width);
        }
        
        CGFloat y = (rect.size.height - self.inactivePageSize.height) / 2.0;
        
        if (i == self.currentPage) {
            CGContextSetFillColorWithColor(context, self.activePageColor.CGColor);
        } else {
            CGContextSetFillColorWithColor(context, self.inactivePageColor.CGColor);
        }
        
        CGSize size = (i == self.currentPage) ? self.activePageSize : self.inactivePageSize;
        UIBezierPath *indicatorPath = [UIBezierPath bezierPathWithRoundedRect:CGRectMake(x, y, size.width, size.height) cornerRadius:size.height / 2.0];
        
        CGContextAddPath(context, indicatorPath.CGPath);
        CGContextFillPath(context);
    }
}

@end
