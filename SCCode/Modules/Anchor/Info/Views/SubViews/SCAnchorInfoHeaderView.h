//
//  SCAnchorInfoHeaderView.h
//  Supercall
//
//  Created by g<PERSON>weihong on 2023/12/26.
//

#import <UIKit/UIKit.h>
//子View
@class SCImageBrowserView,SCVideoHorizontalListView;
//数据模型

NS_ASSUME_NONNULL_BEGIN

@interface SCAnchorInfoHeaderView : UIView

//UI属性
//图片列表
@property(nonatomic,weak,nullable,readonly) SCImageBrowserView * imageBrowserView;
///Follow 按钮
@property(nonatomic,weak,nullable) UIButton * followBtn;
//视频列表
@property(nonatomic,weak,nullable,readonly) SCVideoHorizontalListView * videoListView;

///配置图片
// 移除Model版本的方法，只保留字典版本
-(void)configWithImgs:(NSArray<NSString *> *) images userDict:(NSDictionary *) userDict;
//配置在线状态， 因为数据源是分开的 所以分开获取
-(void)configWithStatus:(NSString *) statusStr;

//配置标签
-(void) configWithLabelDict:(NSDictionary * _Nonnull) userExtraDict;
//配置礼物数量数据
-(void) configWithGiftDicts:(NSArray<NSDictionary*> *)giftDicts;
@end

NS_ASSUME_NONNULL_END
