//
//  SCVideoHorizontalListView.m
//  Supercall
//
//  Created by g<PERSON>weihong on 2023/12/26.
//

#import "SCVideoHorizontalListView.h"
#import "SCSmallVideoCell.h"
#import "SCDictionaryHelper.h"


@interface SCVideoHorizontalListView () <UICollectionViewDataSource, UICollectionViewDelegate,SCSmallVideoCellDelegate>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, copy) NSArray<NSDictionary *> *medias; // 改为使用字典替代SCMediaListModel

@end
@implementation SCVideoHorizontalListView


- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupCollectionView];
    }
    return self;
}

- (void)setupCollectionView {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    layout.minimumLineSpacing = 10;
    layout.itemSize = CGSizeMake(78, 114);
    
    self.collectionView = [UICollectionView collectionViewWithFrame:self.bounds layout:layout delegate:self dataSource:self cellClass:[SCSmallVideoCell class] forCellReuseIdentifier:reuseIdentifier];
    self.collectionView.showsHorizontalScrollIndicator = NO;
    self.collectionView.backgroundColor = [UIColor scGlobalBgColor];
    [self addSubview:self.collectionView];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    self.collectionView.frame = self.bounds;
}

- (void)refreshWithMediaList:(NSArray<NSDictionary *> *)medias{
    

    // 数据验证
    if (![medias isKindOfClass:[NSArray class]]) {
        
        _medias = @[];
    } else {
        // 过滤无效的媒体数据
        NSMutableArray *validMedias = [NSMutableArray array];
        for (id media in medias) {
            if ([media isKindOfClass:[NSDictionary class]]) {
                NSDictionary *mediaDict = (NSDictionary *)media;
                // 检查是否有必要的URL字段
                NSString *url = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"thumbUrl" defaultValue:nil];
                if (!url) {
                    url = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"mediaUrl" defaultValue:nil];
                }
                if (!url) {
                    url = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"url" defaultValue:nil];
                }

                if (url && url.length > 0) {
                    [validMedias addObject:mediaDict];
                } else {
                    
                }
            } else {
                
            }
        }
        _medias = [validMedias copy];
    }

    
    [self.collectionView reloadData];
    [self.collectionView setNeedsLayout];
    [self.collectionView layoutIfNeeded];
}

#pragma mark - UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    
    return self.medias.count;
}
static NSString *reuseIdentifier = @"SCSmallVideoCell";
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCSmallVideoCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:reuseIdentifier forIndexPath:indexPath];
    cell.delegate = self;
    [cell configWithModel:self.medias[indexPath.row] index:indexPath.row];
    return cell;
}

#pragma mark - UICollectionViewDelegate


#pragma mark - SCSmallVideoCellDelegate
- (void)scSmallVideoCell:(SCSmallVideoCell *)cell didClick:(NSInteger)index{
    
    if(self.delegate != nil && [self.delegate respondsToSelector:@selector(scVideoHorizontalListView:index:)]){
        [self.delegate scVideoHorizontalListView:self index:index];
    }
}
@end
