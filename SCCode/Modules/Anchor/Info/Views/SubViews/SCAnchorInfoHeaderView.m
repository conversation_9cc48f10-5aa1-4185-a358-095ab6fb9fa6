//
//  SCAnchorInfoHeaderView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import "SCAnchorInfoHeaderView.h"
#import "SCImageBrowserView.h"
#import "SCOnlineStatusView.h"
#import <Masonry/Masonry.h>
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCRoundedLabelView.h"
#import "SCVideoHorizontalListView.h"
// #import "SCUserExtraInfoModel.h" // 已移除，使用字典替代
#import "SCLabelListView.h"
#import "SCGiftListView.h"
#import "SCFontManager.h"
#import "SCCountryService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorInfoHeaderView ()


///在线状态布局
@property(nonatomic,weak,nullable) SCOnlineStatusView * onlineStatusView;

@property(nonatomic,weak,nullable) UILabel * userNameLabel;
@property(nonatomic,weak,nullable) UILabel * ageLabel;
@property(nonatomic,weak,nullable) UILabel * countryLabel;
@property(nonatomic,weak,nullable) UIButton * chargeBtn;
@property(nonatomic,weak,nullable) UILabel * aboutLabel;


@property(nonatomic,weak,nullable) UIView * videoView;


@property(nonatomic,weak,nullable) UIView * impressionView;
@property(nonatomic,weak,nullable) SCLabelListView * impressionListView;

@property(nonatomic,weak,nullable) UIView * giftView;
@property(nonatomic,weak,nullable) SCGiftListView * giftListView;
@end

@implementation SCAnchorInfoHeaderView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self _initUI];
    }
    return self;
}

-(void) _initUI{
    SCImageBrowserView *imageBrowserView = [[SCImageBrowserView alloc] initWithFrame:CGRectMake(0, 0, self.frame.size.width, kSCScaleWidth(342))];
    imageBrowserView.layer.cornerRadius = kSCBigCornerRadius;
    imageBrowserView.layer.masksToBounds = YES;
    imageBrowserView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
    [self addSubview:imageBrowserView];
    _imageBrowserView = imageBrowserView;
    [self.imageBrowserView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.leading.trailing.equalTo(self);
        make.height.mas_equalTo(kSCScaleWidth(432));
    }];

    UIButton * followBtn = [[UIButton alloc] init];
    [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_follow"] forState:UIControlStateNormal];
    [followBtn setImage:[SCResourceManager loadImageWithName:@"ic_user_inf_followed"] forState:UIControlStateSelected];
    [self addSubview:followBtn];
    self.followBtn = followBtn;
    [self.followBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self).inset(14);
        make.width.height.mas_equalTo(36);
        make.bottom.equalTo(self.imageBrowserView.mas_bottom).offset(-12);
    }];
    //===========   用户信息部分  ======
    //计费按钮
    UIButton * chargeBtn = [[UIButton alloc] init];
    [chargeBtn setTitleColor:[UIColor scWhite] forState:UIControlStateNormal];
    chargeBtn.titleLabel.font = kScUIFontRegular(12);
    chargeBtn.layer.cornerRadius = 14.0f;
    chargeBtn.layer.masksToBounds = YES;
    [chargeBtn setImage:[SCResourceManager loadImageWithName:@"ic_coins_anchor_info"] forState:UIControlStateNormal];
    [chargeBtn sc_setThemeGradientBackgroundWithCornerRadius:14.0f];
    chargeBtn.userInteractionEnabled = NO;
    [self addSubview:chargeBtn];
    self.chargeBtn = chargeBtn;
    if (kScAuthMar.isLanguageForce) {
        chargeBtn.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
    }
//
    [chargeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBrowserView.mas_bottom).offset(20);
        make.trailing.equalTo(self).offset(-16);
        make.height.mas_equalTo(28);
        make.width.mas_equalTo(92);
    }];
    
    //用户状态
    SCOnlineStatusView * onlineStatusView = [[SCOnlineStatusView alloc] initWithType:SCOnlineStatusViewTypeSmall];
    [self addSubview:onlineStatusView];
    self.onlineStatusView = onlineStatusView;
    [self.onlineStatusView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(16);
        make.top.equalTo(self.imageBrowserView.mas_bottom).offset(16);
        make.height.mas_equalTo(10);
    }];
    
    //用户名
    UILabel * userNameLabel = [[UILabel alloc] init];
    userNameLabel.font = [SCFontManager semiBoldItalicFontWithSize:16.0f];
    userNameLabel.textColor = [UIColor scWhite];
    [self addSubview:userNameLabel];
    self.userNameLabel = userNameLabel;
    [userNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.onlineStatusView.mas_trailing).offset(5);
        make.trailing.lessThanOrEqualTo(self.chargeBtn.mas_leading).offset(-10);
        make.centerY.equalTo(self.onlineStatusView);
    }];
    
    ///国家
    UILabel * countryLabel = [[UILabel alloc] init];
    [countryLabel setFont:kScUIFontRegular(12)];
    countryLabel.textColor = [UIColor scWhite];
    [self addSubview:countryLabel];
    self.countryLabel = countryLabel;
    [countryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(userNameLabel.mas_leading).offset(0);
        make.top.equalTo(userNameLabel.mas_bottom).offset(4);
        make.height.mas_equalTo(17);
    }];
    
    //用户年龄
    UILabel * ageLabel = [[UILabel alloc] init];
    [ageLabel setFont:kScUIFontRegular(12)];
    ageLabel.textColor = [UIColor scWhite];
    [self addSubview:ageLabel];
    self.ageLabel = ageLabel;
    [ageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(countryLabel.mas_trailing).offset(8);
        make.centerY.equalTo(countryLabel).offset(0);
        make.height.mas_equalTo(17);
    }];
    
    //关于
    UILabel * aboutLabel = [[UILabel alloc] init];
    aboutLabel.font = kScUIFontRegular(12);
    aboutLabel.textColor = [UIColor colorWithHexString:@"#DEDEDE"];
    aboutLabel.numberOfLines = 0;
    [self addSubview:aboutLabel];
    self.aboutLabel = aboutLabel;
    [aboutLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(16);
        make.trailing.equalTo(self).offset(-16);
        make.top.equalTo(ageLabel.mas_bottom).offset(10);
    }];
    
    ///=========== 视频布局
    
    UIView * videoView = [[UIView alloc] init];
    videoView.backgroundColor = [UIColor scGlobalBgColor];
    [self addSubview:videoView];
    self.videoView = videoView;
    [videoView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self).inset(16.0f);
        make.top.equalTo(aboutLabel.mas_bottom).offset(0);
        make.height.mas_equalTo(150);
    }];
    
    //标题
    UILabel * videoTitleLabel = [[UILabel alloc] init];
    videoTitleLabel.text = @"Video".translateString;
    videoTitleLabel.font = [SCFontManager boldItalicFontWithSize:16.0f];
    videoTitleLabel.textColor = [UIColor scWhite];
    [videoView addSubview:videoTitleLabel];
    [videoTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(videoView).offset(0);
        make.top.equalTo(videoView).offset(7);
        make.height.mas_equalTo(20);
    }];
    
    SCVideoHorizontalListView *videoListView = [[SCVideoHorizontalListView alloc] init];
    [videoView addSubview:videoListView];
    _videoListView = videoListView;
    
    [self.videoListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(videoView);
        make.top.equalTo(videoTitleLabel.mas_bottom).offset(9);
    }];
    
    //============ 礼物布局 ========
    UIView * giftView = [[UIView alloc] init];
    giftView.backgroundColor = [UIColor scGlobalBgColor];
    [self addSubview:giftView];
    self.giftView = giftView;
    [giftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self).inset(16.0f);
        make.top.equalTo(videoView.mas_bottom).offset(0);
    }];
    
    //礼物
    UILabel * giftLabel = [SCAnchorInfoHeaderView labelWithStr:@"Gifts".translateString];
    [giftView addSubview:giftLabel];
    [giftLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(giftView).offset(0);
        make.top.equalTo(giftView).offset(7);
        make.height.mas_equalTo(20);
    }];
    
    //礼物列表
    SCGiftListView * giftListView = [[SCGiftListView alloc] init];
    [giftView addSubview:giftListView];
    self.giftListView = giftListView;
    [giftListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(giftView);
        make.top.equalTo(giftLabel.mas_bottom).offset(9);
        make.height.mas_equalTo([giftListView calculateViewHeight]);
        
    }];
    
    ///========== 标签布局 Impression
    UIView * impressionView = [[UIView alloc] init];
    impressionView.backgroundColor = [UIColor scGlobalBgColor];
    [self addSubview:impressionView];
    self.impressionView = impressionView;
    [impressionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self).inset(16.0f);
        make.top.equalTo(giftView.mas_bottom).offset(0);
        make.bottom.equalTo(self).offset(0);
    }];
    //标签的Label
    UILabel * labelL = [SCAnchorInfoHeaderView labelWithStr:@"Impression".translateString];
    [impressionView addSubview:labelL];
    [labelL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(impressionView).offset(0);
        make.top.equalTo(impressionView).offset(7);
        make.height.mas_equalTo(20);
    }];
    
    //标签布局
    SCLabelListView * labelListView = [[SCLabelListView alloc] init];
    [impressionView addSubview:labelListView];
    self.impressionListView = labelListView;
    [labelListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.bottom.equalTo(impressionView);
        make.top.equalTo(labelL.mas_bottom).offset(9);
        make.height.mas_equalTo([labelListView calculateViewHeight]);
    }];
    
}
+(UILabel *)labelWithStr:(NSString *)str{
    UILabel * label = [[UILabel alloc] init];
    label.text = str;
    label.font = [SCFontManager boldItalicFontWithSize:16.0f];
    label.textColor = [UIColor scWhite];
    return label;
}


// 已废弃的 Model 版本方法
// -(void)configWithImgs:(NSArray<NSString *> *) images model:(SCUserInfoModel *) userModel{
//     [self.imageBrowserView configWithNSArray:images];
//     self.followBtn.selected = userModel.isFriend;
//    self.userNameLabel.text = userModel.nickname;
//    [self.ageLabel setText:[NSString stringWithFormat:kScAuthMar.isLanguageForce ? @"%ld |" : @"| %ld",userModel.age]];
//    [self.countryLabel setText:[NSString stringWithFormat:@"%@ %@", [SCCountryService emojiForCountry:userModel.country] ,userModel.country]];
//    
//    NSString * cellStr = [NSString stringWithFormat:@" %ld / min",userModel.unitPrice];
//    ///计算文本宽度
//    UIFont *cellFont = kScUIFontMedium(12.0f); // 设置文本的字体
//    CGSize cellTextSize = [cellStr sizeWithAttributes:@{NSFontAttributeName: cellFont}];
//    CGFloat cellTextWidth = cellTextSize.width;
//    [self.chargeBtn setTitle:cellStr forState:UIControlStateNormal];
//    [self.chargeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.width.mas_equalTo(cellTextWidth + 40);
//    }];
//    
//    [self.aboutLabel setText:userModel.about];
//     [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.height.mas_equalTo(userModel.videoMedias.count ? 150 : 0);
//    }];
//    [self.videoListView refreshWithMediaList:userModel.videoMedias];
//}
-(void)configWithImgs:(NSArray<NSString *> *) images userDict:(NSDictionary *) userDict{
    [self.imageBrowserView configWithNSArray:images];

    // 使用字典数据配置UI
    BOOL isFriend = [SCDictionaryHelper boolFromDictionary:userDict forKey:@"isFriend" defaultValue:NO];
    self.followBtn.selected = isFriend;

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    self.userNameLabel.text = nickname;

    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    [self.ageLabel setText:[NSString stringWithFormat:kScAuthMar.isLanguageForce ? @"%ld |" : @"| %ld", age]];

    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
    [self.countryLabel setText:[NSString stringWithFormat:@"%@ %@", [SCCountryService emojiForCountry:country], country]];

    NSInteger unitPrice = [SCDictionaryHelper integerFromDictionary:userDict forKey:@"unitPrice" defaultValue:0];
    NSString * cellStr = [NSString stringWithFormat:@" %ld / min", unitPrice];
    ///计算文本宽度
    UIFont *cellFont = kScUIFontMedium(12.0f); // 设置文本的字体
    CGSize cellTextSize = [cellStr sizeWithAttributes:@{NSFontAttributeName: cellFont}];
    CGFloat cellTextWidth = cellTextSize.width;
    [self.chargeBtn setTitle:cellStr forState:UIControlStateNormal];
    [self.chargeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(cellTextWidth + 40);
    }];

    NSString *about = [SCDictionaryHelper stringFromDictionary:userDict forKey:@"about" defaultValue:@""];
    [self.aboutLabel setText:about];

    // 视频媒体处理 - 现在直接使用字典数组
    NSArray *videoMedias = [SCDictionaryHelper videoMediasFromUserDict:userDict];
    [self.videoView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(videoMedias.count ? 150 : 0);
    }];
    // 直接传递字典数组给videoListView
    [self.videoListView refreshWithMediaList:videoMedias];
}

-(void)configWithStatus:(NSString *) statusStr{
    SCAnchorStatus status = [SCDictionaryHelper anchorStatusFromString:statusStr];
    self.onlineStatusView.dotView.backgroundColor = [SCDictionaryHelper dotColorForAnchorStatus:status];
}

-(void) configWithLabelDict:(NSDictionary *)userExtraDict{
    NSArray<NSString *> *labelsList = [SCDictionaryHelper arrayFromDictionary:userExtraDict forKey:SCDictionaryKeys.shared.kSCUserExtraLabelsListKey defaultValue:@[]];
    _impressionListView.tags = labelsList;
    [_impressionListView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([self.impressionListView calculateViewHeight]);
    }];
}

// 配置礼物数量数据
-(void) configWithGiftDicts:(NSArray<NSDictionary*> *)giftDicts{
    _giftListView.giftDicts = giftDicts;
    [_giftListView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo([self.giftListView calculateViewHeight]);
    }];
    _giftView.hidden = giftDicts.count <= 0;
}
@end
