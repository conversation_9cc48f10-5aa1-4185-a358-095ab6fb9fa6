//
//  SCGiftListView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/3.
//

#import "SCGiftListView.h"
#import "SCGiftNumCell.h"
#import <Masonry/Masonry.h>

@interface SCGiftListView ()<UICollectionViewDelegate,UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UICollectionView *collectionView;

@end

@implementation SCGiftListView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
        layout.scrollDirection = UICollectionViewScrollDirectionVertical;
        layout.minimumLineSpacing = 10;
        layout.minimumInteritemSpacing = 9;
        
        self.collectionView = [[UICollectionView alloc] initWithFrame:self.bounds collectionViewLayout:layout];
        self.collectionView.delegate = self;
        self.collectionView.dataSource = self;
        self.collectionView.backgroundColor = [UIColor scGlobalBgColor];
        self.collectionView.scrollEnabled = NO;
        self.collectionView.contentInsetAdjustmentBehavior = NO;
        [self.collectionView registerClass:[SCGiftNumCell class] forCellWithReuseIdentifier:kGiftCellIdentifier];
        [self addSubview:self.collectionView];
        [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self);
        }];
        
        self.itemSpacing = 9;
        self.countFont = kScUIFontMedium(12);
    }
    return self;
}

// 设置礼物数据
- (void)setGiftDicts:(NSArray<NSDictionary *> *)giftDicts {
    _giftDicts = giftDicts;
    [self.collectionView reloadData];
}

- (CGFloat)calculateViewHeight {
    CGFloat totalHeight = 0;

    if (self.giftDicts.count > 0) {
        totalHeight = self.collectionView.collectionViewLayout.collectionViewContentSize.height;
    }

    return totalHeight;
}

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.giftDicts.count;
}
static NSString *const kGiftCellIdentifier = @"SCGiftNumCell";
- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    SCGiftNumCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kGiftCellIdentifier forIndexPath:indexPath];

    if (self.giftDicts.count > 0) {
        NSDictionary *giftDict = self.giftDicts[indexPath.item];
        [cell configureWithDict:giftDict];
    }

    return cell;
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    CGFloat width = (collectionView.scWidth - 3 * self.itemSpacing) / 4;
    CGFloat height = (88.0f / 79.0f) * width;
    return CGSizeMake(width , height);
}

@end
