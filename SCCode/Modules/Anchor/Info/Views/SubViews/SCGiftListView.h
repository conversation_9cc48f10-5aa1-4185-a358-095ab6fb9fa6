//
//  SCGiftListView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/3.
//

#import <UIKit/UIKit.h>
NS_ASSUME_NONNULL_BEGIN

@interface SCGiftListView : UIView

@property (nonatomic, assign) CGFloat itemSpacing; // 每个item之间的间距，默认为9
@property (nonatomic, strong) UIFont *countFont; // 数量文本的字体，默认为系统字体14

// 礼物数据
@property (nonatomic, strong) NSArray<NSDictionary *> *giftDicts;

- (CGFloat)calculateViewHeight;

@end

NS_ASSUME_NONNULL_END
