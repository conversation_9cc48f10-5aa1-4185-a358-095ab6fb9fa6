//
//  SCVideoHorizontalListView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <UIKit/UIKit.h>
//视图
@class SCVideoHorizontalListView;
NS_ASSUME_NONNULL_BEGIN

@protocol SCVideoHorizontalListViewDelegate <NSObject>

- (void) scVideoHorizontalListView:(SCVideoHorizontalListView *)videoListView index:(NSInteger) index;

@end

@interface SCVideoHorizontalListView : UIView

@property(nonatomic,weak) id<SCVideoHorizontalListViewDelegate> delegate;

- (void)refreshWithMediaList:(NSArray<NSDictionary *> *)medias;

@end

NS_ASSUME_NONNULL_END
