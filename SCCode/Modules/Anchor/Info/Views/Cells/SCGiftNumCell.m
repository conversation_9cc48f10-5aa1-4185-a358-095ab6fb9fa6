//
//  SCGiftNumCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/4.
//

#import "SCGiftNumCell.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCGiftNumCell ()
@property (nonatomic,weak) UIImageView * _Nullable imageView;
@property (nonatomic,weak) UILabel * _Nullable numL;
@end

@implementation SCGiftNumCell


- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]) {
        [self setupViews];
    }
    return self;
}

- (void) setupViews{
    self.contentView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    self.contentView.layer.cornerRadius = 5.0f;
    self.contentView.layer.masksToBounds = YES;
    
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:imageView];
    self.imageView = imageView;
    
    UILabel *numL = [[UILabel alloc] init];
    numL.font = kScUIFontMedium(12.0f);
    numL.textColor = [UIColor scWhite];
    numL.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:numL];
    self.numL = numL;
}
static CGFloat kNumLabelHeight = 15.0;
- (void)layoutSubviews{
    [super layoutSubviews];
    ///更新布局
    self.imageView.frame = CGRectMake(0, 5, self.contentView.bounds.size.width, self.contentView.bounds.size.height-kNumLabelHeight-10-5);
    self.numL.frame = CGRectMake(0, CGRectGetMaxY(self.imageView.frame)+2, self.contentView.bounds.size.width, kNumLabelHeight);
}

#pragma mark - Setter

// 配置方法
- (void)configureWithDict:(NSDictionary *)giftNumDict{
    NSString *iconThumbPath = kSCGiftIconThumbPathFromDict(giftNumDict);
    [self.imageView sc_setImageWithURL:iconThumbPath];

    NSInteger giftNum = kSCGiftNumFromDict(giftNumDict);
    self.numL.text = [NSString stringWithFormat:@"x%ld", giftNum];
}

@end
