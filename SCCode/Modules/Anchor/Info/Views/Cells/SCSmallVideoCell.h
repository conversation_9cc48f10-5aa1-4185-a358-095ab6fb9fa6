//
//  SCSmallVideoCell.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <UIKit/UIKit.h>
@class SCSmallVideoCell;

@protocol SCSmallVideoCellDelegate <NSObject>
-(void)scSmallVideoCell:(SCSmallVideoCell * _Nonnull)cell didClick:(NSInteger)index;

@end
NS_ASSUME_NONNULL_BEGIN



@interface SCSmallVideoCell : UICollectionViewCell
@property(nonatomic,strong,readonly) UIImageView * imgV;
@property(nonatomic,weak) id<SCSmallVideoCellDelegate> delegate;
-(void) configWithModel:(NSDictionary * _Nonnull)model index:(NSInteger )index;
@end

NS_ASSUME_NONNULL_END
