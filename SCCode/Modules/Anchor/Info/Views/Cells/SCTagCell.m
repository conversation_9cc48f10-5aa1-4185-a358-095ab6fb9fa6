//
//  SCTagCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/2.
//

#import "SCTagCell.h"

@implementation SCTagCell

- (void)initUI{
    [super initUI];
    
    UILabel *label = [[UILabel alloc] init];
    label.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:label];
    self.label = label;
    UIEdgeInsets padding = UIEdgeInsetsMake(8, 12, 8, 12);
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView).with.insets(padding);
    }];
}

- (void)prepareForReuse {
    [super prepareForReuse];
    self.label.text = nil;
}

- (void)layoutSubviews{
    [super layoutSubviews];
}

@end
