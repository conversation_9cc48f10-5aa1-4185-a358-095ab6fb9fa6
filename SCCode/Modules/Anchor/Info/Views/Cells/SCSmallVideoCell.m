//
//  SCSmallVideoCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26.
//

#import "SCSmallVideoCell.h"
#import <Masonry/Masonry.h>
#import "SCDictionaryHelper.h"


@interface SCSmallVideoCell()
@property(nonatomic,strong) UIButton * playBtn;
@property(nonatomic,nonnull,strong) NSDictionary * model; // 改为使用字典替代SCMediaListModel
@property(nonatomic,assign) NSInteger index;
@end

@implementation SCSmallVideoCell

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupCell];
    }
    return self;
}

- (void)setupCell {
    // 在这里设置cell的样式和布局

    self.contentView.backgroundColor = [UIColor scGlobalBgColor];
    
    //圆角
    self.contentView.layer.cornerRadius = 13.0f;
    self.contentView.layer.masksToBounds = true;
    
    _imgV = [[UIImageView alloc] init];
    _imgV.contentMode = UIViewContentModeScaleAspectFill;
    
    _playBtn = [UIButton buttonWithImage:[SCResourceManager loadImageWithName:@"btn_video_play"] target:self action:@selector(onClickPlay)];
    
    [self.contentView addSubview:self.imgV];
    [self.contentView addSubview:self.playBtn];
    
    
    [self.imgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    [self.playBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    
}
- (void)configWithModel:(NSDictionary *)model index:(NSInteger)index{
    // 数据验证
    if (![model isKindOfClass:[NSDictionary class]]) {
        
        return;
    }

    // 使用字典安全访问媒体URL，支持多种可能的键名
    NSString *imageUrl = [SCDictionaryHelper stringFromDictionary:model forKey:@"thumbUrl" defaultValue:nil];
    if (!imageUrl || imageUrl.length == 0) {
        imageUrl = [SCDictionaryHelper stringFromDictionary:model forKey:@"mediaUrl" defaultValue:nil];
    }
    if (!imageUrl || imageUrl.length == 0) {
        imageUrl = [SCDictionaryHelper stringFromDictionary:model forKey:@"url" defaultValue:@""];
    }

    // 设置图片，如果URL为空则显示占位图
    if (imageUrl && imageUrl.length > 0) {
        [_imgV sc_setImageWithURL:imageUrl];
    } else {
        // 设置默认占位图
        _imgV.image = [UIImage imageNamed:@"placeholder_media"];
        
    }

    // 根据媒体类型显示/隐藏播放按钮
    NSString *mediaType = [SCDictionaryHelper stringFromDictionary:model forKey:@"mediaType" defaultValue:@""];
    _playBtn.hidden = !([mediaType isEqualToString:@"video"]); 

    _model = model;
    _index = index;
}

- (void) onClickPlay{
    if(self.delegate != nil && [self.delegate respondsToSelector:@selector(scSmallVideoCell:didClick:)]){
        [self.delegate scSmallVideoCell:self didClick:_index];
    }
}


@end
