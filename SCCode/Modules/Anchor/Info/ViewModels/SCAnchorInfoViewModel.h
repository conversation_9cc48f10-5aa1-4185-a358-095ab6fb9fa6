//
//  SCAnchorInfoViewModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

@interface SCAnchorInfoViewModel : NSObject
@property (nonatomic, copy,readonly) NSString *userID;
@property (nonatomic, copy) NSString *userStatus;
@property (nonatomic, strong) NSDictionary *userDict;
@property (nonatomic, strong) NSDictionary *userExtraDict;

// 礼物数量字典数组
@property(nonatomic,strong,readonly)NSArray<NSDictionary *> * giftNumDicts;

- (instancetype)initWithUserID:(NSString * _Nonnull) userID;
-(NSArray<NSString *> *)photoMediaUrls;

// 便捷访问方法
- (NSString *)getUserNickname;
- (NSString *)getUserAvatarUrl;
- (NSInteger)getUserAge;
- (NSInteger)getUserGender;
- (BOOL)getIsFriend;
- (BOOL)getIsBlock;

// 网络请求方法
-(void) requestUserInfoDictWithSuccess:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
-(void) requestUserExtraInfoDictWithSuccess:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;

// 请求礼物数量数据
-(void) requestGiftsNumDictsWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure;
@end

NS_ASSUME_NONNULL_END
