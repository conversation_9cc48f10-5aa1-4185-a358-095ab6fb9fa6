//
//  SCAnchorInfoViewModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/26.
//

#import "SCAnchorInfoViewModel.h"
#import "SCAPIServiceManager.h"
#import "SCGiftService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCAnchorInfoViewModel()

@end

@implementation SCAnchorInfoViewModel

- (instancetype)initWithUserID:(NSString * _Nonnull) userID
{
    self = [super init];
    if (self) {
        _userID = userID;
    }
    return self;
}

-(void) _init{
    
}

// 移除Model版本的方法，统一使用字典版本
- (NSArray<NSString *> *)photoMediaUrls{
    if (self.userDict) {
        return [SCDictionaryHelper photoMediaUrlsDefaultAvatarFromUserDict:self.userDict];
    }
    return @[];
}

#pragma mark - 便捷访问方法

- (NSString *)getUserNickname {
    return [SCDictionaryHelper stringFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
}

- (NSString *)getUserAvatarUrl {
    return [SCDictionaryHelper stringFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
}

- (NSInteger)getUserAge {
    return [SCDictionaryHelper integerFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
}

- (NSInteger)getUserGender {
    return [SCDictionaryHelper integerFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserGenderKey defaultValue:0];
}

- (BOOL)getIsFriend {
    return [SCDictionaryHelper boolFromDictionary:self.userDict forKey:@"isFriend" defaultValue:NO];
}

- (BOOL)getIsBlock {
    return [SCDictionaryHelper boolFromDictionary:self.userDict forKey:@"isBlock" defaultValue:NO];
}

-(void) requestUserExtraInfoDictWithSuccess:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);
    if (self.userID) {
        // 使用现有的字典版本API调用
        [SCAPIServiceManager requesGetUserExtraInfoWithUserID:self.userID success:^(NSDictionary * _Nonnull userExtraDict) {
            __strong SCAnchorInfoViewModel * strongSelf = weakself;
            if(strongSelf != nil){
                strongSelf.userExtraDict = userExtraDict;
            }
            kSCBlockExeNotNil(success, userExtraDict);
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            kSCBlockExeNotNil(failure,error);
        }];
    }
}

// 请求礼物数量数据
-(void) requestGiftsNumDictsWithSuccess:(void (^_Nullable)(void))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);

    [kSCAuthGiftService requestGiftsNumDictsWithUserId:self.userID success:^(NSArray<NSDictionary *> * _Nullable giftNumDicts) {
        __strong SCAnchorInfoViewModel * strongSelf = weakself;
        if(strongSelf != nil && giftNumDicts != nil){
            strongSelf -> _giftNumDicts = giftNumDicts;
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            kSCBlockExeNotNil(success);
        });
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        dispatch_async(dispatch_get_main_queue(), ^{
            kSCBlockExeNotNil(failure,error);
        });
    }];

}

-(void) requestUserInfoDictWithSuccess:(void (^_Nullable)(NSDictionary *_Nonnull))success failure:(void (^_Nullable)(SCXErrorModel *_Nonnull))failure{
    kWeakSelf(self);
    // 使用字典版本的API调用
    [SCAPIServiceManager requestUserInfoWithUserId:_userID cachePolicy:SCNetCachePolicyCacheAndRefreshCallback success:^(NSDictionary * _Nonnull userDict) {
        __strong SCAnchorInfoViewModel * strongSelf = weakself;
        if(strongSelf == nil){
            return;
        }

        // 处理字典数据
        NSMutableDictionary *mutableUserDict = [userDict mutableCopy];

        // 如果有自定义状态，更新字典中的状态
        if(strongSelf.userStatus){
            mutableUserDict[SCDictionaryKeys.shared.kSCUserStatusKey] = strongSelf.userStatus;
        }

        // 直接使用字典数据
        strongSelf.userDict = [mutableUserDict copy];

        kSCBlockExeNotNil(success, strongSelf.userDict);
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        kSCBlockExeNotNil(failure,error);
    }];
}

- (void)sc_blank_empty{}
@end
