//
//  SCPersonalItemDisplayModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef enum : NSUInteger {
    SCPersonalItemCellStyleDefault = 0,
    SCPersonalItemCellStyleOnlyTitleAndArrow,
    SCPersonalItemCellStyleCoins,
    SCPersonalItemCellStyleSwitch,
    SCPersonalItemCellStyleCopy,
    SCPersonalItemCellStyleNoArrow,
} SCPersonalItemCellStyle;
@interface SCPersonalItemDisplayModel : NSObject

@property(nonatomic, copy) NSString *title;
@property(nonatomic, copy) NSString *subTitle;
@property(nonatomic, copy) NSString *image;
///当 style 为 SCPersonalItemCellStyleSwitch 时发生作用
@property(nonatomic, assign) Boolean isON;
@property(nonatomic, assign) SCPersonalItemCellStyle style;


///初始化
-(instancetype) initWithTitle:(NSString *_Nullable) title subTitle:(NSString *_Nullable) subTitle image:(NSString *_Nullable) image style:(SCPersonalItemCellStyle)style;

///初始化
-(instancetype) initSwitchStyleWithTitle:(NSString *_Nullable) title subTitle:(NSString *_Nullable) subTitle image:(NSString *_Nullable) image isOn:(Boolean) isOn;

@end

NS_ASSUME_NONNULL_END
