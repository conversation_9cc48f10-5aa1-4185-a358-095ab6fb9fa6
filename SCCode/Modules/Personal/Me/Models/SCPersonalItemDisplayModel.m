//
//  SCPersonalItemDisplayModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCPersonalItemDisplayModel.h"

@implementation SCPersonalItemDisplayModel

- (instancetype)initWithTitle:(NSString *)title subTitle:(NSString *)subTitle image:(NSString *)image style:(SCPersonalItemCellStyle)style{
    self = [super init];
    if (self) {
        _title = title;
        _subTitle = subTitle;
        _image = image;
        _style = style;
    }
    return self;
}
-(instancetype) initSwitchStyleWithTitle:(NSString *_Nullable) title subTitle:(NSString *_Nullable) subTitle image:(NSString *_Nullable) image isOn:(Boolean) isOn{
    self = [super init];
    if (self) {
        _title = title;
        _subTitle = subTitle;
        _image = image;
        _style = SCPersonalItemCellStyleSwitch;
        _isON = isOn;
    }
    return self;
}

@end
