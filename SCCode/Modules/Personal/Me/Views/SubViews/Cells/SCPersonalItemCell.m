//
//  SCPersonalItemCell.m
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCPersonalItemCell.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCPersonalItemCell()

@property(nonatomic,weak) UIImageView *iconImageView;
//渐变色背景
@property(nonatomic,weak) UIImageView *contentBgIV;

//金币cell的标题，font比较大，用来暂时解决刷新列表，字体忽大忽小问题。
@property(nonatomic,weak) UILabel *coinsTitleLabel;
@property(nonatomic,weak) UILabel *titleLabel;
@property(nonatomic,weak) UILabel *subTitleLabel;
@property(nonatomic,weak) UIImageView *arrowImageView;
@property(nonatomic,weak) UISwitch *switchView;
@property(nonatomic,weak) UIButton *mCopyBtn;
@property (nonatomic, weak) UIView *customContentView;
@property (nonatomic, weak) UIStackView *leftStackView;
@property(nonatomic,assign) SCPersonalItemCellStyle scStyle;

@end
@implementation SCPersonalItemCell
#pragma mark - UI初始化
- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (void)initUI{
    [super initUI];
    self.contentView.backgroundColor = [UIColor scGlobalBgColor];
    
    UIView *view = [[UIView alloc]init];
    view.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    [self.contentView addSubview:view];
    self.customContentView = view;
        
    UIImage *coinsImage = [SCResourceManager loadImageWithName:@"bg_personal_coins"];
    UIImageView *contentBgIV = [UIImageView new].setImage(kScAuthMar.isLanguageForce ? [coinsImage imageWithHorizontallyFlippedOrientation] : coinsImage);
    contentBgIV.contentMode = UIViewContentModeScaleAspectFill;
    contentBgIV.hidden = YES;
    [self.customContentView insertSubview:contentBgIV atIndex:0];
    self.contentBgIV = contentBgIV;
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.spacing = 14;
    [self.customContentView addSubview:stackView];
    self.leftStackView = stackView;
    
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.leftStackView addArrangedSubview:iconImageView];
    self.iconImageView = iconImageView;
    
    UILabel *titleLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:[SCFontManager boldFontWithSize:14.0f] alignment:NSTextAlignmentNatural];
    [self.leftStackView addArrangedSubview:titleLabel];
    self.titleLabel = titleLabel;
    
    UILabel *coinTitleLabel = [UILabel labelWithText:@"" textColor:[UIColor scWhite] font:[SCFontManager boldItalicFontWithSize:16.0f] alignment:NSTextAlignmentNatural];
    [self.leftStackView addArrangedSubview:coinTitleLabel];
    self.coinsTitleLabel = coinTitleLabel;
    
    UILabel *subTitleLabel = [UILabel labelWithText:@"" textColor:[UIColor scGray] font:kScUIFontMedium(14.0f) alignment:NSTextAlignmentNatural];
    [self.customContentView addSubview:subTitleLabel];
    self.subTitleLabel = subTitleLabel;
    
    UIImageView *arrowImageView = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_arr_right_gray" isAutoForce:YES]];
    arrowImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.customContentView addSubview:arrowImageView];
    self.arrowImageView = arrowImageView;
    
    UISwitch * switchBtn = [[UISwitch alloc] init];
    [switchBtn addTarget:self action:@selector(onSwitch) forControlEvents:UIControlEventValueChanged];
    //改变开关颜色
    [switchBtn setOnTintColor:[UIColor colorWithHexString:@"#FF5450"]];
    [self.customContentView addSubview:switchBtn];
    self.switchView = switchBtn;
    
    UIButton *copyBtn = [UIButton buttonWithTitle:@"Copy".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(12) image:nil backgroundColor:nil cornerRadius:12];
    [copyBtn sc_setThemeGradientBackgroundWithCornerRadius:12.0f];
    [self.customContentView addSubview:copyBtn];
    self.mCopyBtn = copyBtn;
    
    kSCAddTapGesture(copyBtn, self, onCopy);

    [self setupConstraints];
}

- (void)setupConstraints {
    [self.customContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(20);
        make.trailing.equalTo(self.contentView).offset(-20);
        make.centerY.equalTo(self.contentView);
        make.height.mas_equalTo(50);
    }];
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.customContentView);
    }];
    
    [self.leftStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.customContentView).offset(15);
        make.centerY.equalTo(self.customContentView);
        make.trailing.mas_lessThanOrEqualTo(self.subTitleLabel.mas_leading).offset(20);
    }];

    // 为 titleLabel 添加压缩阻力优先级
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.titleLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.coinsTitleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.coinsTitleLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo([SCPersonalItemCell iconSizeWithStyle:self.scStyle]);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.customContentView).offset(-15);
        make.centerY.equalTo(self.customContentView);
    }];
    
    [self.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.customContentView).offset(-13);
        make.centerY.equalTo(self.customContentView);
        make.size.mas_equalTo(CGSizeMake(15, 15));
    }];
    
    [self.switchView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.customContentView).offset(-17);
        make.centerY.equalTo(self.customContentView);
    }];
    
    [self.mCopyBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.customContentView).offset(-10);
        make.centerY.equalTo(self.customContentView);
        make.size.mas_equalTo(CGSizeMake(60, 24));
    }];
}

- (void)configWithImage:(NSString *)image title:(NSString *)title subTitle:(NSString *)subTitle style:(SCPersonalItemCellStyle)style isNo:(BOOL)isNO {
    _scStyle = style;
    self.iconImageView.image = [SCResourceManager loadImageWithName:image];
    if (style == SCPersonalItemCellStyleCoins) {
        self.titleLabel.hidden = YES;
        self.coinsTitleLabel.hidden = NO;
        self.coinsTitleLabel.text = title;
    } else {
        self.titleLabel.hidden = NO;
        self.coinsTitleLabel.hidden = YES;
        self.titleLabel.text = title;
    }
    self.subTitleLabel.text = subTitle;
    self.titleLabel.textColor = UIColor.scWhite;
    self.subTitleLabel.textColor = UIColor.scWhite;
    self.customContentView.layer.cornerRadius = 10.0f;
    self.switchView.on = isNO;
    self.arrowImageView.hidden = YES;
    self.mCopyBtn.hidden = YES;
    self.switchView.hidden = YES;
    self.contentBgIV.hidden = YES;
    
    switch (style) {
        case SCPersonalItemCellStyleCoins:
        {
            self.customContentView.layer.borderWidth = 0;
            self.subTitleLabel.font = kScUIFontSemibold(24.0f);
            self.contentBgIV.hidden = NO;
            [self.customContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(80);
            }];
            break;
        }
        case SCPersonalItemCellStyleOnlyTitleAndArrow:
        {
            [self.customContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(50);
            }];
            self.arrowImageView.hidden = NO;
            break;
        }
        case SCPersonalItemCellStyleSwitch:
        {
            [self.customContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(50);
            }];
            self.switchView.hidden = NO;
            break;
        }
        case SCPersonalItemCellStyleCopy:
        {
            [self.customContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(50);
            }];
            self.mCopyBtn.hidden = NO;
            break;
        }
        default:
        {
            [self.customContentView mas_updateConstraints:^(MASConstraintMaker *make) {
                make.height.mas_equalTo(50);
            }];
            self.arrowImageView.hidden = NO;
            break;
        }
    }
    
    [self updateConstraintsForStyle:style];
}

- (void)updateConstraintsForStyle:(SCPersonalItemCellStyle)style {
    if (style == SCPersonalItemCellStyleCoins) {
        self.leftStackView.spacing = 5;
        [self.leftStackView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.customContentView).offset(15.0f);
            make.top.equalTo(self.customContentView).offset(13.0f);
            make.trailing.mas_lessThanOrEqualTo(self.customContentView).offset(-15);
            make.height.mas_equalTo(20.0f);
        }];
        [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.leftStackView);
            make.top.equalTo(self.leftStackView.mas_bottom);
        }];
    } else {
        self.leftStackView.spacing = 14;
        [self.leftStackView mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.customContentView).offset(15);
            make.centerY.equalTo(self.customContentView);
            make.trailing.mas_lessThanOrEqualTo(self.subTitleLabel.mas_leading).offset(20);
            make.height.mas_equalTo(25.0f);
        }];
        if (style == SCPersonalItemCellStyleCopy) {
            [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(self.mCopyBtn.mas_leading).offset(-5);
                make.centerY.equalTo(self.customContentView);
            }];
        } else if (style == SCPersonalItemCellStyleSwitch) {
            [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(self.switchView.mas_leading).offset(-10);
                make.centerY.equalTo(self.customContentView);
            }];
        } else {
            [self.subTitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(self.arrowImageView.mas_leading).offset(-5);
                make.centerY.equalTo(self.customContentView);
            }];
        }
    }
    
    self.iconImageView.hidden = self.iconImageView.image == nil;
    [self.customContentView layoutIfNeeded];
}

-(void)onCopy{
    if(_delegate != nil && [_delegate respondsToSelector:@selector(scPersonalItemCell:onClickBtnWithIndex:)]){
        [_delegate scPersonalItemCell:self onClickBtnWithIndex:self.index];
    }
}
-(void)onSwitch{
    if(_delegate != nil && [_delegate respondsToSelector:@selector(scPersonalItemCell:onSwitch:index:)]){
        [_delegate scPersonalItemCell:self onSwitch:self.switchView index:self.index];
    }
}


#pragma mark - 配置
-(void) configWithImage:(NSString *)image
                  title:(NSString *)title
                  subTitle:(NSString *)subTitle
                  style:(SCPersonalItemCellStyle)style{
    [self configWithImage:image title:title subTitle:subTitle style:style isNo:NO];
}
+(CGFloat) heightWithStyle:(SCPersonalItemCellStyle)style{
    switch (style) {
        case SCPersonalItemCellStyleCoins:
            return 90;
            break;
        default:
            return 50;
            break;
    }
}
//图标大小
+ (CGSize) iconSizeWithStyle:(SCPersonalItemCellStyle)style{
    switch (style) {
        case SCPersonalItemCellStyleCoins:
            return CGSizeMake(47, 47);
            break;
        default:
            return CGSizeMake(25, 25);
            break;
    }
}

- (void)configureWithItemDict:(NSDictionary *)itemDict {
    self.itemDict = itemDict;

    // 从字典中获取配置信息
    NSString *title = [SCDictionaryHelper stringFromDictionary:itemDict forKey:@"title" defaultValue:@""];
    NSString *subTitle = [SCDictionaryHelper stringFromDictionary:itemDict forKey:@"subTitle" defaultValue:@""];
    NSString *image = [SCDictionaryHelper stringFromDictionary:itemDict forKey:@"image" defaultValue:@""];
    NSInteger styleValue = [SCDictionaryHelper integerFromDictionary:itemDict forKey:@"style" defaultValue:SCPersonalItemCellStyleDefault];
    SCPersonalItemCellStyle style = (SCPersonalItemCellStyle)styleValue;

    // 调用原有的配置方法
    [self configWithImage:image title:title subTitle:subTitle style:style];
}

@end
