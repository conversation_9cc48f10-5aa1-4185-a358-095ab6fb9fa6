//
//  SCPersonalItemCell.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import <UIKit/UIKit.h>
#import "SCBaseTableViewCell.h"
#import "SCPersonalItemDisplayModel.h"
@class SCPersonalItemCell;

NS_ASSUME_NONNULL_BEGIN

@protocol SCPersonalItemCellDelegate <NSObject>
//点击按钮
-(void)scPersonalItemCell:(SCPersonalItemCell *)cell onClickBtnWithIndex:(NSInteger) index;
//开关
-(void)scPersonalItemCell:(SCPersonalItemCell *)cell onSwitch:(UISwitch *)switchView index:(NSInteger) index;

@end

@interface SCPersonalItemCell : SCBaseTableViewCell
@property(nonatomic,assign) NSInteger index;
///代理
@property(nonatomic,weak) id<SCPersonalItemCellDelegate> delegate;
@property(nonatomic,strong) NSDictionary * itemDict;

///配置数据
-(void) configWithImage:(NSString *)image
                  title:(NSString *)title
                  subTitle:(NSString *)subTitle
                  style:(SCPersonalItemCellStyle)style;
- (void)configWithImage:(NSString *)image title:(NSString *)title subTitle:(NSString *)subTitle style:(SCPersonalItemCellStyle)style isNo:(BOOL) isNO;
+(CGFloat) heightWithStyle:(SCPersonalItemCellStyle)style;

// 字典支持方法
- (void)configureWithItemDict:(NSDictionary *)itemDict;

@end

NS_ASSUME_NONNULL_END
