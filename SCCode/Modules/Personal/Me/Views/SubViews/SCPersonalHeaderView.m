//
//  SCPersonalHeaderView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCPersonalHeaderView.h"
#import <Masonry/Masonry.h>
//模型
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
//View
#import "SCBannerView.h"
#import "SCBannerService.h"
#import "SCFontManager.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCPersonalHeaderView()
///背景
@property (weak, nonatomic) UIImageView *bgImageView;
///标题
@property (weak, nonatomic) UILabel *titleLabel;
///头像
@property (weak, nonatomic) UIImageView *avatarImageView;
///编辑按钮
@property (weak, nonatomic) UIImageView *editBtn;
///昵称
@property (weak, nonatomic) UILabel *nickNameLabel;
///年龄
@property (weak, nonatomic) UILabel *ageLabel;
///国家
@property (weak, nonatomic) UILabel *countryLabel;
///Banner 图
@property (weak, nonatomic) SCBannerView * bannerView;
@property (strong, nonatomic) SCDisposeBag * disposeBag;
@property (nonatomic,assign) BOOL isHaveBanner;
@end

@implementation SCPersonalHeaderView

- (void)dealloc
{
    [_disposeBag dispose];
    _disposeBag = nil;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _disposeBag = [[SCDisposeBag alloc] init];
        [self _initUI];
        [self _initEvent];
    }
    return self;
}
static CGFloat const kAvatarImageViewSize = 74;
-(void) _initUI{
    self.clipsToBounds = YES;
    
    UIImageView *bgImageView = [[UIImageView alloc]initWithImage:[SCResourceManager loadImageWithName:@"bg_personal"]];
    [self addSubview:bgImageView];
    self.bgImageView = bgImageView;
    SCGradientColors *colors = [SCGradientColors gradientWithColors:@[[UIColor colorWithHexString:@"#FF0000"], [UIColor colorWithHexString:@"#460606"]] orientation:SCGradientColorsOrientationHorizontal];
    UIImage *lineImage = [UIImage imageGradientWithSCGradientColors:colors size:CGSizeMake(17, 8)];
    UIImageView *lineImageView = [[UIImageView alloc]initWithImage:kScAuthMar.isLanguageForce ? [lineImage imageWithHorizontallyFlippedOrientation] : lineImage];
    lineImageView.alpha = 0.9;
    [self addSubview:lineImageView];
    
    UILabel *titleLabel = [UILabel labelWithText:@"Me".translateString textColor:[UIColor scWhite] font:[SCFontManager boldItalicFontWithSize:20]];
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
    
    UIImageView *avatarImageView = [UIImageView imageViewWithRadius:kAvatarImageViewSize/2.0];
    avatarImageView.layer.borderWidth = 1.8;
    avatarImageView.layer.borderColor = [UIColor colorWithHexString:@"#FF423E"].CGColor;
    avatarImageView.image = [SCResourceManager loadImageWithName:@"ic_circle_avatar"];
    [self addSubview:avatarImageView];
    self.avatarImageView = avatarImageView;
    
    UIImageView *editBtn = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"btn_personal_edit"]];
    editBtn.userInteractionEnabled = NO;
    kSCAddTapGesture(editBtn, self, onEdit:)
    [self addSubview:editBtn];
    self.editBtn = editBtn;
    
    UILabel *nickNameLabel = [UILabel labelWithText:@"---" textColor:[UIColor scWhite] font:kScUIFontSemibold(16) alignment:NSTextAlignmentCenter];
    [self addSubview:nickNameLabel];
    self.nickNameLabel = nickNameLabel;
    
    UILabel *ageLabel = [UILabel labelWithText:@"---" textColor:[UIColor scWhite] font:kScUIFontMedium(13) alignment:NSTextAlignmentCenter];
    [self addSubview:ageLabel];
    self.ageLabel = ageLabel;
    
    UILabel *countryLabel = [UILabel labelWithText:@"---" textColor:[UIColor scWhite] font:kScUIFontRegular(13) alignment:NSTextAlignmentCenter];
    [self addSubview:countryLabel];
    self.countryLabel = countryLabel;
    
    UIStackView *stackView = [[UIStackView alloc]init];
    stackView.spacing = 10;
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.alignment = UIStackViewAlignmentCenter;
    [self addSubview:stackView];
    
    SCBannerView * bannerView = [[SCBannerView alloc] init];
    bannerView.layer.cornerRadius = kSCNormalCornerRadius;
    bannerView.layer.masksToBounds = true;
    self.bannerView = bannerView;
    self.bannerView.hidden = YES;
    [stackView addArrangedSubview:bannerView];
    
    ///===== 布局 ===
    [bgImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self);
        make.height.mas_equalTo(200.0f);
    }];
    
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self).offset(kSCStatusBarHeight);
    }];
    
    [lineImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(8);
        make.width.equalTo(self.titleLabel.mas_width);
        make.leading.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(-8);
    }];
    
    [avatarImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(23.0f);
        make.top.equalTo(self).offset(73+kSCStatusBarHeight);
        make.size.mas_equalTo(CGSizeMake(kAvatarImageViewSize, kAvatarImageViewSize));
    }];
    
    [editBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(avatarImageView);
        make.trailing.equalTo(self).offset(-15);
        make.size.mas_equalTo(CGSizeMake(30, 30));
    }];
    
    [nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(92+kSCStatusBarHeight);
        make.leading.equalTo(avatarImageView.mas_trailing).offset(20);
        make.height.mas_equalTo(22);
    }];
    
    [ageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(20);
        make.top.equalTo(nickNameLabel.mas_bottom);
        make.leading.equalTo(avatarImageView.mas_trailing).offset(20);
    }];
    
    [countryLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(ageLabel.mas_trailing).offset(5);
        make.centerY.equalTo(ageLabel);
        make.height.mas_equalTo(20);
    }];
    
    [bannerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.mas_equalTo(108);
        make.width.mas_equalTo(kSCScreenWidth - (15*2));
    }];
    
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(avatarImageView.mas_bottom).offset(10);
        make.leading.equalTo(self).offset(15);
        make.trailing.equalTo(self).offset(-15);
    }];
    
    kWeakSelf(self);
    [kScAuthMar.bannerService.bannersObs subscribe:^(NSArray<NSDictionary *> * _Nonnull value) {
        if (value.count > 0) {
            weakself.isHaveBanner = YES;
        }
        if (weakself.isHaveBanner && value.count <= 0) {
            return;
        }
        weakself.bannerView.hidden = value.count <= 0;
        weakself.bannerView.banners = value;
    } error:^(SCXErrorModel * _Nonnull error) {
        weakself.bannerView.hidden = YES;
    } disposeBag:_disposeBag];
}

- (void) _initEvent{
    self.avatarImageView.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onEdit:)];
    [self.avatarImageView addGestureRecognizer:tap];
}


#pragma mark - Setter
// 已废弃的 Model 版本方法
// - (void)setModel:(SCUserInfoModel *)model{
//     _model = model;
//     [self.avatarImageView sc_setImageWithURL:_model.avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
//     self.nickNameLabel.text = _model.nickname;
//     [self.ageLabel setText:[NSString stringWithFormat:@"%ld",_model.age]];
//     [self.countryLabel setText:_model.country];
// }

- (void)setUserDict:(NSDictionary *)userDict {
    _userDict = userDict;

    // 使用字典数据配置UI
    NSString *avatarUrl = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAvatarUrlKey defaultValue:@""];
    [self.avatarImageView sc_setImageWithURL:avatarUrl placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];

    NSString *nickname = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserNicknameKey defaultValue:@""];
    self.nickNameLabel.text = nickname;

    NSInteger age = [SCDictionaryHelper integerFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserAgeKey defaultValue:0];
    [self.ageLabel setText:[NSString stringWithFormat:@"%ld", age]];

    NSString *country = [SCDictionaryHelper stringFromDictionary:userDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""];
    [self.countryLabel setText:country];
}

//计算当前视图高度
+ (CGFloat) calculateHeight{
    CGFloat height = 20 + kSCStatusBarHeight + kAvatarImageViewSize + 10 + 21 + 10 + 20;
    if(SCAuthManager.instance.bannerService.bannersObs.value == nil || SCAuthManager.instance.bannerService.bannersObs.value.count == 0){
        return height;
    }else{
        return height + 108 + 10 ;//加上广告图的高度和间距
    }
}

#pragma mark - Action
- (void) onEdit:(UIGestureRecognizer *) gesture{
    kSCBlockExeNotNil(self.clickAvatar);
}


@end
