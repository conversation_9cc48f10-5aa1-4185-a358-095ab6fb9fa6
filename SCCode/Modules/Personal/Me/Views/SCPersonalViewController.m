//
//  SCPersonalViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCPersonalViewController.h"
//第三方库
#import <Masonry/Masonry.h>
//View
#import "SCPersonalHeaderView.h"
#import "SCPersonalItemCell.h"
//VC
#import "SCAboutViewController.h"
#import "SCSettingViewController.h"
#import "SCPersonalEditViewController.h"
#import "SCCoinsFullScreenViewController.h"
#import "SCCoinsPopupViewController.h"
#import "SCBlockListViewController.h"
#import "SCConversationInfoViewController.h"
#import "SCMyLevelViewController.h"
#import "SCRobotCustomerServiceViewController.h"
//Model
#import "SCPersonalItemDisplayModel.h"
//ViewModel
#import "SCPersonalViewModel.h"
//Service
#import "SCIMService.h"

#pragma mark - interface
#pragma mark UI属性
@interface SCPersonalViewController ()
@property(nonatomic,nullable,weak) SCPersonalHeaderView * headerView;
@property(nonatomic,nullable,weak) UITableView * tableView;
@property(nonatomic,nullable,strong) SCPersonalViewModel * viewModel;
@end
#pragma mark - tableView代理
@interface SCPersonalViewController (SCPersonalTableView)<UITableViewDelegate,UITableViewDataSource>

@end
#pragma mark - implementation
#pragma mark UI初始化
@implementation SCPersonalViewController

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.viewModel reloadUserInfo];
    [self _refreshUI];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    _viewModel = [[SCPersonalViewModel alloc] init];
    [self _initUI];
    [self _initEvent];
    
}

-(void) _initUI{
    
    self.view.backgroundColor = [UIColor scGlobalBgColor];
    [self setIsHiddenSCNavigationBar:YES];
    
    SCPersonalHeaderView * headerView = [[SCPersonalHeaderView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, [SCPersonalHeaderView calculateHeight])];
    _headerView = headerView;
    
    UITableView * tableView = [UITableView tableViewWithFrame:self.view.bounds style:UITableViewStylePlain delegate:self dataSource:self cellClass:[SCPersonalItemCell class] forCellReuseIdentifier:identifierCell];
    tableView.backgroundColor = [UIColor scGlobalBgColor];
    tableView.contentInset = UIEdgeInsetsMake(0, 0, 15, 0);
    [self.view addSubview:tableView];
    _tableView = tableView;
    
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    tableView.tableHeaderView = headerView;
    
    [tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"cell"];
    
    tableView.delegate = self;
    tableView.dataSource = self;
    
}

- (void) _initEvent{
    kWeakSelf(self)
    self.headerView.clickAvatar = ^{
        if(weakself != nil){
            SCPersonalEditViewController *vc = [[SCPersonalEditViewController alloc] init];
            [weakself.navigationController pushViewController:vc animated:true];
        }
    };
    
    [kScAuthMar.availableCoinsObx afterSubscribe:^(NSNumber * _Nullable value) {
        [weakself refreshUserInfo];
    } error:nil disposeBag:self.disposeBag];
    
    ///监听通知
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(receiveUserChangeNotification:) name:kSCChangeUserInfoNoticationKey object:nil];
}

-(void) _refreshUI{
    [_headerView setUserDict:self.viewModel.userDict];
    [_tableView reloadData];
}

- (void)refreshUserInfo {
    kWeakSelf(self)
    [kScAuthMar remoteLoginUserInfo:^(NSDictionary * _Nonnull userInfoDict) {
        [weakself.viewModel reloadUserInfo];
        [weakself _refreshUI];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.viewModel reloadUserInfo];
        [weakself _refreshUI];
    }];
}

- (void) receiveUserChangeNotification:(NSNotification *) notification{
    [self refreshUserInfo];
}

-(void) dealloc{
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kSCChangeUserInfoNoticationKey object:nil];
}


static NSString * identifierCell = @"SCPersonalItemCell";

#pragma mark tableView代理

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCPersonalItemDisplayModel * model = self.viewModel.menus[indexPath.row];
    return [SCPersonalItemCell heightWithStyle:model.style]+10;
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCPersonalItemCell * cell = [tableView dequeueReusableCellWithIdentifier:identifierCell];
    SCPersonalItemDisplayModel * model = self.viewModel.menus[indexPath.row];
    [cell configWithImage:model.image title:model.title subTitle:model.subTitle style:model.style];
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.menus.count;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    
    if(indexPath.row == 0){
        //金币
        SCCoinsFullScreenViewController * vc = [[SCCoinsFullScreenViewController alloc] init];
        [self.navigationController pushViewController:vc animated:YES];
    }else if(indexPath.row == 1){
        //等级
        SCMyLevelViewController *vc = [[SCMyLevelViewController alloc] init];
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if(indexPath.row == 2){
        SCRobotCustomerServiceViewController *vc = [[SCRobotCustomerServiceViewController alloc] init];
        
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if(indexPath.row == 3){
        //拉黑
        SCBlockListViewController *vc = [[SCBlockListViewController alloc] init];
        [self.navigationController pushViewController:vc animated:YES];
    }else if(indexPath.row == 4){
        //about
        SCAboutViewController *vc = [[SCAboutViewController alloc] init];
        [self.navigationController pushViewController:vc animated:YES];
        
    }else if(indexPath.row == 5){
        //设置
        SCSettingViewController * settingVC = [[SCSettingViewController alloc] init];
        [self.navigationController pushViewController:settingVC animated:YES];
    }
    
}

@end
