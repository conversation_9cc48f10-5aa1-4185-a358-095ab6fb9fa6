//
//  SCPersonalViewModel.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/5.
//

#import <Foundation/Foundation.h>
@class SCPersonalItemDisplayModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCPersonalViewModel : NSObject

@property(nonatomic,nonnull, strong,readonly) NSArray<SCPersonalItemDisplayModel *> *menus;

@property(nonatomic,nonnull, strong,readonly) NSDictionary *userDict;
-(void) reloadUserInfo;
-(void) reloadUserDict;
@end

NS_ASSUME_NONNULL_END
