//
//  SCPersonalViewModel.m
//  Supercall
//
//  Created by g<PERSON>weihong on 2024/1/5.
//

#import "SCPersonalViewModel.h"
#import "SCPersonalItemDisplayModel.h"
// #import "SCTokenModel.h" // 已移除，使用字典替代
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
#import "SCCoinsService.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"

@interface SCPersonalViewModel()
@property(nonatomic,strong) SCDisposeBag *disposeBag;
@end
@implementation SCPersonalViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self _init];
    }
    return self;
}

-(void) _init{
    // 直接使用字典数据，不再创建临时Model对象
    [self updateUserDict];
    [self resetMeus];
}
-(void) reloadUserInfo{
    // 直接使用字典数据，不再创建临时Model对象
    [self updateUserDict];
    //刷新金币
    [kSCAuthCoinsService remoteAvailableCoinsWithSuccess:nil failure:nil];
    [self resetMeus];
}

-(void) reloadUserDict{
    [self updateUserDict];
    [self resetMeus];
}

-(void) updateUserDict{
    // 直接从登录用户字典获取用户信息
    NSDictionary *userInfoDict = [SCDictionaryHelper dictionaryFromDictionary:kScAuthMar.loginUserDict forKey:SCDictionaryKeys.shared.kSCUserInfoKey defaultValue:@{}];
    _userDict = userInfoDict;
}

-(void) resetMeus{
    // 使用字典方式获取用户金币和等级信息
    NSInteger availableCoins = [SCDictionaryHelper integerFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserAvailableCoinsKey defaultValue:0];
    NSInteger userLevel = [SCDictionaryHelper integerFromDictionary:self.userDict forKey:SCDictionaryKeys.shared.kSCUserLevelKey defaultValue:1];

    _menus = @[
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"My Diamonds:".translateString subTitle:[NSString stringWithFormat:@"%ld", availableCoins] image:nil style:SCPersonalItemCellStyleCoins],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"My Level".translateString subTitle:[NSString stringWithFormat:@"Lv%ld", userLevel] image:@"ic_persion_level" style:SCPersonalItemCellStyleDefault],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Customer Service".translateString subTitle:@"" image:@"ic_nav_robot_customer" style:SCPersonalItemCellStyleDefault],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Blocked List".translateString subTitle:@"" image:@"ic_persion_block" style:SCPersonalItemCellStyleDefault],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"About".translateString subTitle:@"" image:@"ic_persion_about" style:SCPersonalItemCellStyleDefault],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Setting".translateString subTitle:@"" image:@"ic_persion_setting" style:SCPersonalItemCellStyleDefault],

    ];
}

@end
