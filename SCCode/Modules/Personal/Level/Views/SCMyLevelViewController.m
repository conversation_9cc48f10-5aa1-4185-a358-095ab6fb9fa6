//
//  SCMyLevelViewController.m
//  Supercall
//
//  Created by guanweihong on 2024/2/6.
//

#import "SCMyLevelViewController.h"
#import "SCMyLevelHeaderView.h"
#import "SCMyLevelCell.h"
#import "SCAPIServiceManager.h"
#import "SCCoinsFullScreenViewController.h"
#import "SCPayService.h"
#import "SCSocketService.h"
#import "SCOrderResultModel.h"
#import "SCDictionaryHelper.h"
#import "SCDictionaryKeys.h"
#import "SCCoinsPopupViewController.h"
#import "SCNavigationBar.h"

@interface SCMyLevelViewController ()<UITableViewDelegate,UITableViewDataSource>

//头部
@property(nonatomic, weak) SCMyLevelHeaderView *headerView;
@property(nonatomic, weak) UITableView *tableView;
@property(nonatomic, strong) NSDictionary *levelDict;


@end


@implementation SCMyLevelViewController


- (void)initUI{
    [super initUI];
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    
    self.title = @"My Level".translateString;
    
    _headerView = [[SCMyLevelHeaderView alloc] init].addSuperView(self.scContentView);
    [self.view addSubview:_headerView];
    
    _tableView = [UITableView tableViewWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self].setCornerRadius(kSCNormalCornerRadius).setBackgroundColor(kSCColorWithHexStr(@"#500E0E")).addSuperView(self.scContentView);
    
    if (@available(iOS 15.0, *)) {
        self.tableView.sectionHeaderTopPadding = 0;
    }
    [self.headerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.equalTo(self.scContentView);
    }];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.scContentView).offset(13);
        make.trailing.equalTo(self.scContentView).offset(-13);
        make.bottom.equalTo(self.scContentView).offset(-10 - kSCSafeAreaBottomHeight);
        make.top.equalTo(self.headerView.mas_bottom);
    }];
    kWeakSelf(self);
    ///监听订单变化
    [kScAuthMar.payService.orderChangeObx afterSubscribe:^(SCOrderResultModel * _Nullable value) {
        
        if(value.isSuccess){
            [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];
            [weakself refreshData];
        }
        
    } error:nil disposeBag:self.disposeBag];
    
    //监听socket订单编号 - 使用字典处理
    [kSCAuthSocketService.orderEventObs afterSubscribe:^(NSDictionary * _Nonnull orderDict) {
        NSInteger status = [SCDictionaryHelper integerFromDictionary:orderDict forKey:@"status" defaultValue:0];
        if (status == 2) { // SCSocketOrderStautsSuccess = 2
            [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];
            [weakself refreshData];
        }
    } error:nil disposeBag:self.disposeBag];
    
    [self refreshData];
    [self.headerView.upgradeBtn addTarget:self action:@selector(onClickUpgrade) forControlEvents:UIControlEventTouchUpInside];
}

-(void)refreshData{
    kWeakSelf(self);
    [SCAPIServiceManager requestUserLevelWithSuccess:^(NSDictionary * _Nonnull userLevelDict) {
        // 直接使用字典数据
        weakself.levelDict = userLevelDict;
        [weakself updateUI];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.view toast:@"Network request failed. Please try again later.".translateString];
    }];
}

///更新UI
- (void)updateUI{
    // 优先使用字典数据
    NSInteger currentLevel = [SCDictionaryHelper integerFromDictionary:self.levelDict forKey:SCDictionaryKeys.shared.kSCUserLevelCurrentKey defaultValue:0];
    NSInteger rechargedCoins = [SCDictionaryHelper integerFromDictionary:self.levelDict forKey:SCDictionaryKeys.shared.kSCUserLevelRechargedCoinsKey defaultValue:0];
    NSInteger needRechargeCoins = [SCDictionaryHelper integerFromDictionary:self.levelDict forKey:SCDictionaryKeys.shared.kSCUserLevelNeedRechargeCoinsKey defaultValue:0];
    NSInteger nextLevel = [SCDictionaryHelper nextLevelFromLevelDict:self.levelDict];

    self.headerView.currentLevelL.text = [NSString stringWithFormat:@"Lv%ld", currentLevel];
    self.headerView.nextLevelL.text = nextLevel == -1 ? @"Max".translateString : [NSString stringWithFormat:@"Lv%ld", nextLevel];
    self.headerView.progressV.progress = rechargedCoins/((CGFloat)(needRechargeCoins + rechargedCoins));
    NSString *coins = [NSString stringWithFormat:@"%ld", needRechargeCoins];

    NSString *tipText = nextLevel == -1 ? @"You've reached the highest level.".translateString : [@"Top UP ### coins to level up.".translateString stringByReplacingOccurrencesOfString:@"###" withString:coins];
    self.headerView.tipL.text = tipText;
    [self.tableView reloadData];
}
#pragma mark - action

-(void)onClickUpgrade{
    [SCCoinsPopupViewController showWithFromVC:self entry:SCPayEntry.shared.kPayEntrySourceVipLevelUp];
}


- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *view = [[UIView alloc] initWithFrame:CGRectMake(0, 0, tableView.scWidth, 48.0f)].setBackgroundColor(kSCColorWithHexStr(@"#500E0E"));
    UILabel *levelL = [UILabel new].setText(@"Level".translateString).setTextColor(UIColor.scWhite).setFontMediumSize(16).setTextAlignment(NSTextAlignmentCenter).addSuperView(view);
    UILabel *amoutL = [UILabel new].setText(@"Top-up Amout".translateString).setTextColor(UIColor.scWhite).setFontMediumSize(16).setTextAlignment(NSTextAlignmentCenter).addSuperView(view);
    
    [levelL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.leading.equalTo(view);
    }];
    [amoutL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.trailing.equalTo(view);
        make.leading.equalTo(levelL.mas_trailing);
        make.width.equalTo(levelL);
    }];
    
    return view;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 48.0f;
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCMyLevelCell * cell = [SCMyLevelCell initWithFormTableView:tableView];
    cell.contentView.backgroundColor = indexPath.row % 2 == 0 ? kSCColorWithHexStr(@"#390C0C") : kSCColorWithHexStr(@"#500E0E");

    // 从字典中获取用户等级列表
    NSArray *userLevelList = [SCDictionaryHelper arrayFromDictionary:self.levelDict forKey:SCDictionaryKeys.shared.kSCUserLevelListKey defaultValue:@[]];
    if (indexPath.row < userLevelList.count) {
        NSDictionary *levelItemDict = userLevelList[indexPath.row];
        [cell setModelDict:levelItemDict];
    }

    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    NSArray *userLevelList = [SCDictionaryHelper arrayFromDictionary:self.levelDict forKey:SCDictionaryKeys.shared.kSCUserLevelListKey defaultValue:@[]];
    return userLevelList.count;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 39;
}

@end
