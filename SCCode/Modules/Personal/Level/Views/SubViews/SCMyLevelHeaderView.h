//
//  SCMyLevelHeaderView.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCBaseView.h"

NS_ASSUME_NONNULL_BEGIN

@interface SCMyLevelHeaderView : SCBaseView

//内容布局
@property(nonatomic, weak,readonly) UIView *contentView;
//内容的背景图
@property(nonatomic, weak,readonly) UIImageView *contentBgIV;
//当前等级图标
@property(nonatomic, weak,readonly) UIImageView *currentLevelIV;
//当前等级
@property(nonatomic, weak,readonly) UILabel *currentLevelL;
//下一等级
@property(nonatomic, weak,readonly) UILabel *nextLevelL;
//进度条 主题色，白色
@property(nonatomic, weak,readonly) UIProgressView *progressV;
//提示语
@property(nonatomic, weak,readonly) UILabel *tipL;


//升级按钮
@property(nonatomic, weak,readonly) UIButton *upgradeBtn;
@end

NS_ASSUME_NONNULL_END
