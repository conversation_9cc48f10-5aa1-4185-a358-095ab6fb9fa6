//
//  SCMyLevelHeaderView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCMyLevelHeaderView.h"

@implementation SCMyLevelHeaderView

- (void)initUI{
    [super initUI];
    self.backgroundColor = [UIColor clearColor];
    _contentView = [UIView new].setBackgroundColor(UIColor.clearColor).addSuperView(self);
    _contentBgIV = [UIImageView new].setImage([[SCResourceManager loadImageWithName:@"bg_my_level_header"] resizableImageWithCapInsets:UIEdgeInsetsMake(10, 10, 10, 10) resizingMode:UIImageResizingModeStretch]).addSuperView(_contentView);
    _currentLevelIV = [UIImageView new].setImageName(@"ic_my_level").addSuperView(_contentView);
    _currentLevelL = [UILabel new].setTextColor(UIColor.scWhite).setFontMediumSize(13.0f).setTextAlignment(kScAuthMar.isLanguageForce ? NSTextAlignmentLeft : NSTextAlignmentRight).addSuperView(self.contentView);
    _nextLevelL = [UILabel new].setTextColor(UIColor.scWhite).setFontMediumSize(13.0f).addSuperView(self.contentView);
    _progressV = [[UIProgressView alloc] initWithProgressViewStyle:UIProgressViewStyleDefault].addSuperView(self.contentView);
    _progressV.trackTintColor = UIColor.scWhite;
    _progressV.progressTintColor = UIColor.scTheme;
    _progressV.layer.cornerRadius = 4.0f;
    _progressV.layer.masksToBounds = YES;
    _progressV.layer.borderWidth = 1.5f;
    _progressV.layer.borderColor = UIColor.scWhite.CGColor;
    
    _tipL = [UILabel new].setTextColor(UIColor.scWhite).setFontRegularSize(12.0f).setTextAlignment(NSTextAlignmentCenter).addSuperView(self.contentView);
    
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(10);
        make.leading.equalTo(self).offset(15);
        make.trailing.equalTo(self).offset(-15);
        
    }];
    
    [self.contentBgIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
    
    [self.currentLevelIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(15);
        make.centerX.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(57, 53));
    }];
    
    [self.currentLevelL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.currentLevelIV.mas_bottom).offset(10);
        make.leading.equalTo(self.contentView);
        make.height.mas_equalTo(19);
        make.width.mas_equalTo(55);
    }];
    
    [self.nextLevelL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(19);
        make.width.mas_equalTo(55);
        make.centerY.equalTo(self.currentLevelL);
    }];
    
    [self.progressV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.currentLevelL.mas_trailing).offset(5);
        make.trailing.equalTo(self.nextLevelL.mas_leading).offset(-5);
        make.centerY.equalTo(self.currentLevelL);
        make.height.mas_equalTo(8);
    }];
    
    [self.tipL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.currentLevelL.mas_bottom).offset(3.5f);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(14);
        make.bottom.equalTo(self.contentView).offset(-10);
    }];
    
    _upgradeBtn = [UIButton buttonWithTitle:@"Top Up Now".translateString titleColor:UIColor.scWhite font:kScUIFontMedium(15) image:nil backgroundColor:nil cornerRadius:23.0f].addSuperView(self);
    [_upgradeBtn sc_setThemeGradientBackgroundWithCornerRadius:23.0f];
    
    [self.upgradeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_bottom).offset(12);
        make.leading.trailing.equalTo(self.contentView);
        make.height.mas_equalTo(46.0f);
        make.bottom.equalTo(self).offset(-12);
    }];
}

@end
