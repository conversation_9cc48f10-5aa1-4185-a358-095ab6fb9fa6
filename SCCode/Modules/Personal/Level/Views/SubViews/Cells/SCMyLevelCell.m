//
//  SCMyLevelCell.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/2/6.
//

#import "SCMyLevelCell.h"

@interface SCMyLevelCell()

@property(nonatomic, weak) UIImageView *levelIV;
@property(nonatomic, weak) UILabel *levelLabel;
@property(nonatomic, weak) UIView *leveView;
@property(nonatomic, weak) UIView *leftlineV;
@property(nonatomic, weak) UIView *rightlineV;
@property(nonatomic, weak) UIView *middlelineV;

@property(nonatomic, weak) UILabel *levelDescLabel;


@end

@implementation SCMyLevelCell

- (void)initUI{
    [super initUI];
    _leveView = [UIView new].setBackgroundColor(UIColor.scLevelColors[0]).setCornerRadius(11).addSuperView(self.contentView);
    _levelIV = [UIImageView new].setImageName(@"ic_leve_0").addSuperView(_leveView);
    _levelLabel = [UILabel labelWithText:@"Lv0" textColor:UIColor.scWhite font:kScUIFontRegular(15) alignment:NSTextAlignmentCenter].addSuperView(_leveView);
    
    _levelDescLabel = [UILabel labelWithText:@"#000000" textColor:UIColor.scWhite font:kScUIFontRegular(15) alignment:kScAuthMar.isLanguageForce ? NSTextAlignmentRight : NSTextAlignmentLeft].addSuperView(self.contentView);
    _leftlineV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#500E0E")).addSuperView(self.contentView);
    _middlelineV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#500E0E")).addSuperView(self.contentView);
    _rightlineV = [UIView new].setBackgroundColor(kSCColorWithHexStr(@"#500E0E")).addSuperView(self.contentView);
    [self.leftlineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView);
        make.top.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(2.0f);
    }];
    
    [self.middlelineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.contentView);
        make.top.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(1.5f);
    }];
    
    [self.rightlineV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView);
        make.top.bottom.equalTo(self.contentView);
        make.width.mas_equalTo(2.0f);
    }];
    
    [self.leveView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        if (kScAuthMar.isLanguageForce) { // RTL
            make.centerX.equalTo(self.contentView).multipliedBy(1.5);
        } else { // LTR
            make.centerX.equalTo(self.contentView).multipliedBy(0.5);
        }
        make.height.mas_equalTo(22);
    }];
    
    [self.levelIV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.leveView).offset(5);
        make.centerY.equalTo(self.leveView);
        make.size.mas_equalTo(16);
    }];
    
    [self.levelLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.levelIV.mas_trailing).offset(2.5f);
        make.centerY.equalTo(self.levelIV);
        make.trailing.equalTo(self.leveView).offset(-5);
    }];
    
    [self.levelDescLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.middlelineV.mas_trailing).offset(13);
        make.centerY.equalTo(self.contentView);
    }];
    
}

//- (void)setModel:(SCUserLevelModel *)model{
//    _model = model;
//    self.levelLabel.text = [NSString stringWithFormat:@"Lv%ld",model.level];
//    self.levelLabel.textColor = UIColor.scWhite;
//    NSArray *array = @[
//        @"ic_leve_0",
//        @"ic_leve_1",
//        @"ic_leve_2",
//        @"ic_leve_3",
//        @"ic_leve_4",
//        @"ic_leve_5",
//        @"ic_leve_6",
//        @"ic_leve_7",
//        @"ic_leve_8",
//        @"ic_leve_9",
//        @"ic_leve_10",
//    ];
//    self.levelIV.image = [SCResourceManager loadImageWithName:array[model.level]];
//    self.leveView.backgroundColor = UIColor.scLevelColors[model.level];
//    self.levelDescLabel.text = [NSString stringWithFormat:@"%ld",model.rechargedCoins];
//}

- (void)setModelDict:(NSDictionary *)levelDict{
    // 从字典中获取等级信息
    NSInteger level = [SCDictionaryHelper integerFromDictionary:levelDict forKey:@"level" defaultValue:0];
    NSInteger rechargedCoins = [SCDictionaryHelper integerFromDictionary:levelDict forKey:@"rechargedCoins" defaultValue:0];

    self.levelLabel.text = [NSString stringWithFormat:@"Lv%ld", level];
    self.levelLabel.textColor = UIColor.scWhite;

    NSArray *array = @[
        @"ic_leve_0",
        @"ic_leve_1",
        @"ic_leve_2",
        @"ic_leve_3",
        @"ic_leve_4",
        @"ic_leve_5",
        @"ic_leve_6",
        @"ic_leve_7",
        @"ic_leve_8",
        @"ic_leve_9",
        @"ic_leve_10",
    ];

    // 确保level在数组范围内
    NSInteger safeLevel = MIN(MAX(level, 0), array.count - 1);
    self.levelIV.image = [SCResourceManager loadImageWithName:array[safeLevel]];
    self.leveView.backgroundColor = UIColor.scLevelColors[safeLevel];
    self.levelDescLabel.text = [NSString stringWithFormat:@"%ld", rechargedCoins];
}

@end
