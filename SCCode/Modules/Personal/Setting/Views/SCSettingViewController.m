//
//  SCSettingViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCSettingViewController.h"
#import <Masonry/Masonry.h>
#import "SCPersonalItemCell.h"
#import "SCPersonalItemDisplayModel.h"
#import "SCAlertViewController.h"
#import "SCTranslationService.h"
#import "SCAPIServiceManager.h"
#import "SCCallService.h"
#import "SCLanguageViewController.h"
#import "SCMessagePopupManager.h"
#import "SCNavigationBar.h"

@interface SCSettingViewController ()<SCPersonalItemCellDelegate>
@property(nonatomic,nullable,weak) UITableView *tableView;
@property(nonatomic,nullable,weak) UIButton *logoutBtn;
@property(nonatomic,nullable,weak) UIButton *deleteBtn;
@property(nonatomic,nonnull, strong) NSArray<SCPersonalItemDisplayModel *> *menus;
@end

@interface SCSettingViewController (SCSettingTableView)<UITableViewDelegate,UITableViewDataSource>

@end

@implementation SCSettingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initData];
    [self _initUI];
}
-(void) _initData{
    self.title = @"Setting".translateString;
    
    _menus = @[
        [[SCPersonalItemDisplayModel alloc] initWithTitle:[NSString stringWithFormat:@"ID：%@",kSCCurrentUserID] subTitle:@"" image:nil style:SCPersonalItemCellStyleCopy],
        [[SCPersonalItemDisplayModel alloc] initSwitchStyleWithTitle:@"Auto Translate".translateString subTitle:@"" image:nil isOn:kSCAuthTranslaService.isAutoTranslation],
        [[SCPersonalItemDisplayModel alloc] initSwitchStyleWithTitle:@"Do not disturb - Message".translateString subTitle:@"" image:nil isOn:kSCCurrentUserIsSwitchNotDisturbIm],
        [[SCPersonalItemDisplayModel alloc] initSwitchStyleWithTitle:@"Do not disturb - call".translateString subTitle:@"" image:nil isOn:kSCCurrentUserIsSwitchNotDisturbCall],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Language".translateString subTitle:@"" image:nil style:SCPersonalItemCellStyleNoArrow],
    ];
}
-(void) _initUI{
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    
    UITableView *tableView = [UITableView tableViewWithFrame:self.scContentView.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    tableView.backgroundColor = [UIColor scGlobalBgColor];
    [self.scContentView addSubview:tableView];
    self.tableView = tableView;

    
    UIButton *logoutBtn = [UIButton buttonWithTitle:@"Log out".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(20) image:nil backgroundColor:[UIColor scTheme] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onClickLogout)];
    [logoutBtn sc_setThemeGradientBackground];
    [self.scContentView addSubview:logoutBtn];
    self.logoutBtn = logoutBtn;
    
    UIButton *deleteButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [deleteButton setTitle:@"Delete Account".translateString forState:UIControlStateNormal];
    deleteButton.titleLabel.font = kScUIFontMedium(13);
    [deleteButton setTitleColor:[UIColor colorWithHexString:@"#FF4D4D"] forState:UIControlStateNormal];
    [deleteButton addTarget:self action:@selector(onDeleteButtonClick) forControlEvents:UIControlEventTouchUpInside];
    
    NSString *title = @"Delete Account".translateString;
    NSDictionary *attributes = @{
        NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle)
    };
    NSAttributedString *attributedTitle = [[NSAttributedString alloc] initWithString:title attributes:attributes];
    [deleteButton setAttributedTitle:attributedTitle forState:UIControlStateNormal];
    [self.scContentView addSubview:deleteButton];
    self.deleteBtn = deleteButton;
    
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.top.equalTo(self.scContentView);
        make.bottom.equalTo(self.logoutBtn.mas_top).offset(-8);
    }];
    [logoutBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.scContentView).offset(20);
        make.trailing.equalTo(self.scContentView).offset(-20);
        make.height.mas_equalTo(46);
        make.bottom.equalTo(self.deleteBtn.mas_top).offset(-8);
    }];
    [deleteButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.scContentView);
        make.height.mas_equalTo(24);
        make.bottom.equalTo(self.scContentView).offset(-8 - kSCSafeAreaBottomHeight);
    }];
}

#pragma mark - Action
- (void) onClickLogout{
    // 清除消息弹窗
    [[SCMessagePopupManager shared] clear];
    
    if(kSCAuthCallService.callingSessionModel != nil){
        //有通话
        [kSCKeyWindow toast:@"There is a call in progress, please hang up first".translateString];
        return;
    }
    [kSCKeyWindow showLoading];
    [kScAuthMar doLogout:^(SCXErrorModel * _Nullable error) {
        [kSCKeyWindow hiddenLoading];
        if(error){
            [kSCKeyWindow toast:@"Failed to log out, please try again later".translateString];
        }
        
    }];
}

- (void)onDeleteButtonClick {
    if(kSCAuthCallService.callingSessionModel != nil){
        //有通话
        [kSCKeyWindow toast:@"There is a call in progress, please hang up first".translateString];
        return;
    }
    kWeakSelf(self)
    [SCAlertViewController showDeleteAccountWithFromVC:self red:@"All your infomation, including the purchassed products, will be deleted. Please make a careful decision.".translateString leftBlock:^(SCAlertViewController * _Nonnull alerVC) {
        [alerVC dismissViewControllerAnimated:YES completion:^{
            //删除账号
            [weakself showDeleteAccountWithFromVC];
        }];
    } rightBlock:^(SCAlertViewController * _Nonnull alerVC) {
        [weakself sc_blank_empty];
        [alerVC dismissViewControllerAnimated:YES completion:nil];
    }];
}

- (void)showDeleteAccountWithFromVC{
    [SCAlertViewController showDeleteAccountWithFromVC:self red:nil leftBlock:^(SCAlertViewController * _Nonnull alerVC) {
        [kSCKeyWindow showLoading];
        [kScAuthMar deleteAccount:^(SCXErrorModel * _Nullable error) {
            [kSCKeyWindow hiddenLoading];
            if(error){
                [kSCKeyWindow toast:@"Failed to delete account, please try again later".translateString];
                return;
            }
            [alerVC dismissViewControllerAnimated:YES completion:nil];
        }];
    } rightBlock:^(SCAlertViewController * _Nonnull alerVC) {
        [alerVC dismissViewControllerAnimated:YES completion:nil];
    }];
}

- (void)sc_blank_empty{}

#pragma mark - SCPersonalItemCellDelegate
- (void)scPersonalItemCell:(SCPersonalItemCell *)cell onClickBtnWithIndex:(NSInteger)index{
    if(index == 0){
        // 将ID复制到粘贴板
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = kSCCurrentUserID;
        // 检查是否成功复制
        if ([pasteboard.string isEqualToString:kSCCurrentUserID]) {
            [self.view toast:@"Copy Successfull".translateString];
        } else {
            [self.view toast:@"Copy Failed".translateString];
        }
    }
}

- (void)scPersonalItemCell:(SCPersonalItemCell *)cell onSwitch:(UISwitch *)switchView index:(NSInteger)index{
    if(index == 1){
        //自动翻译
        kSCAuthTranslaService.isAutoTranslation = switchView.on;
    }else if(index == 2){
        //消息免打扰
        kWeakSelf(self);
        
        void(^toChange)(void) = ^{
            BOOL currentImSetting = kSCCurrentUserIsSwitchNotDisturbIm;
            BOOL currentCallSetting = kSCCurrentUserIsSwitchNotDisturbCall;
            [SCAPIServiceManager requestSwitchNotDisturbWithCall:currentCallSetting isSwitchNotDisturbIm:!currentImSetting success:^{
                // 使用新的统一更新方法
                [kScAuthMar updateUserSwitchSettings:@{@"isSwitchNotDisturbIm": @(!currentImSetting)}];
            } failure:^(SCXErrorModel * _Nonnull error) {
                if(!kSCCurrentUserIsSwitchNotDisturbIm){
                    [weakself.view toast:@"Failed to turn on the message do not disturb function".translateString];
                }else{
                    [weakself.view toast:@"Failed to turn off the message do not disturb function".translateString];

                }
                switchView.on = !switchView.on;
                
            }];
        };
        
        if(!kSCCurrentUserIsSwitchNotDisturbIm){
            [SCAlertViewController showNotDisturbMessageWithFromVC:self leftBlock:^(SCAlertViewController * _Nonnull alerVC) {
                [weakself sc_blank_empty];
                switchView.on = !switchView.on;
                [alerVC dismissViewControllerAnimated:true completion:nil];
            } rightBlock:^(SCAlertViewController * _Nonnull alerVC) {
                [weakself sc_blank_empty];
                toChange();
                [alerVC dismissViewControllerAnimated:true completion:nil];
            }];
        }else{
            toChange();
        }
        
    }else if(index == 3){
        //呼叫免打扰
        kWeakSelf(self);
        
        void(^toChange)(void) = ^{
            BOOL currentImSetting = kSCCurrentUserIsSwitchNotDisturbIm;
            BOOL currentCallSetting = kSCCurrentUserIsSwitchNotDisturbCall;
            [SCAPIServiceManager requestSwitchNotDisturbWithCall:!currentCallSetting isSwitchNotDisturbIm:currentImSetting success:^{
                // 使用新的统一更新方法
                [kScAuthMar updateUserSwitchSettings:@{@"isSwitchNotDisturbCall": @(!currentCallSetting)}];
            } failure:^(SCXErrorModel * _Nonnull error) {
                if(!kSCCurrentUserIsSwitchNotDisturbCall){
                    [weakself.view toast:@"Failed to turn on the call do not disturb function".translateString];
                }else{
                    [weakself.view toast:@"Failed to turn off the call do not disturb function".translateString];

                }
                switchView.on = !switchView.on;
                
            }];
        };
        
        if(switchView.on){
            [SCAlertViewController showNotDisturbCallWithFromVC:self leftBlock:^(SCAlertViewController * _Nonnull alerVC) {
                //取消 恢复按钮
                [weakself sc_blank_empty];
                switchView.on = false;
                [alerVC dismissViewControllerAnimated:true completion:nil];
            } rightBlock:^(SCAlertViewController * _Nonnull alerVC) {
                toChange();
                [weakself sc_blank_empty];
                [alerVC dismissViewControllerAnimated:true completion:nil];
            }];
        }else{
            toChange();
        }
        
    }
}


- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCPersonalItemCell *cell = [SCPersonalItemCell initWithFormTableView:tableView];
    SCPersonalItemDisplayModel *model = self.menus[indexPath.row];
    cell.delegate = self;
    cell.index = indexPath.row;
    [cell configWithImage:model.image title:model.title subTitle:model.subTitle style:model.style isNo:model.isON];
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.menus.count;
}
- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCPersonalItemDisplayModel *model = self.menus[indexPath.row];
    return [SCPersonalItemCell heightWithStyle:model.style]+20;
}
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if(indexPath.row == 0){
        
    }else if(indexPath.row == 1){
        //自动翻译
    }else if(indexPath.row == 2){
        //呼叫免打扰
        
    }else if(indexPath.row == 3){
        //消息免打扰
        
    }else if(indexPath.row == 4){
        // 多语言
        SCLanguageViewController *vc = [[SCLanguageViewController alloc]init];
        [self.navigationController pushViewController:vc animated:YES];
    }
    
}



@end
