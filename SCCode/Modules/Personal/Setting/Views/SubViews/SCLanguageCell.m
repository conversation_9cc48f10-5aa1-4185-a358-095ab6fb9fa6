//
//  SCLanguageCell.m
//  Supercall
//
//  Created by sumengliu on 2024/10/22.
//

#import "SCLanguageCell.h"

@implementation SCLanguageCell

- (void)initUI {
    [super initUI];
    [self setupViews];
}

- (void)setupViews {
    self.backgroundColor = [UIColor colorWithHexString:@"#390C0C"];
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    self.containView = [[UIView alloc]init];
    self.containView.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    self.containView.layer.cornerRadius = kSCNormalCornerRadius;
    self.containView.layer.masksToBounds = YES;
    [self.contentView addSubview:self.containView];
    
    self.languageLabel = [[UILabel alloc] init];
    self.languageLabel.font = kScUIFontMedium(14);
    self.languageLabel.textColor = [UIColor scWhite];
    [self.containView addSubview:self.languageLabel];
    
    self.selectionIndicator = [UIButton buttonWithType:UIButtonTypeCustom];
    self.selectionIndicator.userInteractionEnabled = NO;
    [self.selectionIndicator setImage:[SCResourceManager loadImageWithName:@"ic_lan_uncheck"] forState:UIControlStateNormal];
    [self.selectionIndicator setImage:[SCResourceManager loadImageWithName:@"ic_lan_checked"] forState:UIControlStateSelected];
    [self.containView addSubview:self.selectionIndicator];
    
    [self setupConstraints];
}

- (void)setupConstraints {
    [self.containView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView);
        make.leading.equalTo(self.contentView).offset(15.0f);
        make.trailing.equalTo(self.contentView).offset(-15.0f);
        make.bottom.equalTo(self.contentView).offset(-10.0f);
    }];
    
    [self.languageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.containView).offset(16);
        make.centerY.equalTo(self.containView);
    }];
    
    [self.selectionIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.containView).offset(-16);
        make.centerY.equalTo(self.containView);
        make.width.height.equalTo(@20);
    }];
}

- (void)setupWithLanguage:(NSString *)language nativeLanguage:(NSString *)nativeLanguage isSelected:(BOOL)isSelected {
    self.languageLabel.text = language;
    self.selectionIndicator.selected = isSelected;
}

@end
