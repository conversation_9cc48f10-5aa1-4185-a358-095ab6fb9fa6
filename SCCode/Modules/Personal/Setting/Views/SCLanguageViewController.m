//
//  SCLanguageViewController.m
//  Supercall
//
//  Created by sumengliu on 2024/10/22.
//

#import "SCLanguageViewController.h"
#import "SCAlertViewController.h"
#import <Masonry/Masonry.h>
#import "SCLanguageManager.h"
#import "SCLanguageModel.h"
#import "SCLanguageCell.h"
#import "SCNavigationbar.h"

@interface SCLanguageViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSMutableArray<SCLanguageModel *> *languages;
@property (nonatomic, strong) NSDictionary<NSString *, NSString *> *languageCountryCode;

@end

@implementation SCLanguageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initData];
    [self _initUI];
}

- (void)_initData {
    self.title = @"Language".translateString;
    NSMutableArray *supportLanguage = [@[@"en", @"ar", @"es", @"tr", @"ko", @"de", @"ja", @"it", @"hi", @"zh-tw", @"th", @"fr", @"pt"] mutableCopy];
    
    NSString *language = [SCLanguageManager getDeviceLanguage] ?: @"";
    if ([supportLanguage containsObject:language]) {
        NSUInteger index = [supportLanguage indexOfObject:language];
        [supportLanguage removeObjectAtIndex:index];
        [supportLanguage insertObject:language atIndex:0];
    }
    
    NSDictionary *languageFullName = @{
        @"en": @"English",
        @"ar": @"العربية",
        @"es": @"Español",
        @"tr": @"Türkçe",
        @"ko": @"한국어",
        @"de": @"Deutsch",
        @"ja": @"日本語",
        @"it": @"Italiano",
        @"hi": @"हिन्दी",
        @"zh-tw": @"繁體中文",
        @"th": @"ไทย",
        @"fr": @"Français",
        @"pt": @"Português"
    };
    
    NSDictionary *languageEnglishName = @{
        @"en": [@"English" translateString],
        @"ar": [@"Arabic" translateString],
        @"es": [@"Spanish" translateString],
        @"tr": [@"Turkish" translateString],
        @"ko": [@"Korean" translateString],
        @"de": [@"German" translateString],
        @"ja": [@"Japanese" translateString],
        @"it": [@"Italian" translateString],
        @"hi": [@"Hindi" translateString],
        @"zh-tw": [@"Traditional Chinese" translateString],
        @"th": [@"Thai" translateString],
        @"fr": [@"French" translateString],
        @"pt": [@"Portuguese" translateString]
    };
    
    [self.languages removeAllObjects];
    for (NSString *str in supportLanguage) {
        SCLanguageModel *model = [[SCLanguageModel alloc] init];
        model.languageCode = str;
        model.languageText = languageFullName[str] ?: @"";
        model.tanslateText = languageEnglishName[str] ?: @"";
        [self.languages addObject:model];
    }
    
    [self.tableView reloadData];
}

- (void)_initUI {
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.backgroundColor = [UIColor scGlobalBgColor];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.rowHeight = 73;
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.tableView.separatorInset = UIEdgeInsetsZero;
    [self.tableView registerClass:[SCLanguageCell class] forCellReuseIdentifier:@"SCLanguageCell"];
    [self.scContentView addSubview:self.tableView];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.languages.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    SCLanguageCell *cell = [SCLanguageCell initWithFormTableView:tableView];
    NSInteger row = indexPath.row;
    if (row < self.languages.count) {
        SCLanguageModel *language = self.languages[row];
        [cell setupWithLanguage:language.languageText nativeLanguage:language.tanslateText isSelected:row == 0];
    }
    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSInteger row = indexPath.row;
    if (row < self.languages.count) {
        SCLanguageModel *model = self.languages[row];
        kWeakSelf(self)
        NSString *content = [@"Are you sure to switch the ###?".translateString stringByReplacingOccurrencesOfString:@"###" withString:model.languageText ?: @""];
        [SCAlertViewController showChangeLanguageWithFromVC:self title:content leftBlock:^(SCAlertViewController * _Nonnull alerVC) {
            [weakself sc_blank_empty];
            [alerVC dismissViewControllerAnimated:true completion:nil];
        } rightBlock:^(SCAlertViewController * _Nonnull alerVC) {
            [weakself sc_blank_empty];
            [[NSUserDefaults standardUserDefaults] setObject:model.languageCode forKey:kSCLocalLanguageKey];
            
            [SCResourceManager loadLanguageJson];
            [kScAuthMar routeHomeVC];
            
            kScAuthMar.languageObx.value = @(1);
            
            [alerVC dismissViewControllerAnimated:true completion:nil];
        }];
    }
}

- (void)sc_blank_empty{}

#pragma mark - Lazy Loading

- (NSMutableArray<SCLanguageModel *> *)languages {
    if (!_languages) {
        _languages = [NSMutableArray array];
    }
    return _languages;
}

- (NSDictionary<NSString *,NSString *> *)languageCountryCode {
    if (!_languageCountryCode) {
        _languageCountryCode = @{
            @"en": @"US",
            @"ar": @"SA",
            @"es": @"ES",
            @"tr": @"TR",
            @"ko": @"KR",
            @"de": @"DE",
            @"ja": @"JP",
            @"it": @"IT",
            @"hi": @"IN",
            @"zh-tw": @"TW",
            @"th": @"TH",
            @"fr": @"FR",
            @"pt": @"PT"
        };
    }
    return _languageCountryCode;
}

@end
