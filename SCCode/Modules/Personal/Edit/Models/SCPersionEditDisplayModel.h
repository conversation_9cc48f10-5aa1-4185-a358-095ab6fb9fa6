//
//  SCPersionEditDisplayModel.h
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import <Foundation/Foundation.h>
// #import "SCMediaListModel.h" // 已移除，使用字典替代
@class SCPersonalEditMediaDisplayModel;
NS_ASSUME_NONNULL_BEGIN

@interface SCPersionEditDisplayModel : NSObject

//昵称
@property (nonatomic, copy) NSString *nickName;
//生日
@property (nonatomic, copy) NSString *birthday;
//国家
@property (nonatomic, copy) NSString *country;
//about
@property (nonatomic, copy) NSString *about;
//头像
@property (nonatomic,strong) SCPersonalEditMediaDisplayModel * avatar;
//图片列表
@property (nonatomic,strong) NSMutableArray<SCPersonalEditMediaDisplayModel *> *images;

@end


@interface SCPersonalEditMediaDisplayModel : NSObject

//默认URL
@property (nonatomic, copy) NSString *defaultUrl;
@property (nonatomic, strong,nullable) NSData *localData;
@property (nonatomic, strong,readonly) NSString *localFileName;

///媒体数据字典（替代原来的SCMediaListModel）
@property (nonatomic, strong) NSDictionary *mediaDict;

@property (nonatomic, assign,readonly) BOOL isLocal;

#pragma mark - 便捷访问方法
///获取媒体ID
- (NSString *)mediaId;
///获取媒体路径
- (NSString *)mediaPath;
///获取媒体URL
- (NSString *)mediaUrl;
///获取媒体类型
- (NSInteger)mediaType;

@end
NS_ASSUME_NONNULL_END
