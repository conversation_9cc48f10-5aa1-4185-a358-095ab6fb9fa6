//
//  SCPersionEditDisplayModel.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCPersionEditDisplayModel.h"
#import "SCDictionaryHelper.h"

@interface SCPersonalEditMediaDisplayModel ()
// 缓存常用值以提高性能
@property (nonatomic, copy) NSString *cachedMediaId;
@property (nonatomic, copy) NSString *cachedMediaUrl;
@end

@implementation SCPersionEditDisplayModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        _images = [NSMutableArray new];
    }
    return self;
}


@end

@implementation SCPersonalEditMediaDisplayModel


- (void)setLocalData:(NSData *)localData{
    _localData = localData;
    _localFileName = _localData == nil ? nil:[NSString stringWithFormat:@"%@.jpg", [NSUUID UUID].UUIDString];
}


- (BOOL)isLocal{
    if(self.localData != nil){
        return YES;
    }
    return NO;
}

#pragma mark - 便捷访问方法

- (NSString *)mediaId {
    if (!_cachedMediaId) {
        _cachedMediaId = [SCDictionaryHelper stringFromDictionary:self.mediaDict forKey:@"mediaId" defaultValue:@""];
    }
    return _cachedMediaId;
}

- (NSString *)mediaPath {
    return [SCDictionaryHelper stringFromDictionary:self.mediaDict forKey:@"mediaPath" defaultValue:@""];
}

- (NSString *)mediaUrl {
    if (!_cachedMediaUrl) {
        // 支持多种可能的URL键名
        NSString *url = [SCDictionaryHelper stringFromDictionary:self.mediaDict forKey:@"mediaUrl" defaultValue:nil];
        if (!url || url.length == 0) {
            url = [SCDictionaryHelper stringFromDictionary:self.mediaDict forKey:@"url" defaultValue:nil];
        }
        if (!url || url.length == 0) {
            url = [SCDictionaryHelper stringFromDictionary:self.mediaDict forKey:@"thumbUrl" defaultValue:@""];
        }
        _cachedMediaUrl = url ?: @"";
    }
    return _cachedMediaUrl;
}

- (NSInteger)mediaType {
    return [SCDictionaryHelper integerFromDictionary:self.mediaDict forKey:@"mediaType" defaultValue:0];
}

#pragma mark - Setter方法

- (void)setMediaDict:(NSDictionary *)mediaDict {
    _mediaDict = mediaDict;
    // 清除缓存，强制重新计算
    _cachedMediaId = nil;
    _cachedMediaUrl = nil;
}

@end
