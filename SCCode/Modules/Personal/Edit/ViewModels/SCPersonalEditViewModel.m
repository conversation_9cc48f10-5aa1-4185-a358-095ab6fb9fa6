//
//  SCPersonalEditViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/6.
//

#import "SCPersonalEditViewModel.h"
#import "SCPersionEditDisplayModel.h"
// #import "SCTokenModel.h" // 已移除，使用字典替代
// #import "SCMediaListModel.h" // 已移除，使用字典替代
// #import "SCUserInfoModel.h" // 已移除，使用字典替代

@implementation SCPersonalEditViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self initData];
    }
    return self;
}

-(void)initData{
    // 使用便捷访问宏和字典数据初始化显示模型
    _displayModel = [[SCPersionEditDisplayModel alloc] init];
    [_displayModel setNickName:kSCCurrentUserNickname];
    [_displayModel setBirthday:[SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:@"birthday" defaultValue:@""]];
    [_displayModel setCountry:[SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:SCDictionaryKeys.shared.kSCUserCountryKey defaultValue:@""]];
    [_displayModel setAbout:[SCDictionaryHelper stringFromDictionary:kSCCurrentUserInfoDict forKey:@"about" defaultValue:@""]];
    
    SCPersonalEditMediaDisplayModel *avatarModel = [[SCPersonalEditMediaDisplayModel alloc] init];
    avatarModel.defaultUrl = kSCCurrentUserAvatarUrl;
    [_displayModel setAvatar:avatarModel];

    // 从字典中获取媒体列表
    NSArray *mediaListArray = [SCDictionaryHelper arrayFromDictionary:kSCCurrentUserInfoDict forKey:@"mediaList" defaultValue:@[]];
    for (NSDictionary *mediaDict in mediaListArray) {
        SCPersonalEditMediaDisplayModel *model = [[SCPersonalEditMediaDisplayModel alloc] init];
        model.defaultUrl = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"mediaUrl" defaultValue:@""];
        // 使用字典数据
        model.mediaDict = mediaDict;
        [_displayModel.images addObject:model];
    }
    
    
}

@end
