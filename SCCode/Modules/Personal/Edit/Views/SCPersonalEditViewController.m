//
//  SCPersonalEditViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCPersonalEditViewController.h"
//第三方库
#import <Masonry/Masonry.h>
//View
#import "SCLimitTextView.h"
#import "SCDatePicker.h"
#import "SCAvatarActivityIndicatorView.h"
#import "SCNavigationBar.h"
//Model
#import "SCPersionEditDisplayModel.h"
// #import "SCMediaListModel.h" // 已移除，使用字典替代
// #import "SCTokenModel.h" // 已移除，使用字典替代
// #import "SCUserInfoModel.h" // 已移除，使用字典替代
//VC
#import "SCCountryPickerViewController.h"
#import "SCCategoryActionSheetViewControllerPhotoPicker.h"
//ViewModel
#import "SCPersonalEditViewModel.h"
#import "SCOSSManager.h"
#import "SCAPIServiceManager.h"
#import "SCDictionaryHelper.h"


@interface SCPersonalEditViewController () <UITextFieldDelegate, SCLimitTextViewDelegate>

@property (nonatomic, strong) UIImageView *avatarImgView;
@property (nonatomic, strong) SCAvatarActivityIndicatorView *avatarActivityV;
@property (nonatomic, strong) UIView *imgContentView;
@property (nonatomic, strong) UITextField *nameTF;
@property (nonatomic, strong) UILabel *birthLb;
@property (nonatomic, strong) UILabel *countryLb;
@property (nonatomic, strong) SCLimitTextView *aboutTextView;
@property (nonatomic, strong) SCDatePicker *datePicker;

///数据模型
@property (nonatomic,strong) SCPersonalEditViewModel *viewModel;

@property (nonatomic, assign) NSInteger maxInputLength;

// 原生键盘管理相关属性
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, assign) CGFloat originalContentInsetBottom;
@property (nonatomic, assign) CGFloat keyboardHeight;
@property (nonatomic, weak) UIView *activeInputView;

@end


@interface SCPersonalEditViewController (SCCreateView)

- (UIView *)createNameItemViewWithTitle:(NSString *)title ;
- (UIView *) createTextFildItemWithItemView:(UITextField *) inputView title:(NSString *)title;
-(UIView *) createSelectItemWithItemView:(UILabel *) labelL title:(NSString *)title;

@end

@implementation SCPersonalEditViewController

- (instancetype)init {
    self = [super init];
    if (self) {
        _maxInputLength = 30; // 设置最大输入长度
    }
    return self;
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    // 注册键盘通知
    [self registerKeyboardNotifications];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    // 移除键盘通知
    [self unregisterKeyboardNotifications];
}

-(void)viewDidLoad {
    [super viewDidLoad];
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.title = @"Edit Profile".translateString;
    self.viewModel = [[SCPersonalEditViewModel alloc] init];
    [self setupUI];
    [self configMediaListViews];

    [self refreshUI];
    ///获取OSS配置
    [[SCOSSManager sharedManager] remoteOSSPolicyDict:nil failure:nil];

    // 添加点击手势收起键盘
    [self setupTapGestureToHideKeyboard];
}


//刷新UI
-(void)refreshUI{
    self.nameTF.text = self.viewModel.displayModel.nickName;
    self.birthLb.text = self.viewModel.displayModel.birthday;
    self.countryLb.text = self.viewModel.displayModel.country;
    self.aboutTextView.text = self.viewModel.displayModel.about;
    if([self.viewModel.displayModel.avatar isLocal]){
        [self.avatarImgView setImage:[UIImage imageWithData:self.viewModel.displayModel.avatar.localData]];
    }else{
        [self.avatarImgView sc_setImageWithURL:self.viewModel.displayModel.avatar.defaultUrl  placeholderImage:[SCResourceManager loadImageWithName:@"ic_circle_avatar"]];
    }
    
}


//头像大小
static CGFloat avatarImgViewWH = 140.0f;

- (void)setupUI {
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsHorizontalScrollIndicator = NO;
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.bounces = NO;
    self.scrollView.keyboardDismissMode = UIScrollViewKeyboardDismissModeOnDrag;

    self.contentView = [[UIView alloc] init];
    [self.scContentView addSubview:self.scrollView];
    [self.scrollView addSubview:self.contentView];
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];

    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.scrollView);
        make.width.equalTo(@(kSCScreenWidth - 30));
        make.leading.equalTo(self.scrollView).offset(15);
        make.trailing.equalTo(self.scrollView).offset(-15);
    }];

    // 保存原始的contentInset
    self.originalContentInsetBottom = self.scrollView.contentInset.bottom;
    
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.layer.cornerRadius = avatarImgViewWH * 0.5;
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    avatarImgView.layer.masksToBounds = YES;
    [self.contentView addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
    kSCAddTapGesture(self.avatarImgView, self, onTapAvatar);
    [avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(avatarImgViewWH));
        make.top.equalTo(_contentView).offset(20);
        make.centerX.equalTo(_contentView);
    }];
    avatarImgView.userInteractionEnabled = YES;
    
    
    UIImageView *iconImgView = [[UIImageView alloc] initWithImage:[SCResourceManager loadImageWithName:@"ic_personal_edit_avatar"]];
    iconImgView.userInteractionEnabled = NO;
    [self.contentView addSubview:iconImgView];

    [iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(avatarImgView);
        make.bottom.equalTo(avatarImgView).offset(-4);
        make.width.height.mas_equalTo(25.0f);
    }];

    //加载框
    SCAvatarActivityIndicatorView *indicatorView = [[SCAvatarActivityIndicatorView alloc] initWithFrame:CGRectZero];
    [avatarImgView addSubview:indicatorView];
    [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(avatarImgView);
    }];
    self.avatarActivityV = indicatorView;

    UIView *imgContentView = [[UIView alloc] init];

    [self.contentView addSubview:imgContentView];
    self.imgContentView = imgContentView;
    [imgContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentView);
        make.top.equalTo(avatarImgView.mas_bottom).offset(20);
        make.height.equalTo(@((kSCScreenWidth - 30 - 36)/4.0));
    }];

    self.nameTF = [UITextField new];
    UIView *nameContentView = [self createTextFildItemWithItemView:self.nameTF title:@"Nick Name".translateString];
    [self.contentView addSubview:nameContentView];
    [nameContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentView);
        make.top.equalTo(imgContentView.mas_bottom).offset(9);
        make.height.equalTo(@(63));
    }];

    self.birthLb = [UILabel new];
    UIView *birthContentView = [self createSelectItemWithItemView:self.birthLb title:@"Date of Birth".translateString];
    kSCAddTapGesture(birthContentView, self,onBirthdata);

    [self.contentView addSubview:birthContentView];
    [birthContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentView);
        make.top.equalTo(nameContentView.mas_bottom).offset(9);
        make.height.equalTo(@(63));
    }];
    self.countryLb = [UILabel new];
    UIView *countryContentView = [self createSelectItemWithItemView:self.countryLb title:@"Country".translateString];
    [self.contentView addSubview:countryContentView];
    [countryContentView setUserInteractionEnabled:YES];
    [countryContentView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(onSelectCountry)]];
    [countryContentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.trailing.equalTo(self.contentView);
        make.top.equalTo(birthContentView.mas_bottom).offset(9);
        make.height.equalTo(@(63));
    }];
    
    
    SCLimitTextView *aboutTextView =  [[SCLimitTextView alloc] init];
    aboutTextView.textPlaceholder = @"Type here...".translateString;
    aboutTextView.maxCount = 300;
    aboutTextView.layer.borderWidth = kSCLineHeight;
    aboutTextView.layer.borderColor = [UIColor colorWithHexString:@"#979797"].CGColor;
    aboutTextView.layer.cornerRadius = 20.0f;
    aboutTextView.editDelegate = self;
    [self.contentView addSubview:aboutTextView];

    self.aboutTextView = aboutTextView;

    [aboutTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView);
        make.width.equalTo(@(kSCScreenWidth - 30));
        make.top.equalTo(countryContentView.mas_bottom).offset(19);
        make.height.equalTo(@(148.0));
    }];

    UIButton *submitBtn = [UIButton buttonWithTitle:@"Submit".translateString titleColor:[UIColor scWhite] font:kScUIFontMedium(20) image:nil backgroundColor:[UIColor scTheme] cornerRadius:kSCNormalCornerRadius target:self action:@selector(onSubmit)];
    [submitBtn sc_setThemeGradientBackground];
    [self.contentView addSubview:submitBtn];

    [submitBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(aboutTextView.mas_bottom).offset(35);
        make.leading.equalTo(self.contentView);
        make.width.equalTo(@(kSCScreenWidth - 30));
        make.height.equalTo(@(46));
        make.bottom.equalTo(self.contentView).offset(-20);
    }];
}
- (void)configMediaListViews {
    [self.imgContentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    
    UIView *lastView = nil;
    for (NSInteger i = 0; i < self.viewModel.displayModel.images.count; i++) {
        SCPersonalEditMediaDisplayModel *image = self.viewModel.displayModel.images[i];
        
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.layer.cornerRadius = 8;
        imageView.layer.masksToBounds = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        imageView.tag = 1000 + i;
        imageView.userInteractionEnabled = YES;
        if ([image isLocal]) {
            imageView.image = [UIImage imageWithData:image.localData];
        } else {
            [imageView sc_setImageWithURL:image.defaultUrl];
        }
        
        [self.imgContentView addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            if (lastView == nil) {
                make.leading.equalTo(self.imgContentView).offset(0);
            } else {
                make.leading.equalTo(lastView.mas_trailing).offset(12);
            }
            make.top.bottom.equalTo(self.imgContentView);
            make.width.equalTo(imageView.mas_height);
        }];
        
        UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [closeBtn setImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"] forState:UIControlStateNormal];
        [closeBtn setImage:[SCResourceManager loadImageWithName:@"btn_circle_close_white"] forState:UIControlStateHighlighted];
        closeBtn.tag = 1000 + i;
        [closeBtn addTarget:self action:@selector(imageDeleteAction:) forControlEvents:UIControlEventTouchUpInside];
        [imageView addSubview:closeBtn];
        [closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(imageView).offset(-5);
            make.top.equalTo(imageView).offset(5);
            make.size.equalTo(@(CGSizeMake(20, 20)));
        }];
        
        //加载框
        SCAvatarActivityIndicatorView *indicatorView = [[SCAvatarActivityIndicatorView alloc] initWithFrame:CGRectZero];
        indicatorView.tag = ([[image mediaId] length] == 0 ? 0:[[image mediaId] integerValue])+10000;
        [self.imgContentView addSubview:indicatorView];
        [indicatorView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(imageView);
        }];
        
        
        lastView = imageView;
    }
    
    if (self.viewModel.displayModel.images.count < 4) {
        UIButton *addBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [addBtn setImage:[SCResourceManager loadImageWithName:@"btn_personal_add_photo"] forState:UIControlStateNormal];
        [addBtn setImage:[SCResourceManager loadImageWithName:@"btn_personal_add_photo"] forState:UIControlStateHighlighted];
        addBtn.layer.cornerRadius = 8;
        addBtn.layer.masksToBounds = YES;
        [addBtn addTarget:self action:@selector(addImageClick) forControlEvents:UIControlEventTouchUpInside];
        [self.imgContentView addSubview:addBtn];
        [addBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            if (lastView == nil) {
                make.leading.equalTo(self.imgContentView).offset(0);
            } else {
                make.leading.equalTo(lastView.mas_trailing).offset(12);
            }
            make.top.bottom.equalTo(self.imgContentView);
            make.width.equalTo(addBtn.mas_height);
        }];
    }
}


#pragma mark - Action


-(void)onTapAvatar{
    kWeakSelf(self)
    [SCActionSheetViewController showPhotpPickerWithFromVC:self albumBlock:^(UIImage * _Nullable selectedImage) {
        if(selectedImage!= nil){
            weakself.viewModel.displayModel.avatar.localData = UIImageJPEGRepresentation(selectedImage, 1.0);
            weakself.avatarImgView.image = selectedImage;
            
            [weakself uploadImageData:weakself.viewModel.displayModel.avatar.localData];
        }
        
    } cancelBlock:^{
        
    }];
}

- (void)uploadImageData:(NSData *) imageData{
        kWeakSelf(self)
        [[SCOSSManager sharedManager] uploadImage:imageData fileName:weakself.viewModel.displayModel.avatar.localFileName uploadProgress:^(SCProgress * _Nonnull uploadProgress) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [weakself.avatarActivityV startAnimating];
            });
        } success:^(NSString * _Nonnull fileUrl) {
            
            [weakself uploadImageRequest:fileUrl];
            
            
        } failure:^(SCXErrorModel * _Nonnull error) {
            //Toast 更新头像失败
            weakself.viewModel.displayModel.avatar.localData = nil;
            [weakself.avatarImgView sc_setImageWithURL:self.viewModel.displayModel.avatar.defaultUrl];
            [weakself.avatarActivityV stopAnimating];
            [weakself.view toast:@"Upload Avatar Fail".translateString];
        }];
}

- (void)uploadImageRequest:(NSString *_Nonnull)avatarPath{
    kWeakSelf(self)
    [SCAPIServiceManager requesUpdateAvatarWithPath:avatarPath success:^() {
        
        ///修改成功后刷新UserInfo数据
        [weakself remoteLoginUserInfo];
        [weakself.avatarActivityV stopAnimating];
        
    } failure:^(SCXErrorModel * _Nonnull error) {
        
        [weakself.avatarActivityV stopAnimating];
        //Toask 修改头像失败
        [weakself.view toast:@"Upload Avatar Fail".translateString];
        weakself.viewModel.displayModel.avatar.localData = nil;
        [weakself.avatarImgView sc_setImageWithURL:self.viewModel.displayModel.avatar.defaultUrl];
        [weakself.avatarActivityV stopAnimating];
        [weakself.view toast:@"Upload Avatar Fail".translateString];
        
    }];
}

- (void)remoteLoginUserInfo{
    [kScAuthMar remoteLoginUserInfo:^(NSDictionary * _Nonnull userInfoDict) {
        ///通知头像更新
        [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];
    } failure:nil];
}

- (void)sc_blank_empty{}

-(void) actionMediaWithType:(UserMediaActionType) type model:(SCPersonalEditMediaDisplayModel *)model{
    
    kWeakSelf(self)
    if(type == UserMediaActionTypeDelete){
        //删除
        [SCAPIServiceManager requesUpdateImageMediaWithType:type deleteMediaId:[model mediaId] mediaPath:nil replaceMediaId:nil success:^(NSArray<NSDictionary *> * _Nonnull mediaDicts) {
            [weakself sc_blank_empty];
            // 使用新的统一更新方法
            [kScAuthMar updateUserMediaList:mediaDicts];
            ///通知头像更新
            [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];

        } failure:nil];
    }else{
       
        [[SCOSSManager sharedManager] uploadImage:model.localData fileName:model.localFileName uploadProgress:^(SCProgress * _Nonnull uploadProgress) {
            [weakself sc_blank_empty];
            dispatch_async(dispatch_get_main_queue(), ^{
                UIView *v = [self.imgContentView viewWithTag:[[model mediaId] integerValue]+10000];
                if([v isKindOfClass:[SCAvatarActivityIndicatorView class]]){
                    [(SCAvatarActivityIndicatorView *)v startAnimating];
                }
            });
            
        } success:^(NSString * _Nonnull fileUrl) {
            
            [weakself requesUpdateImageMediaWithType:type model:model mediaPath:fileUrl];
            
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            //Toast 更新头像失败
            dispatch_async(dispatch_get_main_queue(), ^{
                UIView *v = [weakself.imgContentView viewWithTag:([[model mediaId] length] == 0 ? 0:[[model mediaId] integerValue])+10000];
                if([v isKindOfClass:[SCAvatarActivityIndicatorView class]]){
                    [(SCAvatarActivityIndicatorView *)v stopAnimating];
                }
            });
        }];
        
        
        
    }
}

- (void)requesUpdateImageMediaWithType:(UserMediaActionType)type model:(SCPersonalEditMediaDisplayModel *)model mediaPath:(NSString * _Nullable)fileUrl{
    kWeakSelf(self)
    [SCAPIServiceManager requesUpdateImageMediaWithType:type deleteMediaId:nil mediaPath:fileUrl replaceMediaId:[model mediaId] success:^(NSArray<NSDictionary *> * _Nonnull mediaDicts) {
        [weakself sc_blank_empty];
        //需要放在数据处理前关闭loading框，因为数据处理后会改变媒体ID
        dispatch_async(dispatch_get_main_queue(), ^{
            UIView *v = [weakself.imgContentView viewWithTag:([[model mediaId] length] == 0 ? 0:[[model mediaId] integerValue])+10000];
            if([v isKindOfClass:[SCAvatarActivityIndicatorView class]]){
                [(SCAvatarActivityIndicatorView *)v stopAnimating];
            }
            // 从字典数组中找到对应的媒体项并更新model
            for (NSDictionary *mediaDict in mediaDicts) {
                NSString *mediaPath = [SCDictionaryHelper stringFromDictionary:mediaDict forKey:@"mediaPath" defaultValue:@""];
                if([mediaPath isEqualToString:fileUrl]){
                    // 更新字典数据
                    model.mediaDict = mediaDict;
                    break;
                }
            }
            // 使用新的统一更新方法
            [kScAuthMar updateUserMediaList:mediaDicts];
            ///通知头像更新
            [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];
        });
        
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        dispatch_async(dispatch_get_main_queue(), ^{
            UIView *v = [weakself.imgContentView viewWithTag:([[model mediaId] length] == 0 ? 0:[[model mediaId] integerValue])+10000];
            if([v isKindOfClass:[SCAvatarActivityIndicatorView class]]){
                [(SCAvatarActivityIndicatorView *)v stopAnimating];
            }
        });
    }];
}

- (void) onSubmit{
    
    if(kSCIsStrEmpty(self.nameTF.text)){
        [self.view toast:@"Please input your nickname".translateString];
        return;
    }

    NSString *birthDayStr = self.birthLb.text;
    if(kSCIsStrEmpty(birthDayStr) || [NSDate dataWithYYMMDDString:birthDayStr] == nil || [NSDate dataWithYYMMDDString:birthDayStr].timeIntervalSince1970 == 0){
        [self.view toast:@"Please input your birthday".translateString];
        return;
    }
    if(kSCIsStrEmpty(self.countryLb.text)){
        [self.view toast:@"Please input your country".translateString];
        return;
    }
    [self.view showLoading];
    kWeakSelf(self)
    [SCAPIServiceManager requesUpdateUserInfoWithNickName:self.nameTF.text birthday:self.birthLb.text country:self.countryLb.text about:self.aboutTextView.text success:^{
        [weakself.view hiddenLoading];
        //修改成功 - 使用新的统一更新方法
        NSDictionary *updates = @{
            SCDictionaryKeys.shared.kSCUserNicknameKey: weakself.nameTF.text ?: @"",
            @"birthday": weakself.birthLb.text ?: @"",
            SCDictionaryKeys.shared.kSCUserCountryKey: weakself.countryLb.text ?: @"",
            @"about": weakself.aboutTextView.text ?: @""
        };
        [kScAuthMar updateLocalUserInfo:updates];
        ///通知头像更新
        [[NSNotificationCenter defaultCenter] postNotificationName:kSCChangeUserInfoNoticationKey object:nil];
        [kSCKeyWindow toast:@"Edit successfully".translateString];
        [weakself.navigationController popViewControllerAnimated:YES];
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself.view hiddenLoading];
        //更新失败
        [weakself.view toast:@"Update User Info Fail".translateString];
    }];
    
    
}
-(void) imageDeleteAction:(UIButton *)sender{
    
    NSInteger index = sender.tag - 1000;
    SCPersonalEditMediaDisplayModel *model = self.viewModel.displayModel.images[index];
    [self.viewModel.displayModel.images removeObjectAtIndex:index];
    [self configMediaListViews];
    [self actionMediaWithType:UserMediaActionTypeDelete model:model];
}

- (void) addImageClick{
    
    kWeakSelf(self)
    [SCActionSheetViewController showPhotpPickerWithFromVC:self albumBlock:^(UIImage * _Nullable selectedImage) {
        if(selectedImage != nil)
            [weakself addImage:selectedImage index:-1];
    } cancelBlock:nil];
}

- (void) addImage:(UIImage *)image index:(NSInteger) index{
    SCPersonalEditMediaDisplayModel * media;
    if(index == -1 || index >= [self.viewModel.displayModel.images count]){
        media = [[SCPersonalEditMediaDisplayModel alloc] init];
        media.localData = UIImageJPEGRepresentation(image, 1.0);
        [self.viewModel.displayModel.images addObject:media];
    }else{
        media = self.viewModel.displayModel.images[index];
        media.localData = UIImageJPEGRepresentation(image, 1.0);
    }
    [self configMediaListViews];
    UserMediaActionType type = [[media mediaId] length] == 0 ? UserMediaActionTypeAdd:UserMediaActionTypeUpdate;
    [self actionMediaWithType:type model:media];
    
}

-(void)onBirthdata{
    [self.view endEditing:YES];
    [self.datePicker hide];
    NSDate *currentDate = [NSDate dataWithYYMMDDString:self.viewModel.displayModel.birthday];
    ///选择器
    kWeakSelf(self)
    self.datePicker = [[SCDatePicker alloc] initWithSureBlock:^(NSDate * _Nonnull selectData) {
        weakself.viewModel.displayModel.birthday = [selectData stringWithYYMMDD];
        weakself.birthLb.text = self.viewModel.displayModel.birthday;
    } title:@"Choose your birthday".translateString sureTitle:@"sure".translateString currentDate:currentDate maxDate:[NSDate dateWithNumYearsAgo:16]   minDate:[NSDate dateWithNumYearsAgo:100]];
    [self.datePicker show];
}
- (void) onSelectCountry{
    [self.view endEditing:YES];
    kWeakSelf(self)
    [SCCountryPickerViewController showWithFromVC:self defaultCountry:self.viewModel.displayModel.country selectBlock:^(SCCountryModel * _Nonnull model) {
        weakself.viewModel.displayModel.country = model.code;
        weakself.countryLb.text = model.name;
    }];
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidBeginEditing:(UITextField *)textField {
    self.activeInputView = textField;
    
    // 延迟执行滚动，确保键盘动画已开始
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self scrollToActiveInputView];
    });
}

- (void)textFieldDidEndEditing:(UITextField *)textField {
    if (self.activeInputView == textField) {
        self.activeInputView = nil;
    }
}

- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    // 获取当前文本
    NSString *currentText = textField.text ?: @"";

    // 计算新文本的长度
    NSUInteger newLength = currentText.length - range.length + string.length;

    // 限制输入长度
    return newLength <= self.maxInputLength;
}

- (BOOL)textFieldShouldReturn:(UITextField *)textField {
    // 收起键盘
    [textField resignFirstResponder];
    return YES;
}

#pragma mark - SCLimitTextViewDelegate

- (void)limitTextViewDidBeginEditing:(UIView *)textView {
    self.activeInputView = textView;
    
    // 对于SCLimitTextView，需要更长的延迟以确保键盘完全显示
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self scrollToActiveInputView];
    });

    // 额外的保护：再次延迟滚动，确保SCLimitTextView完全可见
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self scrollToActiveInputView];
    });
}

- (void)limitTextViewDidEndEditing:(UIView *)textView {
    if (self.activeInputView == textView) {
        self.activeInputView = nil;
    }
}

#pragma mark - 原生键盘管理

- (void)registerKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)unregisterKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                     name:UIKeyboardWillShowNotification
                                                   object:nil];

    [[NSNotificationCenter defaultCenter] removeObserver:self
                                                     name:UIKeyboardWillHideNotification
                                                   object:nil];
}

- (void)setupTapGestureToHideKeyboard {
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hideKeyboard)];
    tapGesture.cancelsTouchesInView = NO;
    [self.scrollView addGestureRecognizer:tapGesture];
}

- (void)hideKeyboard {
    [self.view endEditing:YES];
}



- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [userInfo[UIKeyboardFrameEndUserInfoKey] CGRectValue];
    NSTimeInterval animationDuration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];

    // 转换键盘frame到当前视图坐标系
    CGRect keyboardFrameInView = [self.view convertRect:keyboardFrame fromView:nil];
    self.keyboardHeight = keyboardFrameInView.size.height;

    // 直接使用键盘高度作为contentInset，确保有足够的滚动空间
    CGFloat bottomInset = self.keyboardHeight;

    

    [UIView animateWithDuration:animationDuration
                          delay:0
                        options:(UIViewAnimationOptions)animationCurve
                     animations:^{
        self.scrollView.contentInset = UIEdgeInsetsMake(self.scrollView.contentInset.top,
                                                       self.scrollView.contentInset.left,
                                                       bottomInset,
                                                       self.scrollView.contentInset.right);
        self.scrollView.scrollIndicatorInsets = self.scrollView.contentInset;
    } completion:^(BOOL finished) {
        // 在动画完成后滚动到输入框，确保完全可见
        if (finished) {
            // 立即滚动一次
            [self scrollToActiveInputView];

            // 延迟再次检查，确保滚动位置正确
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self scrollToActiveInputView];
            });
        }
    }];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    NSTimeInterval animationDuration = [userInfo[UIKeyboardAnimationDurationUserInfoKey] doubleValue];
    UIViewAnimationCurve animationCurve = [userInfo[UIKeyboardAnimationCurveUserInfoKey] integerValue];

    [UIView animateWithDuration:animationDuration
                          delay:0
                        options:(UIViewAnimationOptions)animationCurve
                     animations:^{
        self.scrollView.contentInset = UIEdgeInsetsMake(self.scrollView.contentInset.top,
                                                       self.scrollView.contentInset.left,
                                                       self.originalContentInsetBottom,
                                                       self.scrollView.contentInset.right);
        self.scrollView.scrollIndicatorInsets = self.scrollView.contentInset;
    } completion:nil];

    self.activeInputView = nil;
}

- (void)scrollToActiveInputView {
    if (!self.activeInputView) return;

    // 计算输入框在scrollView中的位置
    CGRect inputViewFrame = [self.scrollView convertRect:self.activeInputView.bounds fromView:self.activeInputView];

    // 计算可见区域高度（减去键盘占用的高度）
    CGFloat visibleHeight = self.scrollView.frame.size.height - self.scrollView.contentInset.bottom;

    // 获取当前滚动偏移
    CGFloat currentScrollOffset = self.scrollView.contentOffset.y;

    // 计算输入框的完整区域
    CGFloat inputViewTop = inputViewFrame.origin.y;
    CGFloat inputViewBottom = inputViewFrame.origin.y + inputViewFrame.size.height;

    // 添加安全边距，确保输入框周围有足够的可见空间
    // 对于SCLimitTextView使用更大的边距
    CGFloat topMargin = 30.0f;
    CGFloat bottomMargin = ([self.activeInputView isKindOfClass:[SCLimitTextView class]]) ? 60.0f : 30.0f;

    // 计算输入框在当前滚动位置下的可见区域
    CGFloat visibleTop = currentScrollOffset;
    CGFloat visibleBottom = currentScrollOffset + visibleHeight;

    CGFloat targetScrollOffset = currentScrollOffset;

    // 检查输入框是否完全可见
    BOOL needsScrolling = NO;

    // 如果输入框顶部被遮挡或没有足够的顶部边距
    if (inputViewTop - topMargin < visibleTop) {
        targetScrollOffset = inputViewTop - topMargin;
        needsScrolling = YES;
    }
    // 如果输入框底部被键盘遮挡或没有足够的底部边距
    else if (inputViewBottom + bottomMargin > visibleBottom) {
        targetScrollOffset = inputViewBottom + bottomMargin - visibleHeight;
        needsScrolling = YES;
    }

    // 确保不会滚动到负值
    targetScrollOffset = MAX(0, targetScrollOffset);

    // 确保不会滚动超过内容高度
    CGFloat maxScrollOffset = MAX(0, self.scrollView.contentSize.height - visibleHeight);
    targetScrollOffset = MIN(targetScrollOffset, maxScrollOffset);

    // 对于SCLimitTextView，使用更激进的滚动策略
    if ([self.activeInputView isKindOfClass:[SCLimitTextView class]]) {
        // 强制滚动到输入框底部完全可见的位置
        CGFloat forceTargetOffset = inputViewBottom + bottomMargin - visibleHeight;
        forceTargetOffset = MAX(0, forceTargetOffset);
        forceTargetOffset = MIN(forceTargetOffset, MAX(0, self.scrollView.contentSize.height - visibleHeight));


        [self.scrollView setContentOffset:CGPointMake(0, forceTargetOffset) animated:YES];
        return;
    }

    // 只有当需要滚动且滚动距离足够大时才执行动画
    if (needsScrolling && fabs(targetScrollOffset - currentScrollOffset) > 1) {
        [self.scrollView setContentOffset:CGPointMake(0, targetScrollOffset) animated:YES];
    }
}

#pragma mark - 创建UI

- (UIView *)createNameItemViewWithTitle:(NSString *)title {
    UIView *v = [[UIView alloc] init];
    v.layer.cornerRadius = 10.0f;
    v.layer.masksToBounds = YES;
    v.backgroundColor = [UIColor colorWithHexString:@"#500E0E"];
    
    UILabel *titleLb = [[UILabel alloc] init];
    titleLb.font = kScUIFontMedium(14.0f);
    titleLb.text = title;
    titleLb.textColor = [UIColor colorWithHexString:@"#6F6E6C"];
    
    [v addSubview:titleLb];
    [titleLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(v).offset(16);
        make.centerY.equalTo(v);
        make.width.equalTo(@(100));
    }];
    
    return v;
}
- (UIView *) createTextFildItemWithItemView:(UITextField *) inputView title:(NSString *)title{
    UIView *nameContentView = [self createNameItemViewWithTitle:title];
    
    inputView.borderStyle = UITextBorderStyleNone;
    inputView.font = kScUIFontMedium(14.0f);
    inputView.textColor = UIColor.scWhite;
    inputView.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentLeft : NSTextAlignmentRight;
    inputView.delegate = self; // 设置代理
    
    [nameContentView addSubview:inputView];
    [inputView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(nameContentView).offset(-18);
        make.centerY.equalTo(nameContentView);
        make.leading.equalTo(nameContentView).offset(100);
    }];
    return nameContentView;
}

-(UIView *) createSelectItemWithItemView:(UILabel *) labelL title:(NSString *)title{
    UIView *birthContentView = [self createNameItemViewWithTitle:title];
    labelL.font = kScUIFontMedium(14.0f);
    labelL.textColor = [UIColor scWhite];
    labelL.textAlignment = kScAuthMar.isLanguageForce ? NSTextAlignmentLeft : NSTextAlignmentRight;
    
    UIImageView *birthIconImgView = [[UIImageView alloc] initWithImage:[[SCResourceManager loadImageWithName:@"ic_personal_arr_down"] imageWithTintColor:[UIColor scWhite]]];
    [birthContentView addSubview:birthIconImgView];
    [birthContentView addSubview:labelL];
    [birthIconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(birthContentView).offset(-10);
        make.centerY.equalTo(birthContentView);
    }];
    
    [labelL mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(birthIconImgView.mas_leading).offset(-7);
        make.centerY.equalTo(birthContentView);
        make.leading.greaterThanOrEqualTo(birthContentView).offset(100);
    }];
    return birthContentView;;
}

@end
