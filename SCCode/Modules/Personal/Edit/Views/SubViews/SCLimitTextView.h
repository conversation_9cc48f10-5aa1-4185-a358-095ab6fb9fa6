//
//  SCLimitTextView.h
//  Supercall
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/1/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol SCLimitTextViewDelegate <NSObject>
@optional
- (void)limitTextViewDidBeginEditing:(UIView *)textView;
- (void)limitTextViewDidEndEditing:(UIView *)textView;
@end

@interface SCLimitTextView : UIView

///最多输入字数
@property (nonatomic, assign) NSInteger maxCount;
@property (nonatomic, copy) NSString * textPlaceholder;
@property (nonatomic,copy) NSString * text;
@property (nonatomic, weak) id<SCLimitTextViewDelegate> editDelegate;

@end

NS_ASSUME_NONNULL_END
