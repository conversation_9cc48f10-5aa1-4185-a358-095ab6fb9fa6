//
//  SCAvatarActivityIndicatorView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/8.
//

#import "SCAvatarActivityIndicatorView.h"
#import <Masonry/Masonry.h>

@implementation SCAvatarActivityIndicatorView


- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor scTranBlackBGColor];
        [self setHidden:YES];
        _activityIndicator = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleLarge];
        _activityIndicator.center = CGPointMake(frame.size.width / 2, frame.size.height / 2);
        self.userInteractionEnabled = true;
        [self addSubview:_activityIndicator];
        [_activityIndicator mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self);
        }];
    }
    return self;
}
-(void)startAnimating{
    [self setHidden:NO];
    [_activityIndicator startAnimating];
}
-(void) stopAnimating{
    [self setHidden:YES];
    [_activityIndicator stopAnimating];
}
@end
