//
//  SCLimitTextView.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/6.
//

#import "SCLimitTextView.h"
#import <Masonry/Masonry.h>

@interface SCLimitTextView ()<UITextViewDelegate>
@property (nonatomic, weak) UITextView *textView;
@property (nonatomic, weak) UILabel *textPlaceholderLb;
@property (nonatomic, weak) UILabel *textNumLb;
@end
@implementation SCLimitTextView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _maxCount = 300;
        [self setupUI];
        [self setupLayout];
    }
    return self;
}

-(void) setupUI{
    self.layer.borderColor = [UIColor scGray].CGColor;
    self.layer.borderWidth = 1.0;
    self.layer.cornerRadius = kSCBigCornerRadius;
    
    UITextView *textView = [[UITextView alloc] init];
    textView.backgroundColor = [UIColor clearColor];
    textView.font = [UIFont systemFontOfSize:15];
    textView.textColor = UIColor.scWhite;
    textView.delegate = self;
    
    UILabel *textPlaceholderLb = [[UILabel alloc] init];
    textPlaceholderLb.font = [UIFont systemFontOfSize:15];
    textPlaceholderLb.textColor = [UIColor colorWithHexString:@"#6F6E6C"];
    textPlaceholderLb.text = self.textPlaceholder;
    
    UILabel *textNumLb = [[UILabel alloc] init];
    textNumLb.font = [UIFont systemFontOfSize:15];
    textNumLb.textColor = [UIColor colorWithHexString:@"#6F6E6C"];
    [self calculateTextNum];
    
    [self addSubview:textView];
    [self addSubview:textPlaceholderLb];
    [self addSubview:textNumLb];
    self.textView = textView;
    self.textNumLb = textNumLb;
    self.textPlaceholderLb = textPlaceholderLb;

    [textPlaceholderLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(18);
        make.top.equalTo(self).offset(8);
    }];
    
    [textNumLb mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self).offset(-11);
        make.bottom.equalTo(self).offset(-7);
    }];
    
    [textView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self).offset(13);
        make.trailing.equalTo(self).offset(-13);
        make.top.equalTo(self);
        make.bottom.equalTo(self);
    }];
}

-(void) setupLayout{
    
}

//更新字数限制的UI
- (void) calculateTextNum{
    NSString *text = self.textView.text;
    NSInteger textNum = text.length;
    self.textNumLb.text = [NSString stringWithFormat:@"%ld/%ld",textNum,self.maxCount];
}
//更新是否显示提示文案
- (void) isShowPlaceholder:(NSString *)text{
    if kSCIsStrEmpty(text) {
        [self.textPlaceholderLb setHidden:NO];
    }else {
        [self.textPlaceholderLb setHidden:YES];
    }
}

#pragma mark - GET SET
- (void)setMaxCount:(NSInteger)maxCount{
    _maxCount = maxCount;
    [self calculateTextNum];
}
- (void)setTextPlaceholder:(NSString *)textPlaceholder{
    _textPlaceholder = textPlaceholder;
    self.textPlaceholderLb.text = textPlaceholder;
}

- (void)setText:(NSString *)text{
    self.textView.text = text;
    [self calculateTextNum];
    [self isShowPlaceholder:text];
}
- (NSString *)text{
    return self.textView.text;
}

- (BOOL)becomeFirstResponder {
    return [self.textView becomeFirstResponder];
}

#pragma mark - UITextViewDelegate

- (void)textViewDidBeginEditing:(UITextView *)textView {
    if ([self.editDelegate respondsToSelector:@selector(limitTextViewDidBeginEditing:)]) {
        [self.editDelegate limitTextViewDidBeginEditing:self];
    }
}

- (void)textViewDidEndEditing:(UITextView *)textView {
    if ([self.editDelegate respondsToSelector:@selector(limitTextViewDidEndEditing:)]) {
        [self.editDelegate limitTextViewDidEndEditing:self];
    }
}

- (void)textViewDidChange:(UITextView *)textView{
    [self isShowPlaceholder:textView.text];
    [self calculateTextNum];
}

- (BOOL)textView:(UITextView *)textView shouldChangeTextInRange:(NSRange)range replacementText:(NSString *)text {
    // 获取当前文本
    NSString *currentText = textView.text ?: @"";

    // 计算新文本的长度
    NSUInteger newLength = currentText.length - range.length + text.length;

    // 限制输入长度
    return newLength <= self.maxCount;
}

@end
