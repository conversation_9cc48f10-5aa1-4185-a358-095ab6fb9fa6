//
//  SCAboutHederView.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2024/1/5.
//

#import "SCAboutHederView.h"
#import <Masonry/Masonry.h>
@interface SCAboutHederView ()

@property(nonatomic,nullable,weak) UIImageView *iconImageView;
@property(nonatomic,nullable,weak) UILabel *titleLabel;
@property(nonatomic,nullable,weak) UILabel *versionLabel;



@end

@implementation SCAboutHederView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        [self setupSubviews];
        [self setupSubviewsLayout];
    }
    return self;
}

- (void)setupSubviews {
    UIImageView *iconImageView = [[UIImageView alloc] init];
    iconImageView.image = kSCCodeMar.applogoCallback();
    iconImageView.layer.cornerRadius = kSCNormalCornerRadius;
    iconImageView.layer.masksToBounds = YES;
    [self addSubview:iconImageView];
    self.iconImageView = iconImageView;
    NSString * appName = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleDisplayName"];
    if(appName == nil){
        appName = [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleName"];
    }
    UILabel *titleLabel = [UILabel labelWithText:appName textColor:[UIColor scWhite] font:kScUIFontSemibold(18.0f) alignment:NSTextAlignmentCenter];

    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
    
    UILabel *versionLabel = [UILabel labelWithText:[NSString stringWithFormat:@"V %@",[[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"]] textColor:[UIColor scWhite] font:kScUIFontRegular(11) alignment:NSTextAlignmentCenter];
    [self addSubview:versionLabel];
    self.versionLabel = versionLabel;
}


- (void)setupSubviewsLayout {
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self).offset(40);
        make.size.mas_equalTo(CGSizeMake(100, 100));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.iconImageView.mas_bottom).offset(12);
        make.height.mas_equalTo(32);
    }];
    
    [self.versionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(12);
        make.height.mas_equalTo(16);
    }];
}


@end
