//
//  SCAboutViewController.m
//  Supercall
//
//  Created by guan<PERSON><PERSON> on 2024/1/5.
//

#import "SCAboutViewController.h"
#import <Masonry/Masonry.h>
#import <StoreKit/StoreKit.h>
//View
#import "SCPersonalItemCell.h"
#import "SCAboutHederView.h"
#import "SCWebViewController.h"
#import "SCNavigationBar.h"



@interface SCAboutViewController ()
@property(nonatomic,nullable,weak) UITableView *tableView;
@property(nonatomic,nonnull, strong) NSArray<SCPersonalItemDisplayModel *> *menus;
@end

@interface SCAboutViewController (SCAboutTableView)<UITableViewDelegate,UITableViewDataSource>
@end

@implementation SCAboutViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self _initData];
    [self _initUI];
}
-(void) _initData{
    [self.scNavigationBar setNavigationBarStyle:SCNavigationBarStyleTransparent];
    self.title = @"About".translateString;
    _menus = @[
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Terms & Conditions".translateString subTitle:@"" image:nil style:SCPersonalItemCellStyleOnlyTitleAndArrow],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Privacy Policy".translateString subTitle:@"" image:nil style:SCPersonalItemCellStyleOnlyTitleAndArrow],
        [[SCPersonalItemDisplayModel alloc] initWithTitle:@"Rate Us".translateString subTitle:@"" image:nil style:SCPersonalItemCellStyleOnlyTitleAndArrow],
        
    ];
}
-(void) _initUI{
    UITableView *tableView = [UITableView tableViewWithFrame:self.scContentView.bounds style:UITableViewStylePlain delegate:self dataSource:self];
    tableView.backgroundColor = [UIColor scGlobalBgColor];
    tableView.tableHeaderView = [[SCAboutHederView alloc] initWithFrame:CGRectMake(0, 0, self.scContentView.frame.size.width, 237)];
    [self.scContentView addSubview:tableView];
    _tableView = tableView;
    [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scContentView);
    }];

}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath {
    SCPersonalItemCell *cell = [SCPersonalItemCell initWithFormTableView:tableView];
    SCPersonalItemDisplayModel * model = self.menus[indexPath.row];
    [cell configWithImage:model.image title:model.title subTitle:model.subTitle style:model.style];
    return cell;
}

- (NSInteger)tableView:(nonnull UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.menus.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    SCPersonalItemDisplayModel * model = self.menus[indexPath.row];
    return [SCPersonalItemCell heightWithStyle:model.style]+20;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    if(indexPath.row == 0){
        [SCWebViewController showTermConditionsWithFromVC:self];
    }else if(indexPath.row == 1){
        [SCWebViewController showPrivacyPolicyWithFromVC:self];
    }else if(indexPath.row == 2){
        if ([SKStoreReviewController respondsToSelector:@selector(requestReviewInScene:)]) {
            [SKStoreReviewController requestReviewInScene:self.view.window.windowScene];
        } else {
            // Fallback for older versions of iOS
            [SKStoreReviewController requestReview];
        }
    }
}
@end
