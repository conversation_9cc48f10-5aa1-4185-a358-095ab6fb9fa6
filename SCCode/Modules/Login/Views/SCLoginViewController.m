//
//  SCLoginViewController.m
//  Supercall
//
//  Created by g<PERSON><PERSON><PERSON> on 2023/12/21.
//

#import "SCLoginViewController.h"
#import "SCXErrorModel.h"
#import "SCResourceManager.h"
#import "SCLocalVideoPlayer.h"
#import "SCWebViewController.h"
#import "SCDataConverter.h"

@interface SCLoginViewController ()

@property (strong, nonatomic) SCLocalVideoPlayer *videoPlayer;
@property (strong, nonatomic) UIButton *loginBtn;
@property (strong, nonatomic) UIStackView *agreementStackView;
@property (strong, nonatomic) UITextView *agreementTextView;
@property (strong, nonatomic) UIImageView *videoContainView;

@end

@implementation SCLoginViewController

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self.videoPlayer pause];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    if (!kSCCodeMar.isCourage) {
        [self.videoPlayer play];
    }
}

- (void)dealloc {
    [self.videoPlayer pause];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self configureUI];
    [self addNotifications];
    if (!kSCCodeMar.isCourage) {
        kWeakSelf(self)
        [SCResourceManager initAssterWithCompletion:^(BOOL success) {
            [weakself.videoPlayer play];
        }];
    }
}

#pragma mark - UI
- (void)configureUI {
    self.view.backgroundColor = [UIColor colorWithHexString:@"#222222"];
    [self setIsHiddenSCNavigationBar:YES];
    [self setupVideoConainView];
    [self setupVideoPlayer];
    [self setupLoginButton];
    [self setupAgreementStack];
    [self setupConstraints];
}

- (void)setupVideoConainView {
    self.videoContainView = [[UIImageView alloc]init];
    [self.view addSubview:self.videoContainView];
}

- (void)setupVideoPlayer {
    self.videoPlayer = [[SCLocalVideoPlayer alloc] initWithFrame:CGRectZero 
                                                     videoName:@"login_video"
                                                         type:@"mp4"];
    self.videoPlayer.layer.cornerRadius = 20.0f;
    self.videoPlayer.layer.masksToBounds = YES;
    [self.videoContainView addSubview:self.videoPlayer];
}

- (void)setupLoginButton {
    self.loginBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.loginBtn setTitle:@"Chat Now".translateString forState:UIControlStateNormal];
    [self.loginBtn setTitleColor:[UIColor scBlack] forState:UIControlStateNormal];
    self.loginBtn.titleLabel.font = kScUIFontMedium(20);
    self.loginBtn.backgroundColor = [UIColor scWhite];
    self.loginBtn.layer.cornerRadius = kSCNormalCornerRadius;
    self.loginBtn.clipsToBounds = YES;
    [self.loginBtn addTarget:self action:@selector(loginAction) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.loginBtn];
}

- (void)setupAgreementStack {
    
    // 创建协议文本
    self.agreementTextView = [[UITextView alloc] init];
    self.agreementTextView.editable = NO;
    self.agreementTextView.scrollEnabled = NO;
    self.agreementTextView.backgroundColor = [UIColor clearColor];
    self.agreementTextView.textAlignment = NSTextAlignmentCenter;
    self.agreementTextView.textContainerInset = UIEdgeInsetsZero;
    self.agreementTextView.textContainer.lineFragmentPadding = 0;
    self.agreementTextView.userInteractionEnabled = YES;
    
    NSString *termsText = @"User Agreement".translateString;
    NSString *privacyText = @"Privacy Policy".translateString;
    NSString *fullTranslateText = @"By proceeding, you agree to our ##1## and ##2##.".translateString;
    
    NSString *fullText = [fullTranslateText stringByReplacingOccurrencesOfString:@"##1##" withString:termsText];
    fullText = [fullText stringByReplacingOccurrencesOfString:@"##2##" withString:privacyText];

    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:fullText];
    
    // 设置基础样式，添加段落样式
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.alignment = NSTextAlignmentCenter;
    paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;
    paragraphStyle.lineSpacing = 2;
    
    // 设置基础样式
    [attributedString addAttributes:@{
        NSFontAttributeName: [UIFont systemFontOfSize:12 weight:UIFontWeightMedium],
        NSForegroundColorAttributeName: [UIColor scWhite],
        NSParagraphStyleAttributeName: paragraphStyle
    } range:NSMakeRange(0, fullText.length)];
    
    // 设置Terms & Conditions的样式和点击事件
    NSRange termsRange = [fullText rangeOfString:termsText];
    [attributedString addAttributes:@{
        NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#FF0054"],
        NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle),
        NSUnderlineColorAttributeName: [UIColor colorWithHexString:@"#FF0054"],
        @"ClickableType": @"terms"  // 自定义属性标记点击类型
    } range:termsRange];
    
    // 设置Privacy Policy的样式和点击事件
    NSRange privacyRange = [fullText rangeOfString:privacyText];
    [attributedString addAttributes:@{
        NSForegroundColorAttributeName: [UIColor colorWithHexString:@"#FF0054"],
        NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle),
        NSUnderlineColorAttributeName: [UIColor colorWithHexString:@"#FF0054"],
        @"ClickableType": @"privacy"  // 自定义属性标记点击类型
    } range:privacyRange];

    self.agreementTextView.attributedText = attributedString;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(agreementTextViewTapped:)];
    [self.agreementTextView addGestureRecognizer:tapGesture];
    
    // 更新StackView使用
    self.agreementStackView = [[UIStackView alloc] initWithArrangedSubviews:@[self.agreementTextView]];
    self.agreementStackView.axis = UILayoutConstraintAxisHorizontal;
    self.agreementStackView.alignment = UIStackViewAlignmentTop;
    self.agreementStackView.spacing = 0;
    [self.view addSubview:self.agreementStackView];
}

- (void)setupConstraints {
    // 视频播放器约束
    [self.videoContainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(32);
        make.trailing.equalTo(self.view).offset(-32);
        make.top.equalTo(self.view).offset(100);
        make.height.mas_equalTo(kSCScaleHeight(517));
    }];
    
    [self.videoPlayer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.videoContainView);
    }];
    
    // 登录按钮约束
    [self.loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(68);
        make.trailing.equalTo(self.view).offset(-68);
        make.height.equalTo(@52);
        make.bottom.equalTo(self.agreementStackView.mas_top).offset(-27);
    }];
    
    // 修改协议栈视图约束
    [self.agreementStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view).offset(75);
        make.trailing.equalTo(self.view).offset(-75);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-(kSCSafeAreaBottomHeight > 0 ? 13 : 17));
    }];
}

#pragma mark - Actions
- (void)termsAction {
    // 处理协议点击事件
    [SCWebViewController showTermConditionsWithFromVC:self];
}

// 添加隐私政策处理方法
- (void)privacyAction {
    // 处理隐私政策点击事件
    [SCWebViewController showPrivacyPolicyWithFromVC:self];
}

#pragma mark - Action
- (void)loginAction {
    [self performLogin];
}

- (void)performLogin {
    [UIView showLoading];
    if (kSCIsStrEmpty(kSCCodeMar.accessToken) && kSCCodeMar.preTokenDict != nil) {
        // 如果没有，则请求接口
        kWeakSelf(self)
        [kScAuthMar doLogin:^(NSDictionary * _Nonnull token) {
            [weakself sc_blank_empty];
            [weakself getStrategyWithToken:token];
        } failure:^(SCXErrorModel * _Nonnull error) {
            [weakself sc_blank_empty];
            [UIView hiddenLoading];
            [UIView toast:error.msg];
        }];
    } else {
        // 如果不为空，则使用预先登录的数据。
        if (kSCCodeMar.isCourage) {
            NSString *jsonString = [SCDataConverter jsonStringFromDictionary:kSCCodeMar.preTokenDict];
            [kSCCodeMar callbackToAPageIsLogin:jsonString];
            kSCCodeMar.accessToken = @"";
            kSCCodeMar.preTokenDict = nil;
        } else {
            [kScAuthMar doLogunSuccess:kSCCodeMar.preTokenDict complete:^(SCXErrorModel * _Nullable error) {}];
            kSCCodeMar.accessToken = @"";
            kSCCodeMar.preTokenDict = nil;
        }
    }
}

- (void)getStrategyWithToken:(NSDictionary * _Nonnull)token {
    kWeakSelf(self)
    [kSCCodeMar getStrategyWithToken:[token objectForKey:@"token"] callback:^(BOOL isAudit, BOOL isMatchEnabled) {
        [weakself sc_blank_empty];
        [UIView hiddenLoading];
        if (isAudit) {
            // 审核模式 - 使用字典转JSON
            NSString *jsonString = [SCDataConverter jsonStringFromDictionary:token];
            [kSCCodeMar callbackToAPageIsLogin:jsonString];
        } else {
            // 非审核
            [kScAuthMar doLogunSuccess:token complete:^(SCXErrorModel * _Nullable error) { }];
        }
    } failure:^(SCXErrorModel * _Nonnull error) {
        [weakself sc_blank_empty];
        [UIView hiddenLoading];
        [UIView toast:error.msg];
    }];
}

- (void)sc_blank_empty{
    
}

- (void)addNotifications {
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationWillResignActive:)
                                               name:UIApplicationWillResignActiveNotification
                                             object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationDidBecomeActive:)
                                               name:UIApplicationDidBecomeActiveNotification
                                             object:nil];
}

- (void)applicationWillResignActive:(NSNotification *)notification {
    [self.videoPlayer pause];
}

- (void)applicationDidBecomeActive:(NSNotification *)notification {
    if (!kSCCodeMar.isCourage) {
        [self.videoPlayer play];
    }
}

#pragma mark - 协议文本点击处理
- (void)agreementTextViewTapped:(UITapGestureRecognizer *)gesture {
    UITextView *textView = (UITextView *)gesture.view;
    CGPoint location = [gesture locationInView:textView];

    // 获取点击位置对应的字符索引
    NSTextStorage *textStorage = textView.textStorage;
    NSLayoutManager *layoutManager = textView.layoutManager;
    NSTextContainer *textContainer = textView.textContainer;

    NSUInteger characterIndex = [layoutManager characterIndexForPoint:location
                                                       inTextContainer:textContainer
                                              fractionOfDistanceBetweenInsertionPoints:nil];

    if (characterIndex < textStorage.length) {
        // 获取点击位置的属性
        NSDictionary *attributes = [textStorage attributesAtIndex:characterIndex effectiveRange:NULL];
        NSString *clickableType = attributes[@"ClickableType"];

        if ([clickableType isEqualToString:@"terms"]) {
            [self termsAction];
        } else if ([clickableType isEqualToString:@"privacy"]) {
            [self privacyAction];
        }
    }
}

@end
