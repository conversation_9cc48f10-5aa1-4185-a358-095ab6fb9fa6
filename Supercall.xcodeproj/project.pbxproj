// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		96BED0BE2E375E2400052454 /* SCAnchorInfoViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDB92E375E2400052454 /* SCAnchorInfoViewModel.m */; };
		96BED0BF2E375E2400052454 /* SCMorePanelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEC12E375E2400052454 /* SCMorePanelView.m */; };
		96BED0C02E375E2400052454 /* SCNetworkConstant.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD2A2E375E2400052454 /* SCNetworkConstant.m */; };
		96BED0C12E375E2400052454 /* SCNetworkStatusManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD332E375E2400052454 /* SCNetworkStatusManager.m */; };
		96BED0C22E375E2400052454 /* SCNetWorkResponseModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD2C2E375E2400052454 /* SCNetWorkResponseModel.m */; };
		96BED0C32E375E2400052454 /* SCNoneFlagMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD432E375E2400052454 /* SCNoneFlagMessage.m */; };
		96BED0C42E375E2400052454 /* SCCategoryUIViewControllerCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD222E375E2400052454 /* SCCategoryUIViewControllerCode.m */; };
		96BED0C52E375E2400052454 /* SCImageMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEB82E375E2400052454 /* SCImageMessageCell.m */; };
		96BED0C72E375E2400052454 /* SCThirdPartyPayWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCD32E375E2400052454 /* SCThirdPartyPayWebViewController.m */; };
		96BED0C82E375E2400052454 /* SCCategoryAPIManagerPay.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCC72E375E2400052454 /* SCCategoryAPIManagerPay.m */; };
		96BED0C92E375E2400052454 /* SCWaterFallFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDF12E375E2400052454 /* SCWaterFallFlowLayout.m */; };
		96BED0CA2E375E2400052454 /* SCSafetyUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD9C2E375E2400052454 /* SCSafetyUtils.m */; };
		96BED0CB2E375E2400052454 /* MJRefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD782E375E2400052454 /* MJRefreshConst.m */; };
		96BED0CC2E375E2400052454 /* MJRefreshTrailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD672E375E2400052454 /* MJRefreshTrailer.m */; };
		96BED0CE2E375E2400052454 /* SCBaseTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC6E2E375E2400052454 /* SCBaseTableViewCell.m */; };
		96BED0CF2E375E2400052454 /* SCSingleJsonMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD452E375E2400052454 /* SCSingleJsonMessage.m */; };
		96BED0D02E375E2400052454 /* SCFontManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCA82E375E2400052454 /* SCFontManager.m */; };
		96BED0D12E375E2400052454 /* SCVideoCallViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE502E375E2400052454 /* SCVideoCallViewController.m */; };
		96BED0D22E375E2400052454 /* SCCryptoUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD902E375E2400052454 /* SCCryptoUtils.m */; };
		96BED0D32E375E2400052454 /* SCFlashChatViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE6C2E375E2400052454 /* SCFlashChatViewController.m */; };
		96BED0D42E375E2400052454 /* SCCategoryNSDateCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD0A2E375E2400052454 /* SCCategoryNSDateCode.m */; };
		96BED0D52E375E2400052454 /* SCVideoCallChatCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE322E375E2400052454 /* SCVideoCallChatCell.m */; };
		96BED0D62E375E2400052454 /* SCActivityFloatingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF482E375E2400052454 /* SCActivityFloatingView.m */; };
		96BED0D72E375E2400052454 /* SCLocalVideoPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCAE2E375E2400052454 /* SCLocalVideoPlayer.m */; };
		96BED0D82E375E2400052454 /* SCMyLevelHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEFD2E375E2400052454 /* SCMyLevelHeaderView.m */; };
		96BED0DA2E375E2400052454 /* SCFollowListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDAC2E375E2400052454 /* SCFollowListViewController.m */; };
		96BED0DB2E375E2400052454 /* SCPopupManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCDF2E375E2400052454 /* SCPopupManager.m */; };
		96BED0DC2E375E2400052454 /* MJRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD652E375E2400052454 /* MJRefreshHeader.m */; };
		96BED0DD2E375E2400052454 /* SCNativeSQLiteDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE212E375E2400052454 /* SCNativeSQLiteDatabase.m */; };
		96BED0DE2E375E2400052454 /* SCCircularCountdownView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC8D2E375E2400052454 /* SCCircularCountdownView.m */; };
		96BED0DF2E375E2400052454 /* SCRobotCustomerQuestionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEDA2E375E2400052454 /* SCRobotCustomerQuestionCell.m */; };
		96BED0E02E375E2400052454 /* SCPersonalViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF102E375E2400052454 /* SCPersonalViewController.m */; };
		96BED0E12E375E2400052454 /* SCGradientColors.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD5C2E375E2400052454 /* SCGradientColors.m */; };
		96BED0E22E375E2400052454 /* SCPromotionDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF242E375E2400052454 /* SCPromotionDisplayModel.m */; };
		96BED0E32E375E2400052454 /* SCRankingListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE0D2E375E2400052454 /* SCRankingListView.m */; };
		96BED0E42E375E2400052454 /* SCThirdPartyPayPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCD12E375E2400052454 /* SCThirdPartyPayPopup.m */; };
		96BED0E52E375E2400052454 /* SCAskGiftTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE352E375E2400052454 /* SCAskGiftTipView.m */; };
		96BED0E62E375E2400052454 /* SCFreeTimeCountdownView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE3B2E375E2400052454 /* SCFreeTimeCountdownView.m */; };
		96BED0E72E375E2400052454 /* SCCategoryUIImageViewCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD1A2E375E2400052454 /* SCCategoryUIImageViewCode.m */; };
		96BED0E82E375E2400052454 /* SCPersonalItemCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF0A2E375E2400052454 /* SCPersonalItemCell.m */; };
		96BED0E92E375E2400052454 /* SCAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC812E375E2400052454 /* SCAlertViewController.m */; };
		96BED0EB2E375E2400052454 /* SCVideoHorizontalListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDCF2E375E2400052454 /* SCVideoHorizontalListView.m */; };
		96BED0EC2E375E2400052454 /* SCBaseAppService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC682E375E2400052454 /* SCBaseAppService.m */; };
		96BED0EE2E375E2400052454 /* SCThrottle.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCE82E375E2400052454 /* SCThrottle.m */; };
		96BED0EF2E375E2400052454 /* SCKeychainUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD982E375E2400052454 /* SCKeychainUtils.m */; };
		96BED0F02E375E2400052454 /* SCCallNotificationPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE4C2E375E2400052454 /* SCCallNotificationPopup.m */; };
		96BED0F12E375E2400052454 /* SCFullScreenPreviewMediaViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDFF2E375E2400052454 /* SCFullScreenPreviewMediaViewController.m */; };
		96BED0F22E375E2400052454 /* SCCoinsStoreViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF2D2E375E2400052454 /* SCCoinsStoreViewModel.m */; };
		96BED0F32E375E2400052454 /* SCConversationListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEB22E375E2400052454 /* SCConversationListCell.m */; };
		96BED0F42E375E2400052454 /* UIScrollView+EmptyDataSet.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCF62E375E2400052454 /* UIScrollView+EmptyDataSet.m */; };
		96BED0F52E375E2400052454 /* SCGradientLineView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDEB2E375E2400052454 /* SCGradientLineView.m */; };
		96BED0F62E375E2400052454 /* SCCustomPageControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDC72E375E2400052454 /* SCCustomPageControl.m */; };
		96BED0F72E375E2400052454 /* SCCallHistoryCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE2E2E375E2400052454 /* SCCallHistoryCell.m */; };
		96BED0F82E375E2400052454 /* SCCoinsPopupViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF402E375E2400052454 /* SCCoinsPopupViewController.m */; };
		96BED0F92E375E2400052454 /* SCUserBaseInfoView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEC42E375E2400052454 /* SCUserBaseInfoView.m */; };
		96BED0FA2E375E2400052454 /* SCGiftSendNumModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE732E375E2400052454 /* SCGiftSendNumModel.m */; };
		96BED0FB2E375E2400052454 /* SCConstant.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD042E375E2400052454 /* SCConstant.m */; };
		96BED0FC2E375E2400052454 /* SCRankingTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE062E375E2400052454 /* SCRankingTitleView.m */; };
		96BED0FD2E375E2400052454 /* SCCountryPickerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC9F2E375E2400052454 /* SCCountryPickerViewController.m */; };
		96BED0FE2E375E2400052454 /* AGEmojiPageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCF32E375E2400052454 /* AGEmojiPageView.m */; };
		96BED0FF2E375E2400052454 /* MJRefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD612E375E2400052454 /* MJRefreshComponent.m */; };
		96BED1002E375E2400052454 /* SCRankingHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE092E375E2400052454 /* SCRankingHeaderView.m */; };
		96BED1012E375E2400052454 /* SCOrderResultModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCC22E375E2400052454 /* SCOrderResultModel.m */; };
		96BED1022E375E2400052454 /* SCCategoryAPIManagerGift.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE782E375E2400052454 /* SCCategoryAPIManagerGift.m */; };
		96BED1032E375E2400052454 /* SCOrderedDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCBF2E375E2400052454 /* SCOrderedDictionary.m */; };
		96BED1042E375E2400052454 /* SCNewUserPromotionPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF572E375E2400052454 /* SCNewUserPromotionPopup.m */; };
		96BED1052E375E2400052454 /* SCBannerService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE8B2E375E2400052454 /* SCBannerService.m */; };
		96BED1062E375E2400052454 /* SCCategoryUILabelCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD1C2E375E2400052454 /* SCCategoryUILabelCode.m */; };
		96BED1072E375E2400052454 /* SCActivityPromotionPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF532E375E2400052454 /* SCActivityPromotionPopup.m */; };
		96BED1082E375E2400052454 /* MJRefreshConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD762E375E2400052454 /* MJRefreshConfig.m */; };
		96BED1092E375E2400052454 /* SCUserItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDFC2E375E2400052454 /* SCUserItemView.m */; };
		96BED10A2E375E2400052454 /* MJRefreshStateHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD712E375E2400052454 /* MJRefreshStateHeader.m */; };
		96BED10B2E375E2400052454 /* SCAuthManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC622E375E2400052454 /* SCAuthManager.m */; };
		96BED10C2E375E2400052454 /* SCAppUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD8A2E375E2400052454 /* SCAppUtils.m */; };
		96BED10D2E375E2400052454 /* SCLimitTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEF32E375E2400052454 /* SCLimitTextView.m */; };
		96BED10E2E375E2400052454 /* SCFloatingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF4E2E375E2400052454 /* SCFloatingView.m */; };
		96BED10F2E375E2400052454 /* SCSocketService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD522E375E2400052454 /* SCSocketService.m */; };
		96BED1122E375E2400052454 /* SCLaunchVCViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE972E375E2400052454 /* SCLaunchVCViewController.m */; };
		96BED1132E375E2400052454 /* SCCallingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE392E375E2400052454 /* SCCallingView.m */; };
		96BED1142E375E2400052454 /* SCUserRankModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDD62E375E2400052454 /* SCUserRankModel.m */; };
		96BED1152E375E2400052454 /* SCAnchorInfoBottomView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDC32E375E2400052454 /* SCAnchorInfoBottomView.m */; };
		96BED1162E375E2400052454 /* SCAPIServiceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC5A2E375E2400052454 /* SCAPIServiceManager.m */; };
		96BED1172E375E2400052454 /* SCCallHangupPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE482E375E2400052454 /* SCCallHangupPopup.m */; };
		96BED1182E375E2400052454 /* SCResourceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCE22E375E2400052454 /* SCResourceManager.m */; };
		96BED1192E375E2400052454 /* SCLoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE9A2E375E2400052454 /* SCLoginViewController.m */; };
		96BED11A2E375E2400052454 /* SCVoiceMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEBE2E375E2400052454 /* SCVoiceMessageCell.m */; };
		96BED11B2E375E2400052454 /* SCStrongGuidePopoUp.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE8E2E375E2400052454 /* SCStrongGuidePopoUp.m */; };
		96BED11C2E375E2400052454 /* SCPersonalEditViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEEE2E375E2400052454 /* SCPersonalEditViewModel.m */; };
		96BED11D2E375E2400052454 /* SCGiftSendNumTipDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE752E375E2400052454 /* SCGiftSendNumTipDisplayModel.m */; };
		96BED11E2E375E2400052454 /* SCCallService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE292E375E2400052454 /* SCCallService.m */; };
		96BED1202E375E2400052454 /* SCRobotCustomerAnswerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECED82E375E2400052454 /* SCRobotCustomerAnswerCell.m */; };
		96BED1212E375E2400052454 /* SCThirdPayCannelDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCC42E375E2400052454 /* SCThirdPayCannelDisplayModel.m */; };
		96BED1222E375E2400052454 /* SCPersonalViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF072E375E2400052454 /* SCPersonalViewModel.m */; };
		96BED1232E375E2400052454 /* SCCategoryIM.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD3E2E375E2400052454 /* SCCategoryIM.m */; };
		96BED1242E375E2400052454 /* SCRechargeCardMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEBA2E375E2400052454 /* SCRechargeCardMessageCell.m */; };
		96BED1252E375E2400052454 /* SCCategoryUIImageCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD182E375E2400052454 /* SCCategoryUIImageCode.m */; };
		96BED1262E375E2400052454 /* SCCategoryUIFontCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD162E375E2400052454 /* SCCategoryUIFontCode.m */; };
		96BED1272E375E2400052454 /* SCBaseView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC702E375E2400052454 /* SCBaseView.m */; };
		96BED1282E375E2400052454 /* SCActionSheetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC7C2E375E2400052454 /* SCActionSheetViewController.m */; };
		96BED12A2E375E2400052454 /* SCCountryPickeTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC992E375E2400052454 /* SCCountryPickeTableViewCell.m */; };
		96BED12B2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BED0BB2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.m */; };
		96BED12C2E375E2400052454 /* SCTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCEB2E375E2400052454 /* SCTimer.m */; };
		96BED12D2E375E2400052454 /* SCCategoryAPIManagerDiscover.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE542E375E2400052454 /* SCCategoryAPIManagerDiscover.m */; };
		96BED12E2E375E2400052454 /* SCIMService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD482E375E2400052454 /* SCIMService.m */; };
		96BED12F2E375E2400052454 /* SCCoinsListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF372E375E2400052454 /* SCCoinsListView.m */; };
		96BED1302E375E2400052454 /* SCNativeMessageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEAD2E375E2400052454 /* SCNativeMessageModel.m */; };
		96BED1312E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDE42E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.m */; };
		96BED1322E375E2400052454 /* SCJoinChannelProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE3F2E375E2400052454 /* SCJoinChannelProgressView.m */; };
		96BED1332E375E2400052454 /* SCScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCE52E375E2400052454 /* SCScrollView.m */; };
		96BED1342E375E2400052454 /* SCHTTPRequestSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD362E375E2400052454 /* SCHTTPRequestSerializer.m */; };
		96BED1352E375E2400052454 /* SCCategoryUIButtonCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD102E375E2400052454 /* SCCategoryUIButtonCode.m */; };
		96BED1362E375E2400052454 /* SCFlashMatchSuccessPopupView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE652E375E2400052454 /* SCFlashMatchSuccessPopupView.m */; };
		96BED1372E375E2400052454 /* MJRefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD632E375E2400052454 /* MJRefreshFooter.m */; };
		96BED1382E375E2400052454 /* SCConversationListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECECB2E375E2400052454 /* SCConversationListViewController.m */; };
		96BED1392E375E2400052454 /* SCNavigationBar.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC742E375E2400052454 /* SCNavigationBar.m */; };
		96BED13A2E375E2400052454 /* SCActionService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDB02E375E2400052454 /* SCActionService.m */; };
		96BED13B2E375E2400052454 /* SCWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCFE2E375E2400052454 /* SCWebViewController.m */; };
		96BED13C2E375E2400052454 /* SCCountryService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC932E375E2400052454 /* SCCountryService.m */; };
		96BED13D2E375E2400052454 /* SCBlockListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDB52E375E2400052454 /* SCBlockListViewController.m */; };
		96BED13E2E375E2400052454 /* SCThirdPayItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCCE2E375E2400052454 /* SCThirdPayItemView.m */; };
		96BED13F2E375E2400052454 /* SCCategoryAPIManagerCoins.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF272E375E2400052454 /* SCCategoryAPIManagerCoins.m */; };
		96BED1402E375E2400052454 /* AGEmojiKeyBoardView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCF12E375E2400052454 /* AGEmojiKeyBoardView.m */; };
		96BED1412E375E2400052454 /* SCDBActionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE1F2E375E2400052454 /* SCDBActionModel.m */; };
		96BED1422E375E2400052454 /* SCRankingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE112E375E2400052454 /* SCRankingViewController.m */; };
		96BED1432E375E2400052454 /* SCRearCameraConfigModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE182E375E2400052454 /* SCRearCameraConfigModel.m */; };
		96BED1442E375E2400052454 /* SCGiftTipView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE3D2E375E2400052454 /* SCGiftTipView.m */; };
		96BED1452E375E2400052454 /* SCRobotCustomerQuestionSetCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEDC2E375E2400052454 /* SCRobotCustomerQuestionSetCell.m */; };
		96BED1472E375E2400052454 /* SCAnchorInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDD22E375E2400052454 /* SCAnchorInfoViewController.m */; };
		96BED1482E375E2400052454 /* SCGiftCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE7E2E375E2400052454 /* SCGiftCell.m */; };
		96BED1492E375E2400052454 /* SCMessageTitleItemDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEA92E375E2400052454 /* SCMessageTitleItemDisplayModel.m */; };
		96BED14A2E375E2400052454 /* SCStoreScoreAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC872E375E2400052454 /* SCStoreScoreAlertViewController.m */; };
		96BED14B2E375E2400052454 /* SCNoEnoughCoinsAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC852E375E2400052454 /* SCNoEnoughCoinsAlertViewController.m */; };
		96BED14C2E375E2400052454 /* SCAVAudioSessionUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD8C2E375E2400052454 /* SCAVAudioSessionUtils.m */; };
		96BED14D2E375E2400052454 /* SCObservable.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCB62E375E2400052454 /* SCObservable.m */; };
		96BED14E2E375E2400052454 /* SCBaseNavigationController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC6C2E375E2400052454 /* SCBaseNavigationController.m */; };
		96BED14F2E375E2400052454 /* SCVoicePlayerManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEA02E375E2400052454 /* SCVoicePlayerManager.m */; };
		96BED1502E375E2400052454 /* SCSocketEventModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD4F2E375E2400052454 /* SCSocketEventModel.m */; };
		96BED1512E375E2400052454 /* SCMyLevelCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEFA2E375E2400052454 /* SCMyLevelCell.m */; };
		96BED1522E375E2400052454 /* SCGiftAnimPlayView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE812E375E2400052454 /* SCGiftAnimPlayView.m */; };
		96BED1532E375E2400052454 /* SCFlashRouletteView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE672E375E2400052454 /* SCFlashRouletteView.m */; };
		96BED1542E375E2400052454 /* SCDiscoverBannerCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE582E375E2400052454 /* SCDiscoverBannerCell.m */; };
		96BED1552E375E2400052454 /* SCOnlineStatusChangeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCBB2E375E2400052454 /* SCOnlineStatusChangeViewController.m */; };
		96BED1562E375E2400052454 /* SCCategoryAPIManagerBanner.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC5C2E375E2400052454 /* SCCategoryAPIManagerBanner.m */; };
		96BED1572E375E2400052454 /* SCPersonalHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF0D2E375E2400052454 /* SCPersonalHeaderView.m */; };
		96BED1582E375E2400052454 /* SCRoundedLabelView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDEF2E375E2400052454 /* SCRoundedLabelView.m */; };
		96BED1592E375E2400052454 /* SCTryAnchorAvatarCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE302E375E2400052454 /* SCTryAnchorAvatarCell.m */; };
		96BED15A2E375E2400052454 /* SCMessagePopupManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCB12E375E2400052454 /* SCMessagePopupManager.m */; };
		96BED15B2E375E2400052454 /* SCCategoryAPIManagerFlashChat.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE5F2E375E2400052454 /* SCCategoryAPIManagerFlashChat.m */; };
		96BED15C2E375E2400052454 /* SCUserBoolChangeModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDA62E375E2400052454 /* SCUserBoolChangeModel.m */; };
		96BED15D2E375E2400052454 /* SCModelCompatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD9A2E375E2400052454 /* SCModelCompatibility.m */; };
		96BED15E2E375E2400052454 /* SCAvatarActivityIndicatorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEF12E375E2400052454 /* SCAvatarActivityIndicatorView.m */; };
		96BED15F2E375E2400052454 /* SCCoinsFullScreenHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF352E375E2400052454 /* SCCoinsFullScreenHeaderView.m */; };
		96BED1602E375E2400052454 /* SCFollowListCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDA92E375E2400052454 /* SCFollowListCell.m */; };
		96BED1612E375E2400052454 /* SCAnchorActionSheetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDB32E375E2400052454 /* SCAnchorActionSheetViewController.m */; };
		96BED1622E375E2400052454 /* SCAlignedCollectionViewFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD552E375E2400052454 /* SCAlignedCollectionViewFlowLayout.m */; };
		96BED1632E375E2400052454 /* SCGiftService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE7B2E375E2400052454 /* SCGiftService.m */; };
		96BED1642E375E2400052454 /* SCCountrySelectPopoupView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCA12E375E2400052454 /* SCCountrySelectPopoupView.m */; };
		96BED1652E375E2400052454 /* SCThreadSafeDictionary.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC782E375E2400052454 /* SCThreadSafeDictionary.m */; };
		96BED1662E375E2400052454 /* SCDiscoverViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE5B2E375E2400052454 /* SCDiscoverViewController.m */; };
		96BED1672E375E2400052454 /* SCLanguageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF172E375E2400052454 /* SCLanguageCell.m */; };
		96BED1682E375E2400052454 /* MBProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCFA2E375E2400052454 /* MBProgressHUD.m */; };
		96BED1692E375E2400052454 /* SCThirdPayCannelCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCCB2E375E2400052454 /* SCThirdPayCannelCell.m */; };
		96BED16A2E375E2400052454 /* SCOnlineStatesService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCB92E375E2400052454 /* SCOnlineStatesService.m */; };
		96BED16B2E375E2400052454 /* SCTrackingUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD9E2E375E2400052454 /* SCTrackingUtils.m */; };
		96BED16C2E375E2400052454 /* SCVoiceRecorderManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEA22E375E2400052454 /* SCVoiceRecorderManager.m */; };
		96BED16D2E375E2400052454 /* SCCategoryColor.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD5A2E375E2400052454 /* SCCategoryColor.m */; };
		96BED16E2E375E2400052454 /* SCNetworkManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD382E375E2400052454 /* SCNetworkManager.m */; };
		96BED16F2E375E2400052454 /* SCAnchorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDF62E375E2400052454 /* SCAnchorViewController.m */; };
		96BED1702E375E2400052454 /* SCCallDisablePrompt.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE372E375E2400052454 /* SCCallDisablePrompt.m */; };
		96BED1712E375E2400052454 /* SCCoinsBigCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF302E375E2400052454 /* SCCoinsBigCollectionViewCell.m */; };
		96BED1722E375E2400052454 /* SCCountrySelectCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC9B2E375E2400052454 /* SCCountrySelectCell.m */; };
		96BED1732E375E2400052454 /* SCAboutHederView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEE42E375E2400052454 /* SCAboutHederView.m */; };
		96BED1742E375E2400052454 /* SCIAPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCD72E375E2400052454 /* SCIAPManager.m */; };
		96BED1752E375E2400052454 /* UIScrollView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD7C2E375E2400052454 /* UIScrollView+MJExtension.m */; };
		96BED1762E375E2400052454 /* SCCallSessionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE152E375E2400052454 /* SCCallSessionModel.m */; };
		96BED1772E375E2400052454 /* SCGiftListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDC92E375E2400052454 /* SCGiftListView.m */; };
		96BED1782E375E2400052454 /* SCPersonalEditViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEF62E375E2400052454 /* SCPersonalEditViewController.m */; };
		96BED1792E375E2400052454 /* SCFlashChatMatchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE6A2E375E2400052454 /* SCFlashChatMatchViewController.m */; };
		96BED17A2E375E2400052454 /* SCCategoryAPIManagerCall.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE262E375E2400052454 /* SCCategoryAPIManagerCall.m */; };
		96BED17B2E375E2400052454 /* SCVideoCallRemainTimeCountdownView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE432E375E2400052454 /* SCVideoCallRemainTimeCountdownView.m */; };
		96BED17C2E375E2400052454 /* SCOnlineStatusSubscribe.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCBD2E375E2400052454 /* SCOnlineStatusSubscribe.m */; };
		96BED17D2E375E2400052454 /* SCAnchorCollectionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDE92E375E2400052454 /* SCAnchorCollectionView.m */; };
		96BED17E2E375E2400052454 /* SCCountryModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC902E375E2400052454 /* SCCountryModel.m */; };
		96BED17F2E375E2400052454 /* SCNewUserAwardPopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF552E375E2400052454 /* SCNewUserAwardPopup.m */; };
		96BED1802E375E2400052454 /* SCBaseCollectionCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC6A2E375E2400052454 /* SCBaseCollectionCell.m */; };
		96BED1812E375E2400052454 /* SCAnchorInfoHeaderView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDC52E375E2400052454 /* SCAnchorInfoHeaderView.m */; };
		96BED1822E375E2400052454 /* SCTextMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEBC2E375E2400052454 /* SCTextMessageCell.m */; };
		96BED1832E375E2400052454 /* UIView+MJExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD802E375E2400052454 /* UIView+MJExtension.m */; };
		96BED1842E375E2400052454 /* SCPayService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCD92E375E2400052454 /* SCPayService.m */; };
		96BED1852E375E2400052454 /* SCFloatingLayoutView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF4C2E375E2400052454 /* SCFloatingLayoutView.m */; };
		96BED1862E375E2400052454 /* SCCategoryUserDefaultsCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD242E375E2400052454 /* SCCategoryUserDefaultsCode.m */; };
		96BED1872E375E2400052454 /* SCCategoryUICollectionViewCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD122E375E2400052454 /* SCCategoryUICollectionViewCode.m */; };
		96BED1882E375E2400052454 /* SCOnlineStatusView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDED2E375E2400052454 /* SCOnlineStatusView.m */; };
		96BED1892E375E2400052454 /* SCAnchorCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDDF2E375E2400052454 /* SCAnchorCell.m */; };
		96BED18A2E375E2400052454 /* SCAnchorSubTagViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDF42E375E2400052454 /* SCAnchorSubTagViewController.m */; };
		96BED18B2E375E2400052454 /* SCAnchorCoinsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF3C2E375E2400052454 /* SCAnchorCoinsViewController.m */; };
		96BED18C2E375E2400052454 /* SCConversationDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEA52E375E2400052454 /* SCConversationDisplayModel.m */; };
		96BED18D2E375E2400052454 /* SCDatePicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCA52E375E2400052454 /* SCDatePicker.m */; };
		96BED18E2E375E2400052454 /* SCContinueRechargePopup.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF422E375E2400052454 /* SCContinueRechargePopup.m */; };
		96BED18F2E375E2400052454 /* SCCoinStoeFloatingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF4A2E375E2400052454 /* SCCoinStoeFloatingView.m */; };
		96BED1902E375E2400052454 /* SCDictionaryHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD942E375E2400052454 /* SCDictionaryHelper.m */; };
		96BED1912E375E2400052454 /* SCCategoryNSNumberCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD0C2E375E2400052454 /* SCCategoryNSNumberCode.m */; };
		96BED1922E375E2400052454 /* MJRefreshAutoNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD6A2E375E2400052454 /* MJRefreshAutoNormalFooter.m */; };
		96BED1932E375E2400052454 /* SCActivityPromotionDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF212E375E2400052454 /* SCActivityPromotionDisplayModel.m */; };
		96BED1942E375E2400052454 /* SCProgress.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD2E2E375E2400052454 /* SCProgress.m */; };
		96BED1952E375E2400052454 /* SCCategoryUIViewCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD202E375E2400052454 /* SCCategoryUIViewCode.m */; };
		96BED1962E375E2400052454 /* SCRankingCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE032E375E2400052454 /* SCRankingCell.m */; };
		96BED1972E375E2400052454 /* SCLanguageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF1A2E375E2400052454 /* SCLanguageViewController.m */; };
		96BED1982E375E2400052454 /* SCLanguageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCAB2E375E2400052454 /* SCLanguageManager.m */; };
		96BED1992E375E2400052454 /* SCCallViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE4E2E375E2400052454 /* SCCallViewController.m */; };
		96BED19A2E375E2400052454 /* SCAboutViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEE72E375E2400052454 /* SCAboutViewController.m */; };
		96BED19B2E375E2400052454 /* SCCountryPickerViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC962E375E2400052454 /* SCCountryPickerViewModel.m */; };
		96BED19C2E375E2400052454 /* SCMediaItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDFA2E375E2400052454 /* SCMediaItemView.m */; };
		96BED19D2E375E2400052454 /* SCDictionaryKeys.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD962E375E2400052454 /* SCDictionaryKeys.m */; };
		96BED19E2E375E2400052454 /* SCTranslationService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCEE2E375E2400052454 /* SCTranslationService.m */; };
		96BED19F2E375E2400052454 /* SCNativeConversationModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEAB2E375E2400052454 /* SCNativeConversationModel.m */; };
		96BED1A02E375E2400052454 /* SCAppIntegrationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BED0B92E375E2400052454 /* SCAppIntegrationManager.m */; };
		96BED1A12E375E2400052454 /* SCBaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC722E375E2400052454 /* SCBaseViewController.m */; };
		96BED1A22E375E2400052454 /* SCImageBrowserView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDCB2E375E2400052454 /* SCImageBrowserView.m */; };
		96BED1A32E375E2400052454 /* SCRechargeCardMessageContentModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEAF2E375E2400052454 /* SCRechargeCardMessageContentModel.m */; };
		96BED1A42E375E2400052454 /* SCPersonalItemDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF042E375E2400052454 /* SCPersonalItemDisplayModel.m */; };
		96BED1A52E375E2400052454 /* SCSettingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF1C2E375E2400052454 /* SCSettingViewController.m */; };
		96BED1A62E375E2400052454 /* SCNetworkCacheManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD272E375E2400052454 /* SCNetworkCacheManager.m */; };
		96BED1A72E375E2400052454 /* SCCategoryNSStringCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD0E2E375E2400052454 /* SCCategoryNSStringCode.m */; };
		96BED1A82E375E2400052454 /* SCDataConverter.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD922E375E2400052454 /* SCDataConverter.m */; };
		96BED1A92E375E2400052454 /* SCAnchorEvaluateViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE462E375E2400052454 /* SCAnchorEvaluateViewController.m */; };
		96BED1AA2E375E2400052454 /* SCConversationInfoViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEC92E375E2400052454 /* SCConversationInfoViewController.m */; };
		96BED1AB2E375E2400052454 /* SCMessageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECECD2E375E2400052454 /* SCMessageViewController.m */; };
		96BED1AC2E375E2400052454 /* SCLanguageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF142E375E2400052454 /* SCLanguageModel.m */; };
		96BED1AD2E375E2400052454 /* SCVoiceRecordingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEC62E375E2400052454 /* SCVoiceRecordingView.m */; };
		96BED1AE2E375E2400052454 /* SCGiftMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEB62E375E2400052454 /* SCGiftMessageCell.m */; };
		96BED1AF2E375E2400052454 /* SCFileMessageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEB42E375E2400052454 /* SCFileMessageCell.m */; };
		96BED1B02E375E2400052454 /* SCCallHistoryListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE4A2E375E2400052454 /* SCCallHistoryListViewController.m */; };
		96BED1B12E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD572E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.m */; };
		96BED1B22E375E2400052454 /* SCGameWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE702E375E2400052454 /* SCGameWebViewController.m */; };
		96BED1B32E375E2400052454 /* SCAnchorService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDD92E375E2400052454 /* SCAnchorService.m */; };
		96BED1B42E375E2400052454 /* SCMessageDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEA72E375E2400052454 /* SCMessageDisplayModel.m */; };
		96BED1B52E375E2400052454 /* SCCallDBManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE1D2E375E2400052454 /* SCCallDBManager.m */; };
		96BED1B62E375E2400052454 /* SCSmallVideoCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDBE2E375E2400052454 /* SCSmallVideoCell.m */; };
		96BED1B72E375E2400052454 /* SCGiftPopupViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE842E375E2400052454 /* SCGiftPopupViewController.m */; };
		96BED1B82E375E2400052454 /* SCPermissionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCDC2E375E2400052454 /* SCPermissionManager.m */; };
		96BED1B92E375E2400052454 /* SCAPIManage.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD022E375E2400052454 /* SCAPIManage.m */; };
		96BED1BA2E375E2400052454 /* SCAnchorSubTitleView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDE62E375E2400052454 /* SCAnchorSubTitleView.m */; };
		96BED1BB2E375E2400052454 /* SCTagCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDC02E375E2400052454 /* SCTagCell.m */; };
		96BED1BC2E375E2400052454 /* UIScrollView+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD7E2E375E2400052454 /* UIScrollView+MJRefresh.m */; };
		96BED1BD2E375E2400052454 /* MJRefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD6F2E375E2400052454 /* MJRefreshNormalHeader.m */; };
		96BED1BE2E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF442E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.m */; };
		96BED1BF2E375E2400052454 /* SCCoinsService.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF2A2E375E2400052454 /* SCCoinsService.m */; };
		96BED1C02E375E2400052454 /* SCXErrorModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD302E375E2400052454 /* SCXErrorModel.m */; };
		96BED1C12E375E2400052454 /* SCVideoCallChatMessageModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE1A2E375E2400052454 /* SCVideoCallChatMessageModel.m */; };
		96BED1C22E375E2400052454 /* SCRobotCustomerServiceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEDF2E375E2400052454 /* SCRobotCustomerServiceViewController.m */; };
		96BED1C32E375E2400052454 /* SCDisposeBag.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECCB42E375E2400052454 /* SCDisposeBag.m */; };
		96BED1C42E375E2400052454 /* SCCategoryButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD832E375E2400052454 /* SCCategoryButton.m */; };
		96BED1C52E375E2400052454 /* SCPayEntry.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD072E375E2400052454 /* SCPayEntry.m */; };
		96BED1C62E375E2400052454 /* MJRefreshAutoStateFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD6C2E375E2400052454 /* MJRefreshAutoStateFooter.m */; };
		96BED1C72E375E2400052454 /* SCVideoCallChatInputView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE412E375E2400052454 /* SCVideoCallChatInputView.m */; };
		96BED1C82E375E2400052454 /* SCGradientLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD852E375E2400052454 /* SCGradientLabel.m */; };
		96BED1C92E375E2400052454 /* UICollectionViewLayout+MJRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD7A2E375E2400052454 /* UICollectionViewLayout+MJRefresh.m */; };
		96BED1CA2E375E2400052454 /* SCOSSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC5F2E375E2400052454 /* SCOSSManager.m */; };
		96BED1CB2E375E2400052454 /* MJRefreshAutoFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD5F2E375E2400052454 /* MJRefreshAutoFooter.m */; };
		96BED1CC2E375E2400052454 /* SCLabelListView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDCD2E375E2400052454 /* SCLabelListView.m */; };
		96BED1CD2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC7E2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.m */; };
		96BED1CE2E375E2400052454 /* SCNativeSQLiteResultSet.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE232E375E2400052454 /* SCNativeSQLiteResultSet.m */; };
		96BED1CF2E375E2400052454 /* SCBannerView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE882E375E2400052454 /* SCBannerView.m */; };
		96BED1D02E375E2400052454 /* SCRankingItemView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE0B2E375E2400052454 /* SCRankingItemView.m */; };
		96BED1D12E375E2400052454 /* SCCoinsCollectionViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF322E375E2400052454 /* SCCoinsCollectionViewCell.m */; };
		96BED1D22E375E2400052454 /* SCMyLevelViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF002E375E2400052454 /* SCMyLevelViewController.m */; };
		96BED1D32E375E2400052454 /* SCCallExceptionalAlert.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC832E375E2400052454 /* SCCallExceptionalAlert.m */; };
		96BED1D42E375E2400052454 /* SCGiftNumCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDBC2E375E2400052454 /* SCGiftNumCell.m */; };
		96BED1D52E375E2400052454 /* SCAnchorViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDDC2E375E2400052454 /* SCAnchorViewModel.m */; };
		96BED1D62E375E2400052454 /* SCSafetyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC762E375E2400052454 /* SCSafetyViewController.m */; };
		96BED1D72E375E2400052454 /* SCHomeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECE932E375E2400052454 /* SCHomeViewController.m */; };
		96BED1D82E375E2400052454 /* SCCategorySocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD4B2E375E2400052454 /* SCCategorySocket.m */; };
		96BED1DA2E375E2400052454 /* SCPersionEditDisplayModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECEEB2E375E2400052454 /* SCPersionEditDisplayModel.m */; };
		96BED1DB2E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECDE22E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.m */; };
		96BED1DC2E375E2400052454 /* SCRobotCustomerDictionaryHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECED52E375E2400052454 /* SCRobotCustomerDictionaryHelper.m */; };
		96BED1DD2E375E2400052454 /* SCHyperLinkMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD412E375E2400052454 /* SCHyperLinkMessage.m */; };
		96BED1DE2E375E2400052454 /* SCMyCoinsView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF392E375E2400052454 /* SCMyCoinsView.m */; };
		96BED1DF2E375E2400052454 /* SCCoinsFullScreenViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF3E2E375E2400052454 /* SCCoinsFullScreenViewController.m */; };
		96BED1E02E375E2400052454 /* SCPromotionFloatingView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECF502E375E2400052454 /* SCPromotionFloatingView.m */; };
		96BED1E12E375E2400052454 /* SCCategoryUIColorCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD142E375E2400052454 /* SCCategoryUIColorCode.m */; };
		96BED1E22E375E2400052454 /* SCAntiRecordView.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECC8A2E375E2400052454 /* SCAntiRecordView.m */; };
		96BED1E32E375E2400052454 /* SCCategoryUITableViewCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 96BECD1E2E375E2400052454 /* SCCategoryUITableViewCode.m */; };
		96BED1E42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCA2E375E2400052454 /* <EMAIL> */; };
		96BED1E52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0422E375E2400052454 /* <EMAIL> */; };
		96BED1E62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0352E375E2400052454 /* <EMAIL> */; };
		96BED1E72E375E2400052454 /* supercall_de.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09A2E375E2400052454 /* supercall_de.json */; };
		96BED1E82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB62E375E2400052454 /* <EMAIL> */; };
		96BED1E92E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF922E375E2400052454 /* <EMAIL> */; };
		96BED1EA2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0502E375E2400052454 /* <EMAIL> */; };
		96BED1EB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0612E375E2400052454 /* <EMAIL> */; };
		96BED1EC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02D2E375E2400052454 /* <EMAIL> */; };
		96BED1ED2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF42E375E2400052454 /* <EMAIL> */; };
		96BED1EE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05D2E375E2400052454 /* <EMAIL> */; };
		96BED1EF2E375E2400052454 /* recent_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07A2E375E2400052454 /* recent_n.png */; };
		96BED1F02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0632E375E2400052454 /* <EMAIL> */; };
		96BED1F12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01F2E375E2400052454 /* <EMAIL> */; };
		96BED1F22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6F2E375E2400052454 /* <EMAIL> */; };
		96BED1F32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03B2E375E2400052454 /* <EMAIL> */; };
		96BED1F42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE02E375E2400052454 /* <EMAIL> */; };
		96BED1F52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0432E375E2400052454 /* <EMAIL> */; };
		96BED1F62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0302E375E2400052454 /* <EMAIL> */; };
		96BED1F72E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0142E375E2400052454 /* <EMAIL> */; };
		96BED1F82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDE2E375E2400052454 /* <EMAIL> */; };
		96BED1F92E375E2400052454 /* gift_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0882E375E2400052454 /* gift_animation.json */; };
		96BED1FA2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAB2E375E2400052454 /* <EMAIL> */; };
		96BED1FB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF22E375E2400052454 /* <EMAIL> */; };
		96BED1FC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05A2E375E2400052454 /* <EMAIL> */; };
		96BED1FD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFB2E375E2400052454 /* <EMAIL> */; };
		96BED1FE2E375E2400052454 /* BaiJamjuree-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF5F2E375E2400052454 /* BaiJamjuree-Regular.ttf */; };
		96BED1FF2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02E2E375E2400052454 /* <EMAIL> */; };
		96BED2002E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFD2E375E2400052454 /* <EMAIL> */; };
		96BED2012E375E2400052454 /* next_anchor_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08C2E375E2400052454 /* next_anchor_animation.json */; };
		96BED2022E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0642E375E2400052454 /* <EMAIL> */; };
		96BED2032E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF12E375E2400052454 /* <EMAIL> */; };
		96BED2042E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0692E375E2400052454 /* <EMAIL> */; };
		96BED2052E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDF2E375E2400052454 /* <EMAIL> */; };
		96BED2062E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF852E375E2400052454 /* <EMAIL> */; };
		96BED2072E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0712E375E2400052454 /* <EMAIL> */; };
		96BED2082E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD22E375E2400052454 /* <EMAIL> */; };
		96BED2092E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9E2E375E2400052454 /* <EMAIL> */; };
		96BED20A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0092E375E2400052454 /* <EMAIL> */; };
		96BED20B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03A2E375E2400052454 /* <EMAIL> */; };
		96BED20C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04F2E375E2400052454 /* <EMAIL> */; };
		96BED20D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07F2E375E2400052454 /* <EMAIL> */; };
		96BED20E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03F2E375E2400052454 /* <EMAIL> */; };
		96BED20F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF692E375E2400052454 /* <EMAIL> */; };
		96BED2102E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0782E375E2400052454 /* <EMAIL> */; };
		96BED2112E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF912E375E2400052454 /* <EMAIL> */; };
		96BED2122E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0192E375E2400052454 /* <EMAIL> */; };
		96BED2132E375E2400052454 /* face_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC22E375E2400052454 /* face_s.png */; };
		96BED2142E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01D2E375E2400052454 /* <EMAIL> */; };
		96BED2152E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFC2E375E2400052454 /* <EMAIL> */; };
		96BED2162E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0082E375E2400052454 /* <EMAIL> */; };
		96BED2172E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0162E375E2400052454 /* <EMAIL> */; };
		96BED2182E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF982E375E2400052454 /* <EMAIL> */; };
		96BED2192E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF712E375E2400052454 /* <EMAIL> */; };
		96BED21A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD42E375E2400052454 /* <EMAIL> */; };
		96BED21B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05F2E375E2400052454 /* <EMAIL> */; };
		96BED21C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04B2E375E2400052454 /* <EMAIL> */; };
		96BED21D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0582E375E2400052454 /* <EMAIL> */; };
		96BED21E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE92E375E2400052454 /* <EMAIL> */; };
		96BED21F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0332E375E2400052454 /* <EMAIL> */; };
		96BED2202E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0202E375E2400052454 /* <EMAIL> */; };
		96BED2212E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF822E375E2400052454 /* <EMAIL> */; };
		96BED2222E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9B2E375E2400052454 /* <EMAIL> */; };
		96BED2232E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0062E375E2400052454 /* <EMAIL> */; };
		96BED2242E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0482E375E2400052454 /* <EMAIL> */; };
		96BED2252E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFE2E375E2400052454 /* <EMAIL> */; };
		96BED2262E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0382E375E2400052454 /* <EMAIL> */; };
		96BED2272E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC12E375E2400052454 /* <EMAIL> */; };
		96BED2282E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0242E375E2400052454 /* <EMAIL> */; };
		96BED2292E375E2400052454 /* gift_send_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0892E375E2400052454 /* gift_send_animation.json */; };
		96BED22A2E375E2400052454 /* supercall_data.zip in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0B02E375E2400052454 /* supercall_data.zip */; };
		96BED22B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0722E375E2400052454 /* <EMAIL> */; };
		96BED22C2E375E2400052454 /* login_video.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0AB2E375E2400052454 /* login_video.mp4 */; };
		96BED22D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD32E375E2400052454 /* <EMAIL> */; };
		96BED22E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0812E375E2400052454 /* <EMAIL> */; };
		96BED22F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0042E375E2400052454 /* <EMAIL> */; };
		96BED2302E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF702E375E2400052454 /* <EMAIL> */; };
		96BED2312E375E2400052454 /* match_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08A2E375E2400052454 /* match_animation.json */; };
		96BED2322E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0732E375E2400052454 /* <EMAIL> */; };
		96BED2332E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00A2E375E2400052454 /* <EMAIL> */; };
		96BED2342E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE72E375E2400052454 /* <EMAIL> */; };
		96BED2352E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF792E375E2400052454 /* <EMAIL> */; };
		96BED2362E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0512E375E2400052454 /* <EMAIL> */; };
		96BED2372E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0172E375E2400052454 /* <EMAIL> */; };
		96BED2382E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF882E375E2400052454 /* <EMAIL> */; };
		96BED2392E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00D2E375E2400052454 /* <EMAIL> */; };
		96BED23A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB52E375E2400052454 /* <EMAIL> */; };
		96BED23B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0592E375E2400052454 /* <EMAIL> */; };
		96BED23C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0412E375E2400052454 /* <EMAIL> */; };
		96BED23D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0602E375E2400052454 /* <EMAIL> */; };
		96BED23E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF872E375E2400052454 /* <EMAIL> */; };
		96BED23F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06A2E375E2400052454 /* <EMAIL> */; };
		96BED2402E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCC2E375E2400052454 /* <EMAIL> */; };
		96BED2412E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB92E375E2400052454 /* <EMAIL> */; };
		96BED2422E375E2400052454 /* supercall_ko.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A12E375E2400052454 /* supercall_ko.json */; };
		96BED2432E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0152E375E2400052454 /* <EMAIL> */; };
		96BED2442E375E2400052454 /* BaiJamjuree-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF5D2E375E2400052454 /* BaiJamjuree-BoldItalic.ttf */; };
		96BED2452E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01C2E375E2400052454 /* <EMAIL> */; };
		96BED2462E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF892E375E2400052454 /* <EMAIL> */; };
		96BED2472E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03E2E375E2400052454 /* <EMAIL> */; };
		96BED2482E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA42E375E2400052454 /* <EMAIL> */; };
		96BED2492E375E2400052454 /* BaiJamjuree-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF602E375E2400052454 /* BaiJamjuree-SemiBold.ttf */; };
		96BED24A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF752E375E2400052454 /* <EMAIL> */; };
		96BED24B2E375E2400052454 /* car_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBA2E375E2400052454 /* car_s.png */; };
		96BED24C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00F2E375E2400052454 /* <EMAIL> */; };
		96BED24D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7D2E375E2400052454 /* <EMAIL> */; };
		96BED24E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04A2E375E2400052454 /* <EMAIL> */; };
		96BED24F2E375E2400052454 /* wave_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08F2E375E2400052454 /* wave_animation.json */; };
		96BED2502E375E2400052454 /* bell_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6C2E375E2400052454 /* bell_s.png */; };
		96BED2512E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFEF2E375E2400052454 /* <EMAIL> */; };
		96BED2522E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7F2E375E2400052454 /* <EMAIL> */; };
		96BED2532E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0552E375E2400052454 /* <EMAIL> */; };
		96BED2542E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDB2E375E2400052454 /* <EMAIL> */; };
		96BED2552E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0522E375E2400052454 /* <EMAIL> */; };
		96BED2562E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0802E375E2400052454 /* <EMAIL> */; };
		96BED2572E375E2400052454 /* BaiJamjuree-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF5E2E375E2400052454 /* BaiJamjuree-Italic.ttf */; };
		96BED2582E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA62E375E2400052454 /* <EMAIL> */; };
		96BED2592E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06E2E375E2400052454 /* <EMAIL> */; };
		96BED25A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0402E375E2400052454 /* <EMAIL> */; };
		96BED25B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0742E375E2400052454 /* <EMAIL> */; };
		96BED25C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF832E375E2400052454 /* <EMAIL> */; };
		96BED25D2E375E2400052454 /* flower_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC62E375E2400052454 /* flower_s.png */; };
		96BED25E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF642E375E2400052454 /* <EMAIL> */; };
		96BED25F2E375E2400052454 /* supercall_it.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09F2E375E2400052454 /* supercall_it.json */; };
		96BED2602E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7B2E375E2400052454 /* <EMAIL> */; };
		96BED2612E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFEB2E375E2400052454 /* <EMAIL> */; };
		96BED2622E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02A2E375E2400052454 /* <EMAIL> */; };
		96BED2632E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06D2E375E2400052454 /* <EMAIL> */; };
		96BED2642E375E2400052454 /* sc_country.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08E2E375E2400052454 /* sc_country.json */; };
		96BED2652E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9A2E375E2400052454 /* <EMAIL> */; };
		96BED2662E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0002E375E2400052454 /* <EMAIL> */; };
		96BED2672E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01A2E375E2400052454 /* <EMAIL> */; };
		96BED2682E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8A2E375E2400052454 /* <EMAIL> */; };
		96BED2692E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07E2E375E2400052454 /* <EMAIL> */; };
		96BED26A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA22E375E2400052454 /* <EMAIL> */; };
		96BED26B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0672E375E2400052454 /* <EMAIL> */; };
		96BED26C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0572E375E2400052454 /* <EMAIL> */; };
		96BED26D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04D2E375E2400052454 /* <EMAIL> */; };
		96BED26E2E375E2400052454 /* activity_promotion.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A92E375E2400052454 /* activity_promotion.mp4 */; };
		96BED26F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB72E375E2400052454 /* <EMAIL> */; };
		96BED2702E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAA2E375E2400052454 /* <EMAIL> */; };
		96BED2712E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0312E375E2400052454 /* <EMAIL> */; };
		96BED2722E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF842E375E2400052454 /* <EMAIL> */; };
		96BED2732E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF902E375E2400052454 /* <EMAIL> */; };
		96BED2742E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF02E375E2400052454 /* <EMAIL> */; };
		96BED2752E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0472E375E2400052454 /* <EMAIL> */; };
		96BED2762E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0682E375E2400052454 /* <EMAIL> */; };
		96BED2772E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF942E375E2400052454 /* <EMAIL> */; };
		96BED2782E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF62E375E2400052454 /* <EMAIL> */; };
		96BED2792E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0762E375E2400052454 /* <EMAIL> */; };
		96BED27A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0292E375E2400052454 /* <EMAIL> */; };
		96BED27B2E375E2400052454 /* supercall_fr.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09D2E375E2400052454 /* supercall_fr.json */; };
		96BED27C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAD2E375E2400052454 /* <EMAIL> */; };
		96BED27D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0772E375E2400052454 /* <EMAIL> */; };
		96BED27E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0322E375E2400052454 /* <EMAIL> */; };
		96BED27F2E375E2400052454 /* characters_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBC2E375E2400052454 /* characters_n.png */; };
		96BED2802E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA02E375E2400052454 /* <EMAIL> */; };
		96BED2812E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01E2E375E2400052454 /* <EMAIL> */; };
		96BED2822E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF732E375E2400052454 /* <EMAIL> */; };
		96BED2832E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0362E375E2400052454 /* <EMAIL> */; };
		96BED2842E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0132E375E2400052454 /* <EMAIL> */; };
		96BED2852E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFEE2E375E2400052454 /* <EMAIL> */; };
		96BED2862E375E2400052454 /* supercall_es.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09C2E375E2400052454 /* supercall_es.json */; };
		96BED2872E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD72E375E2400052454 /* <EMAIL> */; };
		96BED2882E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB12E375E2400052454 /* <EMAIL> */; };
		96BED2892E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD12E375E2400052454 /* <EMAIL> */; };
		96BED28A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE42E375E2400052454 /* <EMAIL> */; };
		96BED28B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB22E375E2400052454 /* <EMAIL> */; };
		96BED28C2E375E2400052454 /* backspace_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF662E375E2400052454 /* backspace_n.png */; };
		96BED28D2E375E2400052454 /* supercall_th.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A42E375E2400052454 /* supercall_th.json */; };
		96BED28E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9C2E375E2400052454 /* <EMAIL> */; };
		96BED28F2E375E2400052454 /* new_user_promotion.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0AC2E375E2400052454 /* new_user_promotion.mp4 */; };
		96BED2902E375E2400052454 /* banner_loading.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0852E375E2400052454 /* banner_loading.json */; };
		96BED2912E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE52E375E2400052454 /* <EMAIL> */; };
		96BED2922E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF932E375E2400052454 /* <EMAIL> */; };
		96BED2932E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC52E375E2400052454 /* <EMAIL> */; };
		96BED2942E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBB2E375E2400052454 /* <EMAIL> */; };
		96BED2952E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0372E375E2400052454 /* <EMAIL> */; };
		96BED2962E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0832E375E2400052454 /* <EMAIL> */; };
		96BED2972E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFEC2E375E2400052454 /* <EMAIL> */; };
		96BED2982E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8C2E375E2400052454 /* <EMAIL> */; };
		96BED2992E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF952E375E2400052454 /* <EMAIL> */; };
		96BED29A2E375E2400052454 /* EmojisList.plist in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0932E375E2400052454 /* EmojisList.plist */; };
		96BED29B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF32E375E2400052454 /* <EMAIL> */; };
		96BED29C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0032E375E2400052454 /* <EMAIL> */; };
		96BED29D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCD2E375E2400052454 /* <EMAIL> */; };
		96BED29E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAF2E375E2400052454 /* <EMAIL> */; };
		96BED29F2E375E2400052454 /* message_call_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08B2E375E2400052454 /* message_call_animation.json */; };
		96BED2A02E375E2400052454 /* BaiJamjuree-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF5C2E375E2400052454 /* BaiJamjuree-Bold.ttf */; };
		96BED2A12E375E2400052454 /* supercall_zh-tw.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A72E375E2400052454 /* supercall_zh-tw.json */; };
		96BED2A22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF722E375E2400052454 /* <EMAIL> */; };
		96BED2A32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00E2E375E2400052454 /* <EMAIL> */; };
		96BED2A42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06B2E375E2400052454 /* <EMAIL> */; };
		96BED2A52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6E2E375E2400052454 /* <EMAIL> */; };
		96BED2A62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC92E375E2400052454 /* <EMAIL> */; };
		96BED2A72E375E2400052454 /* call_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0862E375E2400052454 /* call_animation.json */; };
		96BED2A82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC72E375E2400052454 /* <EMAIL> */; };
		96BED2A92E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF762E375E2400052454 /* <EMAIL> */; };
		96BED2AA2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04E2E375E2400052454 /* <EMAIL> */; };
		96BED2AB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0252E375E2400052454 /* <EMAIL> */; };
		96BED2AC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03C2E375E2400052454 /* <EMAIL> */; };
		96BED2AD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFF2E375E2400052454 /* <EMAIL> */; };
		96BED2AE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDC2E375E2400052454 /* <EMAIL> */; };
		96BED2AF2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE32E375E2400052454 /* <EMAIL> */; };
		96BED2B02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFED2E375E2400052454 /* <EMAIL> */; };
		96BED2B12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED01B2E375E2400052454 /* <EMAIL> */; };
		96BED2B22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE12E375E2400052454 /* <EMAIL> */; };
		96BED2B32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0542E375E2400052454 /* <EMAIL> */; };
		96BED2B42E375E2400052454 /* face_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC02E375E2400052454 /* face_n.png */; };
		96BED2B52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD92E375E2400052454 /* <EMAIL> */; };
		96BED2B62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCF2E375E2400052454 /* <EMAIL> */; };
		96BED2B72E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD62E375E2400052454 /* <EMAIL> */; };
		96BED2B82E375E2400052454 /* bell_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6A2E375E2400052454 /* bell_n.png */; };
		96BED2B92E375E2400052454 /* flower_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC42E375E2400052454 /* flower_n.png */; };
		96BED2BA2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0752E375E2400052454 /* <EMAIL> */; };
		96BED2BB2E375E2400052454 /* characters_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBE2E375E2400052454 /* characters_s.png */; };
		96BED2BC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8B2E375E2400052454 /* <EMAIL> */; };
		96BED2BD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7A2E375E2400052454 /* <EMAIL> */; };
		96BED2BE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED03D2E375E2400052454 /* <EMAIL> */; };
		96BED2BF2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF72E375E2400052454 /* <EMAIL> */; };
		96BED2C02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF52E375E2400052454 /* <EMAIL> */; };
		96BED2C12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0792E375E2400052454 /* <EMAIL> */; };
		96BED2C22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0822E375E2400052454 /* <EMAIL> */; };
		96BED2C32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0052E375E2400052454 /* <EMAIL> */; };
		96BED2C42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBD2E375E2400052454 /* <EMAIL> */; };
		96BED2C52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7C2E375E2400052454 /* <EMAIL> */; };
		96BED2C62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDD2E375E2400052454 /* <EMAIL> */; };
		96BED2C72E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF672E375E2400052454 /* <EMAIL> */; };
		96BED2C82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02C2E375E2400052454 /* <EMAIL> */; };
		96BED2C92E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA82E375E2400052454 /* <EMAIL> */; };
		96BED2CA2E375E2400052454 /* en_source.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0972E375E2400052454 /* en_source.json */; };
		96BED2CB2E375E2400052454 /* supercall_hi.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09E2E375E2400052454 /* supercall_hi.json */; };
		96BED2CC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9F2E375E2400052454 /* <EMAIL> */; };
		96BED2CD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0212E375E2400052454 /* <EMAIL> */; };
		96BED2CE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0022E375E2400052454 /* <EMAIL> */; };
		96BED2CF2E375E2400052454 /* BaiJamjuree-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF612E375E2400052454 /* BaiJamjuree-SemiBoldItalic.ttf */; };
		96BED2D02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF812E375E2400052454 /* <EMAIL> */; };
		96BED2D12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0442E375E2400052454 /* <EMAIL> */; };
		96BED2D22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCB2E375E2400052454 /* <EMAIL> */; };
		96BED2D32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6B2E375E2400052454 /* <EMAIL> */; };
		96BED2D42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0622E375E2400052454 /* <EMAIL> */; };
		96BED2D52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED04C2E375E2400052454 /* <EMAIL> */; };
		96BED2D62E375E2400052454 /* exit_recharge.mp4 in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0AA2E375E2400052454 /* exit_recharge.mp4 */; };
		96BED2D72E375E2400052454 /* supercall_ru.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A32E375E2400052454 /* supercall_ru.json */; };
		96BED2D82E375E2400052454 /* supercall_tr.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A52E375E2400052454 /* supercall_tr.json */; };
		96BED2DA2E375E2400052454 /* supercall_en.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED09B2E375E2400052454 /* supercall_en.json */; };
		96BED2DB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC32E375E2400052454 /* <EMAIL> */; };
		96BED2DC2E375E2400052454 /* ring_call.mp3 in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0912E375E2400052454 /* ring_call.mp3 */; };
		96BED2DD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE22E375E2400052454 /* <EMAIL> */; };
		96BED2DE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD02E375E2400052454 /* <EMAIL> */; };
		96BED2DF2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0262E375E2400052454 /* <EMAIL> */; };
		96BED2E02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05B2E375E2400052454 /* <EMAIL> */; };
		96BED2E12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFC82E375E2400052454 /* <EMAIL> */; };
		96BED2E22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF772E375E2400052454 /* <EMAIL> */; };
		96BED2E32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0562E375E2400052454 /* <EMAIL> */; };
		96BED2E42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE62E375E2400052454 /* <EMAIL> */; };
		96BED2E52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0492E375E2400052454 /* <EMAIL> */; };
		96BED2E62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF92E375E2400052454 /* <EMAIL> */; };
		96BED2E72E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0452E375E2400052454 /* <EMAIL> */; };
		96BED2E82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF7E2E375E2400052454 /* <EMAIL> */; };
		96BED2E92E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0532E375E2400052454 /* <EMAIL> */; };
		96BED2EA2E375E2400052454 /* supercall_ja.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A02E375E2400052454 /* supercall_ja.json */; };
		96BED2EB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06C2E375E2400052454 /* <EMAIL> */; };
		96BED2EC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF632E375E2400052454 /* <EMAIL> */; };
		96BED2ED2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFCE2E375E2400052454 /* <EMAIL> */; };
		96BED2EE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAE2E375E2400052454 /* <EMAIL> */; };
		96BED2EF2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD52E375E2400052454 /* <EMAIL> */; };
		96BED2F02E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF9D2E375E2400052454 /* <EMAIL> */; };
		96BED2F12E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF972E375E2400052454 /* <EMAIL> */; };
		96BED2F22E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0182E375E2400052454 /* <EMAIL> */; };
		96BED2F32E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFEA2E375E2400052454 /* <EMAIL> */; };
		96BED2F42E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA12E375E2400052454 /* <EMAIL> */; };
		96BED2F52E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05C2E375E2400052454 /* <EMAIL> */; };
		96BED2F62E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0342E375E2400052454 /* <EMAIL> */; };
		96BED2F72E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF862E375E2400052454 /* <EMAIL> */; };
		96BED2F82E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0462E375E2400052454 /* <EMAIL> */; };
		96BED2F92E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF962E375E2400052454 /* <EMAIL> */; };
		96BED2FA2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0702E375E2400052454 /* <EMAIL> */; };
		96BED2FB2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFFA2E375E2400052454 /* <EMAIL> */; };
		96BED2FC2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00C2E375E2400052454 /* <EMAIL> */; };
		96BED2FD2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07D2E375E2400052454 /* <EMAIL> */; };
		96BED2FE2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02B2E375E2400052454 /* <EMAIL> */; };
		96BED2FF2E375E2400052454 /* supercall_ar.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0992E375E2400052454 /* supercall_ar.json */; };
		96BED3002E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF992E375E2400052454 /* <EMAIL> */; };
		96BED3012E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFD82E375E2400052454 /* <EMAIL> */; };
		96BED3022E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB02E375E2400052454 /* <EMAIL> */; };
		96BED3032E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0652E375E2400052454 /* <EMAIL> */; };
		96BED3042E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07B2E375E2400052454 /* <EMAIL> */; };
		96BED3052E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0112E375E2400052454 /* <EMAIL> */; };
		96BED3062E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB42E375E2400052454 /* <EMAIL> */; };
		96BED3072E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF652E375E2400052454 /* <EMAIL> */; };
		96BED3082E375E2400052454 /* game_loading.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0872E375E2400052454 /* game_loading.json */; };
		96BED3092E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0222E375E2400052454 /* <EMAIL> */; };
		96BED30A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED02F2E375E2400052454 /* <EMAIL> */; };
		96BED30B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0072E375E2400052454 /* <EMAIL> */; };
		96BED30C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA72E375E2400052454 /* <EMAIL> */; };
		96BED30E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFE82E375E2400052454 /* <EMAIL> */; };
		96BED30F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED06F2E375E2400052454 /* <EMAIL> */; };
		96BED3102E375E2400052454 /* recent_s.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BED07C2E375E2400052454 /* recent_s.png */; };
		96BED3112E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0662E375E2400052454 /* <EMAIL> */; };
		96BED3122E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0012E375E2400052454 /* <EMAIL> */; };
		96BED3132E375E2400052454 /* pickup_animation.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED08D2E375E2400052454 /* pickup_animation.json */; };
		96BED3142E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF682E375E2400052454 /* <EMAIL> */; };
		96BED3152E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0102E375E2400052454 /* <EMAIL> */; };
		96BED3162E375E2400052454 /* supercall_vi.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A62E375E2400052454 /* supercall_vi.json */; };
		96BED3172E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA32E375E2400052454 /* <EMAIL> */; };
		96BED3182E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA52E375E2400052454 /* <EMAIL> */; };
		96BED3192E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0282E375E2400052454 /* <EMAIL> */; };
		96BED31A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED05E2E375E2400052454 /* <EMAIL> */; };
		96BED31B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFAC2E375E2400052454 /* <EMAIL> */; };
		96BED31C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED00B2E375E2400052454 /* <EMAIL> */; };
		96BED31D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFDA2E375E2400052454 /* <EMAIL> */; };
		96BED31E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF6D2E375E2400052454 /* <EMAIL> */; };
		96BED31F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF782E375E2400052454 /* <EMAIL> */; };
		96BED3202E375E2400052454 /* asster_change.sh in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0AF2E375E2400052454 /* asster_change.sh */; };
		96BED3212E375E2400052454 /* sc_gift.svga in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0952E375E2400052454 /* sc_gift.svga */; };
		96BED3222E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8F2E375E2400052454 /* <EMAIL> */; };
		96BED3232E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFBF2E375E2400052454 /* <EMAIL> */; };
		96BED3242E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF742E375E2400052454 /* <EMAIL> */; };
		96BED3252E375E2400052454 /* supercall_pt.json in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0A22E375E2400052454 /* supercall_pt.json */; };
		96BED3262E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0232E375E2400052454 /* <EMAIL> */; };
		96BED3272E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0272E375E2400052454 /* <EMAIL> */; };
		96BED3282E375E2400052454 /* car_n.png in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB82E375E2400052454 /* car_n.png */; };
		96BED3292E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8D2E375E2400052454 /* <EMAIL> */; };
		96BED32A2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0122E375E2400052454 /* <EMAIL> */; };
		96BED32B2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF802E375E2400052454 /* <EMAIL> */; };
		96BED32C2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECF8E2E375E2400052454 /* <EMAIL> */; };
		96BED32D2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFB32E375E2400052454 /* <EMAIL> */; };
		96BED32E2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFF82E375E2400052454 /* <EMAIL> */; };
		96BED32F2E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BED0392E375E2400052454 /* <EMAIL> */; };
		96BED3302E375E2400052454 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 96BECFA92E375E2400052454 /* <EMAIL> */; };
		BEA4FAA02CF86AD70029C50B /* SCALoginViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = BEA4FA9F2CF86AD70029C50B /* SCALoginViewController.xib */; };
		BEA4FAA12CF86AD70029C50B /* SCALoginViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BEA4FA9E2CF86AD70029C50B /* SCALoginViewController.m */; };
		BED3F2AB2E38A70C001745A6 /* SCBlockUserDictHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = BED3F2AA2E38A70C001745A6 /* SCBlockUserDictHelper.m */; };
		C589E7FD01072543DFA77199 /* Pods_Supercall.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 973466FE4D2B720075835ED7 /* Pods_Supercall.framework */; };
		E605C7662CCC91C8005DC9B3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E6CC26952B3288450004D794 /* Assets.xcassets */; };
		E6CC268B2B32883E0004D794 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = E6CC268A2B32883E0004D794 /* AppDelegate.m */; };
		E6CC26912B32883E0004D794 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E6CC26902B32883E0004D794 /* ViewController.m */; };
		E6CC26942B32883E0004D794 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E6CC26922B32883E0004D794 /* Main.storyboard */; };
		E6CC26992B3288450004D794 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E6CC26972B3288450004D794 /* LaunchScreen.storyboard */; };
		E6CC269C2B3288450004D794 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = E6CC269B2B3288450004D794 /* main.m */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		96BECC592E375E2400052454 /* SCAPIServiceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAPIServiceManager.h; sourceTree = "<group>"; };
		96BECC5A2E375E2400052454 /* SCAPIServiceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAPIServiceManager.m; sourceTree = "<group>"; };
		96BECC5B2E375E2400052454 /* SCCategoryAPIManagerBanner.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerBanner.h; sourceTree = "<group>"; };
		96BECC5C2E375E2400052454 /* SCCategoryAPIManagerBanner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerBanner.m; sourceTree = "<group>"; };
		96BECC5E2E375E2400052454 /* SCOSSManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOSSManager.h; sourceTree = "<group>"; };
		96BECC5F2E375E2400052454 /* SCOSSManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOSSManager.m; sourceTree = "<group>"; };
		96BECC612E375E2400052454 /* SCAuthManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAuthManager.h; sourceTree = "<group>"; };
		96BECC622E375E2400052454 /* SCAuthManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAuthManager.m; sourceTree = "<group>"; };
		96BECC672E375E2400052454 /* SCBaseAppService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseAppService.h; sourceTree = "<group>"; };
		96BECC682E375E2400052454 /* SCBaseAppService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseAppService.m; sourceTree = "<group>"; };
		96BECC692E375E2400052454 /* SCBaseCollectionCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseCollectionCell.h; sourceTree = "<group>"; };
		96BECC6A2E375E2400052454 /* SCBaseCollectionCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseCollectionCell.m; sourceTree = "<group>"; };
		96BECC6B2E375E2400052454 /* SCBaseNavigationController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseNavigationController.h; sourceTree = "<group>"; };
		96BECC6C2E375E2400052454 /* SCBaseNavigationController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseNavigationController.m; sourceTree = "<group>"; };
		96BECC6D2E375E2400052454 /* SCBaseTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseTableViewCell.h; sourceTree = "<group>"; };
		96BECC6E2E375E2400052454 /* SCBaseTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseTableViewCell.m; sourceTree = "<group>"; };
		96BECC6F2E375E2400052454 /* SCBaseView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseView.h; sourceTree = "<group>"; };
		96BECC702E375E2400052454 /* SCBaseView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseView.m; sourceTree = "<group>"; };
		96BECC712E375E2400052454 /* SCBaseViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBaseViewController.h; sourceTree = "<group>"; };
		96BECC722E375E2400052454 /* SCBaseViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBaseViewController.m; sourceTree = "<group>"; };
		96BECC732E375E2400052454 /* SCNavigationBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNavigationBar.h; sourceTree = "<group>"; };
		96BECC742E375E2400052454 /* SCNavigationBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNavigationBar.m; sourceTree = "<group>"; };
		96BECC752E375E2400052454 /* SCSafetyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSafetyViewController.h; sourceTree = "<group>"; };
		96BECC762E375E2400052454 /* SCSafetyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSafetyViewController.m; sourceTree = "<group>"; };
		96BECC772E375E2400052454 /* SCThreadSafeDictionary.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThreadSafeDictionary.h; sourceTree = "<group>"; };
		96BECC782E375E2400052454 /* SCThreadSafeDictionary.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThreadSafeDictionary.m; sourceTree = "<group>"; };
		96BECC7B2E375E2400052454 /* SCActionSheetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCActionSheetViewController.h; sourceTree = "<group>"; };
		96BECC7C2E375E2400052454 /* SCActionSheetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCActionSheetViewController.m; sourceTree = "<group>"; };
		96BECC7D2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryActionSheetViewControllerPhotoPicker.h; sourceTree = "<group>"; };
		96BECC7E2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryActionSheetViewControllerPhotoPicker.m; sourceTree = "<group>"; };
		96BECC802E375E2400052454 /* SCAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAlertViewController.h; sourceTree = "<group>"; };
		96BECC812E375E2400052454 /* SCAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAlertViewController.m; sourceTree = "<group>"; };
		96BECC822E375E2400052454 /* SCCallExceptionalAlert.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallExceptionalAlert.h; sourceTree = "<group>"; };
		96BECC832E375E2400052454 /* SCCallExceptionalAlert.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallExceptionalAlert.m; sourceTree = "<group>"; };
		96BECC842E375E2400052454 /* SCNoEnoughCoinsAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNoEnoughCoinsAlertViewController.h; sourceTree = "<group>"; };
		96BECC852E375E2400052454 /* SCNoEnoughCoinsAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNoEnoughCoinsAlertViewController.m; sourceTree = "<group>"; };
		96BECC862E375E2400052454 /* SCStoreScoreAlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCStoreScoreAlertViewController.h; sourceTree = "<group>"; };
		96BECC872E375E2400052454 /* SCStoreScoreAlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCStoreScoreAlertViewController.m; sourceTree = "<group>"; };
		96BECC892E375E2400052454 /* SCAntiRecordView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAntiRecordView.h; sourceTree = "<group>"; };
		96BECC8A2E375E2400052454 /* SCAntiRecordView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAntiRecordView.m; sourceTree = "<group>"; };
		96BECC8C2E375E2400052454 /* SCCircularCountdownView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCircularCountdownView.h; sourceTree = "<group>"; };
		96BECC8D2E375E2400052454 /* SCCircularCountdownView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCircularCountdownView.m; sourceTree = "<group>"; };
		96BECC8F2E375E2400052454 /* SCCountryModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountryModel.h; sourceTree = "<group>"; };
		96BECC902E375E2400052454 /* SCCountryModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountryModel.m; sourceTree = "<group>"; };
		96BECC922E375E2400052454 /* SCCountryService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountryService.h; sourceTree = "<group>"; };
		96BECC932E375E2400052454 /* SCCountryService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountryService.m; sourceTree = "<group>"; };
		96BECC952E375E2400052454 /* SCCountryPickerViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountryPickerViewModel.h; sourceTree = "<group>"; };
		96BECC962E375E2400052454 /* SCCountryPickerViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountryPickerViewModel.m; sourceTree = "<group>"; };
		96BECC982E375E2400052454 /* SCCountryPickeTableViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountryPickeTableViewCell.h; sourceTree = "<group>"; };
		96BECC992E375E2400052454 /* SCCountryPickeTableViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountryPickeTableViewCell.m; sourceTree = "<group>"; };
		96BECC9A2E375E2400052454 /* SCCountrySelectCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountrySelectCell.h; sourceTree = "<group>"; };
		96BECC9B2E375E2400052454 /* SCCountrySelectCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountrySelectCell.m; sourceTree = "<group>"; };
		96BECC9E2E375E2400052454 /* SCCountryPickerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountryPickerViewController.h; sourceTree = "<group>"; };
		96BECC9F2E375E2400052454 /* SCCountryPickerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountryPickerViewController.m; sourceTree = "<group>"; };
		96BECCA02E375E2400052454 /* SCCountrySelectPopoupView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCountrySelectPopoupView.h; sourceTree = "<group>"; };
		96BECCA12E375E2400052454 /* SCCountrySelectPopoupView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCountrySelectPopoupView.m; sourceTree = "<group>"; };
		96BECCA42E375E2400052454 /* SCDatePicker.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDatePicker.h; sourceTree = "<group>"; };
		96BECCA52E375E2400052454 /* SCDatePicker.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDatePicker.m; sourceTree = "<group>"; };
		96BECCA72E375E2400052454 /* SCFontManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFontManager.h; sourceTree = "<group>"; };
		96BECCA82E375E2400052454 /* SCFontManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFontManager.m; sourceTree = "<group>"; };
		96BECCAA2E375E2400052454 /* SCLanguageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLanguageManager.h; sourceTree = "<group>"; };
		96BECCAB2E375E2400052454 /* SCLanguageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLanguageManager.m; sourceTree = "<group>"; };
		96BECCAD2E375E2400052454 /* SCLocalVideoPlayer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLocalVideoPlayer.h; sourceTree = "<group>"; };
		96BECCAE2E375E2400052454 /* SCLocalVideoPlayer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLocalVideoPlayer.m; sourceTree = "<group>"; };
		96BECCB02E375E2400052454 /* SCMessagePopupManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMessagePopupManager.h; sourceTree = "<group>"; };
		96BECCB12E375E2400052454 /* SCMessagePopupManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMessagePopupManager.m; sourceTree = "<group>"; };
		96BECCB32E375E2400052454 /* SCDisposeBag.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDisposeBag.h; sourceTree = "<group>"; };
		96BECCB42E375E2400052454 /* SCDisposeBag.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDisposeBag.m; sourceTree = "<group>"; };
		96BECCB52E375E2400052454 /* SCObservable.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCObservable.h; sourceTree = "<group>"; };
		96BECCB62E375E2400052454 /* SCObservable.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCObservable.m; sourceTree = "<group>"; };
		96BECCB82E375E2400052454 /* SCOnlineStatesService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOnlineStatesService.h; sourceTree = "<group>"; };
		96BECCB92E375E2400052454 /* SCOnlineStatesService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOnlineStatesService.m; sourceTree = "<group>"; };
		96BECCBA2E375E2400052454 /* SCOnlineStatusChangeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOnlineStatusChangeViewController.h; sourceTree = "<group>"; };
		96BECCBB2E375E2400052454 /* SCOnlineStatusChangeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOnlineStatusChangeViewController.m; sourceTree = "<group>"; };
		96BECCBC2E375E2400052454 /* SCOnlineStatusSubscribe.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOnlineStatusSubscribe.h; sourceTree = "<group>"; };
		96BECCBD2E375E2400052454 /* SCOnlineStatusSubscribe.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOnlineStatusSubscribe.m; sourceTree = "<group>"; };
		96BECCBE2E375E2400052454 /* SCOrderedDictionary.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOrderedDictionary.h; sourceTree = "<group>"; };
		96BECCBF2E375E2400052454 /* SCOrderedDictionary.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOrderedDictionary.m; sourceTree = "<group>"; };
		96BECCC12E375E2400052454 /* SCOrderResultModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOrderResultModel.h; sourceTree = "<group>"; };
		96BECCC22E375E2400052454 /* SCOrderResultModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOrderResultModel.m; sourceTree = "<group>"; };
		96BECCC32E375E2400052454 /* SCThirdPayCannelDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThirdPayCannelDisplayModel.h; sourceTree = "<group>"; };
		96BECCC42E375E2400052454 /* SCThirdPayCannelDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThirdPayCannelDisplayModel.m; sourceTree = "<group>"; };
		96BECCC62E375E2400052454 /* SCCategoryAPIManagerPay.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerPay.h; sourceTree = "<group>"; };
		96BECCC72E375E2400052454 /* SCCategoryAPIManagerPay.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerPay.m; sourceTree = "<group>"; };
		96BECCCA2E375E2400052454 /* SCThirdPayCannelCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThirdPayCannelCell.h; sourceTree = "<group>"; };
		96BECCCB2E375E2400052454 /* SCThirdPayCannelCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThirdPayCannelCell.m; sourceTree = "<group>"; };
		96BECCCD2E375E2400052454 /* SCThirdPayItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThirdPayItemView.h; sourceTree = "<group>"; };
		96BECCCE2E375E2400052454 /* SCThirdPayItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThirdPayItemView.m; sourceTree = "<group>"; };
		96BECCD02E375E2400052454 /* SCThirdPartyPayPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThirdPartyPayPopup.h; sourceTree = "<group>"; };
		96BECCD12E375E2400052454 /* SCThirdPartyPayPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThirdPartyPayPopup.m; sourceTree = "<group>"; };
		96BECCD22E375E2400052454 /* SCThirdPartyPayWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThirdPartyPayWebViewController.h; sourceTree = "<group>"; };
		96BECCD32E375E2400052454 /* SCThirdPartyPayWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThirdPartyPayWebViewController.m; sourceTree = "<group>"; };
		96BECCD62E375E2400052454 /* SCIAPManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCIAPManager.h; sourceTree = "<group>"; };
		96BECCD72E375E2400052454 /* SCIAPManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCIAPManager.m; sourceTree = "<group>"; };
		96BECCD82E375E2400052454 /* SCPayService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPayService.h; sourceTree = "<group>"; };
		96BECCD92E375E2400052454 /* SCPayService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPayService.m; sourceTree = "<group>"; };
		96BECCDB2E375E2400052454 /* SCPermissionManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPermissionManager.h; sourceTree = "<group>"; };
		96BECCDC2E375E2400052454 /* SCPermissionManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPermissionManager.m; sourceTree = "<group>"; };
		96BECCDE2E375E2400052454 /* SCPopupManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPopupManager.h; sourceTree = "<group>"; };
		96BECCDF2E375E2400052454 /* SCPopupManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPopupManager.m; sourceTree = "<group>"; };
		96BECCE12E375E2400052454 /* SCResourceManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCResourceManager.h; sourceTree = "<group>"; };
		96BECCE22E375E2400052454 /* SCResourceManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCResourceManager.m; sourceTree = "<group>"; };
		96BECCE42E375E2400052454 /* SCScrollView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCScrollView.h; sourceTree = "<group>"; };
		96BECCE52E375E2400052454 /* SCScrollView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCScrollView.m; sourceTree = "<group>"; };
		96BECCE72E375E2400052454 /* SCThrottle.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCThrottle.h; sourceTree = "<group>"; };
		96BECCE82E375E2400052454 /* SCThrottle.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCThrottle.m; sourceTree = "<group>"; };
		96BECCEA2E375E2400052454 /* SCTimer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTimer.h; sourceTree = "<group>"; };
		96BECCEB2E375E2400052454 /* SCTimer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTimer.m; sourceTree = "<group>"; };
		96BECCED2E375E2400052454 /* SCTranslationService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTranslationService.h; sourceTree = "<group>"; };
		96BECCEE2E375E2400052454 /* SCTranslationService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTranslationService.m; sourceTree = "<group>"; };
		96BECCF02E375E2400052454 /* AGEmojiKeyBoardView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AGEmojiKeyBoardView.h; sourceTree = "<group>"; };
		96BECCF12E375E2400052454 /* AGEmojiKeyBoardView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AGEmojiKeyBoardView.m; sourceTree = "<group>"; };
		96BECCF22E375E2400052454 /* AGEmojiPageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AGEmojiPageView.h; sourceTree = "<group>"; };
		96BECCF32E375E2400052454 /* AGEmojiPageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AGEmojiPageView.m; sourceTree = "<group>"; };
		96BECCF52E375E2400052454 /* UIScrollView+EmptyDataSet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+EmptyDataSet.h"; sourceTree = "<group>"; };
		96BECCF62E375E2400052454 /* UIScrollView+EmptyDataSet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+EmptyDataSet.m"; sourceTree = "<group>"; };
		96BECCF92E375E2400052454 /* MBProgressHUD.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MBProgressHUD.h; sourceTree = "<group>"; };
		96BECCFA2E375E2400052454 /* MBProgressHUD.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MBProgressHUD.m; sourceTree = "<group>"; };
		96BECCFD2E375E2400052454 /* SCWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCWebViewController.h; sourceTree = "<group>"; };
		96BECCFE2E375E2400052454 /* SCWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCWebViewController.m; sourceTree = "<group>"; };
		96BECD012E375E2400052454 /* SCAPIManage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAPIManage.h; sourceTree = "<group>"; };
		96BECD022E375E2400052454 /* SCAPIManage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAPIManage.m; sourceTree = "<group>"; };
		96BECD032E375E2400052454 /* SCConstant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCConstant.h; sourceTree = "<group>"; };
		96BECD042E375E2400052454 /* SCConstant.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCConstant.m; sourceTree = "<group>"; };
		96BECD052E375E2400052454 /* SCKey.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCKey.h; sourceTree = "<group>"; };
		96BECD062E375E2400052454 /* SCPayEntry.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPayEntry.h; sourceTree = "<group>"; };
		96BECD072E375E2400052454 /* SCPayEntry.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPayEntry.m; sourceTree = "<group>"; };
		96BECD092E375E2400052454 /* SCCategoryNSDateCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryNSDateCode.h; sourceTree = "<group>"; };
		96BECD0A2E375E2400052454 /* SCCategoryNSDateCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryNSDateCode.m; sourceTree = "<group>"; };
		96BECD0B2E375E2400052454 /* SCCategoryNSNumberCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryNSNumberCode.h; sourceTree = "<group>"; };
		96BECD0C2E375E2400052454 /* SCCategoryNSNumberCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryNSNumberCode.m; sourceTree = "<group>"; };
		96BECD0D2E375E2400052454 /* SCCategoryNSStringCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryNSStringCode.h; sourceTree = "<group>"; };
		96BECD0E2E375E2400052454 /* SCCategoryNSStringCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryNSStringCode.m; sourceTree = "<group>"; };
		96BECD0F2E375E2400052454 /* SCCategoryUIButtonCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIButtonCode.h; sourceTree = "<group>"; };
		96BECD102E375E2400052454 /* SCCategoryUIButtonCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIButtonCode.m; sourceTree = "<group>"; };
		96BECD112E375E2400052454 /* SCCategoryUICollectionViewCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUICollectionViewCode.h; sourceTree = "<group>"; };
		96BECD122E375E2400052454 /* SCCategoryUICollectionViewCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUICollectionViewCode.m; sourceTree = "<group>"; };
		96BECD132E375E2400052454 /* SCCategoryUIColorCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIColorCode.h; sourceTree = "<group>"; };
		96BECD142E375E2400052454 /* SCCategoryUIColorCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIColorCode.m; sourceTree = "<group>"; };
		96BECD152E375E2400052454 /* SCCategoryUIFontCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIFontCode.h; sourceTree = "<group>"; };
		96BECD162E375E2400052454 /* SCCategoryUIFontCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIFontCode.m; sourceTree = "<group>"; };
		96BECD172E375E2400052454 /* SCCategoryUIImageCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIImageCode.h; sourceTree = "<group>"; };
		96BECD182E375E2400052454 /* SCCategoryUIImageCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIImageCode.m; sourceTree = "<group>"; };
		96BECD192E375E2400052454 /* SCCategoryUIImageViewCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIImageViewCode.h; sourceTree = "<group>"; };
		96BECD1A2E375E2400052454 /* SCCategoryUIImageViewCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIImageViewCode.m; sourceTree = "<group>"; };
		96BECD1B2E375E2400052454 /* SCCategoryUILabelCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUILabelCode.h; sourceTree = "<group>"; };
		96BECD1C2E375E2400052454 /* SCCategoryUILabelCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUILabelCode.m; sourceTree = "<group>"; };
		96BECD1D2E375E2400052454 /* SCCategoryUITableViewCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUITableViewCode.h; sourceTree = "<group>"; };
		96BECD1E2E375E2400052454 /* SCCategoryUITableViewCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUITableViewCode.m; sourceTree = "<group>"; };
		96BECD1F2E375E2400052454 /* SCCategoryUIViewCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIViewCode.h; sourceTree = "<group>"; };
		96BECD202E375E2400052454 /* SCCategoryUIViewCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIViewCode.m; sourceTree = "<group>"; };
		96BECD212E375E2400052454 /* SCCategoryUIViewControllerCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUIViewControllerCode.h; sourceTree = "<group>"; };
		96BECD222E375E2400052454 /* SCCategoryUIViewControllerCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUIViewControllerCode.m; sourceTree = "<group>"; };
		96BECD232E375E2400052454 /* SCCategoryUserDefaultsCode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryUserDefaultsCode.h; sourceTree = "<group>"; };
		96BECD242E375E2400052454 /* SCCategoryUserDefaultsCode.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryUserDefaultsCode.m; sourceTree = "<group>"; };
		96BECD262E375E2400052454 /* SCNetworkCacheManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNetworkCacheManager.h; sourceTree = "<group>"; };
		96BECD272E375E2400052454 /* SCNetworkCacheManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNetworkCacheManager.m; sourceTree = "<group>"; };
		96BECD292E375E2400052454 /* SCNetworkConstant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNetworkConstant.h; sourceTree = "<group>"; };
		96BECD2A2E375E2400052454 /* SCNetworkConstant.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNetworkConstant.m; sourceTree = "<group>"; };
		96BECD2B2E375E2400052454 /* SCNetWorkResponseModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNetWorkResponseModel.h; sourceTree = "<group>"; };
		96BECD2C2E375E2400052454 /* SCNetWorkResponseModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNetWorkResponseModel.m; sourceTree = "<group>"; };
		96BECD2D2E375E2400052454 /* SCProgress.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCProgress.h; sourceTree = "<group>"; };
		96BECD2E2E375E2400052454 /* SCProgress.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCProgress.m; sourceTree = "<group>"; };
		96BECD2F2E375E2400052454 /* SCXErrorModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCXErrorModel.h; sourceTree = "<group>"; };
		96BECD302E375E2400052454 /* SCXErrorModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCXErrorModel.m; sourceTree = "<group>"; };
		96BECD322E375E2400052454 /* SCNetworkStatusManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNetworkStatusManager.h; sourceTree = "<group>"; };
		96BECD332E375E2400052454 /* SCNetworkStatusManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNetworkStatusManager.m; sourceTree = "<group>"; };
		96BECD352E375E2400052454 /* SCHTTPRequestSerializer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCHTTPRequestSerializer.h; sourceTree = "<group>"; };
		96BECD362E375E2400052454 /* SCHTTPRequestSerializer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCHTTPRequestSerializer.m; sourceTree = "<group>"; };
		96BECD372E375E2400052454 /* SCNetworkManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNetworkManager.h; sourceTree = "<group>"; };
		96BECD382E375E2400052454 /* SCNetworkManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNetworkManager.m; sourceTree = "<group>"; };
		96BECD3A2E375E2400052454 /* SCCodeBlockDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCodeBlockDefine.h; sourceTree = "<group>"; };
		96BECD3B2E375E2400052454 /* SCCodeLayoutDefine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCodeLayoutDefine.h; sourceTree = "<group>"; };
		96BECD3D2E375E2400052454 /* SCCategoryIM.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryIM.h; sourceTree = "<group>"; };
		96BECD3E2E375E2400052454 /* SCCategoryIM.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryIM.m; sourceTree = "<group>"; };
		96BECD402E375E2400052454 /* SCHyperLinkMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCHyperLinkMessage.h; sourceTree = "<group>"; };
		96BECD412E375E2400052454 /* SCHyperLinkMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCHyperLinkMessage.m; sourceTree = "<group>"; };
		96BECD422E375E2400052454 /* SCNoneFlagMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNoneFlagMessage.h; sourceTree = "<group>"; };
		96BECD432E375E2400052454 /* SCNoneFlagMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNoneFlagMessage.m; sourceTree = "<group>"; };
		96BECD442E375E2400052454 /* SCSingleJsonMessage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSingleJsonMessage.h; sourceTree = "<group>"; };
		96BECD452E375E2400052454 /* SCSingleJsonMessage.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSingleJsonMessage.m; sourceTree = "<group>"; };
		96BECD472E375E2400052454 /* SCIMService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCIMService.h; sourceTree = "<group>"; };
		96BECD482E375E2400052454 /* SCIMService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCIMService.m; sourceTree = "<group>"; };
		96BECD4A2E375E2400052454 /* SCCategorySocket.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategorySocket.h; sourceTree = "<group>"; };
		96BECD4B2E375E2400052454 /* SCCategorySocket.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategorySocket.m; sourceTree = "<group>"; };
		96BECD4E2E375E2400052454 /* SCSocketEventModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSocketEventModel.h; sourceTree = "<group>"; };
		96BECD4F2E375E2400052454 /* SCSocketEventModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSocketEventModel.m; sourceTree = "<group>"; };
		96BECD512E375E2400052454 /* SCSocketService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSocketService.h; sourceTree = "<group>"; };
		96BECD522E375E2400052454 /* SCSocketService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSocketService.m; sourceTree = "<group>"; };
		96BECD542E375E2400052454 /* SCAlignedCollectionViewFlowLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAlignedCollectionViewFlowLayout.h; sourceTree = "<group>"; };
		96BECD552E375E2400052454 /* SCAlignedCollectionViewFlowLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAlignedCollectionViewFlowLayout.m; sourceTree = "<group>"; };
		96BECD562E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCollectionViewLeftAlignedFlowLayout.h; sourceTree = "<group>"; };
		96BECD572E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCollectionViewLeftAlignedFlowLayout.m; sourceTree = "<group>"; };
		96BECD592E375E2400052454 /* SCCategoryColor.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryColor.h; sourceTree = "<group>"; };
		96BECD5A2E375E2400052454 /* SCCategoryColor.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryColor.m; sourceTree = "<group>"; };
		96BECD5B2E375E2400052454 /* SCGradientColors.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGradientColors.h; sourceTree = "<group>"; };
		96BECD5C2E375E2400052454 /* SCGradientColors.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGradientColors.m; sourceTree = "<group>"; };
		96BECD5E2E375E2400052454 /* MJRefreshAutoFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoFooter.h; sourceTree = "<group>"; };
		96BECD5F2E375E2400052454 /* MJRefreshAutoFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoFooter.m; sourceTree = "<group>"; };
		96BECD602E375E2400052454 /* MJRefreshComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshComponent.h; sourceTree = "<group>"; };
		96BECD612E375E2400052454 /* MJRefreshComponent.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshComponent.m; sourceTree = "<group>"; };
		96BECD622E375E2400052454 /* MJRefreshFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshFooter.h; sourceTree = "<group>"; };
		96BECD632E375E2400052454 /* MJRefreshFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshFooter.m; sourceTree = "<group>"; };
		96BECD642E375E2400052454 /* MJRefreshHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshHeader.h; sourceTree = "<group>"; };
		96BECD652E375E2400052454 /* MJRefreshHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshHeader.m; sourceTree = "<group>"; };
		96BECD662E375E2400052454 /* MJRefreshTrailer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshTrailer.h; sourceTree = "<group>"; };
		96BECD672E375E2400052454 /* MJRefreshTrailer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshTrailer.m; sourceTree = "<group>"; };
		96BECD692E375E2400052454 /* MJRefreshAutoNormalFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoNormalFooter.h; sourceTree = "<group>"; };
		96BECD6A2E375E2400052454 /* MJRefreshAutoNormalFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoNormalFooter.m; sourceTree = "<group>"; };
		96BECD6B2E375E2400052454 /* MJRefreshAutoStateFooter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshAutoStateFooter.h; sourceTree = "<group>"; };
		96BECD6C2E375E2400052454 /* MJRefreshAutoStateFooter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshAutoStateFooter.m; sourceTree = "<group>"; };
		96BECD6E2E375E2400052454 /* MJRefreshNormalHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshNormalHeader.h; sourceTree = "<group>"; };
		96BECD6F2E375E2400052454 /* MJRefreshNormalHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshNormalHeader.m; sourceTree = "<group>"; };
		96BECD702E375E2400052454 /* MJRefreshStateHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshStateHeader.h; sourceTree = "<group>"; };
		96BECD712E375E2400052454 /* MJRefreshStateHeader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshStateHeader.m; sourceTree = "<group>"; };
		96BECD742E375E2400052454 /* MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefresh.h; sourceTree = "<group>"; };
		96BECD752E375E2400052454 /* MJRefreshConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshConfig.h; sourceTree = "<group>"; };
		96BECD762E375E2400052454 /* MJRefreshConfig.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshConfig.m; sourceTree = "<group>"; };
		96BECD772E375E2400052454 /* MJRefreshConst.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MJRefreshConst.h; sourceTree = "<group>"; };
		96BECD782E375E2400052454 /* MJRefreshConst.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MJRefreshConst.m; sourceTree = "<group>"; };
		96BECD792E375E2400052454 /* UICollectionViewLayout+MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UICollectionViewLayout+MJRefresh.h"; sourceTree = "<group>"; };
		96BECD7A2E375E2400052454 /* UICollectionViewLayout+MJRefresh.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UICollectionViewLayout+MJRefresh.m"; sourceTree = "<group>"; };
		96BECD7B2E375E2400052454 /* UIScrollView+MJExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+MJExtension.h"; sourceTree = "<group>"; };
		96BECD7C2E375E2400052454 /* UIScrollView+MJExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+MJExtension.m"; sourceTree = "<group>"; };
		96BECD7D2E375E2400052454 /* UIScrollView+MJRefresh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+MJRefresh.h"; sourceTree = "<group>"; };
		96BECD7E2E375E2400052454 /* UIScrollView+MJRefresh.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+MJRefresh.m"; sourceTree = "<group>"; };
		96BECD7F2E375E2400052454 /* UIView+MJExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIView+MJExtension.h"; sourceTree = "<group>"; };
		96BECD802E375E2400052454 /* UIView+MJExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIView+MJExtension.m"; sourceTree = "<group>"; };
		96BECD822E375E2400052454 /* SCCategoryButton.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryButton.h; sourceTree = "<group>"; };
		96BECD832E375E2400052454 /* SCCategoryButton.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryButton.m; sourceTree = "<group>"; };
		96BECD842E375E2400052454 /* SCGradientLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGradientLabel.h; sourceTree = "<group>"; };
		96BECD852E375E2400052454 /* SCGradientLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGradientLabel.m; sourceTree = "<group>"; };
		96BECD872E375E2400052454 /* SCUIKit.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCUIKit.h; sourceTree = "<group>"; };
		96BECD892E375E2400052454 /* SCAppUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAppUtils.h; sourceTree = "<group>"; };
		96BECD8A2E375E2400052454 /* SCAppUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAppUtils.m; sourceTree = "<group>"; };
		96BECD8B2E375E2400052454 /* SCAVAudioSessionUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAVAudioSessionUtils.h; sourceTree = "<group>"; };
		96BECD8C2E375E2400052454 /* SCAVAudioSessionUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAVAudioSessionUtils.m; sourceTree = "<group>"; };
		96BECD8F2E375E2400052454 /* SCCryptoUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCryptoUtils.h; sourceTree = "<group>"; };
		96BECD902E375E2400052454 /* SCCryptoUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCryptoUtils.m; sourceTree = "<group>"; };
		96BECD912E375E2400052454 /* SCDataConverter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDataConverter.h; sourceTree = "<group>"; };
		96BECD922E375E2400052454 /* SCDataConverter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDataConverter.m; sourceTree = "<group>"; };
		96BECD932E375E2400052454 /* SCDictionaryHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDictionaryHelper.h; sourceTree = "<group>"; };
		96BECD942E375E2400052454 /* SCDictionaryHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDictionaryHelper.m; sourceTree = "<group>"; };
		96BECD952E375E2400052454 /* SCDictionaryKeys.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDictionaryKeys.h; sourceTree = "<group>"; };
		96BECD962E375E2400052454 /* SCDictionaryKeys.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDictionaryKeys.m; sourceTree = "<group>"; };
		96BECD972E375E2400052454 /* SCKeychainUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCKeychainUtils.h; sourceTree = "<group>"; };
		96BECD982E375E2400052454 /* SCKeychainUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCKeychainUtils.m; sourceTree = "<group>"; };
		96BECD992E375E2400052454 /* SCModelCompatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCModelCompatibility.h; sourceTree = "<group>"; };
		96BECD9A2E375E2400052454 /* SCModelCompatibility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCModelCompatibility.m; sourceTree = "<group>"; };
		96BECD9B2E375E2400052454 /* SCSafetyUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSafetyUtils.h; sourceTree = "<group>"; };
		96BECD9C2E375E2400052454 /* SCSafetyUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSafetyUtils.m; sourceTree = "<group>"; };
		96BECD9D2E375E2400052454 /* SCTrackingUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTrackingUtils.h; sourceTree = "<group>"; };
		96BECD9E2E375E2400052454 /* SCTrackingUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTrackingUtils.m; sourceTree = "<group>"; };
		96BECDA52E375E2400052454 /* SCUserBoolChangeModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCUserBoolChangeModel.h; sourceTree = "<group>"; };
		96BECDA62E375E2400052454 /* SCUserBoolChangeModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCUserBoolChangeModel.m; sourceTree = "<group>"; };
		96BECDA82E375E2400052454 /* SCFollowListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFollowListCell.h; sourceTree = "<group>"; };
		96BECDA92E375E2400052454 /* SCFollowListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFollowListCell.m; sourceTree = "<group>"; };
		96BECDAB2E375E2400052454 /* SCFollowListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFollowListViewController.h; sourceTree = "<group>"; };
		96BECDAC2E375E2400052454 /* SCFollowListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFollowListViewController.m; sourceTree = "<group>"; };
		96BECDAF2E375E2400052454 /* SCActionService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCActionService.h; sourceTree = "<group>"; };
		96BECDB02E375E2400052454 /* SCActionService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCActionService.m; sourceTree = "<group>"; };
		96BECDB22E375E2400052454 /* SCAnchorActionSheetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorActionSheetViewController.h; sourceTree = "<group>"; };
		96BECDB32E375E2400052454 /* SCAnchorActionSheetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorActionSheetViewController.m; sourceTree = "<group>"; };
		96BECDB42E375E2400052454 /* SCBlockListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBlockListViewController.h; sourceTree = "<group>"; };
		96BECDB52E375E2400052454 /* SCBlockListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBlockListViewController.m; sourceTree = "<group>"; };
		96BECDB82E375E2400052454 /* SCAnchorInfoViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorInfoViewModel.h; sourceTree = "<group>"; };
		96BECDB92E375E2400052454 /* SCAnchorInfoViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorInfoViewModel.m; sourceTree = "<group>"; };
		96BECDBB2E375E2400052454 /* SCGiftNumCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftNumCell.h; sourceTree = "<group>"; };
		96BECDBC2E375E2400052454 /* SCGiftNumCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftNumCell.m; sourceTree = "<group>"; };
		96BECDBD2E375E2400052454 /* SCSmallVideoCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSmallVideoCell.h; sourceTree = "<group>"; };
		96BECDBE2E375E2400052454 /* SCSmallVideoCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSmallVideoCell.m; sourceTree = "<group>"; };
		96BECDBF2E375E2400052454 /* SCTagCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTagCell.h; sourceTree = "<group>"; };
		96BECDC02E375E2400052454 /* SCTagCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTagCell.m; sourceTree = "<group>"; };
		96BECDC22E375E2400052454 /* SCAnchorInfoBottomView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorInfoBottomView.h; sourceTree = "<group>"; };
		96BECDC32E375E2400052454 /* SCAnchorInfoBottomView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorInfoBottomView.m; sourceTree = "<group>"; };
		96BECDC42E375E2400052454 /* SCAnchorInfoHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorInfoHeaderView.h; sourceTree = "<group>"; };
		96BECDC52E375E2400052454 /* SCAnchorInfoHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorInfoHeaderView.m; sourceTree = "<group>"; };
		96BECDC62E375E2400052454 /* SCCustomPageControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCustomPageControl.h; sourceTree = "<group>"; };
		96BECDC72E375E2400052454 /* SCCustomPageControl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCustomPageControl.m; sourceTree = "<group>"; };
		96BECDC82E375E2400052454 /* SCGiftListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftListView.h; sourceTree = "<group>"; };
		96BECDC92E375E2400052454 /* SCGiftListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftListView.m; sourceTree = "<group>"; };
		96BECDCA2E375E2400052454 /* SCImageBrowserView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCImageBrowserView.h; sourceTree = "<group>"; };
		96BECDCB2E375E2400052454 /* SCImageBrowserView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCImageBrowserView.m; sourceTree = "<group>"; };
		96BECDCC2E375E2400052454 /* SCLabelListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLabelListView.h; sourceTree = "<group>"; };
		96BECDCD2E375E2400052454 /* SCLabelListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLabelListView.m; sourceTree = "<group>"; };
		96BECDCE2E375E2400052454 /* SCVideoHorizontalListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoHorizontalListView.h; sourceTree = "<group>"; };
		96BECDCF2E375E2400052454 /* SCVideoHorizontalListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoHorizontalListView.m; sourceTree = "<group>"; };
		96BECDD12E375E2400052454 /* SCAnchorInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorInfoViewController.h; sourceTree = "<group>"; };
		96BECDD22E375E2400052454 /* SCAnchorInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorInfoViewController.m; sourceTree = "<group>"; };
		96BECDD52E375E2400052454 /* SCUserRankModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCUserRankModel.h; sourceTree = "<group>"; };
		96BECDD62E375E2400052454 /* SCUserRankModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCUserRankModel.m; sourceTree = "<group>"; };
		96BECDD82E375E2400052454 /* SCAnchorService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorService.h; sourceTree = "<group>"; };
		96BECDD92E375E2400052454 /* SCAnchorService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorService.m; sourceTree = "<group>"; };
		96BECDDB2E375E2400052454 /* SCAnchorViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorViewModel.h; sourceTree = "<group>"; };
		96BECDDC2E375E2400052454 /* SCAnchorViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorViewModel.m; sourceTree = "<group>"; };
		96BECDDE2E375E2400052454 /* SCAnchorCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorCell.h; sourceTree = "<group>"; };
		96BECDDF2E375E2400052454 /* SCAnchorCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorCell.m; sourceTree = "<group>"; };
		96BECDE12E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorSubJXCategoryTitleCell.h; sourceTree = "<group>"; };
		96BECDE22E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorSubJXCategoryTitleCell.m; sourceTree = "<group>"; };
		96BECDE32E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorSubTitleJXCategoryTitleCellModel.h; sourceTree = "<group>"; };
		96BECDE42E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorSubTitleJXCategoryTitleCellModel.m; sourceTree = "<group>"; };
		96BECDE52E375E2400052454 /* SCAnchorSubTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorSubTitleView.h; sourceTree = "<group>"; };
		96BECDE62E375E2400052454 /* SCAnchorSubTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorSubTitleView.m; sourceTree = "<group>"; };
		96BECDE82E375E2400052454 /* SCAnchorCollectionView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorCollectionView.h; sourceTree = "<group>"; };
		96BECDE92E375E2400052454 /* SCAnchorCollectionView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorCollectionView.m; sourceTree = "<group>"; };
		96BECDEA2E375E2400052454 /* SCGradientLineView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGradientLineView.h; sourceTree = "<group>"; };
		96BECDEB2E375E2400052454 /* SCGradientLineView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGradientLineView.m; sourceTree = "<group>"; };
		96BECDEC2E375E2400052454 /* SCOnlineStatusView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCOnlineStatusView.h; sourceTree = "<group>"; };
		96BECDED2E375E2400052454 /* SCOnlineStatusView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCOnlineStatusView.m; sourceTree = "<group>"; };
		96BECDEE2E375E2400052454 /* SCRoundedLabelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRoundedLabelView.h; sourceTree = "<group>"; };
		96BECDEF2E375E2400052454 /* SCRoundedLabelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRoundedLabelView.m; sourceTree = "<group>"; };
		96BECDF02E375E2400052454 /* SCWaterFallFlowLayout.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCWaterFallFlowLayout.h; sourceTree = "<group>"; };
		96BECDF12E375E2400052454 /* SCWaterFallFlowLayout.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCWaterFallFlowLayout.m; sourceTree = "<group>"; };
		96BECDF32E375E2400052454 /* SCAnchorSubTagViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorSubTagViewController.h; sourceTree = "<group>"; };
		96BECDF42E375E2400052454 /* SCAnchorSubTagViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorSubTagViewController.m; sourceTree = "<group>"; };
		96BECDF52E375E2400052454 /* SCAnchorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorViewController.h; sourceTree = "<group>"; };
		96BECDF62E375E2400052454 /* SCAnchorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorViewController.m; sourceTree = "<group>"; };
		96BECDF92E375E2400052454 /* SCMediaItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMediaItemView.h; sourceTree = "<group>"; };
		96BECDFA2E375E2400052454 /* SCMediaItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMediaItemView.m; sourceTree = "<group>"; };
		96BECDFB2E375E2400052454 /* SCUserItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCUserItemView.h; sourceTree = "<group>"; };
		96BECDFC2E375E2400052454 /* SCUserItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCUserItemView.m; sourceTree = "<group>"; };
		96BECDFE2E375E2400052454 /* SCFullScreenPreviewMediaViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFullScreenPreviewMediaViewController.h; sourceTree = "<group>"; };
		96BECDFF2E375E2400052454 /* SCFullScreenPreviewMediaViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFullScreenPreviewMediaViewController.m; sourceTree = "<group>"; };
		96BECE022E375E2400052454 /* SCRankingCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingCell.h; sourceTree = "<group>"; };
		96BECE032E375E2400052454 /* SCRankingCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingCell.m; sourceTree = "<group>"; };
		96BECE052E375E2400052454 /* SCRankingTitleView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingTitleView.h; sourceTree = "<group>"; };
		96BECE062E375E2400052454 /* SCRankingTitleView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingTitleView.m; sourceTree = "<group>"; };
		96BECE082E375E2400052454 /* SCRankingHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingHeaderView.h; sourceTree = "<group>"; };
		96BECE092E375E2400052454 /* SCRankingHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingHeaderView.m; sourceTree = "<group>"; };
		96BECE0A2E375E2400052454 /* SCRankingItemView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingItemView.h; sourceTree = "<group>"; };
		96BECE0B2E375E2400052454 /* SCRankingItemView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingItemView.m; sourceTree = "<group>"; };
		96BECE0C2E375E2400052454 /* SCRankingListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingListView.h; sourceTree = "<group>"; };
		96BECE0D2E375E2400052454 /* SCRankingListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingListView.m; sourceTree = "<group>"; };
		96BECE102E375E2400052454 /* SCRankingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRankingViewController.h; sourceTree = "<group>"; };
		96BECE112E375E2400052454 /* SCRankingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRankingViewController.m; sourceTree = "<group>"; };
		96BECE142E375E2400052454 /* SCCallSessionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallSessionModel.h; sourceTree = "<group>"; };
		96BECE152E375E2400052454 /* SCCallSessionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallSessionModel.m; sourceTree = "<group>"; };
		96BECE162E375E2400052454 /* SCCallSessionStatus.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallSessionStatus.h; sourceTree = "<group>"; };
		96BECE172E375E2400052454 /* SCRearCameraConfigModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRearCameraConfigModel.h; sourceTree = "<group>"; };
		96BECE182E375E2400052454 /* SCRearCameraConfigModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRearCameraConfigModel.m; sourceTree = "<group>"; };
		96BECE192E375E2400052454 /* SCVideoCallChatMessageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallChatMessageModel.h; sourceTree = "<group>"; };
		96BECE1A2E375E2400052454 /* SCVideoCallChatMessageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoCallChatMessageModel.m; sourceTree = "<group>"; };
		96BECE1C2E375E2400052454 /* SCCallDBManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallDBManager.h; sourceTree = "<group>"; };
		96BECE1D2E375E2400052454 /* SCCallDBManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallDBManager.m; sourceTree = "<group>"; };
		96BECE1E2E375E2400052454 /* SCDBActionModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDBActionModel.h; sourceTree = "<group>"; };
		96BECE1F2E375E2400052454 /* SCDBActionModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDBActionModel.m; sourceTree = "<group>"; };
		96BECE202E375E2400052454 /* SCNativeSQLiteDatabase.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNativeSQLiteDatabase.h; sourceTree = "<group>"; };
		96BECE212E375E2400052454 /* SCNativeSQLiteDatabase.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNativeSQLiteDatabase.m; sourceTree = "<group>"; };
		96BECE222E375E2400052454 /* SCNativeSQLiteResultSet.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNativeSQLiteResultSet.h; sourceTree = "<group>"; };
		96BECE232E375E2400052454 /* SCNativeSQLiteResultSet.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNativeSQLiteResultSet.m; sourceTree = "<group>"; };
		96BECE252E375E2400052454 /* SCCategoryAPIManagerCall.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerCall.h; sourceTree = "<group>"; };
		96BECE262E375E2400052454 /* SCCategoryAPIManagerCall.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerCall.m; sourceTree = "<group>"; };
		96BECE282E375E2400052454 /* SCCallService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallService.h; sourceTree = "<group>"; };
		96BECE292E375E2400052454 /* SCCallService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallService.m; sourceTree = "<group>"; };
		96BECE2A2E375E2400052454 /* SCCallServiceDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallServiceDelegate.h; sourceTree = "<group>"; };
		96BECE2B2E375E2400052454 /* SCVideoCallDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallDelegate.h; sourceTree = "<group>"; };
		96BECE2D2E375E2400052454 /* SCCallHistoryCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallHistoryCell.h; sourceTree = "<group>"; };
		96BECE2E2E375E2400052454 /* SCCallHistoryCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallHistoryCell.m; sourceTree = "<group>"; };
		96BECE2F2E375E2400052454 /* SCTryAnchorAvatarCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTryAnchorAvatarCell.h; sourceTree = "<group>"; };
		96BECE302E375E2400052454 /* SCTryAnchorAvatarCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTryAnchorAvatarCell.m; sourceTree = "<group>"; };
		96BECE312E375E2400052454 /* SCVideoCallChatCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallChatCell.h; sourceTree = "<group>"; };
		96BECE322E375E2400052454 /* SCVideoCallChatCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoCallChatCell.m; sourceTree = "<group>"; };
		96BECE342E375E2400052454 /* SCAskGiftTipView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAskGiftTipView.h; sourceTree = "<group>"; };
		96BECE352E375E2400052454 /* SCAskGiftTipView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAskGiftTipView.m; sourceTree = "<group>"; };
		96BECE362E375E2400052454 /* SCCallDisablePrompt.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallDisablePrompt.h; sourceTree = "<group>"; };
		96BECE372E375E2400052454 /* SCCallDisablePrompt.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallDisablePrompt.m; sourceTree = "<group>"; };
		96BECE382E375E2400052454 /* SCCallingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallingView.h; sourceTree = "<group>"; };
		96BECE392E375E2400052454 /* SCCallingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallingView.m; sourceTree = "<group>"; };
		96BECE3A2E375E2400052454 /* SCFreeTimeCountdownView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFreeTimeCountdownView.h; sourceTree = "<group>"; };
		96BECE3B2E375E2400052454 /* SCFreeTimeCountdownView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFreeTimeCountdownView.m; sourceTree = "<group>"; };
		96BECE3C2E375E2400052454 /* SCGiftTipView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftTipView.h; sourceTree = "<group>"; };
		96BECE3D2E375E2400052454 /* SCGiftTipView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftTipView.m; sourceTree = "<group>"; };
		96BECE3E2E375E2400052454 /* SCJoinChannelProgressView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCJoinChannelProgressView.h; sourceTree = "<group>"; };
		96BECE3F2E375E2400052454 /* SCJoinChannelProgressView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCJoinChannelProgressView.m; sourceTree = "<group>"; };
		96BECE402E375E2400052454 /* SCVideoCallChatInputView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallChatInputView.h; sourceTree = "<group>"; };
		96BECE412E375E2400052454 /* SCVideoCallChatInputView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoCallChatInputView.m; sourceTree = "<group>"; };
		96BECE422E375E2400052454 /* SCVideoCallRemainTimeCountdownView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallRemainTimeCountdownView.h; sourceTree = "<group>"; };
		96BECE432E375E2400052454 /* SCVideoCallRemainTimeCountdownView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoCallRemainTimeCountdownView.m; sourceTree = "<group>"; };
		96BECE452E375E2400052454 /* SCAnchorEvaluateViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorEvaluateViewController.h; sourceTree = "<group>"; };
		96BECE462E375E2400052454 /* SCAnchorEvaluateViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorEvaluateViewController.m; sourceTree = "<group>"; };
		96BECE472E375E2400052454 /* SCCallHangupPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallHangupPopup.h; sourceTree = "<group>"; };
		96BECE482E375E2400052454 /* SCCallHangupPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallHangupPopup.m; sourceTree = "<group>"; };
		96BECE492E375E2400052454 /* SCCallHistoryListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallHistoryListViewController.h; sourceTree = "<group>"; };
		96BECE4A2E375E2400052454 /* SCCallHistoryListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallHistoryListViewController.m; sourceTree = "<group>"; };
		96BECE4B2E375E2400052454 /* SCCallNotificationPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallNotificationPopup.h; sourceTree = "<group>"; };
		96BECE4C2E375E2400052454 /* SCCallNotificationPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallNotificationPopup.m; sourceTree = "<group>"; };
		96BECE4D2E375E2400052454 /* SCCallViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCallViewController.h; sourceTree = "<group>"; };
		96BECE4E2E375E2400052454 /* SCCallViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCallViewController.m; sourceTree = "<group>"; };
		96BECE4F2E375E2400052454 /* SCVideoCallViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVideoCallViewController.h; sourceTree = "<group>"; };
		96BECE502E375E2400052454 /* SCVideoCallViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVideoCallViewController.m; sourceTree = "<group>"; };
		96BECE532E375E2400052454 /* SCCategoryAPIManagerDiscover.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerDiscover.h; sourceTree = "<group>"; };
		96BECE542E375E2400052454 /* SCCategoryAPIManagerDiscover.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerDiscover.m; sourceTree = "<group>"; };
		96BECE572E375E2400052454 /* SCDiscoverBannerCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDiscoverBannerCell.h; sourceTree = "<group>"; };
		96BECE582E375E2400052454 /* SCDiscoverBannerCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDiscoverBannerCell.m; sourceTree = "<group>"; };
		96BECE5A2E375E2400052454 /* SCDiscoverViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCDiscoverViewController.h; sourceTree = "<group>"; };
		96BECE5B2E375E2400052454 /* SCDiscoverViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCDiscoverViewController.m; sourceTree = "<group>"; };
		96BECE5E2E375E2400052454 /* SCCategoryAPIManagerFlashChat.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerFlashChat.h; sourceTree = "<group>"; };
		96BECE5F2E375E2400052454 /* SCCategoryAPIManagerFlashChat.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerFlashChat.m; sourceTree = "<group>"; };
		96BECE642E375E2400052454 /* SCFlashMatchSuccessPopupView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFlashMatchSuccessPopupView.h; sourceTree = "<group>"; };
		96BECE652E375E2400052454 /* SCFlashMatchSuccessPopupView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFlashMatchSuccessPopupView.m; sourceTree = "<group>"; };
		96BECE662E375E2400052454 /* SCFlashRouletteView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFlashRouletteView.h; sourceTree = "<group>"; };
		96BECE672E375E2400052454 /* SCFlashRouletteView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFlashRouletteView.m; sourceTree = "<group>"; };
		96BECE692E375E2400052454 /* SCFlashChatMatchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFlashChatMatchViewController.h; sourceTree = "<group>"; };
		96BECE6A2E375E2400052454 /* SCFlashChatMatchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFlashChatMatchViewController.m; sourceTree = "<group>"; };
		96BECE6B2E375E2400052454 /* SCFlashChatViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFlashChatViewController.h; sourceTree = "<group>"; };
		96BECE6C2E375E2400052454 /* SCFlashChatViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFlashChatViewController.m; sourceTree = "<group>"; };
		96BECE6F2E375E2400052454 /* SCGameWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGameWebViewController.h; sourceTree = "<group>"; };
		96BECE702E375E2400052454 /* SCGameWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGameWebViewController.m; sourceTree = "<group>"; };
		96BECE722E375E2400052454 /* SCGiftSendNumModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftSendNumModel.h; sourceTree = "<group>"; };
		96BECE732E375E2400052454 /* SCGiftSendNumModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftSendNumModel.m; sourceTree = "<group>"; };
		96BECE742E375E2400052454 /* SCGiftSendNumTipDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftSendNumTipDisplayModel.h; sourceTree = "<group>"; };
		96BECE752E375E2400052454 /* SCGiftSendNumTipDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftSendNumTipDisplayModel.m; sourceTree = "<group>"; };
		96BECE772E375E2400052454 /* SCCategoryAPIManagerGift.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerGift.h; sourceTree = "<group>"; };
		96BECE782E375E2400052454 /* SCCategoryAPIManagerGift.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerGift.m; sourceTree = "<group>"; };
		96BECE7A2E375E2400052454 /* SCGiftService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftService.h; sourceTree = "<group>"; };
		96BECE7B2E375E2400052454 /* SCGiftService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftService.m; sourceTree = "<group>"; };
		96BECE7D2E375E2400052454 /* SCGiftCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftCell.h; sourceTree = "<group>"; };
		96BECE7E2E375E2400052454 /* SCGiftCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftCell.m; sourceTree = "<group>"; };
		96BECE802E375E2400052454 /* SCGiftAnimPlayView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftAnimPlayView.h; sourceTree = "<group>"; };
		96BECE812E375E2400052454 /* SCGiftAnimPlayView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftAnimPlayView.m; sourceTree = "<group>"; };
		96BECE832E375E2400052454 /* SCGiftPopupViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftPopupViewController.h; sourceTree = "<group>"; };
		96BECE842E375E2400052454 /* SCGiftPopupViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftPopupViewController.m; sourceTree = "<group>"; };
		96BECE872E375E2400052454 /* SCBannerView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBannerView.h; sourceTree = "<group>"; };
		96BECE882E375E2400052454 /* SCBannerView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBannerView.m; sourceTree = "<group>"; };
		96BECE8A2E375E2400052454 /* SCBannerService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBannerService.h; sourceTree = "<group>"; };
		96BECE8B2E375E2400052454 /* SCBannerService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBannerService.m; sourceTree = "<group>"; };
		96BECE8D2E375E2400052454 /* SCStrongGuidePopoUp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCStrongGuidePopoUp.h; sourceTree = "<group>"; };
		96BECE8E2E375E2400052454 /* SCStrongGuidePopoUp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCStrongGuidePopoUp.m; sourceTree = "<group>"; };
		96BECE922E375E2400052454 /* SCHomeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCHomeViewController.h; sourceTree = "<group>"; };
		96BECE932E375E2400052454 /* SCHomeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCHomeViewController.m; sourceTree = "<group>"; };
		96BECE962E375E2400052454 /* SCLaunchVCViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLaunchVCViewController.h; sourceTree = "<group>"; };
		96BECE972E375E2400052454 /* SCLaunchVCViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLaunchVCViewController.m; sourceTree = "<group>"; };
		96BECE992E375E2400052454 /* SCLoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLoginViewController.h; sourceTree = "<group>"; };
		96BECE9A2E375E2400052454 /* SCLoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLoginViewController.m; sourceTree = "<group>"; };
		96BECE9D2E375E2400052454 /* SCIMUIConfig.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCIMUIConfig.h; sourceTree = "<group>"; };
		96BECE9F2E375E2400052454 /* SCVoicePlayerManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVoicePlayerManager.h; sourceTree = "<group>"; };
		96BECEA02E375E2400052454 /* SCVoicePlayerManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVoicePlayerManager.m; sourceTree = "<group>"; };
		96BECEA12E375E2400052454 /* SCVoiceRecorderManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVoiceRecorderManager.h; sourceTree = "<group>"; };
		96BECEA22E375E2400052454 /* SCVoiceRecorderManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVoiceRecorderManager.m; sourceTree = "<group>"; };
		96BECEA42E375E2400052454 /* SCConversationDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCConversationDisplayModel.h; sourceTree = "<group>"; };
		96BECEA52E375E2400052454 /* SCConversationDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCConversationDisplayModel.m; sourceTree = "<group>"; };
		96BECEA62E375E2400052454 /* SCMessageDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMessageDisplayModel.h; sourceTree = "<group>"; };
		96BECEA72E375E2400052454 /* SCMessageDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMessageDisplayModel.m; sourceTree = "<group>"; };
		96BECEA82E375E2400052454 /* SCMessageTitleItemDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMessageTitleItemDisplayModel.h; sourceTree = "<group>"; };
		96BECEA92E375E2400052454 /* SCMessageTitleItemDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMessageTitleItemDisplayModel.m; sourceTree = "<group>"; };
		96BECEAA2E375E2400052454 /* SCNativeConversationModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNativeConversationModel.h; sourceTree = "<group>"; };
		96BECEAB2E375E2400052454 /* SCNativeConversationModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNativeConversationModel.m; sourceTree = "<group>"; };
		96BECEAC2E375E2400052454 /* SCNativeMessageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNativeMessageModel.h; sourceTree = "<group>"; };
		96BECEAD2E375E2400052454 /* SCNativeMessageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNativeMessageModel.m; sourceTree = "<group>"; };
		96BECEAE2E375E2400052454 /* SCRechargeCardMessageContentModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRechargeCardMessageContentModel.h; sourceTree = "<group>"; };
		96BECEAF2E375E2400052454 /* SCRechargeCardMessageContentModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRechargeCardMessageContentModel.m; sourceTree = "<group>"; };
		96BECEB12E375E2400052454 /* SCConversationListCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCConversationListCell.h; sourceTree = "<group>"; };
		96BECEB22E375E2400052454 /* SCConversationListCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCConversationListCell.m; sourceTree = "<group>"; };
		96BECEB32E375E2400052454 /* SCFileMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFileMessageCell.h; sourceTree = "<group>"; };
		96BECEB42E375E2400052454 /* SCFileMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFileMessageCell.m; sourceTree = "<group>"; };
		96BECEB52E375E2400052454 /* SCGiftMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCGiftMessageCell.h; sourceTree = "<group>"; };
		96BECEB62E375E2400052454 /* SCGiftMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCGiftMessageCell.m; sourceTree = "<group>"; };
		96BECEB72E375E2400052454 /* SCImageMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCImageMessageCell.h; sourceTree = "<group>"; };
		96BECEB82E375E2400052454 /* SCImageMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCImageMessageCell.m; sourceTree = "<group>"; };
		96BECEB92E375E2400052454 /* SCRechargeCardMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRechargeCardMessageCell.h; sourceTree = "<group>"; };
		96BECEBA2E375E2400052454 /* SCRechargeCardMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRechargeCardMessageCell.m; sourceTree = "<group>"; };
		96BECEBB2E375E2400052454 /* SCTextMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCTextMessageCell.h; sourceTree = "<group>"; };
		96BECEBC2E375E2400052454 /* SCTextMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCTextMessageCell.m; sourceTree = "<group>"; };
		96BECEBD2E375E2400052454 /* SCVoiceMessageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVoiceMessageCell.h; sourceTree = "<group>"; };
		96BECEBE2E375E2400052454 /* SCVoiceMessageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVoiceMessageCell.m; sourceTree = "<group>"; };
		96BECEC02E375E2400052454 /* SCMorePanelView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMorePanelView.h; sourceTree = "<group>"; };
		96BECEC12E375E2400052454 /* SCMorePanelView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMorePanelView.m; sourceTree = "<group>"; };
		96BECEC32E375E2400052454 /* SCUserBaseInfoView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCUserBaseInfoView.h; sourceTree = "<group>"; };
		96BECEC42E375E2400052454 /* SCUserBaseInfoView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCUserBaseInfoView.m; sourceTree = "<group>"; };
		96BECEC52E375E2400052454 /* SCVoiceRecordingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCVoiceRecordingView.h; sourceTree = "<group>"; };
		96BECEC62E375E2400052454 /* SCVoiceRecordingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCVoiceRecordingView.m; sourceTree = "<group>"; };
		96BECEC82E375E2400052454 /* SCConversationInfoViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCConversationInfoViewController.h; sourceTree = "<group>"; };
		96BECEC92E375E2400052454 /* SCConversationInfoViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCConversationInfoViewController.m; sourceTree = "<group>"; };
		96BECECA2E375E2400052454 /* SCConversationListViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCConversationListViewController.h; sourceTree = "<group>"; };
		96BECECB2E375E2400052454 /* SCConversationListViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCConversationListViewController.m; sourceTree = "<group>"; };
		96BECECC2E375E2400052454 /* SCMessageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMessageViewController.h; sourceTree = "<group>"; };
		96BECECD2E375E2400052454 /* SCMessageViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMessageViewController.m; sourceTree = "<group>"; };
		96BECED42E375E2400052454 /* SCRobotCustomerDictionaryHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRobotCustomerDictionaryHelper.h; sourceTree = "<group>"; };
		96BECED52E375E2400052454 /* SCRobotCustomerDictionaryHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRobotCustomerDictionaryHelper.m; sourceTree = "<group>"; };
		96BECED72E375E2400052454 /* SCRobotCustomerAnswerCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRobotCustomerAnswerCell.h; sourceTree = "<group>"; };
		96BECED82E375E2400052454 /* SCRobotCustomerAnswerCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRobotCustomerAnswerCell.m; sourceTree = "<group>"; };
		96BECED92E375E2400052454 /* SCRobotCustomerQuestionCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRobotCustomerQuestionCell.h; sourceTree = "<group>"; };
		96BECEDA2E375E2400052454 /* SCRobotCustomerQuestionCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRobotCustomerQuestionCell.m; sourceTree = "<group>"; };
		96BECEDB2E375E2400052454 /* SCRobotCustomerQuestionSetCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRobotCustomerQuestionSetCell.h; sourceTree = "<group>"; };
		96BECEDC2E375E2400052454 /* SCRobotCustomerQuestionSetCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRobotCustomerQuestionSetCell.m; sourceTree = "<group>"; };
		96BECEDE2E375E2400052454 /* SCRobotCustomerServiceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCRobotCustomerServiceViewController.h; sourceTree = "<group>"; };
		96BECEDF2E375E2400052454 /* SCRobotCustomerServiceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCRobotCustomerServiceViewController.m; sourceTree = "<group>"; };
		96BECEE32E375E2400052454 /* SCAboutHederView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAboutHederView.h; sourceTree = "<group>"; };
		96BECEE42E375E2400052454 /* SCAboutHederView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAboutHederView.m; sourceTree = "<group>"; };
		96BECEE62E375E2400052454 /* SCAboutViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAboutViewController.h; sourceTree = "<group>"; };
		96BECEE72E375E2400052454 /* SCAboutViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAboutViewController.m; sourceTree = "<group>"; };
		96BECEEA2E375E2400052454 /* SCPersionEditDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersionEditDisplayModel.h; sourceTree = "<group>"; };
		96BECEEB2E375E2400052454 /* SCPersionEditDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersionEditDisplayModel.m; sourceTree = "<group>"; };
		96BECEED2E375E2400052454 /* SCPersonalEditViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalEditViewModel.h; sourceTree = "<group>"; };
		96BECEEE2E375E2400052454 /* SCPersonalEditViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalEditViewModel.m; sourceTree = "<group>"; };
		96BECEF02E375E2400052454 /* SCAvatarActivityIndicatorView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAvatarActivityIndicatorView.h; sourceTree = "<group>"; };
		96BECEF12E375E2400052454 /* SCAvatarActivityIndicatorView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAvatarActivityIndicatorView.m; sourceTree = "<group>"; };
		96BECEF22E375E2400052454 /* SCLimitTextView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLimitTextView.h; sourceTree = "<group>"; };
		96BECEF32E375E2400052454 /* SCLimitTextView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLimitTextView.m; sourceTree = "<group>"; };
		96BECEF52E375E2400052454 /* SCPersonalEditViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalEditViewController.h; sourceTree = "<group>"; };
		96BECEF62E375E2400052454 /* SCPersonalEditViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalEditViewController.m; sourceTree = "<group>"; };
		96BECEF92E375E2400052454 /* SCMyLevelCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMyLevelCell.h; sourceTree = "<group>"; };
		96BECEFA2E375E2400052454 /* SCMyLevelCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMyLevelCell.m; sourceTree = "<group>"; };
		96BECEFC2E375E2400052454 /* SCMyLevelHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMyLevelHeaderView.h; sourceTree = "<group>"; };
		96BECEFD2E375E2400052454 /* SCMyLevelHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMyLevelHeaderView.m; sourceTree = "<group>"; };
		96BECEFF2E375E2400052454 /* SCMyLevelViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMyLevelViewController.h; sourceTree = "<group>"; };
		96BECF002E375E2400052454 /* SCMyLevelViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMyLevelViewController.m; sourceTree = "<group>"; };
		96BECF032E375E2400052454 /* SCPersonalItemDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalItemDisplayModel.h; sourceTree = "<group>"; };
		96BECF042E375E2400052454 /* SCPersonalItemDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalItemDisplayModel.m; sourceTree = "<group>"; };
		96BECF062E375E2400052454 /* SCPersonalViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalViewModel.h; sourceTree = "<group>"; };
		96BECF072E375E2400052454 /* SCPersonalViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalViewModel.m; sourceTree = "<group>"; };
		96BECF092E375E2400052454 /* SCPersonalItemCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalItemCell.h; sourceTree = "<group>"; };
		96BECF0A2E375E2400052454 /* SCPersonalItemCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalItemCell.m; sourceTree = "<group>"; };
		96BECF0C2E375E2400052454 /* SCPersonalHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalHeaderView.h; sourceTree = "<group>"; };
		96BECF0D2E375E2400052454 /* SCPersonalHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalHeaderView.m; sourceTree = "<group>"; };
		96BECF0F2E375E2400052454 /* SCPersonalViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPersonalViewController.h; sourceTree = "<group>"; };
		96BECF102E375E2400052454 /* SCPersonalViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPersonalViewController.m; sourceTree = "<group>"; };
		96BECF132E375E2400052454 /* SCLanguageModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLanguageModel.h; sourceTree = "<group>"; };
		96BECF142E375E2400052454 /* SCLanguageModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLanguageModel.m; sourceTree = "<group>"; };
		96BECF162E375E2400052454 /* SCLanguageCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLanguageCell.h; sourceTree = "<group>"; };
		96BECF172E375E2400052454 /* SCLanguageCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLanguageCell.m; sourceTree = "<group>"; };
		96BECF192E375E2400052454 /* SCLanguageViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCLanguageViewController.h; sourceTree = "<group>"; };
		96BECF1A2E375E2400052454 /* SCLanguageViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCLanguageViewController.m; sourceTree = "<group>"; };
		96BECF1B2E375E2400052454 /* SCSettingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCSettingViewController.h; sourceTree = "<group>"; };
		96BECF1C2E375E2400052454 /* SCSettingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCSettingViewController.m; sourceTree = "<group>"; };
		96BECF202E375E2400052454 /* SCActivityPromotionDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCActivityPromotionDisplayModel.h; sourceTree = "<group>"; };
		96BECF212E375E2400052454 /* SCActivityPromotionDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCActivityPromotionDisplayModel.m; sourceTree = "<group>"; };
		96BECF232E375E2400052454 /* SCPromotionDisplayModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPromotionDisplayModel.h; sourceTree = "<group>"; };
		96BECF242E375E2400052454 /* SCPromotionDisplayModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPromotionDisplayModel.m; sourceTree = "<group>"; };
		96BECF262E375E2400052454 /* SCCategoryAPIManagerCoins.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCategoryAPIManagerCoins.h; sourceTree = "<group>"; };
		96BECF272E375E2400052454 /* SCCategoryAPIManagerCoins.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCategoryAPIManagerCoins.m; sourceTree = "<group>"; };
		96BECF292E375E2400052454 /* SCCoinsService.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsService.h; sourceTree = "<group>"; };
		96BECF2A2E375E2400052454 /* SCCoinsService.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsService.m; sourceTree = "<group>"; };
		96BECF2C2E375E2400052454 /* SCCoinsStoreViewModel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsStoreViewModel.h; sourceTree = "<group>"; };
		96BECF2D2E375E2400052454 /* SCCoinsStoreViewModel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsStoreViewModel.m; sourceTree = "<group>"; };
		96BECF2F2E375E2400052454 /* SCCoinsBigCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsBigCollectionViewCell.h; sourceTree = "<group>"; };
		96BECF302E375E2400052454 /* SCCoinsBigCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsBigCollectionViewCell.m; sourceTree = "<group>"; };
		96BECF312E375E2400052454 /* SCCoinsCollectionViewCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsCollectionViewCell.h; sourceTree = "<group>"; };
		96BECF322E375E2400052454 /* SCCoinsCollectionViewCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsCollectionViewCell.m; sourceTree = "<group>"; };
		96BECF342E375E2400052454 /* SCCoinsFullScreenHeaderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsFullScreenHeaderView.h; sourceTree = "<group>"; };
		96BECF352E375E2400052454 /* SCCoinsFullScreenHeaderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsFullScreenHeaderView.m; sourceTree = "<group>"; };
		96BECF362E375E2400052454 /* SCCoinsListView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsListView.h; sourceTree = "<group>"; };
		96BECF372E375E2400052454 /* SCCoinsListView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsListView.m; sourceTree = "<group>"; };
		96BECF382E375E2400052454 /* SCMyCoinsView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCMyCoinsView.h; sourceTree = "<group>"; };
		96BECF392E375E2400052454 /* SCMyCoinsView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCMyCoinsView.m; sourceTree = "<group>"; };
		96BECF3B2E375E2400052454 /* SCAnchorCoinsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAnchorCoinsViewController.h; sourceTree = "<group>"; };
		96BECF3C2E375E2400052454 /* SCAnchorCoinsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAnchorCoinsViewController.m; sourceTree = "<group>"; };
		96BECF3D2E375E2400052454 /* SCCoinsFullScreenViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsFullScreenViewController.h; sourceTree = "<group>"; };
		96BECF3E2E375E2400052454 /* SCCoinsFullScreenViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsFullScreenViewController.m; sourceTree = "<group>"; };
		96BECF3F2E375E2400052454 /* SCCoinsPopupViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinsPopupViewController.h; sourceTree = "<group>"; };
		96BECF402E375E2400052454 /* SCCoinsPopupViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinsPopupViewController.m; sourceTree = "<group>"; };
		96BECF412E375E2400052454 /* SCContinueRechargePopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCContinueRechargePopup.h; sourceTree = "<group>"; };
		96BECF422E375E2400052454 /* SCContinueRechargePopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCContinueRechargePopup.m; sourceTree = "<group>"; };
		96BECF432E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCInviteRechargeCoinsPopupViewController.h; sourceTree = "<group>"; };
		96BECF442E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCInviteRechargeCoinsPopupViewController.m; sourceTree = "<group>"; };
		96BECF472E375E2400052454 /* SCActivityFloatingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCActivityFloatingView.h; sourceTree = "<group>"; };
		96BECF482E375E2400052454 /* SCActivityFloatingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCActivityFloatingView.m; sourceTree = "<group>"; };
		96BECF492E375E2400052454 /* SCCoinStoeFloatingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCoinStoeFloatingView.h; sourceTree = "<group>"; };
		96BECF4A2E375E2400052454 /* SCCoinStoeFloatingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCCoinStoeFloatingView.m; sourceTree = "<group>"; };
		96BECF4B2E375E2400052454 /* SCFloatingLayoutView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFloatingLayoutView.h; sourceTree = "<group>"; };
		96BECF4C2E375E2400052454 /* SCFloatingLayoutView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFloatingLayoutView.m; sourceTree = "<group>"; };
		96BECF4D2E375E2400052454 /* SCFloatingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCFloatingView.h; sourceTree = "<group>"; };
		96BECF4E2E375E2400052454 /* SCFloatingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCFloatingView.m; sourceTree = "<group>"; };
		96BECF4F2E375E2400052454 /* SCPromotionFloatingView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCPromotionFloatingView.h; sourceTree = "<group>"; };
		96BECF502E375E2400052454 /* SCPromotionFloatingView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCPromotionFloatingView.m; sourceTree = "<group>"; };
		96BECF522E375E2400052454 /* SCActivityPromotionPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCActivityPromotionPopup.h; sourceTree = "<group>"; };
		96BECF532E375E2400052454 /* SCActivityPromotionPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCActivityPromotionPopup.m; sourceTree = "<group>"; };
		96BECF542E375E2400052454 /* SCNewUserAwardPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNewUserAwardPopup.h; sourceTree = "<group>"; };
		96BECF552E375E2400052454 /* SCNewUserAwardPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNewUserAwardPopup.m; sourceTree = "<group>"; };
		96BECF562E375E2400052454 /* SCNewUserPromotionPopup.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCNewUserPromotionPopup.h; sourceTree = "<group>"; };
		96BECF572E375E2400052454 /* SCNewUserPromotionPopup.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCNewUserPromotionPopup.m; sourceTree = "<group>"; };
		96BECF5C2E375E2400052454 /* BaiJamjuree-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-Bold.ttf"; sourceTree = "<group>"; };
		96BECF5D2E375E2400052454 /* BaiJamjuree-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-BoldItalic.ttf"; sourceTree = "<group>"; };
		96BECF5E2E375E2400052454 /* BaiJamjuree-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-Italic.ttf"; sourceTree = "<group>"; };
		96BECF5F2E375E2400052454 /* BaiJamjuree-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-Regular.ttf"; sourceTree = "<group>"; };
		96BECF602E375E2400052454 /* BaiJamjuree-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-SemiBold.ttf"; sourceTree = "<group>"; };
		96BECF612E375E2400052454 /* BaiJamjuree-SemiBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "BaiJamjuree-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		96BECF632E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF642E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF652E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF662E375E2400052454 /* backspace_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = backspace_n.png; sourceTree = "<group>"; };
		96BECF672E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF682E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF692E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF6A2E375E2400052454 /* bell_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bell_n.png; sourceTree = "<group>"; };
		96BECF6B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF6C2E375E2400052454 /* bell_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bell_s.png; sourceTree = "<group>"; };
		96BECF6D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF6E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF6F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF702E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF712E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF722E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF732E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF742E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF752E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF762E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF772E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF782E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF792E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF7F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF802E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF812E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF822E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF832E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF842E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF852E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF862E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF872E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF882E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF892E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF8F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF902E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF912E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF922E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF932E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF942E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF952E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF962E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF972E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF982E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF992E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECF9F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA02E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA22E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA42E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA62E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA82E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFA92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAA2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAC2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAD2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAE2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFAF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB02E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB22E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB42E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB62E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFB82E375E2400052454 /* car_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = car_n.png; sourceTree = "<group>"; };
		96BECFB92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFBA2E375E2400052454 /* car_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = car_s.png; sourceTree = "<group>"; };
		96BECFBB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFBC2E375E2400052454 /* characters_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = characters_n.png; sourceTree = "<group>"; };
		96BECFBD2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFBE2E375E2400052454 /* characters_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = characters_s.png; sourceTree = "<group>"; };
		96BECFBF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC02E375E2400052454 /* face_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = face_n.png; sourceTree = "<group>"; };
		96BECFC12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC22E375E2400052454 /* face_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = face_s.png; sourceTree = "<group>"; };
		96BECFC32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC42E375E2400052454 /* flower_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = flower_n.png; sourceTree = "<group>"; };
		96BECFC52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC62E375E2400052454 /* flower_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = flower_s.png; sourceTree = "<group>"; };
		96BECFC72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC82E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFC92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCA2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCC2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCD2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCE2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFCF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD02E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD22E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD42E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD62E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD82E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFD92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDA2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDC2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDD2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDE2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFDF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE02E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE22E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE42E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE62E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE82E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFE92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFEA2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFEB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFEC2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFED2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFEE2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFEF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF02E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF12E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF22E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF32E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF42E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF52E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF62E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF72E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF82E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFF92E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFA2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFB2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFC2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFD2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFE2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BECFFF2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0002E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0012E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0022E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0032E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0042E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0052E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0062E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0072E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0082E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0092E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED00F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0102E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0112E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0122E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0132E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0142E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0152E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0162E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0172E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0182E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0192E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED01F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0202E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0212E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0222E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0232E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0242E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0252E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0262E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0272E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0282E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0292E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED02F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0302E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0312E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0322E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0332E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0342E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0352E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0362E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0372E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0382E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0392E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED03F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0402E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0412E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0422E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0432E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0442E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0452E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0462E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0472E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0482E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0492E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED04F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0502E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0512E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0522E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0532E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0542E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0552E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0562E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0572E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0582E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0592E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED05F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0602E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0612E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0622E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0632E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0642E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0652E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0662E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0672E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0682E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0692E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06A2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06C2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED06F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0702E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0712E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0722E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0732E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0742E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0752E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0762E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0772E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0782E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0792E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED07A2E375E2400052454 /* recent_n.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = recent_n.png; sourceTree = "<group>"; };
		96BED07B2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED07C2E375E2400052454 /* recent_s.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = recent_s.png; sourceTree = "<group>"; };
		96BED07D2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED07E2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED07F2E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0802E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0812E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0822E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0832E375E2400052454 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		96BED0852E375E2400052454 /* banner_loading.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = banner_loading.json; sourceTree = "<group>"; };
		96BED0862E375E2400052454 /* call_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = call_animation.json; sourceTree = "<group>"; };
		96BED0872E375E2400052454 /* game_loading.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = game_loading.json; sourceTree = "<group>"; };
		96BED0882E375E2400052454 /* gift_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = gift_animation.json; sourceTree = "<group>"; };
		96BED0892E375E2400052454 /* gift_send_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = gift_send_animation.json; sourceTree = "<group>"; };
		96BED08A2E375E2400052454 /* match_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = match_animation.json; sourceTree = "<group>"; };
		96BED08B2E375E2400052454 /* message_call_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = message_call_animation.json; sourceTree = "<group>"; };
		96BED08C2E375E2400052454 /* next_anchor_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = next_anchor_animation.json; sourceTree = "<group>"; };
		96BED08D2E375E2400052454 /* pickup_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = pickup_animation.json; sourceTree = "<group>"; };
		96BED08E2E375E2400052454 /* sc_country.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = sc_country.json; sourceTree = "<group>"; };
		96BED08F2E375E2400052454 /* wave_animation.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = wave_animation.json; sourceTree = "<group>"; };
		96BED0912E375E2400052454 /* ring_call.mp3 */ = {isa = PBXFileReference; lastKnownFileType = audio.mp3; path = ring_call.mp3; sourceTree = "<group>"; };
		96BED0932E375E2400052454 /* EmojisList.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = EmojisList.plist; sourceTree = "<group>"; };
		96BED0952E375E2400052454 /* sc_gift.svga */ = {isa = PBXFileReference; lastKnownFileType = file; path = sc_gift.svga; sourceTree = "<group>"; };
		96BED0972E375E2400052454 /* en_source.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = en_source.json; sourceTree = "<group>"; };
		96BED0992E375E2400052454 /* supercall_ar.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_ar.json; sourceTree = "<group>"; };
		96BED09A2E375E2400052454 /* supercall_de.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_de.json; sourceTree = "<group>"; };
		96BED09B2E375E2400052454 /* supercall_en.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_en.json; sourceTree = "<group>"; };
		96BED09C2E375E2400052454 /* supercall_es.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_es.json; sourceTree = "<group>"; };
		96BED09D2E375E2400052454 /* supercall_fr.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_fr.json; sourceTree = "<group>"; };
		96BED09E2E375E2400052454 /* supercall_hi.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_hi.json; sourceTree = "<group>"; };
		96BED09F2E375E2400052454 /* supercall_it.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_it.json; sourceTree = "<group>"; };
		96BED0A02E375E2400052454 /* supercall_ja.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_ja.json; sourceTree = "<group>"; };
		96BED0A12E375E2400052454 /* supercall_ko.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_ko.json; sourceTree = "<group>"; };
		96BED0A22E375E2400052454 /* supercall_pt.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_pt.json; sourceTree = "<group>"; };
		96BED0A32E375E2400052454 /* supercall_ru.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_ru.json; sourceTree = "<group>"; };
		96BED0A42E375E2400052454 /* supercall_th.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_th.json; sourceTree = "<group>"; };
		96BED0A52E375E2400052454 /* supercall_tr.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_tr.json; sourceTree = "<group>"; };
		96BED0A62E375E2400052454 /* supercall_vi.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = supercall_vi.json; sourceTree = "<group>"; };
		96BED0A72E375E2400052454 /* supercall_zh-tw.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = "supercall_zh-tw.json"; sourceTree = "<group>"; };
		96BED0A92E375E2400052454 /* activity_promotion.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = activity_promotion.mp4; sourceTree = "<group>"; };
		96BED0AA2E375E2400052454 /* exit_recharge.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = exit_recharge.mp4; sourceTree = "<group>"; };
		96BED0AB2E375E2400052454 /* login_video.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = login_video.mp4; sourceTree = "<group>"; };
		96BED0AC2E375E2400052454 /* new_user_promotion.mp4 */ = {isa = PBXFileReference; lastKnownFileType = file; path = new_user_promotion.mp4; sourceTree = "<group>"; };
		96BED0AF2E375E2400052454 /* asster_change.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = asster_change.sh; sourceTree = "<group>"; };
		96BED0B02E375E2400052454 /* supercall_data.zip */ = {isa = PBXFileReference; lastKnownFileType = archive.zip; path = supercall_data.zip; sourceTree = "<group>"; };
		96BED0B82E375E2400052454 /* SCAppIntegrationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCAppIntegrationManager.h; sourceTree = "<group>"; };
		96BED0B92E375E2400052454 /* SCAppIntegrationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCAppIntegrationManager.m; sourceTree = "<group>"; };
		96BED0BA2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SCAppIntegrationManager+FlutterMerge.h"; sourceTree = "<group>"; };
		96BED0BB2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "SCAppIntegrationManager+FlutterMerge.m"; sourceTree = "<group>"; };
		96BED0BC2E375E2400052454 /* SCCodeHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCCodeHeader.h; sourceTree = "<group>"; };
		973466FE4D2B720075835ED7 /* Pods_Supercall.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Supercall.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B9A3999F036961456BE2A952 /* Pods-Supercall.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Supercall.release.xcconfig"; path = "Target Support Files/Pods-Supercall/Pods-Supercall.release.xcconfig"; sourceTree = "<group>"; };
		BEA4FA9D2CF86AD70029C50B /* SCALoginViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCALoginViewController.h; sourceTree = "<group>"; };
		BEA4FA9E2CF86AD70029C50B /* SCALoginViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCALoginViewController.m; sourceTree = "<group>"; };
		BEA4FA9F2CF86AD70029C50B /* SCALoginViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SCALoginViewController.xib; sourceTree = "<group>"; };
		BED3F2A92E38A70C001745A6 /* SCBlockUserDictHelper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SCBlockUserDictHelper.h; sourceTree = "<group>"; };
		BED3F2AA2E38A70C001745A6 /* SCBlockUserDictHelper.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SCBlockUserDictHelper.m; sourceTree = "<group>"; };
		E23541B5E0E2A835639EB28C /* Pods-Supercall.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Supercall.debug.xcconfig"; path = "Target Support Files/Pods-Supercall/Pods-Supercall.debug.xcconfig"; sourceTree = "<group>"; };
		E69CE72C2B34100100290E17 /* SuperCallPrefixHeader.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SuperCallPrefixHeader.pch; sourceTree = "<group>"; };
		E6CC26862B32883E0004D794 /* Supercall.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Supercall.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E6CC26892B32883E0004D794 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		E6CC268A2B32883E0004D794 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		E6CC268F2B32883E0004D794 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		E6CC26902B32883E0004D794 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		E6CC26932B32883E0004D794 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		E6CC26952B3288450004D794 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		E6CC26982B3288450004D794 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		E6CC269A2B3288450004D794 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		E6CC269B2B3288450004D794 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E6CC26832B32883E0004D794 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C589E7FD01072543DFA77199 /* Pods_Supercall.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		35E5F736999B0677F30F4530 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				973466FE4D2B720075835ED7 /* Pods_Supercall.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		96BECC5D2E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECC592E375E2400052454 /* SCAPIServiceManager.h */,
				96BECC5A2E375E2400052454 /* SCAPIServiceManager.m */,
				96BECC5B2E375E2400052454 /* SCCategoryAPIManagerBanner.h */,
				96BECC5C2E375E2400052454 /* SCCategoryAPIManagerBanner.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECC602E375E2400052454 /* OSS */ = {
			isa = PBXGroup;
			children = (
				96BECC5E2E375E2400052454 /* SCOSSManager.h */,
				96BECC5F2E375E2400052454 /* SCOSSManager.m */,
			);
			path = OSS;
			sourceTree = "<group>";
		};
		96BECC632E375E2400052454 /* Auth */ = {
			isa = PBXGroup;
			children = (
				96BECC5D2E375E2400052454 /* API */,
				96BECC602E375E2400052454 /* OSS */,
				96BECC612E375E2400052454 /* SCAuthManager.h */,
				96BECC622E375E2400052454 /* SCAuthManager.m */,
			);
			path = Auth;
			sourceTree = "<group>";
		};
		96BECC792E375E2400052454 /* Bases */ = {
			isa = PBXGroup;
			children = (
				96BECC672E375E2400052454 /* SCBaseAppService.h */,
				96BECC682E375E2400052454 /* SCBaseAppService.m */,
				96BECC692E375E2400052454 /* SCBaseCollectionCell.h */,
				96BECC6A2E375E2400052454 /* SCBaseCollectionCell.m */,
				96BECC6B2E375E2400052454 /* SCBaseNavigationController.h */,
				96BECC6C2E375E2400052454 /* SCBaseNavigationController.m */,
				96BECC6D2E375E2400052454 /* SCBaseTableViewCell.h */,
				96BECC6E2E375E2400052454 /* SCBaseTableViewCell.m */,
				96BECC6F2E375E2400052454 /* SCBaseView.h */,
				96BECC702E375E2400052454 /* SCBaseView.m */,
				96BECC712E375E2400052454 /* SCBaseViewController.h */,
				96BECC722E375E2400052454 /* SCBaseViewController.m */,
				96BECC732E375E2400052454 /* SCNavigationBar.h */,
				96BECC742E375E2400052454 /* SCNavigationBar.m */,
				96BECC752E375E2400052454 /* SCSafetyViewController.h */,
				96BECC762E375E2400052454 /* SCSafetyViewController.m */,
				96BECC772E375E2400052454 /* SCThreadSafeDictionary.h */,
				96BECC782E375E2400052454 /* SCThreadSafeDictionary.m */,
			);
			path = Bases;
			sourceTree = "<group>";
		};
		96BECC7F2E375E2400052454 /* SCActionSheet */ = {
			isa = PBXGroup;
			children = (
				96BECC7B2E375E2400052454 /* SCActionSheetViewController.h */,
				96BECC7C2E375E2400052454 /* SCActionSheetViewController.m */,
				96BECC7D2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.h */,
				96BECC7E2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.m */,
			);
			path = SCActionSheet;
			sourceTree = "<group>";
		};
		96BECC882E375E2400052454 /* SCAlert */ = {
			isa = PBXGroup;
			children = (
				96BECC802E375E2400052454 /* SCAlertViewController.h */,
				96BECC812E375E2400052454 /* SCAlertViewController.m */,
				96BECC822E375E2400052454 /* SCCallExceptionalAlert.h */,
				96BECC832E375E2400052454 /* SCCallExceptionalAlert.m */,
				96BECC842E375E2400052454 /* SCNoEnoughCoinsAlertViewController.h */,
				96BECC852E375E2400052454 /* SCNoEnoughCoinsAlertViewController.m */,
				96BECC862E375E2400052454 /* SCStoreScoreAlertViewController.h */,
				96BECC872E375E2400052454 /* SCStoreScoreAlertViewController.m */,
			);
			path = SCAlert;
			sourceTree = "<group>";
		};
		96BECC8B2E375E2400052454 /* SCAntiRecordView */ = {
			isa = PBXGroup;
			children = (
				96BECC892E375E2400052454 /* SCAntiRecordView.h */,
				96BECC8A2E375E2400052454 /* SCAntiRecordView.m */,
			);
			path = SCAntiRecordView;
			sourceTree = "<group>";
		};
		96BECC8E2E375E2400052454 /* SCCircularCountdown */ = {
			isa = PBXGroup;
			children = (
				96BECC8C2E375E2400052454 /* SCCircularCountdownView.h */,
				96BECC8D2E375E2400052454 /* SCCircularCountdownView.m */,
			);
			path = SCCircularCountdown;
			sourceTree = "<group>";
		};
		96BECC912E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECC8F2E375E2400052454 /* SCCountryModel.h */,
				96BECC902E375E2400052454 /* SCCountryModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECC942E375E2400052454 /* Services */ = {
			isa = PBXGroup;
			children = (
				96BECC922E375E2400052454 /* SCCountryService.h */,
				96BECC932E375E2400052454 /* SCCountryService.m */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		96BECC972E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECC952E375E2400052454 /* SCCountryPickerViewModel.h */,
				96BECC962E375E2400052454 /* SCCountryPickerViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECC9C2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECC982E375E2400052454 /* SCCountryPickeTableViewCell.h */,
				96BECC992E375E2400052454 /* SCCountryPickeTableViewCell.m */,
				96BECC9A2E375E2400052454 /* SCCountrySelectCell.h */,
				96BECC9B2E375E2400052454 /* SCCountrySelectCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECC9D2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECC9C2E375E2400052454 /* Cells */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECCA22E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECC9D2E375E2400052454 /* SubViews */,
				96BECC9E2E375E2400052454 /* SCCountryPickerViewController.h */,
				96BECC9F2E375E2400052454 /* SCCountryPickerViewController.m */,
				96BECCA02E375E2400052454 /* SCCountrySelectPopoupView.h */,
				96BECCA12E375E2400052454 /* SCCountrySelectPopoupView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECCA32E375E2400052454 /* SCCountryPicker */ = {
			isa = PBXGroup;
			children = (
				96BECC912E375E2400052454 /* Models */,
				96BECC942E375E2400052454 /* Services */,
				96BECC972E375E2400052454 /* ViewModels */,
				96BECCA22E375E2400052454 /* Views */,
			);
			path = SCCountryPicker;
			sourceTree = "<group>";
		};
		96BECCA62E375E2400052454 /* SCDatePicker */ = {
			isa = PBXGroup;
			children = (
				96BECCA42E375E2400052454 /* SCDatePicker.h */,
				96BECCA52E375E2400052454 /* SCDatePicker.m */,
			);
			path = SCDatePicker;
			sourceTree = "<group>";
		};
		96BECCA92E375E2400052454 /* SCFontManager */ = {
			isa = PBXGroup;
			children = (
				96BECCA72E375E2400052454 /* SCFontManager.h */,
				96BECCA82E375E2400052454 /* SCFontManager.m */,
			);
			path = SCFontManager;
			sourceTree = "<group>";
		};
		96BECCAC2E375E2400052454 /* SCLanguage */ = {
			isa = PBXGroup;
			children = (
				96BECCAA2E375E2400052454 /* SCLanguageManager.h */,
				96BECCAB2E375E2400052454 /* SCLanguageManager.m */,
			);
			path = SCLanguage;
			sourceTree = "<group>";
		};
		96BECCAF2E375E2400052454 /* SCLocalVideoPlayer */ = {
			isa = PBXGroup;
			children = (
				96BECCAD2E375E2400052454 /* SCLocalVideoPlayer.h */,
				96BECCAE2E375E2400052454 /* SCLocalVideoPlayer.m */,
			);
			path = SCLocalVideoPlayer;
			sourceTree = "<group>";
		};
		96BECCB22E375E2400052454 /* SCMessagePopupManager */ = {
			isa = PBXGroup;
			children = (
				96BECCB02E375E2400052454 /* SCMessagePopupManager.h */,
				96BECCB12E375E2400052454 /* SCMessagePopupManager.m */,
			);
			path = SCMessagePopupManager;
			sourceTree = "<group>";
		};
		96BECCB72E375E2400052454 /* SCObservable */ = {
			isa = PBXGroup;
			children = (
				96BECCB32E375E2400052454 /* SCDisposeBag.h */,
				96BECCB42E375E2400052454 /* SCDisposeBag.m */,
				96BECCB52E375E2400052454 /* SCObservable.h */,
				96BECCB62E375E2400052454 /* SCObservable.m */,
			);
			path = SCObservable;
			sourceTree = "<group>";
		};
		96BECCC02E375E2400052454 /* SCOnlineStates */ = {
			isa = PBXGroup;
			children = (
				96BECCB82E375E2400052454 /* SCOnlineStatesService.h */,
				96BECCB92E375E2400052454 /* SCOnlineStatesService.m */,
				96BECCBA2E375E2400052454 /* SCOnlineStatusChangeViewController.h */,
				96BECCBB2E375E2400052454 /* SCOnlineStatusChangeViewController.m */,
				96BECCBC2E375E2400052454 /* SCOnlineStatusSubscribe.h */,
				96BECCBD2E375E2400052454 /* SCOnlineStatusSubscribe.m */,
				96BECCBE2E375E2400052454 /* SCOrderedDictionary.h */,
				96BECCBF2E375E2400052454 /* SCOrderedDictionary.m */,
			);
			path = SCOnlineStates;
			sourceTree = "<group>";
		};
		96BECCC52E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECCC12E375E2400052454 /* SCOrderResultModel.h */,
				96BECCC22E375E2400052454 /* SCOrderResultModel.m */,
				96BECCC32E375E2400052454 /* SCThirdPayCannelDisplayModel.h */,
				96BECCC42E375E2400052454 /* SCThirdPayCannelDisplayModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECCC82E375E2400052454 /* APIs */ = {
			isa = PBXGroup;
			children = (
				96BECCC62E375E2400052454 /* SCCategoryAPIManagerPay.h */,
				96BECCC72E375E2400052454 /* SCCategoryAPIManagerPay.m */,
			);
			path = APIs;
			sourceTree = "<group>";
		};
		96BECCC92E375E2400052454 /* Services */ = {
			isa = PBXGroup;
			children = (
				96BECCC82E375E2400052454 /* APIs */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		96BECCCC2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECCCA2E375E2400052454 /* SCThirdPayCannelCell.h */,
				96BECCCB2E375E2400052454 /* SCThirdPayCannelCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECCCF2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECCCC2E375E2400052454 /* Cells */,
				96BECCCD2E375E2400052454 /* SCThirdPayItemView.h */,
				96BECCCE2E375E2400052454 /* SCThirdPayItemView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECCD42E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECCCF2E375E2400052454 /* SubViews */,
				96BECCD02E375E2400052454 /* SCThirdPartyPayPopup.h */,
				96BECCD12E375E2400052454 /* SCThirdPartyPayPopup.m */,
				96BECCD22E375E2400052454 /* SCThirdPartyPayWebViewController.h */,
				96BECCD32E375E2400052454 /* SCThirdPartyPayWebViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECCD52E375E2400052454 /* ThirdPartyPay */ = {
			isa = PBXGroup;
			children = (
				96BECCD42E375E2400052454 /* Views */,
			);
			path = ThirdPartyPay;
			sourceTree = "<group>";
		};
		96BECCDA2E375E2400052454 /* SCPay */ = {
			isa = PBXGroup;
			children = (
				96BECCC52E375E2400052454 /* Models */,
				96BECCC92E375E2400052454 /* Services */,
				96BECCD52E375E2400052454 /* ThirdPartyPay */,
				96BECCD62E375E2400052454 /* SCIAPManager.h */,
				96BECCD72E375E2400052454 /* SCIAPManager.m */,
				96BECCD82E375E2400052454 /* SCPayService.h */,
				96BECCD92E375E2400052454 /* SCPayService.m */,
			);
			path = SCPay;
			sourceTree = "<group>";
		};
		96BECCDD2E375E2400052454 /* SCPermissionManager */ = {
			isa = PBXGroup;
			children = (
				96BECCDB2E375E2400052454 /* SCPermissionManager.h */,
				96BECCDC2E375E2400052454 /* SCPermissionManager.m */,
			);
			path = SCPermissionManager;
			sourceTree = "<group>";
		};
		96BECCE02E375E2400052454 /* SCPopupManager */ = {
			isa = PBXGroup;
			children = (
				96BECCDE2E375E2400052454 /* SCPopupManager.h */,
				96BECCDF2E375E2400052454 /* SCPopupManager.m */,
			);
			path = SCPopupManager;
			sourceTree = "<group>";
		};
		96BECCE32E375E2400052454 /* SCResource */ = {
			isa = PBXGroup;
			children = (
				96BECCE12E375E2400052454 /* SCResourceManager.h */,
				96BECCE22E375E2400052454 /* SCResourceManager.m */,
			);
			path = SCResource;
			sourceTree = "<group>";
		};
		96BECCE62E375E2400052454 /* SCScrollView */ = {
			isa = PBXGroup;
			children = (
				96BECCE42E375E2400052454 /* SCScrollView.h */,
				96BECCE52E375E2400052454 /* SCScrollView.m */,
			);
			path = SCScrollView;
			sourceTree = "<group>";
		};
		96BECCE92E375E2400052454 /* SCThrottle */ = {
			isa = PBXGroup;
			children = (
				96BECCE72E375E2400052454 /* SCThrottle.h */,
				96BECCE82E375E2400052454 /* SCThrottle.m */,
			);
			path = SCThrottle;
			sourceTree = "<group>";
		};
		96BECCEC2E375E2400052454 /* SCTimer */ = {
			isa = PBXGroup;
			children = (
				96BECCEA2E375E2400052454 /* SCTimer.h */,
				96BECCEB2E375E2400052454 /* SCTimer.m */,
			);
			path = SCTimer;
			sourceTree = "<group>";
		};
		96BECCEF2E375E2400052454 /* SCTranslation */ = {
			isa = PBXGroup;
			children = (
				96BECCED2E375E2400052454 /* SCTranslationService.h */,
				96BECCEE2E375E2400052454 /* SCTranslationService.m */,
			);
			path = SCTranslation;
			sourceTree = "<group>";
		};
		96BECCF42E375E2400052454 /* AGEmojiKeyboard */ = {
			isa = PBXGroup;
			children = (
				96BECCF02E375E2400052454 /* AGEmojiKeyBoardView.h */,
				96BECCF12E375E2400052454 /* AGEmojiKeyBoardView.m */,
				96BECCF22E375E2400052454 /* AGEmojiPageView.h */,
				96BECCF32E375E2400052454 /* AGEmojiPageView.m */,
			);
			path = AGEmojiKeyboard;
			sourceTree = "<group>";
		};
		96BECCF72E375E2400052454 /* Source */ = {
			isa = PBXGroup;
			children = (
				96BECCF52E375E2400052454 /* UIScrollView+EmptyDataSet.h */,
				96BECCF62E375E2400052454 /* UIScrollView+EmptyDataSet.m */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		96BECCF82E375E2400052454 /* DZNEmptyDataSet */ = {
			isa = PBXGroup;
			children = (
				96BECCF72E375E2400052454 /* Source */,
			);
			path = DZNEmptyDataSet;
			sourceTree = "<group>";
		};
		96BECCFB2E375E2400052454 /* MBProgressHUD */ = {
			isa = PBXGroup;
			children = (
				96BECCF92E375E2400052454 /* MBProgressHUD.h */,
				96BECCFA2E375E2400052454 /* MBProgressHUD.m */,
			);
			path = MBProgressHUD;
			sourceTree = "<group>";
		};
		96BECCFC2E375E2400052454 /* ThirdParty */ = {
			isa = PBXGroup;
			children = (
				96BECCF42E375E2400052454 /* AGEmojiKeyboard */,
				96BECCF82E375E2400052454 /* DZNEmptyDataSet */,
				96BECCFB2E375E2400052454 /* MBProgressHUD */,
			);
			path = ThirdParty;
			sourceTree = "<group>";
		};
		96BECCFF2E375E2400052454 /* WebView */ = {
			isa = PBXGroup;
			children = (
				96BECCFD2E375E2400052454 /* SCWebViewController.h */,
				96BECCFE2E375E2400052454 /* SCWebViewController.m */,
			);
			path = WebView;
			sourceTree = "<group>";
		};
		96BECD002E375E2400052454 /* Components */ = {
			isa = PBXGroup;
			children = (
				96BECC7F2E375E2400052454 /* SCActionSheet */,
				96BECC882E375E2400052454 /* SCAlert */,
				96BECC8B2E375E2400052454 /* SCAntiRecordView */,
				96BECC8E2E375E2400052454 /* SCCircularCountdown */,
				96BECCA32E375E2400052454 /* SCCountryPicker */,
				96BECCA62E375E2400052454 /* SCDatePicker */,
				96BECCA92E375E2400052454 /* SCFontManager */,
				96BECCAC2E375E2400052454 /* SCLanguage */,
				96BECCAF2E375E2400052454 /* SCLocalVideoPlayer */,
				96BECCB22E375E2400052454 /* SCMessagePopupManager */,
				96BECCB72E375E2400052454 /* SCObservable */,
				96BECCC02E375E2400052454 /* SCOnlineStates */,
				96BECCDA2E375E2400052454 /* SCPay */,
				96BECCDD2E375E2400052454 /* SCPermissionManager */,
				96BECCE02E375E2400052454 /* SCPopupManager */,
				96BECCE32E375E2400052454 /* SCResource */,
				96BECCE62E375E2400052454 /* SCScrollView */,
				96BECCE92E375E2400052454 /* SCThrottle */,
				96BECCEC2E375E2400052454 /* SCTimer */,
				96BECCEF2E375E2400052454 /* SCTranslation */,
				96BECCFC2E375E2400052454 /* ThirdParty */,
				96BECCFF2E375E2400052454 /* WebView */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		96BECD082E375E2400052454 /* Config */ = {
			isa = PBXGroup;
			children = (
				96BECD012E375E2400052454 /* SCAPIManage.h */,
				96BECD022E375E2400052454 /* SCAPIManage.m */,
				96BECD032E375E2400052454 /* SCConstant.h */,
				96BECD042E375E2400052454 /* SCConstant.m */,
				96BECD052E375E2400052454 /* SCKey.h */,
				96BECD062E375E2400052454 /* SCPayEntry.h */,
				96BECD072E375E2400052454 /* SCPayEntry.m */,
			);
			path = Config;
			sourceTree = "<group>";
		};
		96BECD252E375E2400052454 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				96BECD092E375E2400052454 /* SCCategoryNSDateCode.h */,
				96BECD0A2E375E2400052454 /* SCCategoryNSDateCode.m */,
				96BECD0B2E375E2400052454 /* SCCategoryNSNumberCode.h */,
				96BECD0C2E375E2400052454 /* SCCategoryNSNumberCode.m */,
				96BECD0D2E375E2400052454 /* SCCategoryNSStringCode.h */,
				96BECD0E2E375E2400052454 /* SCCategoryNSStringCode.m */,
				96BECD0F2E375E2400052454 /* SCCategoryUIButtonCode.h */,
				96BECD102E375E2400052454 /* SCCategoryUIButtonCode.m */,
				96BECD112E375E2400052454 /* SCCategoryUICollectionViewCode.h */,
				96BECD122E375E2400052454 /* SCCategoryUICollectionViewCode.m */,
				96BECD132E375E2400052454 /* SCCategoryUIColorCode.h */,
				96BECD142E375E2400052454 /* SCCategoryUIColorCode.m */,
				96BECD152E375E2400052454 /* SCCategoryUIFontCode.h */,
				96BECD162E375E2400052454 /* SCCategoryUIFontCode.m */,
				96BECD172E375E2400052454 /* SCCategoryUIImageCode.h */,
				96BECD182E375E2400052454 /* SCCategoryUIImageCode.m */,
				96BECD192E375E2400052454 /* SCCategoryUIImageViewCode.h */,
				96BECD1A2E375E2400052454 /* SCCategoryUIImageViewCode.m */,
				96BECD1B2E375E2400052454 /* SCCategoryUILabelCode.h */,
				96BECD1C2E375E2400052454 /* SCCategoryUILabelCode.m */,
				96BECD1D2E375E2400052454 /* SCCategoryUITableViewCode.h */,
				96BECD1E2E375E2400052454 /* SCCategoryUITableViewCode.m */,
				96BECD1F2E375E2400052454 /* SCCategoryUIViewCode.h */,
				96BECD202E375E2400052454 /* SCCategoryUIViewCode.m */,
				96BECD212E375E2400052454 /* SCCategoryUIViewControllerCode.h */,
				96BECD222E375E2400052454 /* SCCategoryUIViewControllerCode.m */,
				96BECD232E375E2400052454 /* SCCategoryUserDefaultsCode.h */,
				96BECD242E375E2400052454 /* SCCategoryUserDefaultsCode.m */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		96BECD282E375E2400052454 /* Cache */ = {
			isa = PBXGroup;
			children = (
				96BECD262E375E2400052454 /* SCNetworkCacheManager.h */,
				96BECD272E375E2400052454 /* SCNetworkCacheManager.m */,
			);
			path = Cache;
			sourceTree = "<group>";
		};
		96BECD312E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECD292E375E2400052454 /* SCNetworkConstant.h */,
				96BECD2A2E375E2400052454 /* SCNetworkConstant.m */,
				96BECD2B2E375E2400052454 /* SCNetWorkResponseModel.h */,
				96BECD2C2E375E2400052454 /* SCNetWorkResponseModel.m */,
				96BECD2D2E375E2400052454 /* SCProgress.h */,
				96BECD2E2E375E2400052454 /* SCProgress.m */,
				96BECD2F2E375E2400052454 /* SCXErrorModel.h */,
				96BECD302E375E2400052454 /* SCXErrorModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECD342E375E2400052454 /* Reachable */ = {
			isa = PBXGroup;
			children = (
				96BECD322E375E2400052454 /* SCNetworkStatusManager.h */,
				96BECD332E375E2400052454 /* SCNetworkStatusManager.m */,
			);
			path = Reachable;
			sourceTree = "<group>";
		};
		96BECD392E375E2400052454 /* Network */ = {
			isa = PBXGroup;
			children = (
				96BECD282E375E2400052454 /* Cache */,
				96BECD312E375E2400052454 /* Models */,
				96BECD342E375E2400052454 /* Reachable */,
				96BECD352E375E2400052454 /* SCHTTPRequestSerializer.h */,
				96BECD362E375E2400052454 /* SCHTTPRequestSerializer.m */,
				96BECD372E375E2400052454 /* SCNetworkManager.h */,
				96BECD382E375E2400052454 /* SCNetworkManager.m */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		96BECD3C2E375E2400052454 /* SCDefine */ = {
			isa = PBXGroup;
			children = (
				96BECD3A2E375E2400052454 /* SCCodeBlockDefine.h */,
				96BECD3B2E375E2400052454 /* SCCodeLayoutDefine.h */,
			);
			path = SCDefine;
			sourceTree = "<group>";
		};
		96BECD3F2E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECD3D2E375E2400052454 /* SCCategoryIM.h */,
				96BECD3E2E375E2400052454 /* SCCategoryIM.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECD462E375E2400052454 /* CustomMessage */ = {
			isa = PBXGroup;
			children = (
				96BECD402E375E2400052454 /* SCHyperLinkMessage.h */,
				96BECD412E375E2400052454 /* SCHyperLinkMessage.m */,
				96BECD422E375E2400052454 /* SCNoneFlagMessage.h */,
				96BECD432E375E2400052454 /* SCNoneFlagMessage.m */,
				96BECD442E375E2400052454 /* SCSingleJsonMessage.h */,
				96BECD452E375E2400052454 /* SCSingleJsonMessage.m */,
			);
			path = CustomMessage;
			sourceTree = "<group>";
		};
		96BECD492E375E2400052454 /* SCIM */ = {
			isa = PBXGroup;
			children = (
				96BECD3F2E375E2400052454 /* API */,
				96BECD462E375E2400052454 /* CustomMessage */,
				96BECD472E375E2400052454 /* SCIMService.h */,
				96BECD482E375E2400052454 /* SCIMService.m */,
			);
			path = SCIM;
			sourceTree = "<group>";
		};
		96BECD4C2E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECD4A2E375E2400052454 /* SCCategorySocket.h */,
				96BECD4B2E375E2400052454 /* SCCategorySocket.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECD502E375E2400052454 /* Model */ = {
			isa = PBXGroup;
			children = (
				96BECD4E2E375E2400052454 /* SCSocketEventModel.h */,
				96BECD4F2E375E2400052454 /* SCSocketEventModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		96BECD532E375E2400052454 /* SCSocket */ = {
			isa = PBXGroup;
			children = (
				96BECD4C2E375E2400052454 /* API */,
				96BECD502E375E2400052454 /* Model */,
				96BECD512E375E2400052454 /* SCSocketService.h */,
				96BECD522E375E2400052454 /* SCSocketService.m */,
			);
			path = SCSocket;
			sourceTree = "<group>";
		};
		96BECD582E375E2400052454 /* layout */ = {
			isa = PBXGroup;
			children = (
				96BECD542E375E2400052454 /* SCAlignedCollectionViewFlowLayout.h */,
				96BECD552E375E2400052454 /* SCAlignedCollectionViewFlowLayout.m */,
				96BECD562E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.h */,
				96BECD572E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.m */,
			);
			path = layout;
			sourceTree = "<group>";
		};
		96BECD5D2E375E2400052454 /* Style */ = {
			isa = PBXGroup;
			children = (
				96BECD592E375E2400052454 /* SCCategoryColor.h */,
				96BECD5A2E375E2400052454 /* SCCategoryColor.m */,
				96BECD5B2E375E2400052454 /* SCGradientColors.h */,
				96BECD5C2E375E2400052454 /* SCGradientColors.m */,
			);
			path = Style;
			sourceTree = "<group>";
		};
		96BECD682E375E2400052454 /* Base */ = {
			isa = PBXGroup;
			children = (
				96BECD5E2E375E2400052454 /* MJRefreshAutoFooter.h */,
				96BECD5F2E375E2400052454 /* MJRefreshAutoFooter.m */,
				96BECD602E375E2400052454 /* MJRefreshComponent.h */,
				96BECD612E375E2400052454 /* MJRefreshComponent.m */,
				96BECD622E375E2400052454 /* MJRefreshFooter.h */,
				96BECD632E375E2400052454 /* MJRefreshFooter.m */,
				96BECD642E375E2400052454 /* MJRefreshHeader.h */,
				96BECD652E375E2400052454 /* MJRefreshHeader.m */,
				96BECD662E375E2400052454 /* MJRefreshTrailer.h */,
				96BECD672E375E2400052454 /* MJRefreshTrailer.m */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		96BECD6D2E375E2400052454 /* Footer */ = {
			isa = PBXGroup;
			children = (
				96BECD692E375E2400052454 /* MJRefreshAutoNormalFooter.h */,
				96BECD6A2E375E2400052454 /* MJRefreshAutoNormalFooter.m */,
				96BECD6B2E375E2400052454 /* MJRefreshAutoStateFooter.h */,
				96BECD6C2E375E2400052454 /* MJRefreshAutoStateFooter.m */,
			);
			path = Footer;
			sourceTree = "<group>";
		};
		96BECD722E375E2400052454 /* Header */ = {
			isa = PBXGroup;
			children = (
				96BECD6E2E375E2400052454 /* MJRefreshNormalHeader.h */,
				96BECD6F2E375E2400052454 /* MJRefreshNormalHeader.m */,
				96BECD702E375E2400052454 /* MJRefreshStateHeader.h */,
				96BECD712E375E2400052454 /* MJRefreshStateHeader.m */,
			);
			path = Header;
			sourceTree = "<group>";
		};
		96BECD732E375E2400052454 /* Custom */ = {
			isa = PBXGroup;
			children = (
				96BECD6D2E375E2400052454 /* Footer */,
				96BECD722E375E2400052454 /* Header */,
			);
			path = Custom;
			sourceTree = "<group>";
		};
		96BECD812E375E2400052454 /* MJRefresh */ = {
			isa = PBXGroup;
			children = (
				96BECD682E375E2400052454 /* Base */,
				96BECD732E375E2400052454 /* Custom */,
				96BECD742E375E2400052454 /* MJRefresh.h */,
				96BECD752E375E2400052454 /* MJRefreshConfig.h */,
				96BECD762E375E2400052454 /* MJRefreshConfig.m */,
				96BECD772E375E2400052454 /* MJRefreshConst.h */,
				96BECD782E375E2400052454 /* MJRefreshConst.m */,
				96BECD792E375E2400052454 /* UICollectionViewLayout+MJRefresh.h */,
				96BECD7A2E375E2400052454 /* UICollectionViewLayout+MJRefresh.m */,
				96BECD7B2E375E2400052454 /* UIScrollView+MJExtension.h */,
				96BECD7C2E375E2400052454 /* UIScrollView+MJExtension.m */,
				96BECD7D2E375E2400052454 /* UIScrollView+MJRefresh.h */,
				96BECD7E2E375E2400052454 /* UIScrollView+MJRefresh.m */,
				96BECD7F2E375E2400052454 /* UIView+MJExtension.h */,
				96BECD802E375E2400052454 /* UIView+MJExtension.m */,
			);
			path = MJRefresh;
			sourceTree = "<group>";
		};
		96BECD862E375E2400052454 /* View */ = {
			isa = PBXGroup;
			children = (
				96BECD812E375E2400052454 /* MJRefresh */,
				96BECD822E375E2400052454 /* SCCategoryButton.h */,
				96BECD832E375E2400052454 /* SCCategoryButton.m */,
				96BECD842E375E2400052454 /* SCGradientLabel.h */,
				96BECD852E375E2400052454 /* SCGradientLabel.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		96BECD882E375E2400052454 /* SCUIKit */ = {
			isa = PBXGroup;
			children = (
				96BECD582E375E2400052454 /* layout */,
				96BECD5D2E375E2400052454 /* Style */,
				96BECD862E375E2400052454 /* View */,
				96BECD872E375E2400052454 /* SCUIKit.h */,
			);
			path = SCUIKit;
			sourceTree = "<group>";
		};
		96BECD9F2E375E2400052454 /* Utils */ = {
			isa = PBXGroup;
			children = (
				96BECD892E375E2400052454 /* SCAppUtils.h */,
				96BECD8A2E375E2400052454 /* SCAppUtils.m */,
				96BECD8B2E375E2400052454 /* SCAVAudioSessionUtils.h */,
				96BECD8C2E375E2400052454 /* SCAVAudioSessionUtils.m */,
				96BECD8F2E375E2400052454 /* SCCryptoUtils.h */,
				96BECD902E375E2400052454 /* SCCryptoUtils.m */,
				96BECD912E375E2400052454 /* SCDataConverter.h */,
				96BECD922E375E2400052454 /* SCDataConverter.m */,
				96BECD932E375E2400052454 /* SCDictionaryHelper.h */,
				96BECD942E375E2400052454 /* SCDictionaryHelper.m */,
				96BECD952E375E2400052454 /* SCDictionaryKeys.h */,
				96BECD962E375E2400052454 /* SCDictionaryKeys.m */,
				96BECD972E375E2400052454 /* SCKeychainUtils.h */,
				96BECD982E375E2400052454 /* SCKeychainUtils.m */,
				96BECD992E375E2400052454 /* SCModelCompatibility.h */,
				96BECD9A2E375E2400052454 /* SCModelCompatibility.m */,
				96BECD9B2E375E2400052454 /* SCSafetyUtils.h */,
				96BECD9C2E375E2400052454 /* SCSafetyUtils.m */,
				96BECD9D2E375E2400052454 /* SCTrackingUtils.h */,
				96BECD9E2E375E2400052454 /* SCTrackingUtils.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		96BECDA02E375E2400052454 /* Commonts */ = {
			isa = PBXGroup;
			children = (
				96BECC632E375E2400052454 /* Auth */,
				96BECC792E375E2400052454 /* Bases */,
				96BECD002E375E2400052454 /* Components */,
				96BECD082E375E2400052454 /* Config */,
				96BECD252E375E2400052454 /* Extensions */,
				96BECD392E375E2400052454 /* Network */,
				96BECD3C2E375E2400052454 /* SCDefine */,
				96BECD492E375E2400052454 /* SCIM */,
				96BECD532E375E2400052454 /* SCSocket */,
				96BECD882E375E2400052454 /* SCUIKit */,
				96BECD9F2E375E2400052454 /* Utils */,
			);
			path = Commonts;
			sourceTree = "<group>";
		};
		96BECDA72E375E2400052454 /* Model */ = {
			isa = PBXGroup;
			children = (
				BED3F2A92E38A70C001745A6 /* SCBlockUserDictHelper.h */,
				BED3F2AA2E38A70C001745A6 /* SCBlockUserDictHelper.m */,
				96BECDA52E375E2400052454 /* SCUserBoolChangeModel.h */,
				96BECDA62E375E2400052454 /* SCUserBoolChangeModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		96BECDAA2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECDA82E375E2400052454 /* SCFollowListCell.h */,
				96BECDA92E375E2400052454 /* SCFollowListCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECDAD2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECDAA2E375E2400052454 /* Cells */,
				96BECDAB2E375E2400052454 /* SCFollowListViewController.h */,
				96BECDAC2E375E2400052454 /* SCFollowListViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECDAE2E375E2400052454 /* Follow */ = {
			isa = PBXGroup;
			children = (
				96BECDA72E375E2400052454 /* Model */,
				96BECDAD2E375E2400052454 /* Views */,
			);
			path = Follow;
			sourceTree = "<group>";
		};
		96BECDB12E375E2400052454 /* Services */ = {
			isa = PBXGroup;
			children = (
				96BECDAF2E375E2400052454 /* SCActionService.h */,
				96BECDB02E375E2400052454 /* SCActionService.m */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		96BECDB62E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECDB22E375E2400052454 /* SCAnchorActionSheetViewController.h */,
				96BECDB32E375E2400052454 /* SCAnchorActionSheetViewController.m */,
				96BECDB42E375E2400052454 /* SCBlockListViewController.h */,
				96BECDB52E375E2400052454 /* SCBlockListViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECDB72E375E2400052454 /* Actions */ = {
			isa = PBXGroup;
			children = (
				96BECDAE2E375E2400052454 /* Follow */,
				96BECDB12E375E2400052454 /* Services */,
				96BECDB62E375E2400052454 /* Views */,
			);
			path = Actions;
			sourceTree = "<group>";
		};
		96BECDBA2E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECDB82E375E2400052454 /* SCAnchorInfoViewModel.h */,
				96BECDB92E375E2400052454 /* SCAnchorInfoViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECDC12E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECDBB2E375E2400052454 /* SCGiftNumCell.h */,
				96BECDBC2E375E2400052454 /* SCGiftNumCell.m */,
				96BECDBD2E375E2400052454 /* SCSmallVideoCell.h */,
				96BECDBE2E375E2400052454 /* SCSmallVideoCell.m */,
				96BECDBF2E375E2400052454 /* SCTagCell.h */,
				96BECDC02E375E2400052454 /* SCTagCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECDD02E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECDC22E375E2400052454 /* SCAnchorInfoBottomView.h */,
				96BECDC32E375E2400052454 /* SCAnchorInfoBottomView.m */,
				96BECDC42E375E2400052454 /* SCAnchorInfoHeaderView.h */,
				96BECDC52E375E2400052454 /* SCAnchorInfoHeaderView.m */,
				96BECDC62E375E2400052454 /* SCCustomPageControl.h */,
				96BECDC72E375E2400052454 /* SCCustomPageControl.m */,
				96BECDC82E375E2400052454 /* SCGiftListView.h */,
				96BECDC92E375E2400052454 /* SCGiftListView.m */,
				96BECDCA2E375E2400052454 /* SCImageBrowserView.h */,
				96BECDCB2E375E2400052454 /* SCImageBrowserView.m */,
				96BECDCC2E375E2400052454 /* SCLabelListView.h */,
				96BECDCD2E375E2400052454 /* SCLabelListView.m */,
				96BECDCE2E375E2400052454 /* SCVideoHorizontalListView.h */,
				96BECDCF2E375E2400052454 /* SCVideoHorizontalListView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECDD32E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECDC12E375E2400052454 /* Cells */,
				96BECDD02E375E2400052454 /* SubViews */,
				96BECDD12E375E2400052454 /* SCAnchorInfoViewController.h */,
				96BECDD22E375E2400052454 /* SCAnchorInfoViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECDD42E375E2400052454 /* Info */ = {
			isa = PBXGroup;
			children = (
				96BECDBA2E375E2400052454 /* ViewModels */,
				96BECDD32E375E2400052454 /* Views */,
			);
			path = Info;
			sourceTree = "<group>";
		};
		96BECDD72E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECDD52E375E2400052454 /* SCUserRankModel.h */,
				96BECDD62E375E2400052454 /* SCUserRankModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECDDA2E375E2400052454 /* Service */ = {
			isa = PBXGroup;
			children = (
				96BECDD82E375E2400052454 /* SCAnchorService.h */,
				96BECDD92E375E2400052454 /* SCAnchorService.m */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		96BECDDD2E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECDDB2E375E2400052454 /* SCAnchorViewModel.h */,
				96BECDDC2E375E2400052454 /* SCAnchorViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECDE02E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECDDE2E375E2400052454 /* SCAnchorCell.h */,
				96BECDDF2E375E2400052454 /* SCAnchorCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECDE72E375E2400052454 /* SubTitle */ = {
			isa = PBXGroup;
			children = (
				96BECDE12E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.h */,
				96BECDE22E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.m */,
				96BECDE32E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.h */,
				96BECDE42E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.m */,
				96BECDE52E375E2400052454 /* SCAnchorSubTitleView.h */,
				96BECDE62E375E2400052454 /* SCAnchorSubTitleView.m */,
			);
			path = SubTitle;
			sourceTree = "<group>";
		};
		96BECDF22E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECDE02E375E2400052454 /* Cells */,
				96BECDE72E375E2400052454 /* SubTitle */,
				96BECDE82E375E2400052454 /* SCAnchorCollectionView.h */,
				96BECDE92E375E2400052454 /* SCAnchorCollectionView.m */,
				96BECDEA2E375E2400052454 /* SCGradientLineView.h */,
				96BECDEB2E375E2400052454 /* SCGradientLineView.m */,
				96BECDEC2E375E2400052454 /* SCOnlineStatusView.h */,
				96BECDED2E375E2400052454 /* SCOnlineStatusView.m */,
				96BECDEE2E375E2400052454 /* SCRoundedLabelView.h */,
				96BECDEF2E375E2400052454 /* SCRoundedLabelView.m */,
				96BECDF02E375E2400052454 /* SCWaterFallFlowLayout.h */,
				96BECDF12E375E2400052454 /* SCWaterFallFlowLayout.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECDF72E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECDF22E375E2400052454 /* SubViews */,
				96BECDF32E375E2400052454 /* SCAnchorSubTagViewController.h */,
				96BECDF42E375E2400052454 /* SCAnchorSubTagViewController.m */,
				96BECDF52E375E2400052454 /* SCAnchorViewController.h */,
				96BECDF62E375E2400052454 /* SCAnchorViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECDF82E375E2400052454 /* List */ = {
			isa = PBXGroup;
			children = (
				96BECDD72E375E2400052454 /* Models */,
				96BECDDA2E375E2400052454 /* Service */,
				96BECDDD2E375E2400052454 /* ViewModels */,
				96BECDF72E375E2400052454 /* Views */,
			);
			path = List;
			sourceTree = "<group>";
		};
		96BECDFD2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECDF92E375E2400052454 /* SCMediaItemView.h */,
				96BECDFA2E375E2400052454 /* SCMediaItemView.m */,
				96BECDFB2E375E2400052454 /* SCUserItemView.h */,
				96BECDFC2E375E2400052454 /* SCUserItemView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECE002E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECDFD2E375E2400052454 /* SubViews */,
				96BECDFE2E375E2400052454 /* SCFullScreenPreviewMediaViewController.h */,
				96BECDFF2E375E2400052454 /* SCFullScreenPreviewMediaViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE012E375E2400052454 /* Preview */ = {
			isa = PBXGroup;
			children = (
				96BECE002E375E2400052454 /* Views */,
			);
			path = Preview;
			sourceTree = "<group>";
		};
		96BECE042E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECE022E375E2400052454 /* SCRankingCell.h */,
				96BECE032E375E2400052454 /* SCRankingCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECE072E375E2400052454 /* RankingTitle */ = {
			isa = PBXGroup;
			children = (
				96BECE052E375E2400052454 /* SCRankingTitleView.h */,
				96BECE062E375E2400052454 /* SCRankingTitleView.m */,
			);
			path = RankingTitle;
			sourceTree = "<group>";
		};
		96BECE0E2E375E2400052454 /* Subviews */ = {
			isa = PBXGroup;
			children = (
				96BECE042E375E2400052454 /* Cells */,
				96BECE072E375E2400052454 /* RankingTitle */,
				96BECE082E375E2400052454 /* SCRankingHeaderView.h */,
				96BECE092E375E2400052454 /* SCRankingHeaderView.m */,
				96BECE0A2E375E2400052454 /* SCRankingItemView.h */,
				96BECE0B2E375E2400052454 /* SCRankingItemView.m */,
				96BECE0C2E375E2400052454 /* SCRankingListView.h */,
				96BECE0D2E375E2400052454 /* SCRankingListView.m */,
			);
			path = Subviews;
			sourceTree = "<group>";
		};
		96BECE0F2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE0E2E375E2400052454 /* Subviews */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE122E375E2400052454 /* Ranking */ = {
			isa = PBXGroup;
			children = (
				96BECE0F2E375E2400052454 /* Views */,
				96BECE102E375E2400052454 /* SCRankingViewController.h */,
				96BECE112E375E2400052454 /* SCRankingViewController.m */,
			);
			path = Ranking;
			sourceTree = "<group>";
		};
		96BECE132E375E2400052454 /* Anchor */ = {
			isa = PBXGroup;
			children = (
				96BECDB72E375E2400052454 /* Actions */,
				96BECDD42E375E2400052454 /* Info */,
				96BECDF82E375E2400052454 /* List */,
				96BECE012E375E2400052454 /* Preview */,
				96BECE122E375E2400052454 /* Ranking */,
			);
			path = Anchor;
			sourceTree = "<group>";
		};
		96BECE1B2E375E2400052454 /* Model */ = {
			isa = PBXGroup;
			children = (
				96BECE142E375E2400052454 /* SCCallSessionModel.h */,
				96BECE152E375E2400052454 /* SCCallSessionModel.m */,
				96BECE162E375E2400052454 /* SCCallSessionStatus.h */,
				96BECE172E375E2400052454 /* SCRearCameraConfigModel.h */,
				96BECE182E375E2400052454 /* SCRearCameraConfigModel.m */,
				96BECE192E375E2400052454 /* SCVideoCallChatMessageModel.h */,
				96BECE1A2E375E2400052454 /* SCVideoCallChatMessageModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		96BECE242E375E2400052454 /* DB */ = {
			isa = PBXGroup;
			children = (
				96BECE1C2E375E2400052454 /* SCCallDBManager.h */,
				96BECE1D2E375E2400052454 /* SCCallDBManager.m */,
				96BECE1E2E375E2400052454 /* SCDBActionModel.h */,
				96BECE1F2E375E2400052454 /* SCDBActionModel.m */,
				96BECE202E375E2400052454 /* SCNativeSQLiteDatabase.h */,
				96BECE212E375E2400052454 /* SCNativeSQLiteDatabase.m */,
				96BECE222E375E2400052454 /* SCNativeSQLiteResultSet.h */,
				96BECE232E375E2400052454 /* SCNativeSQLiteResultSet.m */,
			);
			path = DB;
			sourceTree = "<group>";
		};
		96BECE272E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECE242E375E2400052454 /* DB */,
				96BECE252E375E2400052454 /* SCCategoryAPIManagerCall.h */,
				96BECE262E375E2400052454 /* SCCategoryAPIManagerCall.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECE2C2E375E2400052454 /* Service */ = {
			isa = PBXGroup;
			children = (
				96BECE272E375E2400052454 /* API */,
				96BECE282E375E2400052454 /* SCCallService.h */,
				96BECE292E375E2400052454 /* SCCallService.m */,
				96BECE2A2E375E2400052454 /* SCCallServiceDelegate.h */,
				96BECE2B2E375E2400052454 /* SCVideoCallDelegate.h */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		96BECE332E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECE2D2E375E2400052454 /* SCCallHistoryCell.h */,
				96BECE2E2E375E2400052454 /* SCCallHistoryCell.m */,
				96BECE2F2E375E2400052454 /* SCTryAnchorAvatarCell.h */,
				96BECE302E375E2400052454 /* SCTryAnchorAvatarCell.m */,
				96BECE312E375E2400052454 /* SCVideoCallChatCell.h */,
				96BECE322E375E2400052454 /* SCVideoCallChatCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECE442E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECE332E375E2400052454 /* Cells */,
				96BECE342E375E2400052454 /* SCAskGiftTipView.h */,
				96BECE352E375E2400052454 /* SCAskGiftTipView.m */,
				96BECE362E375E2400052454 /* SCCallDisablePrompt.h */,
				96BECE372E375E2400052454 /* SCCallDisablePrompt.m */,
				96BECE382E375E2400052454 /* SCCallingView.h */,
				96BECE392E375E2400052454 /* SCCallingView.m */,
				96BECE3A2E375E2400052454 /* SCFreeTimeCountdownView.h */,
				96BECE3B2E375E2400052454 /* SCFreeTimeCountdownView.m */,
				96BECE3C2E375E2400052454 /* SCGiftTipView.h */,
				96BECE3D2E375E2400052454 /* SCGiftTipView.m */,
				96BECE3E2E375E2400052454 /* SCJoinChannelProgressView.h */,
				96BECE3F2E375E2400052454 /* SCJoinChannelProgressView.m */,
				96BECE402E375E2400052454 /* SCVideoCallChatInputView.h */,
				96BECE412E375E2400052454 /* SCVideoCallChatInputView.m */,
				96BECE422E375E2400052454 /* SCVideoCallRemainTimeCountdownView.h */,
				96BECE432E375E2400052454 /* SCVideoCallRemainTimeCountdownView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECE512E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE442E375E2400052454 /* SubViews */,
				96BECE452E375E2400052454 /* SCAnchorEvaluateViewController.h */,
				96BECE462E375E2400052454 /* SCAnchorEvaluateViewController.m */,
				96BECE472E375E2400052454 /* SCCallHangupPopup.h */,
				96BECE482E375E2400052454 /* SCCallHangupPopup.m */,
				96BECE492E375E2400052454 /* SCCallHistoryListViewController.h */,
				96BECE4A2E375E2400052454 /* SCCallHistoryListViewController.m */,
				96BECE4B2E375E2400052454 /* SCCallNotificationPopup.h */,
				96BECE4C2E375E2400052454 /* SCCallNotificationPopup.m */,
				96BECE4D2E375E2400052454 /* SCCallViewController.h */,
				96BECE4E2E375E2400052454 /* SCCallViewController.m */,
				96BECE4F2E375E2400052454 /* SCVideoCallViewController.h */,
				96BECE502E375E2400052454 /* SCVideoCallViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE522E375E2400052454 /* Calls */ = {
			isa = PBXGroup;
			children = (
				96BECE1B2E375E2400052454 /* Model */,
				96BECE2C2E375E2400052454 /* Service */,
				96BECE512E375E2400052454 /* Views */,
			);
			path = Calls;
			sourceTree = "<group>";
		};
		96BECE552E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECE532E375E2400052454 /* SCCategoryAPIManagerDiscover.h */,
				96BECE542E375E2400052454 /* SCCategoryAPIManagerDiscover.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECE562E375E2400052454 /* Service */ = {
			isa = PBXGroup;
			children = (
				96BECE552E375E2400052454 /* API */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		96BECE592E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECE572E375E2400052454 /* SCDiscoverBannerCell.h */,
				96BECE582E375E2400052454 /* SCDiscoverBannerCell.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECE5C2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE592E375E2400052454 /* SubViews */,
				96BECE5A2E375E2400052454 /* SCDiscoverViewController.h */,
				96BECE5B2E375E2400052454 /* SCDiscoverViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE5D2E375E2400052454 /* Discover */ = {
			isa = PBXGroup;
			children = (
				96BECE562E375E2400052454 /* Service */,
				96BECE5C2E375E2400052454 /* Views */,
			);
			path = Discover;
			sourceTree = "<group>";
		};
		96BECE602E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECE5E2E375E2400052454 /* SCCategoryAPIManagerFlashChat.h */,
				96BECE5F2E375E2400052454 /* SCCategoryAPIManagerFlashChat.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECE632E375E2400052454 /* Service */ = {
			isa = PBXGroup;
			children = (
				96BECE602E375E2400052454 /* API */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		96BECE682E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECE642E375E2400052454 /* SCFlashMatchSuccessPopupView.h */,
				96BECE652E375E2400052454 /* SCFlashMatchSuccessPopupView.m */,
				96BECE662E375E2400052454 /* SCFlashRouletteView.h */,
				96BECE672E375E2400052454 /* SCFlashRouletteView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECE6D2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE682E375E2400052454 /* SubViews */,
				96BECE692E375E2400052454 /* SCFlashChatMatchViewController.h */,
				96BECE6A2E375E2400052454 /* SCFlashChatMatchViewController.m */,
				96BECE6B2E375E2400052454 /* SCFlashChatViewController.h */,
				96BECE6C2E375E2400052454 /* SCFlashChatViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE6E2E375E2400052454 /* FlashChat */ = {
			isa = PBXGroup;
			children = (
				96BECE632E375E2400052454 /* Service */,
				96BECE6D2E375E2400052454 /* Views */,
			);
			path = FlashChat;
			sourceTree = "<group>";
		};
		96BECE712E375E2400052454 /* Game */ = {
			isa = PBXGroup;
			children = (
				96BECE6F2E375E2400052454 /* SCGameWebViewController.h */,
				96BECE702E375E2400052454 /* SCGameWebViewController.m */,
			);
			path = Game;
			sourceTree = "<group>";
		};
		96BECE762E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECE722E375E2400052454 /* SCGiftSendNumModel.h */,
				96BECE732E375E2400052454 /* SCGiftSendNumModel.m */,
				96BECE742E375E2400052454 /* SCGiftSendNumTipDisplayModel.h */,
				96BECE752E375E2400052454 /* SCGiftSendNumTipDisplayModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECE792E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECE772E375E2400052454 /* SCCategoryAPIManagerGift.h */,
				96BECE782E375E2400052454 /* SCCategoryAPIManagerGift.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECE7C2E375E2400052454 /* Service */ = {
			isa = PBXGroup;
			children = (
				96BECE792E375E2400052454 /* API */,
				96BECE7A2E375E2400052454 /* SCGiftService.h */,
				96BECE7B2E375E2400052454 /* SCGiftService.m */,
			);
			path = Service;
			sourceTree = "<group>";
		};
		96BECE7F2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECE7D2E375E2400052454 /* SCGiftCell.h */,
				96BECE7E2E375E2400052454 /* SCGiftCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECE822E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECE7F2E375E2400052454 /* Cells */,
				96BECE802E375E2400052454 /* SCGiftAnimPlayView.h */,
				96BECE812E375E2400052454 /* SCGiftAnimPlayView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECE852E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE822E375E2400052454 /* SubViews */,
				96BECE832E375E2400052454 /* SCGiftPopupViewController.h */,
				96BECE842E375E2400052454 /* SCGiftPopupViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE862E375E2400052454 /* Gift */ = {
			isa = PBXGroup;
			children = (
				96BECE762E375E2400052454 /* Models */,
				96BECE7C2E375E2400052454 /* Service */,
				96BECE852E375E2400052454 /* Views */,
			);
			path = Gift;
			sourceTree = "<group>";
		};
		96BECE892E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE872E375E2400052454 /* SCBannerView.h */,
				96BECE882E375E2400052454 /* SCBannerView.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE8C2E375E2400052454 /* Banner */ = {
			isa = PBXGroup;
			children = (
				96BECE892E375E2400052454 /* Views */,
				96BECE8A2E375E2400052454 /* SCBannerService.h */,
				96BECE8B2E375E2400052454 /* SCBannerService.m */,
			);
			path = Banner;
			sourceTree = "<group>";
		};
		96BECE8F2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE8D2E375E2400052454 /* SCStrongGuidePopoUp.h */,
				96BECE8E2E375E2400052454 /* SCStrongGuidePopoUp.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE902E375E2400052454 /* StrongGuide */ = {
			isa = PBXGroup;
			children = (
				96BECE8F2E375E2400052454 /* Views */,
			);
			path = StrongGuide;
			sourceTree = "<group>";
		};
		96BECE912E375E2400052454 /* Globa */ = {
			isa = PBXGroup;
			children = (
				96BECE8C2E375E2400052454 /* Banner */,
				96BECE902E375E2400052454 /* StrongGuide */,
			);
			path = Globa;
			sourceTree = "<group>";
		};
		96BECE942E375E2400052454 /* View */ = {
			isa = PBXGroup;
			children = (
				96BECE922E375E2400052454 /* SCHomeViewController.h */,
				96BECE932E375E2400052454 /* SCHomeViewController.m */,
			);
			path = View;
			sourceTree = "<group>";
		};
		96BECE952E375E2400052454 /* Home */ = {
			isa = PBXGroup;
			children = (
				96BECE912E375E2400052454 /* Globa */,
				96BECE942E375E2400052454 /* View */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		96BECE982E375E2400052454 /* Launch */ = {
			isa = PBXGroup;
			children = (
				96BECE962E375E2400052454 /* SCLaunchVCViewController.h */,
				96BECE972E375E2400052454 /* SCLaunchVCViewController.m */,
			);
			path = Launch;
			sourceTree = "<group>";
		};
		96BECE9B2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECE992E375E2400052454 /* SCLoginViewController.h */,
				96BECE9A2E375E2400052454 /* SCLoginViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECE9C2E375E2400052454 /* Login */ = {
			isa = PBXGroup;
			children = (
				96BECE9B2E375E2400052454 /* Views */,
			);
			path = Login;
			sourceTree = "<group>";
		};
		96BECE9E2E375E2400052454 /* Config */ = {
			isa = PBXGroup;
			children = (
				96BECE9D2E375E2400052454 /* SCIMUIConfig.h */,
			);
			path = Config;
			sourceTree = "<group>";
		};
		96BECEA32E375E2400052454 /* Manager */ = {
			isa = PBXGroup;
			children = (
				96BECE9F2E375E2400052454 /* SCVoicePlayerManager.h */,
				96BECEA02E375E2400052454 /* SCVoicePlayerManager.m */,
				96BECEA12E375E2400052454 /* SCVoiceRecorderManager.h */,
				96BECEA22E375E2400052454 /* SCVoiceRecorderManager.m */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		96BECEB02E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECEA42E375E2400052454 /* SCConversationDisplayModel.h */,
				96BECEA52E375E2400052454 /* SCConversationDisplayModel.m */,
				96BECEA62E375E2400052454 /* SCMessageDisplayModel.h */,
				96BECEA72E375E2400052454 /* SCMessageDisplayModel.m */,
				96BECEA82E375E2400052454 /* SCMessageTitleItemDisplayModel.h */,
				96BECEA92E375E2400052454 /* SCMessageTitleItemDisplayModel.m */,
				96BECEAA2E375E2400052454 /* SCNativeConversationModel.h */,
				96BECEAB2E375E2400052454 /* SCNativeConversationModel.m */,
				96BECEAC2E375E2400052454 /* SCNativeMessageModel.h */,
				96BECEAD2E375E2400052454 /* SCNativeMessageModel.m */,
				96BECEAE2E375E2400052454 /* SCRechargeCardMessageContentModel.h */,
				96BECEAF2E375E2400052454 /* SCRechargeCardMessageContentModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECEBF2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECEB12E375E2400052454 /* SCConversationListCell.h */,
				96BECEB22E375E2400052454 /* SCConversationListCell.m */,
				96BECEB32E375E2400052454 /* SCFileMessageCell.h */,
				96BECEB42E375E2400052454 /* SCFileMessageCell.m */,
				96BECEB52E375E2400052454 /* SCGiftMessageCell.h */,
				96BECEB62E375E2400052454 /* SCGiftMessageCell.m */,
				96BECEB72E375E2400052454 /* SCImageMessageCell.h */,
				96BECEB82E375E2400052454 /* SCImageMessageCell.m */,
				96BECEB92E375E2400052454 /* SCRechargeCardMessageCell.h */,
				96BECEBA2E375E2400052454 /* SCRechargeCardMessageCell.m */,
				96BECEBB2E375E2400052454 /* SCTextMessageCell.h */,
				96BECEBC2E375E2400052454 /* SCTextMessageCell.m */,
				96BECEBD2E375E2400052454 /* SCVoiceMessageCell.h */,
				96BECEBE2E375E2400052454 /* SCVoiceMessageCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECEC22E375E2400052454 /* SCMorePanelView */ = {
			isa = PBXGroup;
			children = (
				96BECEC02E375E2400052454 /* SCMorePanelView.h */,
				96BECEC12E375E2400052454 /* SCMorePanelView.m */,
			);
			path = SCMorePanelView;
			sourceTree = "<group>";
		};
		96BECEC72E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECEBF2E375E2400052454 /* Cells */,
				96BECEC22E375E2400052454 /* SCMorePanelView */,
				96BECEC32E375E2400052454 /* SCUserBaseInfoView.h */,
				96BECEC42E375E2400052454 /* SCUserBaseInfoView.m */,
				96BECEC52E375E2400052454 /* SCVoiceRecordingView.h */,
				96BECEC62E375E2400052454 /* SCVoiceRecordingView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECECE2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECEC72E375E2400052454 /* SubViews */,
				96BECEC82E375E2400052454 /* SCConversationInfoViewController.h */,
				96BECEC92E375E2400052454 /* SCConversationInfoViewController.m */,
				96BECECA2E375E2400052454 /* SCConversationListViewController.h */,
				96BECECB2E375E2400052454 /* SCConversationListViewController.m */,
				96BECECC2E375E2400052454 /* SCMessageViewController.h */,
				96BECECD2E375E2400052454 /* SCMessageViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECED02E375E2400052454 /* Conversation */ = {
			isa = PBXGroup;
			children = (
				96BECEA32E375E2400052454 /* Manager */,
				96BECEB02E375E2400052454 /* Models */,
				96BECECE2E375E2400052454 /* Views */,
			);
			path = Conversation;
			sourceTree = "<group>";
		};
		96BECED62E375E2400052454 /* Utils */ = {
			isa = PBXGroup;
			children = (
				96BECED42E375E2400052454 /* SCRobotCustomerDictionaryHelper.h */,
				96BECED52E375E2400052454 /* SCRobotCustomerDictionaryHelper.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		96BECEDD2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECED72E375E2400052454 /* SCRobotCustomerAnswerCell.h */,
				96BECED82E375E2400052454 /* SCRobotCustomerAnswerCell.m */,
				96BECED92E375E2400052454 /* SCRobotCustomerQuestionCell.h */,
				96BECEDA2E375E2400052454 /* SCRobotCustomerQuestionCell.m */,
				96BECEDB2E375E2400052454 /* SCRobotCustomerQuestionSetCell.h */,
				96BECEDC2E375E2400052454 /* SCRobotCustomerQuestionSetCell.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECEE02E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECEDD2E375E2400052454 /* SubViews */,
				96BECEDE2E375E2400052454 /* SCRobotCustomerServiceViewController.h */,
				96BECEDF2E375E2400052454 /* SCRobotCustomerServiceViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECEE12E375E2400052454 /* RobotCustomerService */ = {
			isa = PBXGroup;
			children = (
				96BECED62E375E2400052454 /* Utils */,
				96BECEE02E375E2400052454 /* Views */,
			);
			path = RobotCustomerService;
			sourceTree = "<group>";
		};
		96BECEE22E375E2400052454 /* Message */ = {
			isa = PBXGroup;
			children = (
				96BECE9E2E375E2400052454 /* Config */,
				96BECED02E375E2400052454 /* Conversation */,
				96BECEE12E375E2400052454 /* RobotCustomerService */,
			);
			path = Message;
			sourceTree = "<group>";
		};
		96BECEE52E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECEE32E375E2400052454 /* SCAboutHederView.h */,
				96BECEE42E375E2400052454 /* SCAboutHederView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECEE82E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECEE52E375E2400052454 /* SubViews */,
				96BECEE62E375E2400052454 /* SCAboutViewController.h */,
				96BECEE72E375E2400052454 /* SCAboutViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECEE92E375E2400052454 /* About */ = {
			isa = PBXGroup;
			children = (
				96BECEE82E375E2400052454 /* Views */,
			);
			path = About;
			sourceTree = "<group>";
		};
		96BECEEC2E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECEEA2E375E2400052454 /* SCPersionEditDisplayModel.h */,
				96BECEEB2E375E2400052454 /* SCPersionEditDisplayModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECEEF2E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECEED2E375E2400052454 /* SCPersonalEditViewModel.h */,
				96BECEEE2E375E2400052454 /* SCPersonalEditViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECEF42E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECEF02E375E2400052454 /* SCAvatarActivityIndicatorView.h */,
				96BECEF12E375E2400052454 /* SCAvatarActivityIndicatorView.m */,
				96BECEF22E375E2400052454 /* SCLimitTextView.h */,
				96BECEF32E375E2400052454 /* SCLimitTextView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECEF72E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECEF42E375E2400052454 /* SubViews */,
				96BECEF52E375E2400052454 /* SCPersonalEditViewController.h */,
				96BECEF62E375E2400052454 /* SCPersonalEditViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECEF82E375E2400052454 /* Edit */ = {
			isa = PBXGroup;
			children = (
				96BECEEC2E375E2400052454 /* Models */,
				96BECEEF2E375E2400052454 /* ViewModels */,
				96BECEF72E375E2400052454 /* Views */,
			);
			path = Edit;
			sourceTree = "<group>";
		};
		96BECEFB2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECEF92E375E2400052454 /* SCMyLevelCell.h */,
				96BECEFA2E375E2400052454 /* SCMyLevelCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECEFE2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECEFB2E375E2400052454 /* Cells */,
				96BECEFC2E375E2400052454 /* SCMyLevelHeaderView.h */,
				96BECEFD2E375E2400052454 /* SCMyLevelHeaderView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECF012E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECEFE2E375E2400052454 /* SubViews */,
				96BECEFF2E375E2400052454 /* SCMyLevelViewController.h */,
				96BECF002E375E2400052454 /* SCMyLevelViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECF022E375E2400052454 /* Level */ = {
			isa = PBXGroup;
			children = (
				96BECF012E375E2400052454 /* Views */,
			);
			path = Level;
			sourceTree = "<group>";
		};
		96BECF052E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECF032E375E2400052454 /* SCPersonalItemDisplayModel.h */,
				96BECF042E375E2400052454 /* SCPersonalItemDisplayModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECF082E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECF062E375E2400052454 /* SCPersonalViewModel.h */,
				96BECF072E375E2400052454 /* SCPersonalViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECF0B2E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECF092E375E2400052454 /* SCPersonalItemCell.h */,
				96BECF0A2E375E2400052454 /* SCPersonalItemCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECF0E2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECF0B2E375E2400052454 /* Cells */,
				96BECF0C2E375E2400052454 /* SCPersonalHeaderView.h */,
				96BECF0D2E375E2400052454 /* SCPersonalHeaderView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECF112E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECF0E2E375E2400052454 /* SubViews */,
				96BECF0F2E375E2400052454 /* SCPersonalViewController.h */,
				96BECF102E375E2400052454 /* SCPersonalViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECF122E375E2400052454 /* Me */ = {
			isa = PBXGroup;
			children = (
				96BECF052E375E2400052454 /* Models */,
				96BECF082E375E2400052454 /* ViewModels */,
				96BECF112E375E2400052454 /* Views */,
			);
			path = Me;
			sourceTree = "<group>";
		};
		96BECF152E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECF132E375E2400052454 /* SCLanguageModel.h */,
				96BECF142E375E2400052454 /* SCLanguageModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECF182E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECF162E375E2400052454 /* SCLanguageCell.h */,
				96BECF172E375E2400052454 /* SCLanguageCell.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECF1D2E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECF182E375E2400052454 /* SubViews */,
				96BECF192E375E2400052454 /* SCLanguageViewController.h */,
				96BECF1A2E375E2400052454 /* SCLanguageViewController.m */,
				96BECF1B2E375E2400052454 /* SCSettingViewController.h */,
				96BECF1C2E375E2400052454 /* SCSettingViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECF1E2E375E2400052454 /* Setting */ = {
			isa = PBXGroup;
			children = (
				96BECF152E375E2400052454 /* Models */,
				96BECF1D2E375E2400052454 /* Views */,
			);
			path = Setting;
			sourceTree = "<group>";
		};
		96BECF1F2E375E2400052454 /* Personal */ = {
			isa = PBXGroup;
			children = (
				96BECEE92E375E2400052454 /* About */,
				96BECEF82E375E2400052454 /* Edit */,
				96BECF022E375E2400052454 /* Level */,
				96BECF122E375E2400052454 /* Me */,
				96BECF1E2E375E2400052454 /* Setting */,
			);
			path = Personal;
			sourceTree = "<group>";
		};
		96BECF252E375E2400052454 /* Models */ = {
			isa = PBXGroup;
			children = (
				96BECF202E375E2400052454 /* SCActivityPromotionDisplayModel.h */,
				96BECF212E375E2400052454 /* SCActivityPromotionDisplayModel.m */,
				96BECF232E375E2400052454 /* SCPromotionDisplayModel.h */,
				96BECF242E375E2400052454 /* SCPromotionDisplayModel.m */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		96BECF282E375E2400052454 /* API */ = {
			isa = PBXGroup;
			children = (
				96BECF262E375E2400052454 /* SCCategoryAPIManagerCoins.h */,
				96BECF272E375E2400052454 /* SCCategoryAPIManagerCoins.m */,
			);
			path = API;
			sourceTree = "<group>";
		};
		96BECF2B2E375E2400052454 /* Services */ = {
			isa = PBXGroup;
			children = (
				96BECF282E375E2400052454 /* API */,
				96BECF292E375E2400052454 /* SCCoinsService.h */,
				96BECF2A2E375E2400052454 /* SCCoinsService.m */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		96BECF2E2E375E2400052454 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				96BECF2C2E375E2400052454 /* SCCoinsStoreViewModel.h */,
				96BECF2D2E375E2400052454 /* SCCoinsStoreViewModel.m */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		96BECF332E375E2400052454 /* Cells */ = {
			isa = PBXGroup;
			children = (
				96BECF2F2E375E2400052454 /* SCCoinsBigCollectionViewCell.h */,
				96BECF302E375E2400052454 /* SCCoinsBigCollectionViewCell.m */,
				96BECF312E375E2400052454 /* SCCoinsCollectionViewCell.h */,
				96BECF322E375E2400052454 /* SCCoinsCollectionViewCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		96BECF3A2E375E2400052454 /* SubViews */ = {
			isa = PBXGroup;
			children = (
				96BECF332E375E2400052454 /* Cells */,
				96BECF342E375E2400052454 /* SCCoinsFullScreenHeaderView.h */,
				96BECF352E375E2400052454 /* SCCoinsFullScreenHeaderView.m */,
				96BECF362E375E2400052454 /* SCCoinsListView.h */,
				96BECF372E375E2400052454 /* SCCoinsListView.m */,
				96BECF382E375E2400052454 /* SCMyCoinsView.h */,
				96BECF392E375E2400052454 /* SCMyCoinsView.m */,
			);
			path = SubViews;
			sourceTree = "<group>";
		};
		96BECF452E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECF3A2E375E2400052454 /* SubViews */,
				96BECF3B2E375E2400052454 /* SCAnchorCoinsViewController.h */,
				96BECF3C2E375E2400052454 /* SCAnchorCoinsViewController.m */,
				96BECF3D2E375E2400052454 /* SCCoinsFullScreenViewController.h */,
				96BECF3E2E375E2400052454 /* SCCoinsFullScreenViewController.m */,
				96BECF3F2E375E2400052454 /* SCCoinsPopupViewController.h */,
				96BECF402E375E2400052454 /* SCCoinsPopupViewController.m */,
				96BECF412E375E2400052454 /* SCContinueRechargePopup.h */,
				96BECF422E375E2400052454 /* SCContinueRechargePopup.m */,
				96BECF432E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.h */,
				96BECF442E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECF462E375E2400052454 /* Coins */ = {
			isa = PBXGroup;
			children = (
				96BECF252E375E2400052454 /* Models */,
				96BECF2B2E375E2400052454 /* Services */,
				96BECF2E2E375E2400052454 /* ViewModels */,
				96BECF452E375E2400052454 /* Views */,
			);
			path = Coins;
			sourceTree = "<group>";
		};
		96BECF512E375E2400052454 /* FloatingButton */ = {
			isa = PBXGroup;
			children = (
				96BECF472E375E2400052454 /* SCActivityFloatingView.h */,
				96BECF482E375E2400052454 /* SCActivityFloatingView.m */,
				96BECF492E375E2400052454 /* SCCoinStoeFloatingView.h */,
				96BECF4A2E375E2400052454 /* SCCoinStoeFloatingView.m */,
				96BECF4B2E375E2400052454 /* SCFloatingLayoutView.h */,
				96BECF4C2E375E2400052454 /* SCFloatingLayoutView.m */,
				96BECF4D2E375E2400052454 /* SCFloatingView.h */,
				96BECF4E2E375E2400052454 /* SCFloatingView.m */,
				96BECF4F2E375E2400052454 /* SCPromotionFloatingView.h */,
				96BECF502E375E2400052454 /* SCPromotionFloatingView.m */,
			);
			path = FloatingButton;
			sourceTree = "<group>";
		};
		96BECF582E375E2400052454 /* Views */ = {
			isa = PBXGroup;
			children = (
				96BECF512E375E2400052454 /* FloatingButton */,
				96BECF522E375E2400052454 /* SCActivityPromotionPopup.h */,
				96BECF532E375E2400052454 /* SCActivityPromotionPopup.m */,
				96BECF542E375E2400052454 /* SCNewUserAwardPopup.h */,
				96BECF552E375E2400052454 /* SCNewUserAwardPopup.m */,
				96BECF562E375E2400052454 /* SCNewUserPromotionPopup.h */,
				96BECF572E375E2400052454 /* SCNewUserPromotionPopup.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		96BECF592E375E2400052454 /* Promotion */ = {
			isa = PBXGroup;
			children = (
				96BECF582E375E2400052454 /* Views */,
			);
			path = Promotion;
			sourceTree = "<group>";
		};
		96BECF5A2E375E2400052454 /* Recharge */ = {
			isa = PBXGroup;
			children = (
				96BECF462E375E2400052454 /* Coins */,
				96BECF592E375E2400052454 /* Promotion */,
			);
			path = Recharge;
			sourceTree = "<group>";
		};
		96BECF5B2E375E2400052454 /* Modules */ = {
			isa = PBXGroup;
			children = (
				96BECE132E375E2400052454 /* Anchor */,
				96BECE522E375E2400052454 /* Calls */,
				96BECE5D2E375E2400052454 /* Discover */,
				96BECE6E2E375E2400052454 /* FlashChat */,
				96BECE712E375E2400052454 /* Game */,
				96BECE862E375E2400052454 /* Gift */,
				96BECE952E375E2400052454 /* Home */,
				96BECE982E375E2400052454 /* Launch */,
				96BECE9C2E375E2400052454 /* Login */,
				96BECEE22E375E2400052454 /* Message */,
				96BECF1F2E375E2400052454 /* Personal */,
				96BECF5A2E375E2400052454 /* Recharge */,
			);
			path = Modules;
			sourceTree = "<group>";
		};
		96BECF622E375E2400052454 /* font_family */ = {
			isa = PBXGroup;
			children = (
				96BECF5C2E375E2400052454 /* BaiJamjuree-Bold.ttf */,
				96BECF5D2E375E2400052454 /* BaiJamjuree-BoldItalic.ttf */,
				96BECF5E2E375E2400052454 /* BaiJamjuree-Italic.ttf */,
				96BECF5F2E375E2400052454 /* BaiJamjuree-Regular.ttf */,
				96BECF602E375E2400052454 /* BaiJamjuree-SemiBold.ttf */,
				96BECF612E375E2400052454 /* BaiJamjuree-SemiBoldItalic.ttf */,
			);
			path = font_family;
			sourceTree = "<group>";
		};
		96BED0842E375E2400052454 /* images */ = {
			isa = PBXGroup;
			children = (
				96BECF632E375E2400052454 /* <EMAIL> */,
				96BECF642E375E2400052454 /* <EMAIL> */,
				96BECF652E375E2400052454 /* <EMAIL> */,
				96BECF662E375E2400052454 /* backspace_n.png */,
				96BECF672E375E2400052454 /* <EMAIL> */,
				96BECF682E375E2400052454 /* <EMAIL> */,
				96BECF692E375E2400052454 /* <EMAIL> */,
				96BECF6A2E375E2400052454 /* bell_n.png */,
				96BECF6B2E375E2400052454 /* <EMAIL> */,
				96BECF6C2E375E2400052454 /* bell_s.png */,
				96BECF6D2E375E2400052454 /* <EMAIL> */,
				96BECF6E2E375E2400052454 /* <EMAIL> */,
				96BECF6F2E375E2400052454 /* <EMAIL> */,
				96BECF702E375E2400052454 /* <EMAIL> */,
				96BECF712E375E2400052454 /* <EMAIL> */,
				96BECF722E375E2400052454 /* <EMAIL> */,
				96BECF732E375E2400052454 /* <EMAIL> */,
				96BECF742E375E2400052454 /* <EMAIL> */,
				96BECF752E375E2400052454 /* <EMAIL> */,
				96BECF762E375E2400052454 /* <EMAIL> */,
				96BECF772E375E2400052454 /* <EMAIL> */,
				96BECF782E375E2400052454 /* <EMAIL> */,
				96BECF792E375E2400052454 /* <EMAIL> */,
				96BECF7A2E375E2400052454 /* <EMAIL> */,
				96BECF7B2E375E2400052454 /* <EMAIL> */,
				96BECF7C2E375E2400052454 /* <EMAIL> */,
				96BECF7D2E375E2400052454 /* <EMAIL> */,
				96BECF7E2E375E2400052454 /* <EMAIL> */,
				96BECF7F2E375E2400052454 /* <EMAIL> */,
				96BECF802E375E2400052454 /* <EMAIL> */,
				96BECF812E375E2400052454 /* <EMAIL> */,
				96BECF822E375E2400052454 /* <EMAIL> */,
				96BECF832E375E2400052454 /* <EMAIL> */,
				96BECF842E375E2400052454 /* <EMAIL> */,
				96BECF852E375E2400052454 /* <EMAIL> */,
				96BECF862E375E2400052454 /* <EMAIL> */,
				96BECF872E375E2400052454 /* <EMAIL> */,
				96BECF882E375E2400052454 /* <EMAIL> */,
				96BECF892E375E2400052454 /* <EMAIL> */,
				96BECF8A2E375E2400052454 /* <EMAIL> */,
				96BECF8B2E375E2400052454 /* <EMAIL> */,
				96BECF8C2E375E2400052454 /* <EMAIL> */,
				96BECF8D2E375E2400052454 /* <EMAIL> */,
				96BECF8E2E375E2400052454 /* <EMAIL> */,
				96BECF8F2E375E2400052454 /* <EMAIL> */,
				96BECF902E375E2400052454 /* <EMAIL> */,
				96BECF912E375E2400052454 /* <EMAIL> */,
				96BECF922E375E2400052454 /* <EMAIL> */,
				96BECF932E375E2400052454 /* <EMAIL> */,
				96BECF942E375E2400052454 /* <EMAIL> */,
				96BECF952E375E2400052454 /* <EMAIL> */,
				96BECF962E375E2400052454 /* <EMAIL> */,
				96BECF972E375E2400052454 /* <EMAIL> */,
				96BECF982E375E2400052454 /* <EMAIL> */,
				96BECF992E375E2400052454 /* <EMAIL> */,
				96BECF9A2E375E2400052454 /* <EMAIL> */,
				96BECF9B2E375E2400052454 /* <EMAIL> */,
				96BECF9C2E375E2400052454 /* <EMAIL> */,
				96BECF9D2E375E2400052454 /* <EMAIL> */,
				96BECF9E2E375E2400052454 /* <EMAIL> */,
				96BECF9F2E375E2400052454 /* <EMAIL> */,
				96BECFA02E375E2400052454 /* <EMAIL> */,
				96BECFA12E375E2400052454 /* <EMAIL> */,
				96BECFA22E375E2400052454 /* <EMAIL> */,
				96BECFA32E375E2400052454 /* <EMAIL> */,
				96BECFA42E375E2400052454 /* <EMAIL> */,
				96BECFA52E375E2400052454 /* <EMAIL> */,
				96BECFA62E375E2400052454 /* <EMAIL> */,
				96BECFA72E375E2400052454 /* <EMAIL> */,
				96BECFA82E375E2400052454 /* <EMAIL> */,
				96BECFA92E375E2400052454 /* <EMAIL> */,
				96BECFAA2E375E2400052454 /* <EMAIL> */,
				96BECFAB2E375E2400052454 /* <EMAIL> */,
				96BECFAC2E375E2400052454 /* <EMAIL> */,
				96BECFAD2E375E2400052454 /* <EMAIL> */,
				96BECFAE2E375E2400052454 /* <EMAIL> */,
				96BECFAF2E375E2400052454 /* <EMAIL> */,
				96BECFB02E375E2400052454 /* <EMAIL> */,
				96BECFB12E375E2400052454 /* <EMAIL> */,
				96BECFB22E375E2400052454 /* <EMAIL> */,
				96BECFB32E375E2400052454 /* <EMAIL> */,
				96BECFB42E375E2400052454 /* <EMAIL> */,
				96BECFB52E375E2400052454 /* <EMAIL> */,
				96BECFB62E375E2400052454 /* <EMAIL> */,
				96BECFB72E375E2400052454 /* <EMAIL> */,
				96BECFB82E375E2400052454 /* car_n.png */,
				96BECFB92E375E2400052454 /* <EMAIL> */,
				96BECFBA2E375E2400052454 /* car_s.png */,
				96BECFBB2E375E2400052454 /* <EMAIL> */,
				96BECFBC2E375E2400052454 /* characters_n.png */,
				96BECFBD2E375E2400052454 /* <EMAIL> */,
				96BECFBE2E375E2400052454 /* characters_s.png */,
				96BECFBF2E375E2400052454 /* <EMAIL> */,
				96BECFC02E375E2400052454 /* face_n.png */,
				96BECFC12E375E2400052454 /* <EMAIL> */,
				96BECFC22E375E2400052454 /* face_s.png */,
				96BECFC32E375E2400052454 /* <EMAIL> */,
				96BECFC42E375E2400052454 /* flower_n.png */,
				96BECFC52E375E2400052454 /* <EMAIL> */,
				96BECFC62E375E2400052454 /* flower_s.png */,
				96BECFC72E375E2400052454 /* <EMAIL> */,
				96BECFC82E375E2400052454 /* <EMAIL> */,
				96BECFC92E375E2400052454 /* <EMAIL> */,
				96BECFCA2E375E2400052454 /* <EMAIL> */,
				96BECFCB2E375E2400052454 /* <EMAIL> */,
				96BECFCC2E375E2400052454 /* <EMAIL> */,
				96BECFCD2E375E2400052454 /* <EMAIL> */,
				96BECFCE2E375E2400052454 /* <EMAIL> */,
				96BECFCF2E375E2400052454 /* <EMAIL> */,
				96BECFD02E375E2400052454 /* <EMAIL> */,
				96BECFD12E375E2400052454 /* <EMAIL> */,
				96BECFD22E375E2400052454 /* <EMAIL> */,
				96BECFD32E375E2400052454 /* <EMAIL> */,
				96BECFD42E375E2400052454 /* <EMAIL> */,
				96BECFD52E375E2400052454 /* <EMAIL> */,
				96BECFD62E375E2400052454 /* <EMAIL> */,
				96BECFD72E375E2400052454 /* <EMAIL> */,
				96BECFD82E375E2400052454 /* <EMAIL> */,
				96BECFD92E375E2400052454 /* <EMAIL> */,
				96BECFDA2E375E2400052454 /* <EMAIL> */,
				96BECFDB2E375E2400052454 /* <EMAIL> */,
				96BECFDC2E375E2400052454 /* <EMAIL> */,
				96BECFDD2E375E2400052454 /* <EMAIL> */,
				96BECFDE2E375E2400052454 /* <EMAIL> */,
				96BECFDF2E375E2400052454 /* <EMAIL> */,
				96BECFE02E375E2400052454 /* <EMAIL> */,
				96BECFE12E375E2400052454 /* <EMAIL> */,
				96BECFE22E375E2400052454 /* <EMAIL> */,
				96BECFE32E375E2400052454 /* <EMAIL> */,
				96BECFE42E375E2400052454 /* <EMAIL> */,
				96BECFE52E375E2400052454 /* <EMAIL> */,
				96BECFE62E375E2400052454 /* <EMAIL> */,
				96BECFE72E375E2400052454 /* <EMAIL> */,
				96BECFE82E375E2400052454 /* <EMAIL> */,
				96BECFE92E375E2400052454 /* <EMAIL> */,
				96BECFEA2E375E2400052454 /* <EMAIL> */,
				96BECFEB2E375E2400052454 /* <EMAIL> */,
				96BECFEC2E375E2400052454 /* <EMAIL> */,
				96BECFED2E375E2400052454 /* <EMAIL> */,
				96BECFEE2E375E2400052454 /* <EMAIL> */,
				96BECFEF2E375E2400052454 /* <EMAIL> */,
				96BECFF02E375E2400052454 /* <EMAIL> */,
				96BECFF12E375E2400052454 /* <EMAIL> */,
				96BECFF22E375E2400052454 /* <EMAIL> */,
				96BECFF32E375E2400052454 /* <EMAIL> */,
				96BECFF42E375E2400052454 /* <EMAIL> */,
				96BECFF52E375E2400052454 /* <EMAIL> */,
				96BECFF62E375E2400052454 /* <EMAIL> */,
				96BECFF72E375E2400052454 /* <EMAIL> */,
				96BECFF82E375E2400052454 /* <EMAIL> */,
				96BECFF92E375E2400052454 /* <EMAIL> */,
				96BECFFA2E375E2400052454 /* <EMAIL> */,
				96BECFFB2E375E2400052454 /* <EMAIL> */,
				96BECFFC2E375E2400052454 /* <EMAIL> */,
				96BECFFD2E375E2400052454 /* <EMAIL> */,
				96BECFFE2E375E2400052454 /* <EMAIL> */,
				96BECFFF2E375E2400052454 /* <EMAIL> */,
				96BED0002E375E2400052454 /* <EMAIL> */,
				96BED0012E375E2400052454 /* <EMAIL> */,
				96BED0022E375E2400052454 /* <EMAIL> */,
				96BED0032E375E2400052454 /* <EMAIL> */,
				96BED0042E375E2400052454 /* <EMAIL> */,
				96BED0052E375E2400052454 /* <EMAIL> */,
				96BED0062E375E2400052454 /* <EMAIL> */,
				96BED0072E375E2400052454 /* <EMAIL> */,
				96BED0082E375E2400052454 /* <EMAIL> */,
				96BED0092E375E2400052454 /* <EMAIL> */,
				96BED00A2E375E2400052454 /* <EMAIL> */,
				96BED00B2E375E2400052454 /* <EMAIL> */,
				96BED00C2E375E2400052454 /* <EMAIL> */,
				96BED00D2E375E2400052454 /* <EMAIL> */,
				96BED00E2E375E2400052454 /* <EMAIL> */,
				96BED00F2E375E2400052454 /* <EMAIL> */,
				96BED0102E375E2400052454 /* <EMAIL> */,
				96BED0112E375E2400052454 /* <EMAIL> */,
				96BED0122E375E2400052454 /* <EMAIL> */,
				96BED0132E375E2400052454 /* <EMAIL> */,
				96BED0142E375E2400052454 /* <EMAIL> */,
				96BED0152E375E2400052454 /* <EMAIL> */,
				96BED0162E375E2400052454 /* <EMAIL> */,
				96BED0172E375E2400052454 /* <EMAIL> */,
				96BED0182E375E2400052454 /* <EMAIL> */,
				96BED0192E375E2400052454 /* <EMAIL> */,
				96BED01A2E375E2400052454 /* <EMAIL> */,
				96BED01B2E375E2400052454 /* <EMAIL> */,
				96BED01C2E375E2400052454 /* <EMAIL> */,
				96BED01D2E375E2400052454 /* <EMAIL> */,
				96BED01E2E375E2400052454 /* <EMAIL> */,
				96BED01F2E375E2400052454 /* <EMAIL> */,
				96BED0202E375E2400052454 /* <EMAIL> */,
				96BED0212E375E2400052454 /* <EMAIL> */,
				96BED0222E375E2400052454 /* <EMAIL> */,
				96BED0232E375E2400052454 /* <EMAIL> */,
				96BED0242E375E2400052454 /* <EMAIL> */,
				96BED0252E375E2400052454 /* <EMAIL> */,
				96BED0262E375E2400052454 /* <EMAIL> */,
				96BED0272E375E2400052454 /* <EMAIL> */,
				96BED0282E375E2400052454 /* <EMAIL> */,
				96BED0292E375E2400052454 /* <EMAIL> */,
				96BED02A2E375E2400052454 /* <EMAIL> */,
				96BED02B2E375E2400052454 /* <EMAIL> */,
				96BED02C2E375E2400052454 /* <EMAIL> */,
				96BED02D2E375E2400052454 /* <EMAIL> */,
				96BED02E2E375E2400052454 /* <EMAIL> */,
				96BED02F2E375E2400052454 /* <EMAIL> */,
				96BED0302E375E2400052454 /* <EMAIL> */,
				96BED0312E375E2400052454 /* <EMAIL> */,
				96BED0322E375E2400052454 /* <EMAIL> */,
				96BED0332E375E2400052454 /* <EMAIL> */,
				96BED0342E375E2400052454 /* <EMAIL> */,
				96BED0352E375E2400052454 /* <EMAIL> */,
				96BED0362E375E2400052454 /* <EMAIL> */,
				96BED0372E375E2400052454 /* <EMAIL> */,
				96BED0382E375E2400052454 /* <EMAIL> */,
				96BED0392E375E2400052454 /* <EMAIL> */,
				96BED03A2E375E2400052454 /* <EMAIL> */,
				96BED03B2E375E2400052454 /* <EMAIL> */,
				96BED03C2E375E2400052454 /* <EMAIL> */,
				96BED03D2E375E2400052454 /* <EMAIL> */,
				96BED03E2E375E2400052454 /* <EMAIL> */,
				96BED03F2E375E2400052454 /* <EMAIL> */,
				96BED0402E375E2400052454 /* <EMAIL> */,
				96BED0412E375E2400052454 /* <EMAIL> */,
				96BED0422E375E2400052454 /* <EMAIL> */,
				96BED0432E375E2400052454 /* <EMAIL> */,
				96BED0442E375E2400052454 /* <EMAIL> */,
				96BED0452E375E2400052454 /* <EMAIL> */,
				96BED0462E375E2400052454 /* <EMAIL> */,
				96BED0472E375E2400052454 /* <EMAIL> */,
				96BED0482E375E2400052454 /* <EMAIL> */,
				96BED0492E375E2400052454 /* <EMAIL> */,
				96BED04A2E375E2400052454 /* <EMAIL> */,
				96BED04B2E375E2400052454 /* <EMAIL> */,
				96BED04C2E375E2400052454 /* <EMAIL> */,
				96BED04D2E375E2400052454 /* <EMAIL> */,
				96BED04E2E375E2400052454 /* <EMAIL> */,
				96BED04F2E375E2400052454 /* <EMAIL> */,
				96BED0502E375E2400052454 /* <EMAIL> */,
				96BED0512E375E2400052454 /* <EMAIL> */,
				96BED0522E375E2400052454 /* <EMAIL> */,
				96BED0532E375E2400052454 /* <EMAIL> */,
				96BED0542E375E2400052454 /* <EMAIL> */,
				96BED0552E375E2400052454 /* <EMAIL> */,
				96BED0562E375E2400052454 /* <EMAIL> */,
				96BED0572E375E2400052454 /* <EMAIL> */,
				96BED0582E375E2400052454 /* <EMAIL> */,
				96BED0592E375E2400052454 /* <EMAIL> */,
				96BED05A2E375E2400052454 /* <EMAIL> */,
				96BED05B2E375E2400052454 /* <EMAIL> */,
				96BED05C2E375E2400052454 /* <EMAIL> */,
				96BED05D2E375E2400052454 /* <EMAIL> */,
				96BED05E2E375E2400052454 /* <EMAIL> */,
				96BED05F2E375E2400052454 /* <EMAIL> */,
				96BED0602E375E2400052454 /* <EMAIL> */,
				96BED0612E375E2400052454 /* <EMAIL> */,
				96BED0622E375E2400052454 /* <EMAIL> */,
				96BED0632E375E2400052454 /* <EMAIL> */,
				96BED0642E375E2400052454 /* <EMAIL> */,
				96BED0652E375E2400052454 /* <EMAIL> */,
				96BED0662E375E2400052454 /* <EMAIL> */,
				96BED0672E375E2400052454 /* <EMAIL> */,
				96BED0682E375E2400052454 /* <EMAIL> */,
				96BED0692E375E2400052454 /* <EMAIL> */,
				96BED06A2E375E2400052454 /* <EMAIL> */,
				96BED06B2E375E2400052454 /* <EMAIL> */,
				96BED06C2E375E2400052454 /* <EMAIL> */,
				96BED06D2E375E2400052454 /* <EMAIL> */,
				96BED06E2E375E2400052454 /* <EMAIL> */,
				96BED06F2E375E2400052454 /* <EMAIL> */,
				96BED0702E375E2400052454 /* <EMAIL> */,
				96BED0712E375E2400052454 /* <EMAIL> */,
				96BED0722E375E2400052454 /* <EMAIL> */,
				96BED0732E375E2400052454 /* <EMAIL> */,
				96BED0742E375E2400052454 /* <EMAIL> */,
				96BED0752E375E2400052454 /* <EMAIL> */,
				96BED0762E375E2400052454 /* <EMAIL> */,
				96BED0772E375E2400052454 /* <EMAIL> */,
				96BED0782E375E2400052454 /* <EMAIL> */,
				96BED0792E375E2400052454 /* <EMAIL> */,
				96BED07A2E375E2400052454 /* recent_n.png */,
				96BED07B2E375E2400052454 /* <EMAIL> */,
				96BED07C2E375E2400052454 /* recent_s.png */,
				96BED07D2E375E2400052454 /* <EMAIL> */,
				96BED07E2E375E2400052454 /* <EMAIL> */,
				96BED07F2E375E2400052454 /* <EMAIL> */,
				96BED0802E375E2400052454 /* <EMAIL> */,
				96BED0812E375E2400052454 /* <EMAIL> */,
				96BED0822E375E2400052454 /* <EMAIL> */,
				96BED0832E375E2400052454 /* <EMAIL> */,
			);
			path = images;
			sourceTree = "<group>";
		};
		96BED0902E375E2400052454 /* json */ = {
			isa = PBXGroup;
			children = (
				96BED0852E375E2400052454 /* banner_loading.json */,
				96BED0862E375E2400052454 /* call_animation.json */,
				96BED0872E375E2400052454 /* game_loading.json */,
				96BED0882E375E2400052454 /* gift_animation.json */,
				96BED0892E375E2400052454 /* gift_send_animation.json */,
				96BED08A2E375E2400052454 /* match_animation.json */,
				96BED08B2E375E2400052454 /* message_call_animation.json */,
				96BED08C2E375E2400052454 /* next_anchor_animation.json */,
				96BED08D2E375E2400052454 /* pickup_animation.json */,
				96BED08E2E375E2400052454 /* sc_country.json */,
				96BED08F2E375E2400052454 /* wave_animation.json */,
			);
			path = json;
			sourceTree = "<group>";
		};
		96BED0922E375E2400052454 /* mp3 */ = {
			isa = PBXGroup;
			children = (
				96BED0912E375E2400052454 /* ring_call.mp3 */,
			);
			path = mp3;
			sourceTree = "<group>";
		};
		96BED0942E375E2400052454 /* plist */ = {
			isa = PBXGroup;
			children = (
				96BED0932E375E2400052454 /* EmojisList.plist */,
			);
			path = plist;
			sourceTree = "<group>";
		};
		96BED0962E375E2400052454 /* svga */ = {
			isa = PBXGroup;
			children = (
				96BED0952E375E2400052454 /* sc_gift.svga */,
			);
			path = svga;
			sourceTree = "<group>";
		};
		96BED0982E375E2400052454 /* source */ = {
			isa = PBXGroup;
			children = (
				96BED0972E375E2400052454 /* en_source.json */,
			);
			path = source;
			sourceTree = "<group>";
		};
		96BED0A82E375E2400052454 /* translate */ = {
			isa = PBXGroup;
			children = (
				96BED0982E375E2400052454 /* source */,
				96BED0992E375E2400052454 /* supercall_ar.json */,
				96BED09A2E375E2400052454 /* supercall_de.json */,
				96BED09B2E375E2400052454 /* supercall_en.json */,
				96BED09C2E375E2400052454 /* supercall_es.json */,
				96BED09D2E375E2400052454 /* supercall_fr.json */,
				96BED09E2E375E2400052454 /* supercall_hi.json */,
				96BED09F2E375E2400052454 /* supercall_it.json */,
				96BED0A02E375E2400052454 /* supercall_ja.json */,
				96BED0A12E375E2400052454 /* supercall_ko.json */,
				96BED0A22E375E2400052454 /* supercall_pt.json */,
				96BED0A32E375E2400052454 /* supercall_ru.json */,
				96BED0A42E375E2400052454 /* supercall_th.json */,
				96BED0A52E375E2400052454 /* supercall_tr.json */,
				96BED0A62E375E2400052454 /* supercall_vi.json */,
				96BED0A72E375E2400052454 /* supercall_zh-tw.json */,
			);
			path = translate;
			sourceTree = "<group>";
		};
		96BED0AD2E375E2400052454 /* video */ = {
			isa = PBXGroup;
			children = (
				96BED0A92E375E2400052454 /* activity_promotion.mp4 */,
				96BED0AA2E375E2400052454 /* exit_recharge.mp4 */,
				96BED0AB2E375E2400052454 /* login_video.mp4 */,
				96BED0AC2E375E2400052454 /* new_user_promotion.mp4 */,
			);
			path = video;
			sourceTree = "<group>";
		};
		96BED0AE2E375E2400052454 /* supercall_data */ = {
			isa = PBXGroup;
			children = (
				96BECF622E375E2400052454 /* font_family */,
				96BED0842E375E2400052454 /* images */,
				96BED0902E375E2400052454 /* json */,
				96BED0922E375E2400052454 /* mp3 */,
				96BED0942E375E2400052454 /* plist */,
				96BED0962E375E2400052454 /* svga */,
				96BED0A82E375E2400052454 /* translate */,
				96BED0AD2E375E2400052454 /* video */,
			);
			path = supercall_data;
			sourceTree = "<group>";
		};
		96BED0B12E375E2400052454 /* script */ = {
			isa = PBXGroup;
			children = (
				96BED0AE2E375E2400052454 /* supercall_data */,
				96BED0AF2E375E2400052454 /* asster_change.sh */,
				96BED0B02E375E2400052454 /* supercall_data.zip */,
			);
			path = script;
			sourceTree = "<group>";
		};
		96BED0BD2E375E2400052454 /* SCCode */ = {
			isa = PBXGroup;
			children = (
				96BECDA02E375E2400052454 /* Commonts */,
				96BECF5B2E375E2400052454 /* Modules */,
				96BED0B12E375E2400052454 /* script */,
				96BED0B82E375E2400052454 /* SCAppIntegrationManager.h */,
				96BED0B92E375E2400052454 /* SCAppIntegrationManager.m */,
				96BED0BA2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.h */,
				96BED0BB2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.m */,
				96BED0BC2E375E2400052454 /* SCCodeHeader.h */,
			);
			path = SCCode;
			sourceTree = "<group>";
		};
		BA6E826579C4044FE7883C40 /* Pods */ = {
			isa = PBXGroup;
			children = (
				E23541B5E0E2A835639EB28C /* Pods-Supercall.debug.xcconfig */,
				B9A3999F036961456BE2A952 /* Pods-Supercall.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E6CC267D2B32883E0004D794 = {
			isa = PBXGroup;
			children = (
				96BED0BD2E375E2400052454 /* SCCode */,
				E6CC26882B32883E0004D794 /* Supercall */,
				E6CC26872B32883E0004D794 /* Products */,
				BA6E826579C4044FE7883C40 /* Pods */,
				35E5F736999B0677F30F4530 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		E6CC26872B32883E0004D794 /* Products */ = {
			isa = PBXGroup;
			children = (
				E6CC26862B32883E0004D794 /* Supercall.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E6CC26882B32883E0004D794 /* Supercall */ = {
			isa = PBXGroup;
			children = (
				E6CC26892B32883E0004D794 /* AppDelegate.h */,
				E6CC268A2B32883E0004D794 /* AppDelegate.m */,
				E6CC268F2B32883E0004D794 /* ViewController.h */,
				E6CC26902B32883E0004D794 /* ViewController.m */,
				E6CC26922B32883E0004D794 /* Main.storyboard */,
				E6CC26952B3288450004D794 /* Assets.xcassets */,
				E6CC26972B3288450004D794 /* LaunchScreen.storyboard */,
				E6CC269A2B3288450004D794 /* Info.plist */,
				E6CC269B2B3288450004D794 /* main.m */,
				E69CE72C2B34100100290E17 /* SuperCallPrefixHeader.pch */,
				BEA4FA9D2CF86AD70029C50B /* SCALoginViewController.h */,
				BEA4FA9E2CF86AD70029C50B /* SCALoginViewController.m */,
				BEA4FA9F2CF86AD70029C50B /* SCALoginViewController.xib */,
			);
			path = Supercall;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E6CC26852B32883E0004D794 /* Supercall */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E6CC269F2B3288450004D794 /* Build configuration list for PBXNativeTarget "Supercall" */;
			buildPhases = (
				6FBC36A9A68670340E6B7C14 /* [CP] Check Pods Manifest.lock */,
				E605C7692CCCA2E5005DC9B3 /* Run Script */,
				E6CC26822B32883E0004D794 /* Sources */,
				E6CC26832B32883E0004D794 /* Frameworks */,
				E6CC26842B32883E0004D794 /* Resources */,
				7DFA456BF66FAAA64876ED13 /* [CP] Embed Pods Frameworks */,
				D5CAA8D8932332346B1A0B60 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Supercall;
			productName = Supercall;
			productReference = E6CC26862B32883E0004D794 /* Supercall.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E6CC267E2B32883E0004D794 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				CLASSPREFIX = SC;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					E6CC26852B32883E0004D794 = {
						CreatedOnToolsVersion = 14.3.1;
						LastSwiftMigration = 1600;
					};
				};
			};
			buildConfigurationList = E6CC26812B32883E0004D794 /* Build configuration list for PBXProject "Supercall" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E6CC267D2B32883E0004D794;
			productRefGroup = E6CC26872B32883E0004D794 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E6CC26852B32883E0004D794 /* Supercall */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E6CC26842B32883E0004D794 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96BED1E42E375E2400052454 /* <EMAIL> in Resources */,
				96BED1E52E375E2400052454 /* <EMAIL> in Resources */,
				96BED1E62E375E2400052454 /* <EMAIL> in Resources */,
				96BED1E72E375E2400052454 /* supercall_de.json in Resources */,
				96BED1E82E375E2400052454 /* <EMAIL> in Resources */,
				96BED1E92E375E2400052454 /* <EMAIL> in Resources */,
				96BED1EA2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1EB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1EC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1ED2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1EE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1EF2E375E2400052454 /* recent_n.png in Resources */,
				96BED1F02E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F12E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F22E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F32E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F42E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F52E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F62E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F72E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F82E375E2400052454 /* <EMAIL> in Resources */,
				96BED1F92E375E2400052454 /* gift_animation.json in Resources */,
				96BED1FA2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1FB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1FC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1FD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED1FE2E375E2400052454 /* BaiJamjuree-Regular.ttf in Resources */,
				96BED1FF2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2002E375E2400052454 /* <EMAIL> in Resources */,
				96BED2012E375E2400052454 /* next_anchor_animation.json in Resources */,
				96BED2022E375E2400052454 /* <EMAIL> in Resources */,
				96BED2032E375E2400052454 /* <EMAIL> in Resources */,
				96BED2042E375E2400052454 /* <EMAIL> in Resources */,
				96BED2052E375E2400052454 /* <EMAIL> in Resources */,
				96BED2062E375E2400052454 /* <EMAIL> in Resources */,
				96BED2072E375E2400052454 /* <EMAIL> in Resources */,
				96BED2082E375E2400052454 /* <EMAIL> in Resources */,
				96BED2092E375E2400052454 /* <EMAIL> in Resources */,
				96BED20A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED20B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED20C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED20D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED20E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED20F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2102E375E2400052454 /* <EMAIL> in Resources */,
				96BED2112E375E2400052454 /* <EMAIL> in Resources */,
				96BED2122E375E2400052454 /* <EMAIL> in Resources */,
				96BED2132E375E2400052454 /* face_s.png in Resources */,
				96BED2142E375E2400052454 /* <EMAIL> in Resources */,
				96BED2152E375E2400052454 /* <EMAIL> in Resources */,
				96BED2162E375E2400052454 /* <EMAIL> in Resources */,
				96BED2172E375E2400052454 /* <EMAIL> in Resources */,
				96BED2182E375E2400052454 /* <EMAIL> in Resources */,
				96BED2192E375E2400052454 /* <EMAIL> in Resources */,
				96BED21A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED21B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED21C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED21D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED21E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED21F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2202E375E2400052454 /* <EMAIL> in Resources */,
				96BED2212E375E2400052454 /* <EMAIL> in Resources */,
				96BED2222E375E2400052454 /* <EMAIL> in Resources */,
				96BED2232E375E2400052454 /* <EMAIL> in Resources */,
				96BED2242E375E2400052454 /* <EMAIL> in Resources */,
				96BED2252E375E2400052454 /* <EMAIL> in Resources */,
				96BED2262E375E2400052454 /* <EMAIL> in Resources */,
				96BED2272E375E2400052454 /* <EMAIL> in Resources */,
				96BED2282E375E2400052454 /* <EMAIL> in Resources */,
				96BED2292E375E2400052454 /* gift_send_animation.json in Resources */,
				96BED22A2E375E2400052454 /* supercall_data.zip in Resources */,
				96BED22B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED22C2E375E2400052454 /* login_video.mp4 in Resources */,
				96BED22D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED22E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED22F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2302E375E2400052454 /* <EMAIL> in Resources */,
				96BED2312E375E2400052454 /* match_animation.json in Resources */,
				96BED2322E375E2400052454 /* <EMAIL> in Resources */,
				96BED2332E375E2400052454 /* <EMAIL> in Resources */,
				96BED2342E375E2400052454 /* <EMAIL> in Resources */,
				96BED2352E375E2400052454 /* <EMAIL> in Resources */,
				96BED2362E375E2400052454 /* <EMAIL> in Resources */,
				96BED2372E375E2400052454 /* <EMAIL> in Resources */,
				96BED2382E375E2400052454 /* <EMAIL> in Resources */,
				96BED2392E375E2400052454 /* <EMAIL> in Resources */,
				96BED23A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED23B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED23C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED23D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED23E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED23F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2402E375E2400052454 /* <EMAIL> in Resources */,
				96BED2412E375E2400052454 /* <EMAIL> in Resources */,
				96BED2422E375E2400052454 /* supercall_ko.json in Resources */,
				96BED2432E375E2400052454 /* <EMAIL> in Resources */,
				96BED2442E375E2400052454 /* BaiJamjuree-BoldItalic.ttf in Resources */,
				96BED2452E375E2400052454 /* <EMAIL> in Resources */,
				96BED2462E375E2400052454 /* <EMAIL> in Resources */,
				96BED2472E375E2400052454 /* <EMAIL> in Resources */,
				96BED2482E375E2400052454 /* <EMAIL> in Resources */,
				96BED2492E375E2400052454 /* BaiJamjuree-SemiBold.ttf in Resources */,
				96BED24A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED24B2E375E2400052454 /* car_s.png in Resources */,
				96BED24C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED24D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED24E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED24F2E375E2400052454 /* wave_animation.json in Resources */,
				96BED2502E375E2400052454 /* bell_s.png in Resources */,
				96BED2512E375E2400052454 /* <EMAIL> in Resources */,
				96BED2522E375E2400052454 /* <EMAIL> in Resources */,
				96BED2532E375E2400052454 /* <EMAIL> in Resources */,
				96BED2542E375E2400052454 /* <EMAIL> in Resources */,
				96BED2552E375E2400052454 /* <EMAIL> in Resources */,
				96BED2562E375E2400052454 /* <EMAIL> in Resources */,
				96BED2572E375E2400052454 /* BaiJamjuree-Italic.ttf in Resources */,
				96BED2582E375E2400052454 /* <EMAIL> in Resources */,
				96BED2592E375E2400052454 /* <EMAIL> in Resources */,
				96BED25A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED25B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED25C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED25D2E375E2400052454 /* flower_s.png in Resources */,
				96BED25E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED25F2E375E2400052454 /* supercall_it.json in Resources */,
				96BED2602E375E2400052454 /* <EMAIL> in Resources */,
				96BED2612E375E2400052454 /* <EMAIL> in Resources */,
				96BED2622E375E2400052454 /* <EMAIL> in Resources */,
				96BED2632E375E2400052454 /* <EMAIL> in Resources */,
				96BED2642E375E2400052454 /* sc_country.json in Resources */,
				96BED2652E375E2400052454 /* <EMAIL> in Resources */,
				96BED2662E375E2400052454 /* <EMAIL> in Resources */,
				96BED2672E375E2400052454 /* <EMAIL> in Resources */,
				96BED2682E375E2400052454 /* <EMAIL> in Resources */,
				96BED2692E375E2400052454 /* <EMAIL> in Resources */,
				96BED26A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED26B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED26C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED26D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED26E2E375E2400052454 /* activity_promotion.mp4 in Resources */,
				96BED26F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2702E375E2400052454 /* <EMAIL> in Resources */,
				96BED2712E375E2400052454 /* <EMAIL> in Resources */,
				96BED2722E375E2400052454 /* <EMAIL> in Resources */,
				96BED2732E375E2400052454 /* <EMAIL> in Resources */,
				96BED2742E375E2400052454 /* <EMAIL> in Resources */,
				96BED2752E375E2400052454 /* <EMAIL> in Resources */,
				96BED2762E375E2400052454 /* <EMAIL> in Resources */,
				96BED2772E375E2400052454 /* <EMAIL> in Resources */,
				96BED2782E375E2400052454 /* <EMAIL> in Resources */,
				96BED2792E375E2400052454 /* <EMAIL> in Resources */,
				96BED27A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED27B2E375E2400052454 /* supercall_fr.json in Resources */,
				96BED27C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED27D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED27E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED27F2E375E2400052454 /* characters_n.png in Resources */,
				96BED2802E375E2400052454 /* <EMAIL> in Resources */,
				96BED2812E375E2400052454 /* <EMAIL> in Resources */,
				96BED2822E375E2400052454 /* <EMAIL> in Resources */,
				96BED2832E375E2400052454 /* <EMAIL> in Resources */,
				96BED2842E375E2400052454 /* <EMAIL> in Resources */,
				96BED2852E375E2400052454 /* <EMAIL> in Resources */,
				96BED2862E375E2400052454 /* supercall_es.json in Resources */,
				96BED2872E375E2400052454 /* <EMAIL> in Resources */,
				96BED2882E375E2400052454 /* <EMAIL> in Resources */,
				96BED2892E375E2400052454 /* <EMAIL> in Resources */,
				96BED28A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED28B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED28C2E375E2400052454 /* backspace_n.png in Resources */,
				96BED28D2E375E2400052454 /* supercall_th.json in Resources */,
				96BED28E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED28F2E375E2400052454 /* new_user_promotion.mp4 in Resources */,
				96BED2902E375E2400052454 /* banner_loading.json in Resources */,
				96BED2912E375E2400052454 /* <EMAIL> in Resources */,
				96BED2922E375E2400052454 /* <EMAIL> in Resources */,
				96BED2932E375E2400052454 /* <EMAIL> in Resources */,
				96BED2942E375E2400052454 /* <EMAIL> in Resources */,
				96BED2952E375E2400052454 /* <EMAIL> in Resources */,
				96BED2962E375E2400052454 /* <EMAIL> in Resources */,
				96BED2972E375E2400052454 /* <EMAIL> in Resources */,
				96BED2982E375E2400052454 /* <EMAIL> in Resources */,
				96BED2992E375E2400052454 /* <EMAIL> in Resources */,
				96BED29A2E375E2400052454 /* EmojisList.plist in Resources */,
				96BED29B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED29C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED29D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED29E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED29F2E375E2400052454 /* message_call_animation.json in Resources */,
				96BED2A02E375E2400052454 /* BaiJamjuree-Bold.ttf in Resources */,
				96BED2A12E375E2400052454 /* supercall_zh-tw.json in Resources */,
				96BED2A22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A42E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A62E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A72E375E2400052454 /* call_animation.json in Resources */,
				96BED2A82E375E2400052454 /* <EMAIL> in Resources */,
				96BED2A92E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AA2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2AF2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B02E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B12E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B42E375E2400052454 /* face_n.png in Resources */,
				96BED2B52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B62E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B72E375E2400052454 /* <EMAIL> in Resources */,
				96BED2B82E375E2400052454 /* bell_n.png in Resources */,
				96BED2B92E375E2400052454 /* flower_n.png in Resources */,
				96BED2BA2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2BB2E375E2400052454 /* characters_s.png in Resources */,
				96BED2BC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2BD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2BE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2BF2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C02E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C12E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C42E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C62E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C72E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C82E375E2400052454 /* <EMAIL> in Resources */,
				96BED2C92E375E2400052454 /* <EMAIL> in Resources */,
				96BED2CA2E375E2400052454 /* en_source.json in Resources */,
				96BED2CB2E375E2400052454 /* supercall_hi.json in Resources */,
				96BED2CC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2CD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2CE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2CF2E375E2400052454 /* BaiJamjuree-SemiBoldItalic.ttf in Resources */,
				96BED2D02E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D12E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D42E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2D62E375E2400052454 /* exit_recharge.mp4 in Resources */,
				96BED2D72E375E2400052454 /* supercall_ru.json in Resources */,
				96BED2D82E375E2400052454 /* supercall_tr.json in Resources */,
				96BED2DA2E375E2400052454 /* supercall_en.json in Resources */,
				96BED2DB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2DC2E375E2400052454 /* ring_call.mp3 in Resources */,
				96BED2DD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2DE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2DF2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E02E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E12E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E42E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E62E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E72E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E82E375E2400052454 /* <EMAIL> in Resources */,
				96BED2E92E375E2400052454 /* <EMAIL> in Resources */,
				96BED2EA2E375E2400052454 /* supercall_ja.json in Resources */,
				96BED2EB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2EC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2ED2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2EE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2EF2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F02E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F12E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F22E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F32E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F42E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F52E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F62E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F72E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F82E375E2400052454 /* <EMAIL> in Resources */,
				96BED2F92E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FA2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FB2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FC2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FD2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FE2E375E2400052454 /* <EMAIL> in Resources */,
				96BED2FF2E375E2400052454 /* supercall_ar.json in Resources */,
				96BED3002E375E2400052454 /* <EMAIL> in Resources */,
				96BED3012E375E2400052454 /* <EMAIL> in Resources */,
				96BED3022E375E2400052454 /* <EMAIL> in Resources */,
				96BED3032E375E2400052454 /* <EMAIL> in Resources */,
				96BED3042E375E2400052454 /* <EMAIL> in Resources */,
				96BED3052E375E2400052454 /* <EMAIL> in Resources */,
				96BED3062E375E2400052454 /* <EMAIL> in Resources */,
				96BED3072E375E2400052454 /* <EMAIL> in Resources */,
				96BED3082E375E2400052454 /* game_loading.json in Resources */,
				96BED3092E375E2400052454 /* <EMAIL> in Resources */,
				96BED30A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED30B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED30C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED30E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED30F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED3102E375E2400052454 /* recent_s.png in Resources */,
				96BED3112E375E2400052454 /* <EMAIL> in Resources */,
				96BED3122E375E2400052454 /* <EMAIL> in Resources */,
				96BED3132E375E2400052454 /* pickup_animation.json in Resources */,
				96BED3142E375E2400052454 /* <EMAIL> in Resources */,
				96BED3152E375E2400052454 /* <EMAIL> in Resources */,
				96BED3162E375E2400052454 /* supercall_vi.json in Resources */,
				96BED3172E375E2400052454 /* <EMAIL> in Resources */,
				96BED3182E375E2400052454 /* <EMAIL> in Resources */,
				96BED3192E375E2400052454 /* <EMAIL> in Resources */,
				96BED31A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED31B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED31C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED31D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED31E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED31F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED3202E375E2400052454 /* asster_change.sh in Resources */,
				96BED3212E375E2400052454 /* sc_gift.svga in Resources */,
				96BED3222E375E2400052454 /* <EMAIL> in Resources */,
				96BED3232E375E2400052454 /* <EMAIL> in Resources */,
				96BED3242E375E2400052454 /* <EMAIL> in Resources */,
				96BED3252E375E2400052454 /* supercall_pt.json in Resources */,
				96BED3262E375E2400052454 /* <EMAIL> in Resources */,
				96BED3272E375E2400052454 /* <EMAIL> in Resources */,
				96BED3282E375E2400052454 /* car_n.png in Resources */,
				96BED3292E375E2400052454 /* <EMAIL> in Resources */,
				96BED32A2E375E2400052454 /* <EMAIL> in Resources */,
				96BED32B2E375E2400052454 /* <EMAIL> in Resources */,
				96BED32C2E375E2400052454 /* <EMAIL> in Resources */,
				96BED32D2E375E2400052454 /* <EMAIL> in Resources */,
				96BED32E2E375E2400052454 /* <EMAIL> in Resources */,
				96BED32F2E375E2400052454 /* <EMAIL> in Resources */,
				96BED3302E375E2400052454 /* <EMAIL> in Resources */,
				BEA4FAA02CF86AD70029C50B /* SCALoginViewController.xib in Resources */,
				E6CC26992B3288450004D794 /* LaunchScreen.storyboard in Resources */,
				E6CC26942B32883E0004D794 /* Main.storyboard in Resources */,
				E605C7662CCC91C8005DC9B3 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		6FBC36A9A68670340E6B7C14 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Supercall-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		7DFA456BF66FAAA64876ED13 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D5CAA8D8932332346B1A0B60 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Supercall/Pods-Supercall-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E605C7692CCCA2E5005DC9B3 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "##执行打包脚本\necho `pwd`\necho \"开始压缩资源\"\ncd \"SCCode/script\"\nsh \"./asster_change.sh\"\ncd ..\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E6CC26822B32883E0004D794 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96BED0BE2E375E2400052454 /* SCAnchorInfoViewModel.m in Sources */,
				96BED0BF2E375E2400052454 /* SCMorePanelView.m in Sources */,
				96BED0C02E375E2400052454 /* SCNetworkConstant.m in Sources */,
				96BED0C12E375E2400052454 /* SCNetworkStatusManager.m in Sources */,
				96BED0C22E375E2400052454 /* SCNetWorkResponseModel.m in Sources */,
				96BED0C32E375E2400052454 /* SCNoneFlagMessage.m in Sources */,
				96BED0C42E375E2400052454 /* SCCategoryUIViewControllerCode.m in Sources */,
				96BED0C52E375E2400052454 /* SCImageMessageCell.m in Sources */,
				96BED0C72E375E2400052454 /* SCThirdPartyPayWebViewController.m in Sources */,
				96BED0C82E375E2400052454 /* SCCategoryAPIManagerPay.m in Sources */,
				96BED0C92E375E2400052454 /* SCWaterFallFlowLayout.m in Sources */,
				96BED0CA2E375E2400052454 /* SCSafetyUtils.m in Sources */,
				96BED0CB2E375E2400052454 /* MJRefreshConst.m in Sources */,
				96BED0CC2E375E2400052454 /* MJRefreshTrailer.m in Sources */,
				96BED0CE2E375E2400052454 /* SCBaseTableViewCell.m in Sources */,
				96BED0CF2E375E2400052454 /* SCSingleJsonMessage.m in Sources */,
				96BED0D02E375E2400052454 /* SCFontManager.m in Sources */,
				96BED0D12E375E2400052454 /* SCVideoCallViewController.m in Sources */,
				96BED0D22E375E2400052454 /* SCCryptoUtils.m in Sources */,
				96BED0D32E375E2400052454 /* SCFlashChatViewController.m in Sources */,
				96BED0D42E375E2400052454 /* SCCategoryNSDateCode.m in Sources */,
				96BED0D52E375E2400052454 /* SCVideoCallChatCell.m in Sources */,
				96BED0D62E375E2400052454 /* SCActivityFloatingView.m in Sources */,
				96BED0D72E375E2400052454 /* SCLocalVideoPlayer.m in Sources */,
				96BED0D82E375E2400052454 /* SCMyLevelHeaderView.m in Sources */,
				96BED0DA2E375E2400052454 /* SCFollowListViewController.m in Sources */,
				96BED0DB2E375E2400052454 /* SCPopupManager.m in Sources */,
				96BED0DC2E375E2400052454 /* MJRefreshHeader.m in Sources */,
				96BED0DD2E375E2400052454 /* SCNativeSQLiteDatabase.m in Sources */,
				96BED0DE2E375E2400052454 /* SCCircularCountdownView.m in Sources */,
				96BED0DF2E375E2400052454 /* SCRobotCustomerQuestionCell.m in Sources */,
				96BED0E02E375E2400052454 /* SCPersonalViewController.m in Sources */,
				96BED0E12E375E2400052454 /* SCGradientColors.m in Sources */,
				96BED0E22E375E2400052454 /* SCPromotionDisplayModel.m in Sources */,
				96BED0E32E375E2400052454 /* SCRankingListView.m in Sources */,
				96BED0E42E375E2400052454 /* SCThirdPartyPayPopup.m in Sources */,
				96BED0E52E375E2400052454 /* SCAskGiftTipView.m in Sources */,
				96BED0E62E375E2400052454 /* SCFreeTimeCountdownView.m in Sources */,
				96BED0E72E375E2400052454 /* SCCategoryUIImageViewCode.m in Sources */,
				96BED0E82E375E2400052454 /* SCPersonalItemCell.m in Sources */,
				96BED0E92E375E2400052454 /* SCAlertViewController.m in Sources */,
				96BED0EB2E375E2400052454 /* SCVideoHorizontalListView.m in Sources */,
				96BED0EC2E375E2400052454 /* SCBaseAppService.m in Sources */,
				96BED0EE2E375E2400052454 /* SCThrottle.m in Sources */,
				96BED0EF2E375E2400052454 /* SCKeychainUtils.m in Sources */,
				96BED0F02E375E2400052454 /* SCCallNotificationPopup.m in Sources */,
				96BED0F12E375E2400052454 /* SCFullScreenPreviewMediaViewController.m in Sources */,
				96BED0F22E375E2400052454 /* SCCoinsStoreViewModel.m in Sources */,
				96BED0F32E375E2400052454 /* SCConversationListCell.m in Sources */,
				96BED0F42E375E2400052454 /* UIScrollView+EmptyDataSet.m in Sources */,
				96BED0F52E375E2400052454 /* SCGradientLineView.m in Sources */,
				96BED0F62E375E2400052454 /* SCCustomPageControl.m in Sources */,
				96BED0F72E375E2400052454 /* SCCallHistoryCell.m in Sources */,
				96BED0F82E375E2400052454 /* SCCoinsPopupViewController.m in Sources */,
				96BED0F92E375E2400052454 /* SCUserBaseInfoView.m in Sources */,
				96BED0FA2E375E2400052454 /* SCGiftSendNumModel.m in Sources */,
				96BED0FB2E375E2400052454 /* SCConstant.m in Sources */,
				96BED0FC2E375E2400052454 /* SCRankingTitleView.m in Sources */,
				96BED0FD2E375E2400052454 /* SCCountryPickerViewController.m in Sources */,
				96BED0FE2E375E2400052454 /* AGEmojiPageView.m in Sources */,
				96BED0FF2E375E2400052454 /* MJRefreshComponent.m in Sources */,
				96BED1002E375E2400052454 /* SCRankingHeaderView.m in Sources */,
				96BED1012E375E2400052454 /* SCOrderResultModel.m in Sources */,
				96BED1022E375E2400052454 /* SCCategoryAPIManagerGift.m in Sources */,
				96BED1032E375E2400052454 /* SCOrderedDictionary.m in Sources */,
				96BED1042E375E2400052454 /* SCNewUserPromotionPopup.m in Sources */,
				96BED1052E375E2400052454 /* SCBannerService.m in Sources */,
				96BED1062E375E2400052454 /* SCCategoryUILabelCode.m in Sources */,
				96BED1072E375E2400052454 /* SCActivityPromotionPopup.m in Sources */,
				96BED1082E375E2400052454 /* MJRefreshConfig.m in Sources */,
				96BED1092E375E2400052454 /* SCUserItemView.m in Sources */,
				96BED10A2E375E2400052454 /* MJRefreshStateHeader.m in Sources */,
				96BED10B2E375E2400052454 /* SCAuthManager.m in Sources */,
				96BED10C2E375E2400052454 /* SCAppUtils.m in Sources */,
				96BED10D2E375E2400052454 /* SCLimitTextView.m in Sources */,
				96BED10E2E375E2400052454 /* SCFloatingView.m in Sources */,
				96BED10F2E375E2400052454 /* SCSocketService.m in Sources */,
				96BED1122E375E2400052454 /* SCLaunchVCViewController.m in Sources */,
				96BED1132E375E2400052454 /* SCCallingView.m in Sources */,
				96BED1142E375E2400052454 /* SCUserRankModel.m in Sources */,
				96BED1152E375E2400052454 /* SCAnchorInfoBottomView.m in Sources */,
				96BED1162E375E2400052454 /* SCAPIServiceManager.m in Sources */,
				96BED1172E375E2400052454 /* SCCallHangupPopup.m in Sources */,
				96BED1182E375E2400052454 /* SCResourceManager.m in Sources */,
				96BED1192E375E2400052454 /* SCLoginViewController.m in Sources */,
				96BED11A2E375E2400052454 /* SCVoiceMessageCell.m in Sources */,
				96BED11B2E375E2400052454 /* SCStrongGuidePopoUp.m in Sources */,
				96BED11C2E375E2400052454 /* SCPersonalEditViewModel.m in Sources */,
				96BED11D2E375E2400052454 /* SCGiftSendNumTipDisplayModel.m in Sources */,
				96BED11E2E375E2400052454 /* SCCallService.m in Sources */,
				96BED1202E375E2400052454 /* SCRobotCustomerAnswerCell.m in Sources */,
				96BED1212E375E2400052454 /* SCThirdPayCannelDisplayModel.m in Sources */,
				96BED1222E375E2400052454 /* SCPersonalViewModel.m in Sources */,
				96BED1232E375E2400052454 /* SCCategoryIM.m in Sources */,
				96BED1242E375E2400052454 /* SCRechargeCardMessageCell.m in Sources */,
				96BED1252E375E2400052454 /* SCCategoryUIImageCode.m in Sources */,
				96BED1262E375E2400052454 /* SCCategoryUIFontCode.m in Sources */,
				96BED1272E375E2400052454 /* SCBaseView.m in Sources */,
				96BED1282E375E2400052454 /* SCActionSheetViewController.m in Sources */,
				96BED12A2E375E2400052454 /* SCCountryPickeTableViewCell.m in Sources */,
				96BED12B2E375E2400052454 /* SCAppIntegrationManager+FlutterMerge.m in Sources */,
				96BED12C2E375E2400052454 /* SCTimer.m in Sources */,
				96BED12D2E375E2400052454 /* SCCategoryAPIManagerDiscover.m in Sources */,
				96BED12E2E375E2400052454 /* SCIMService.m in Sources */,
				96BED12F2E375E2400052454 /* SCCoinsListView.m in Sources */,
				96BED1302E375E2400052454 /* SCNativeMessageModel.m in Sources */,
				96BED1312E375E2400052454 /* SCAnchorSubTitleJXCategoryTitleCellModel.m in Sources */,
				96BED1322E375E2400052454 /* SCJoinChannelProgressView.m in Sources */,
				96BED1332E375E2400052454 /* SCScrollView.m in Sources */,
				96BED1342E375E2400052454 /* SCHTTPRequestSerializer.m in Sources */,
				96BED1352E375E2400052454 /* SCCategoryUIButtonCode.m in Sources */,
				96BED1362E375E2400052454 /* SCFlashMatchSuccessPopupView.m in Sources */,
				96BED1372E375E2400052454 /* MJRefreshFooter.m in Sources */,
				96BED1382E375E2400052454 /* SCConversationListViewController.m in Sources */,
				96BED1392E375E2400052454 /* SCNavigationBar.m in Sources */,
				96BED13A2E375E2400052454 /* SCActionService.m in Sources */,
				96BED13B2E375E2400052454 /* SCWebViewController.m in Sources */,
				96BED13C2E375E2400052454 /* SCCountryService.m in Sources */,
				96BED13D2E375E2400052454 /* SCBlockListViewController.m in Sources */,
				96BED13E2E375E2400052454 /* SCThirdPayItemView.m in Sources */,
				96BED13F2E375E2400052454 /* SCCategoryAPIManagerCoins.m in Sources */,
				96BED1402E375E2400052454 /* AGEmojiKeyBoardView.m in Sources */,
				96BED1412E375E2400052454 /* SCDBActionModel.m in Sources */,
				96BED1422E375E2400052454 /* SCRankingViewController.m in Sources */,
				96BED1432E375E2400052454 /* SCRearCameraConfigModel.m in Sources */,
				96BED1442E375E2400052454 /* SCGiftTipView.m in Sources */,
				96BED1452E375E2400052454 /* SCRobotCustomerQuestionSetCell.m in Sources */,
				96BED1472E375E2400052454 /* SCAnchorInfoViewController.m in Sources */,
				96BED1482E375E2400052454 /* SCGiftCell.m in Sources */,
				96BED1492E375E2400052454 /* SCMessageTitleItemDisplayModel.m in Sources */,
				96BED14A2E375E2400052454 /* SCStoreScoreAlertViewController.m in Sources */,
				96BED14B2E375E2400052454 /* SCNoEnoughCoinsAlertViewController.m in Sources */,
				96BED14C2E375E2400052454 /* SCAVAudioSessionUtils.m in Sources */,
				96BED14D2E375E2400052454 /* SCObservable.m in Sources */,
				96BED14E2E375E2400052454 /* SCBaseNavigationController.m in Sources */,
				96BED14F2E375E2400052454 /* SCVoicePlayerManager.m in Sources */,
				96BED1502E375E2400052454 /* SCSocketEventModel.m in Sources */,
				96BED1512E375E2400052454 /* SCMyLevelCell.m in Sources */,
				96BED1522E375E2400052454 /* SCGiftAnimPlayView.m in Sources */,
				96BED1532E375E2400052454 /* SCFlashRouletteView.m in Sources */,
				96BED1542E375E2400052454 /* SCDiscoverBannerCell.m in Sources */,
				96BED1552E375E2400052454 /* SCOnlineStatusChangeViewController.m in Sources */,
				96BED1562E375E2400052454 /* SCCategoryAPIManagerBanner.m in Sources */,
				96BED1572E375E2400052454 /* SCPersonalHeaderView.m in Sources */,
				96BED1582E375E2400052454 /* SCRoundedLabelView.m in Sources */,
				96BED1592E375E2400052454 /* SCTryAnchorAvatarCell.m in Sources */,
				96BED15A2E375E2400052454 /* SCMessagePopupManager.m in Sources */,
				96BED15B2E375E2400052454 /* SCCategoryAPIManagerFlashChat.m in Sources */,
				96BED15C2E375E2400052454 /* SCUserBoolChangeModel.m in Sources */,
				96BED15D2E375E2400052454 /* SCModelCompatibility.m in Sources */,
				96BED15E2E375E2400052454 /* SCAvatarActivityIndicatorView.m in Sources */,
				96BED15F2E375E2400052454 /* SCCoinsFullScreenHeaderView.m in Sources */,
				96BED1602E375E2400052454 /* SCFollowListCell.m in Sources */,
				96BED1612E375E2400052454 /* SCAnchorActionSheetViewController.m in Sources */,
				96BED1622E375E2400052454 /* SCAlignedCollectionViewFlowLayout.m in Sources */,
				96BED1632E375E2400052454 /* SCGiftService.m in Sources */,
				96BED1642E375E2400052454 /* SCCountrySelectPopoupView.m in Sources */,
				96BED1652E375E2400052454 /* SCThreadSafeDictionary.m in Sources */,
				96BED1662E375E2400052454 /* SCDiscoverViewController.m in Sources */,
				96BED1672E375E2400052454 /* SCLanguageCell.m in Sources */,
				96BED1682E375E2400052454 /* MBProgressHUD.m in Sources */,
				96BED1692E375E2400052454 /* SCThirdPayCannelCell.m in Sources */,
				96BED16A2E375E2400052454 /* SCOnlineStatesService.m in Sources */,
				96BED16B2E375E2400052454 /* SCTrackingUtils.m in Sources */,
				96BED16C2E375E2400052454 /* SCVoiceRecorderManager.m in Sources */,
				96BED16D2E375E2400052454 /* SCCategoryColor.m in Sources */,
				96BED16E2E375E2400052454 /* SCNetworkManager.m in Sources */,
				96BED16F2E375E2400052454 /* SCAnchorViewController.m in Sources */,
				96BED1702E375E2400052454 /* SCCallDisablePrompt.m in Sources */,
				96BED1712E375E2400052454 /* SCCoinsBigCollectionViewCell.m in Sources */,
				96BED1722E375E2400052454 /* SCCountrySelectCell.m in Sources */,
				96BED1732E375E2400052454 /* SCAboutHederView.m in Sources */,
				96BED1742E375E2400052454 /* SCIAPManager.m in Sources */,
				96BED1752E375E2400052454 /* UIScrollView+MJExtension.m in Sources */,
				96BED1762E375E2400052454 /* SCCallSessionModel.m in Sources */,
				96BED1772E375E2400052454 /* SCGiftListView.m in Sources */,
				96BED1782E375E2400052454 /* SCPersonalEditViewController.m in Sources */,
				96BED1792E375E2400052454 /* SCFlashChatMatchViewController.m in Sources */,
				96BED17A2E375E2400052454 /* SCCategoryAPIManagerCall.m in Sources */,
				96BED17B2E375E2400052454 /* SCVideoCallRemainTimeCountdownView.m in Sources */,
				96BED17C2E375E2400052454 /* SCOnlineStatusSubscribe.m in Sources */,
				96BED17D2E375E2400052454 /* SCAnchorCollectionView.m in Sources */,
				96BED17E2E375E2400052454 /* SCCountryModel.m in Sources */,
				96BED17F2E375E2400052454 /* SCNewUserAwardPopup.m in Sources */,
				96BED1802E375E2400052454 /* SCBaseCollectionCell.m in Sources */,
				96BED1812E375E2400052454 /* SCAnchorInfoHeaderView.m in Sources */,
				96BED1822E375E2400052454 /* SCTextMessageCell.m in Sources */,
				96BED1832E375E2400052454 /* UIView+MJExtension.m in Sources */,
				96BED1842E375E2400052454 /* SCPayService.m in Sources */,
				96BED1852E375E2400052454 /* SCFloatingLayoutView.m in Sources */,
				96BED1862E375E2400052454 /* SCCategoryUserDefaultsCode.m in Sources */,
				96BED1872E375E2400052454 /* SCCategoryUICollectionViewCode.m in Sources */,
				96BED1882E375E2400052454 /* SCOnlineStatusView.m in Sources */,
				96BED1892E375E2400052454 /* SCAnchorCell.m in Sources */,
				96BED18A2E375E2400052454 /* SCAnchorSubTagViewController.m in Sources */,
				96BED18B2E375E2400052454 /* SCAnchorCoinsViewController.m in Sources */,
				96BED18C2E375E2400052454 /* SCConversationDisplayModel.m in Sources */,
				96BED18D2E375E2400052454 /* SCDatePicker.m in Sources */,
				96BED18E2E375E2400052454 /* SCContinueRechargePopup.m in Sources */,
				96BED18F2E375E2400052454 /* SCCoinStoeFloatingView.m in Sources */,
				96BED1902E375E2400052454 /* SCDictionaryHelper.m in Sources */,
				96BED1912E375E2400052454 /* SCCategoryNSNumberCode.m in Sources */,
				96BED1922E375E2400052454 /* MJRefreshAutoNormalFooter.m in Sources */,
				96BED1932E375E2400052454 /* SCActivityPromotionDisplayModel.m in Sources */,
				96BED1942E375E2400052454 /* SCProgress.m in Sources */,
				96BED1952E375E2400052454 /* SCCategoryUIViewCode.m in Sources */,
				96BED1962E375E2400052454 /* SCRankingCell.m in Sources */,
				96BED1972E375E2400052454 /* SCLanguageViewController.m in Sources */,
				96BED1982E375E2400052454 /* SCLanguageManager.m in Sources */,
				96BED1992E375E2400052454 /* SCCallViewController.m in Sources */,
				96BED19A2E375E2400052454 /* SCAboutViewController.m in Sources */,
				96BED19B2E375E2400052454 /* SCCountryPickerViewModel.m in Sources */,
				96BED19C2E375E2400052454 /* SCMediaItemView.m in Sources */,
				96BED19D2E375E2400052454 /* SCDictionaryKeys.m in Sources */,
				96BED19E2E375E2400052454 /* SCTranslationService.m in Sources */,
				96BED19F2E375E2400052454 /* SCNativeConversationModel.m in Sources */,
				96BED1A02E375E2400052454 /* SCAppIntegrationManager.m in Sources */,
				96BED1A12E375E2400052454 /* SCBaseViewController.m in Sources */,
				96BED1A22E375E2400052454 /* SCImageBrowserView.m in Sources */,
				96BED1A32E375E2400052454 /* SCRechargeCardMessageContentModel.m in Sources */,
				96BED1A42E375E2400052454 /* SCPersonalItemDisplayModel.m in Sources */,
				96BED1A52E375E2400052454 /* SCSettingViewController.m in Sources */,
				96BED1A62E375E2400052454 /* SCNetworkCacheManager.m in Sources */,
				96BED1A72E375E2400052454 /* SCCategoryNSStringCode.m in Sources */,
				96BED1A82E375E2400052454 /* SCDataConverter.m in Sources */,
				96BED1A92E375E2400052454 /* SCAnchorEvaluateViewController.m in Sources */,
				96BED1AA2E375E2400052454 /* SCConversationInfoViewController.m in Sources */,
				96BED1AB2E375E2400052454 /* SCMessageViewController.m in Sources */,
				96BED1AC2E375E2400052454 /* SCLanguageModel.m in Sources */,
				96BED1AD2E375E2400052454 /* SCVoiceRecordingView.m in Sources */,
				96BED1AE2E375E2400052454 /* SCGiftMessageCell.m in Sources */,
				96BED1AF2E375E2400052454 /* SCFileMessageCell.m in Sources */,
				96BED1B02E375E2400052454 /* SCCallHistoryListViewController.m in Sources */,
				BED3F2AB2E38A70C001745A6 /* SCBlockUserDictHelper.m in Sources */,
				96BED1B12E375E2400052454 /* SCCollectionViewLeftAlignedFlowLayout.m in Sources */,
				96BED1B22E375E2400052454 /* SCGameWebViewController.m in Sources */,
				96BED1B32E375E2400052454 /* SCAnchorService.m in Sources */,
				96BED1B42E375E2400052454 /* SCMessageDisplayModel.m in Sources */,
				96BED1B52E375E2400052454 /* SCCallDBManager.m in Sources */,
				96BED1B62E375E2400052454 /* SCSmallVideoCell.m in Sources */,
				96BED1B72E375E2400052454 /* SCGiftPopupViewController.m in Sources */,
				96BED1B82E375E2400052454 /* SCPermissionManager.m in Sources */,
				96BED1B92E375E2400052454 /* SCAPIManage.m in Sources */,
				96BED1BA2E375E2400052454 /* SCAnchorSubTitleView.m in Sources */,
				96BED1BB2E375E2400052454 /* SCTagCell.m in Sources */,
				96BED1BC2E375E2400052454 /* UIScrollView+MJRefresh.m in Sources */,
				96BED1BD2E375E2400052454 /* MJRefreshNormalHeader.m in Sources */,
				96BED1BE2E375E2400052454 /* SCInviteRechargeCoinsPopupViewController.m in Sources */,
				96BED1BF2E375E2400052454 /* SCCoinsService.m in Sources */,
				96BED1C02E375E2400052454 /* SCXErrorModel.m in Sources */,
				96BED1C12E375E2400052454 /* SCVideoCallChatMessageModel.m in Sources */,
				96BED1C22E375E2400052454 /* SCRobotCustomerServiceViewController.m in Sources */,
				96BED1C32E375E2400052454 /* SCDisposeBag.m in Sources */,
				96BED1C42E375E2400052454 /* SCCategoryButton.m in Sources */,
				96BED1C52E375E2400052454 /* SCPayEntry.m in Sources */,
				96BED1C62E375E2400052454 /* MJRefreshAutoStateFooter.m in Sources */,
				96BED1C72E375E2400052454 /* SCVideoCallChatInputView.m in Sources */,
				96BED1C82E375E2400052454 /* SCGradientLabel.m in Sources */,
				96BED1C92E375E2400052454 /* UICollectionViewLayout+MJRefresh.m in Sources */,
				96BED1CA2E375E2400052454 /* SCOSSManager.m in Sources */,
				96BED1CB2E375E2400052454 /* MJRefreshAutoFooter.m in Sources */,
				96BED1CC2E375E2400052454 /* SCLabelListView.m in Sources */,
				96BED1CD2E375E2400052454 /* SCCategoryActionSheetViewControllerPhotoPicker.m in Sources */,
				96BED1CE2E375E2400052454 /* SCNativeSQLiteResultSet.m in Sources */,
				96BED1CF2E375E2400052454 /* SCBannerView.m in Sources */,
				96BED1D02E375E2400052454 /* SCRankingItemView.m in Sources */,
				96BED1D12E375E2400052454 /* SCCoinsCollectionViewCell.m in Sources */,
				96BED1D22E375E2400052454 /* SCMyLevelViewController.m in Sources */,
				96BED1D32E375E2400052454 /* SCCallExceptionalAlert.m in Sources */,
				96BED1D42E375E2400052454 /* SCGiftNumCell.m in Sources */,
				96BED1D52E375E2400052454 /* SCAnchorViewModel.m in Sources */,
				96BED1D62E375E2400052454 /* SCSafetyViewController.m in Sources */,
				96BED1D72E375E2400052454 /* SCHomeViewController.m in Sources */,
				96BED1D82E375E2400052454 /* SCCategorySocket.m in Sources */,
				96BED1DA2E375E2400052454 /* SCPersionEditDisplayModel.m in Sources */,
				96BED1DB2E375E2400052454 /* SCAnchorSubJXCategoryTitleCell.m in Sources */,
				96BED1DC2E375E2400052454 /* SCRobotCustomerDictionaryHelper.m in Sources */,
				96BED1DD2E375E2400052454 /* SCHyperLinkMessage.m in Sources */,
				96BED1DE2E375E2400052454 /* SCMyCoinsView.m in Sources */,
				96BED1DF2E375E2400052454 /* SCCoinsFullScreenViewController.m in Sources */,
				96BED1E02E375E2400052454 /* SCPromotionFloatingView.m in Sources */,
				96BED1E12E375E2400052454 /* SCCategoryUIColorCode.m in Sources */,
				96BED1E22E375E2400052454 /* SCAntiRecordView.m in Sources */,
				96BED1E32E375E2400052454 /* SCCategoryUITableViewCode.m in Sources */,
				E6CC26912B32883E0004D794 /* ViewController.m in Sources */,
				E6CC268B2B32883E0004D794 /* AppDelegate.m in Sources */,
				BEA4FAA12CF86AD70029C50B /* SCALoginViewController.m in Sources */,
				E6CC269C2B3288450004D794 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		E6CC26922B32883E0004D794 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E6CC26932B32883E0004D794 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		E6CC26972B3288450004D794 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E6CC26982B3288450004D794 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		E6CC269D2B3288450004D794 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		E6CC269E2B3288450004D794 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E6CC26A02B3288450004D794 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E23541B5E0E2A835639EB28C /* Pods-Supercall.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 764FX73K48;
				GCC_PREFIX_HEADER = Supercall/SuperCallPrefixHeader.pch;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Supercall/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "We need access to your camera for video calls.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "We use access to your microphone for voice chat";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "We need access to your photo library for setting up your profile picture.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "We need access to your photo library for setting up your profile picture.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "This identifier will be used by a third party to provide you with personalized advertising.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleLightContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.test.demok2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = test2;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		E6CC26A12B3288450004D794 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B9A3999F036961456BE2A952 /* Pods-Supercall.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "";
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 11;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 764FX73K48;
				GCC_PREFIX_HEADER = Supercall/SuperCallPrefixHeader.pch;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Supercall/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "We need access to your camera for video calls.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "We use access to your microphone for voice chat";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "We need access to your photo library for setting up your profile picture.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "We need access to your photo library for setting up your profile picture.";
				INFOPLIST_KEY_NSUserTrackingUsageDescription = "This identifier will be used by a third party to provide you with personalized advertising.";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen.storyboard;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleLightContent;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UIUserInterfaceStyle = Light;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.test.demok2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = test2;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E6CC26812B32883E0004D794 /* Build configuration list for PBXProject "Supercall" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E6CC269D2B3288450004D794 /* Debug */,
				E6CC269E2B3288450004D794 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E6CC269F2B3288450004D794 /* Build configuration list for PBXNativeTarget "Supercall" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E6CC26A02B3288450004D794 /* Debug */,
				E6CC26A12B3288450004D794 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E6CC267E2B32883E0004D794 /* Project object */;
}
