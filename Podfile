# Uncomment the next line to define a global platform for your project
 platform :ios, '12.0'

target 'Supercall' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for Supercall
  
  # IM 融云
  pod 'RongCloudIM/IMLib', '~> 5.20.1'
  
  # 资源压缩
  pod 'SSZipArchive'
  
  # Socket
  pod 'Socket.IO-Client-Swift', '~> 15.2.0'
  
  pod 'FBSDKCoreKit', '~> 17.3.0'

  pod 'Adjust', '~> 4.38'

  pod 'lottie-ios'
  
  pod 'AgoraRtcEngine_iOS','4.3.1'
  
  pod 'JXCategoryView','1.6.1'
  
  pod 'AFNetworking', '~> 4.0.1'
  
  pod 'SDWebImage', '~> 5.18.3'
  
  pod 'Masonry','1.1.0'
  
end
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '14.0'
     end
    
    end

  
end
